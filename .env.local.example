# Local Development Overrides
# Copy this to .env.local for local-specific settings
# This file is also ignored by Git for security

# Local Development Settings
VITE_ENVIRONMENT=development
VITE_APP_URL=http://localhost:8081
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_MOCK_DATA=false

# Local Database (if using local Supabase)
# VITE_SUPABASE_URL=http://localhost:54321
# VITE_SUPABASE_ANON_KEY=your-local-anon-key

# Local Payment Testing
# Use Paystack test keys for local development
# VITE_PAYSTACK_PUBLIC_KEY=pk_test_your-local-test-key

# Debug Settings
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=debug

# Local Feature Flags
VITE_ENABLE_EXPERIMENTAL_FEATURES=true
VITE_ENABLE_DEBUG_PANEL=true
