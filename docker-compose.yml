version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: phcityrent-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-phcityrent_dev}
      POSTGRES_USER: ${DB_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./Backend/database/init:/docker-entrypoint-initdb.d
    networks:
      - phcityrent-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: phcityrent-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - phcityrent-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./Backend
      dockerfile: Dockerfile
      target: ${NODE_ENV:-development}
    container_name: phcityrent-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DB_HOST=postgres
      - REDIS_HOST=redis
    ports:
      - "${PORT:-3001}:3001"
    volumes:
      - ./Backend:/app
      - /app/node_modules
      - uploads_data:/app/uploads
    networks:
      - phcityrent-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Application
  frontend:
    build:
      context: ./Frontend
      dockerfile: Dockerfile
      target: ${NODE_ENV:-development}
    container_name: phcityrent-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - VITE_API_BASE_URL=http://localhost:3001/api/v1
    ports:
      - "${FRONTEND_PORT:-8081}:8081"
    volumes:
      - ./Frontend:/app
      - /app/node_modules
    networks:
      - phcityrent-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: phcityrent-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
    networks:
      - phcityrent-network
    depends_on:
      - backend
      - frontend
    profiles:
      - production

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: phcityrent-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - phcityrent-network
    profiles:
      - monitoring

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: phcityrent-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - phcityrent-network
    profiles:
      - monitoring

  # Log Management - ELK Stack
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: phcityrent-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - phcityrent-network
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: phcityrent-logstash
    restart: unless-stopped
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
    networks:
      - phcityrent-network
    depends_on:
      - elasticsearch
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: phcityrent-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - phcityrent-network
    depends_on:
      - elasticsearch
    profiles:
      - logging

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  phcityrent-network:
    driver: bridge
