import { registerAs } from '@nestjs/config';

export const redisConfig = registerAs('redis', () => ({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT, 10) || 6379,
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB, 10) || 0,
  queueDb: parseInt(process.env.REDIS_QUEUE_DB, 10) || 1,
  sessionDb: parseInt(process.env.REDIS_SESSION_DB, 10) || 2,
  
  // Cache configuration
  cacheTtl: parseInt(process.env.CACHE_TTL, 10) || 300, // 5 minutes
  cacheMaxItems: parseInt(process.env.CACHE_MAX_ITEMS, 10) || 1000,
  
  // Connection options
  connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT, 10) || 10000,
  lazyConnect: process.env.REDIS_LAZY_CONNECT === 'true',
  retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY, 10) || 100,
  maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES, 10) || 3,
}));
