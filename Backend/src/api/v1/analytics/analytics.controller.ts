import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { AnalyticsService } from './analytics.service';
import { UserRole } from '../../../modules/users/entities/user.entity';

import { JwtAuthGuard } from '../../../modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../modules/auth/guards/roles.guard';
import { Roles } from '../../../modules/auth/decorators/roles.decorator';

@ApiTags('Analytics')
@Controller('analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN, UserRole.AGENT)
@ApiBearerAuth('JWT-auth')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('dashboard')
  @ApiOperation({ summary: 'Get dashboard statistics' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        users: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 1500 },
            activeAgents: { type: 'number', example: 25 },
          },
        },
        properties: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 500 },
            available: { type: 'number', example: 400 },
            rented: { type: 'number', example: 100 },
            occupancyRate: { type: 'number', example: 20.5 },
          },
        },
        payments: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 200 },
            totalRevenue: { type: 'number', example: 50000000 },
            monthlyRevenue: { type: 'number', example: 5000000 },
          },
        },
      },
    },
  })
  getDashboardStats() {
    return this.analyticsService.getDashboardStats();
  }

  @Get('properties')
  @ApiOperation({ summary: 'Get property analytics' })
  @ApiResponse({
    status: 200,
    description: 'Property analytics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        typeDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string', example: 'apartment' },
              count: { type: 'number', example: 150 },
            },
          },
        },
        priceRangeDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              label: { type: 'string', example: '₦1M - ₦2M' },
              count: { type: 'number', example: 75 },
            },
          },
        },
        locationDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              city: { type: 'string', example: 'Port Harcourt' },
              count: { type: 'number', example: 450 },
            },
          },
        },
      },
    },
  })
  getPropertyAnalytics() {
    return this.analyticsService.getPropertyAnalytics();
  }

  @Get('users')
  @ApiOperation({ summary: 'Get user analytics' })
  @ApiResponse({
    status: 200,
    description: 'User analytics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        roleDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              role: { type: 'string', example: 'tenant' },
              count: { type: 'number', example: 800 },
            },
          },
        },
        monthlyRegistrations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              month: { type: 'string', example: '2023-12-01' },
              count: { type: 'number', example: 45 },
            },
          },
        },
        activityStats: {
          type: 'object',
          properties: {
            activeUsers: { type: 'number', example: 1200 },
            inactiveUsers: { type: 'number', example: 300 },
            verifiedUsers: { type: 'number', example: 900 },
          },
        },
      },
    },
  })
  getUserAnalytics() {
    return this.analyticsService.getUserAnalytics();
  }

  @Get('payments')
  @ApiOperation({ summary: 'Get payment analytics' })
  @ApiResponse({
    status: 200,
    description: 'Payment analytics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        typeDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string', example: 'rent' },
              count: { type: 'number', example: 150 },
            },
          },
        },
        statusDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              status: { type: 'string', example: 'completed' },
              count: { type: 'number', example: 120 },
            },
          },
        },
        monthlyPayments: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              month: { type: 'string', example: '2023-12-01' },
              count: { type: 'number', example: 25 },
              amount: { type: 'number', example: 5000000 },
            },
          },
        },
      },
    },
  })
  getPaymentAnalytics() {
    return this.analyticsService.getPaymentAnalytics();
  }

  @Get('market-insights')
  @ApiOperation({ summary: 'Get market insights and trends' })
  @ApiResponse({
    status: 200,
    description: 'Market insights retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        averagePrices: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string', example: 'apartment' },
              averagePrice: { type: 'number', example: 1500000 },
            },
          },
        },
        pricetrends: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              month: { type: 'string', example: '2023-12-01' },
              averagePrice: { type: 'number', example: 1450000 },
            },
          },
        },
        demandAnalysis: {
          type: 'object',
          properties: {
            totalViews: { type: 'number', example: 15000 },
            totalInquiries: { type: 'number', example: 1500 },
            conversionRate: { type: 'number', example: 10.5 },
          },
        },
        popularAmenities: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              amenity: { type: 'string', example: 'parking' },
              count: { type: 'number', example: 350 },
            },
          },
        },
      },
    },
  })
  getMarketInsights() {
    return this.analyticsService.getMarketInsights();
  }
}
