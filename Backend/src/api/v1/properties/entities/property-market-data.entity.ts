export class PropertyMarketData {
    location: string;
    propertyType: string;
    averagePrice: number;
    medianPrice: number;
    pricePerSqft: number;
    totalProperties: number;
    availableProperties: number;
    averageDaysOnMarket: number;
    priceTrend: 'stable' | 'rising' | 'falling';
    demandScore: number; // e.g., 0-100
    competitionLevel: 'low' | 'medium' | 'high';
    // Add more market data fields
  }