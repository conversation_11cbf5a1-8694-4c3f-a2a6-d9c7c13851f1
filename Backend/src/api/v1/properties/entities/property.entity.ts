import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../../../modules/users/entities/user.entity';
import { Commission } from '../../../../api/v1/commissions/entities/commission.entity';
import { VerificationStep } from './verification-step.entity';

export enum PropertyType {
  APARTMENT = 'apartment',
  HOUSE = 'house',
  DUPLEX = 'duplex',
  BUNGALOW = 'bungalow',
  FLAT = 'flat',
  ROOM = 'room',
  SELF_CONTAIN = 'self_contain',
  SHOP = 'shop',
  OFFICE = 'office',
  WAREHOUSE = 'warehouse',
}

export enum PropertyStatus {
  AVAILABLE = 'available',
  RENTED = 'rented',
  MAINTENANCE = 'maintenance',
  INACTIVE = 'inactive',
}

export enum FurnishingStatus {
  FURNISHED = 'furnished',
  SEMI_FURNISHED = 'semi_furnished',
  UNFURNISHED = 'unfurnished',
}

@Entity('properties')
@Index(['bedrooms', 'bathrooms'])
export class Property {
  @ApiProperty({
    description: 'Unique identifier for the property',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Property title',
    example: 'Beautiful 3-bedroom apartment in GRA',
  })
  @Column()
  title: string;

  @ApiProperty({
    description: 'Property description',
    example: 'Spacious 3-bedroom apartment with modern amenities...',
  })
  @Column('text')
  description: string;

  @ApiProperty({
    description: 'Property type',
    enum: PropertyType,
    example: PropertyType.APARTMENT,
  })
  @Column({
    type: 'enum',
    enum: PropertyType,
  })
  @Index()
  propertyType: PropertyType;

  @ApiProperty({
    description: 'Property status',
    enum: PropertyStatus,
    example: PropertyStatus.AVAILABLE,
  })
  @Column({
    type: 'enum',
    enum: PropertyStatus,
    default: PropertyStatus.AVAILABLE,
  })
  @Index()
  status: PropertyStatus;

  @ApiProperty({
    description: 'Annual rent price in Naira',
    example: 1200000,
  })
  @Column({ type: 'decimal', precision: 15, scale: 2 })
  @Index()
  pricePerYear: number;

  @ApiProperty({
    description: 'Monthly rent price in Naira',
    example: 100000,
  })
  @Column({ type: 'decimal', precision: 15, scale: 2 })
  pricePerMonth: number;

  @ApiProperty({
    description: 'Security deposit amount',
    example: 200000,
  })
  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  securityDeposit?: number;

  @ApiProperty({
    description: 'Agent commission percentage',
    example: 10,
  })
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  agentCommission?: number;

  @ApiProperty({
    description: 'Property location/address',
    example: 'No. 15 Aba Road, GRA Phase 2, Port Harcourt',
  })
  @Column()
  @Index()
  location: string;

  @ApiProperty({
    description: 'City',
    example: 'Port Harcourt',
  })
  @Column({ default: 'Port Harcourt' })
  city: string;

  @ApiProperty({
    description: 'State',
    example: 'Rivers',
  })
  @Column({ default: 'Rivers' })
  state: string;

  @ApiProperty({
    description: 'Country',
    example: 'Nigeria',
  })
  @Column({ default: 'Nigeria' })
  country: string;

  @ApiProperty({
    description: 'Number of bedrooms',
    example: 3,
  })
  @Column({ type: 'int' })
  @Index()
  bedrooms: number;

  @ApiProperty({
    description: 'Number of bathrooms',
    example: 2,
  })
  @Column({ type: 'int' })
  @Index()
  bathrooms: number;

  @ApiProperty({
    description: 'Number of toilets',
    example: 3,
  })
  @Column({ type: 'int', nullable: true })
  toilets?: number;

  @ApiProperty({
    description: 'Property size in square meters',
    example: 120.5,
  })
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  sizeInSqm?: number;

  @ApiProperty({
    description: 'Furnishing status',
    enum: FurnishingStatus,
    example: FurnishingStatus.SEMI_FURNISHED,
  })
  @Column({
    type: 'enum',
    enum: FurnishingStatus,
    nullable: true,
  })
  furnishingStatus?: FurnishingStatus;

  @ApiProperty({
    description: 'Property amenities',
    example: ['parking', 'generator', 'security', 'water'],
    type: [String],
  })
  @Column('text', { array: true, default: [] })
  amenities: string[];

  @ApiProperty({
    description: 'Property images URLs',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    type: [String],
  })
  @Column('text', { array: true, default: [] })
  images: string[];

  @ApiProperty({
    description: 'Property video URL',
    example: 'https://example.com/video.mp4',
    required: false,
  })
  @Column({ nullable: true })
  videoUrl?: string;

  @ApiProperty({
    description: 'Virtual tour URL',
    example: 'https://example.com/virtual-tour',
    required: false,
  })
  @Column({ nullable: true })
  virtualTourUrl?: string;

  @ApiProperty({
    description: 'Latitude coordinate',
    example: 4.8156,
  })
  @Column({ type: 'decimal', precision: 10, scale: 8, nullable: true })
  latitude?: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 7.0498,
  })
  @Column({ type: 'decimal', precision: 11, scale: 8, nullable: true })
  longitude?: number;

  @ApiProperty({
    description: 'Whether property is featured',
    example: false,
  })
  @Column({ default: false })
  @Index()
  isFeatured: boolean;

  @ApiProperty({
    description: 'Whether property is verified',
    example: true,
  })
  @Column({ default: false })
  @Index()
  isVerified: boolean;

  @ApiProperty({
    description: 'Property views count',
    example: 150,
  })
  @Column({ default: 0 })
  viewsCount: number;

  @ApiProperty({
    description: 'Property inquiries count',
    example: 25,
  })
  @Column({ default: 0 })
  inquiriesCount: number;

  // Relations
  @ApiProperty({
    description: 'Property owner/landlord',
    type: () => User,
  })
  @ManyToOne(() => User, (user) => user.landlordProperties, { eager: false })
  @JoinColumn({ name: 'landlord_id' })
  landlord: User;

  @Column({ name: 'landlord_id' })
  landlordId: string;

  @ApiProperty({
    description: 'Property agent (if any)',
    type: () => User,
    required: false,
  })
  @ManyToOne(() => User, (user) => user.agentProperties, { eager: false, nullable: true })
  @JoinColumn({ name: 'agent_id' })
  agent?: User;

  @OneToMany(() => Commission, (commission) => commission.property)
  commissions: Commission[];

  @Column({ name: 'agent_id', nullable: true })
  agentId?: string;

  @OneToMany(() => VerificationStep, verificationSteps => verificationSteps.property)
  verificationSteps: VerificationStep[];

  @ApiProperty({
    description: 'Additional property metadata',
    example: { nearbySchools: ['University of Port Harcourt'], transportLinks: ['Bus stop nearby'] },
    required: false,
  })
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'When the property was created',
    example: '2023-12-01T10:00:00Z',
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the property was last updated',
    example: '2023-12-01T10:00:00Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  @ApiProperty({
    description: 'Property full address',
    example: 'No. 15 Aba Road, GRA Phase 2, Port Harcourt, Rivers, Nigeria',
  })
  get fullAddress(): string {
    return `${this.location}, ${this.city}, ${this.state}, ${this.country}`;
  }

  @ApiProperty({
    description: 'Whether property is available for rent',
    example: true,
  })
  get isAvailable(): boolean {
    return this.status === PropertyStatus.AVAILABLE;
  }
}
