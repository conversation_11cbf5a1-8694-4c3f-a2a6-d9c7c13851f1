import {
    Entity,
    PrimaryGeneratedColumn,
    Column, ManyTo<PERSON>ne,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn,
    UpdateDateColumn
} from 'typeorm';
import { Property } from './property.entity';

export enum VerificationStepType {
  DOCUMENT_UPLOAD = 'document_upload',
  SITE_INSPECTION = 'site_inspection',
  BACKGROUND_CHECK = 'background_check',
}

export enum VerificationStepStatus {
  PENDING = 'pending',
  IN_REVIEW = 'in_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Entity({ name: 'property_verification_steps' })
export class VerificationStep {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  propertyId: string;

  @ManyToOne(() => Property, property => property.verificationSteps)
  @JoinColumn({ name: 'propertyId' })
  property: Property;

  @Column({
    type: 'enum',
    enum: VerificationStepType,
    default: VerificationStepType.DOCUMENT_UPLOAD,
  })
  stepType: VerificationStepType;

  @Column({
    type: 'enum',
    enum: VerificationStepStatus,
    default: VerificationStepStatus.PENDING,
  })
  status: VerificationStepStatus;

  @Column({ type: 'text', nullable: true })
  comments: string;

  @Column({ type: 'text', array: true, nullable: true })
  files: string[]; // URLs or file paths for uploaded documents

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
}
