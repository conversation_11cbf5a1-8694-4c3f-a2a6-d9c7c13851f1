import {
    <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column,
    ManyTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>umn
} from 'typeorm';
import { Property } from './property.entity';

@Entity('property_images')
export class PropertyImage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  propertyId: string;

  @Column()
  url: string;

  @Column({ nullable: true })
  thumbnailUrl: string;

  @Column({ nullable: true })
  altText: string;

  @Column({ type: 'int' })
  orderIndex: number;

  @Column({ default: false })
  isPrimary: boolean;

  @Column({ nullable: true })
  fileSize: number; // in bytes

  @Column({ nullable: true })
  fileType: string;

  @Column({ nullable: true })
  width: number;

  @Column({ nullable: true })
  height: number;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @ManyToOne(
    () => Property, (property) => property.images, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'propertyId' })
  property: Property;
}