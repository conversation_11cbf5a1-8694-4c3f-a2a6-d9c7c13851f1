import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Core Proper Management
import { PropertiesService } from '@/api/v1/properties/services';
import { PropertiesController } from '@/api/v1/properties/controllers';
import { Property } from '@/api/v1/properties/entities';

// User Module for user entity injection
import { User } from '@/modules/users/entities/user.entity';

// Property Images Module
import { PropertyImagesService } from './services/property-images.service';
import { PropertyImagesController } from './controllers/property-images.controller';
import { PropertyImage } from '@/api/v1/properties/entities';

// Property Analytics Module
import { PropertyAnalyticsService } from './services/property-analytics.service';
import { PropertyAnalyticsController } from './controllers/property-analytics.controller';

// Saved Searches Module
import { SavedSearchesService } from './services/saved-searches.service';
import { SavedSearchesController } from './controllers/saved-searches.controller';
import { SavedSearch } from './entities/saved-search.entity';


@Module({
  imports: [
    TypeOrmModule.forFeature([
      Property,
      User,
      PropertyImage,
      SavedSearch
    ])
  ],
  controllers: [
    PropertiesController,
    PropertyImagesController,
    PropertyAnalyticsController,
    SavedSearchesController
  ],
  providers: [
    PropertiesService,
    PropertyImagesService,
    PropertyAnalyticsService,
    SavedSearchesService
  ],
  exports: [
    PropertiesService,
    PropertyImagesService,
    PropertyAnalyticsService,
    SavedSearchesService,
  ],
})
export class PropertiesModule {}
