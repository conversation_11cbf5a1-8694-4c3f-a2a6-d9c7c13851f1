import {
    Injectable, NotFoundException,
    BadRequestException, ForbiddenException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PropertyImage } from '@/api/v1/properties/entities';
import { Property } from '../entities/property.entity';
import { User, UserRole } from '@/modules/users/entities/user.entity';
import { UploadImageDto } from '@/api/v1/properties/dto';
import { ReorderImagesDto } from '@/api/v1/properties/dto';

@Injectable()
export class PropertyImagesService {
  constructor(
    @InjectRepository(PropertyImage)
    private readonly propertyImageRepository: Repository<PropertyImage>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  // Helper to check if user can manage property images
  private async canManagePropertyImages(
    propertyId: string, user: User
): Promise<Property> {
    const property = await this.propertyRepository.findOne(
        { where: { id: propertyId }
    });
    if (!property) {
      throw new NotFoundException(`Property with ID ${propertyId} not found`);
    }

    // Admins can manage any property's images
    if (user.role === UserRole.ADMIN) {
      return property;
    }
    // Landlords can manage their own property's images
    if (user.role === UserRole.LANDLORD && property.landlordId === user.id) {
      return property;
    }
    // Agents can manage properties they are assigned to
    if (user.role === UserRole.AGENT && property.agentId === user.id) {
      return property;
    }

    throw new ForbiddenException(
        'You do not have permission to manage images for this property'
    );
  }

  async uploadImages(
    propertyId: string,
    files: Express.Multer.File[],
    uploadDto: UploadImageDto,
    user: User
): Promise<PropertyImage[]> {
    const property = await this.canManagePropertyImages(propertyId, user);

    if (!files || files.length === 0) {
      throw new BadRequestException('No files provided for upload');
    }

    const uploadedImages: PropertyImage[] = [];
    let currentMaxOrder = (
        await this.propertyImageRepository.maximum(
            'orderIndex', { propertyId }
        )
    ) || 0;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const altText = uploadDto.altTexts?.[i] || file.originalname;
      const isPrimary = uploadDto.isPrimaryIndex === i;

      // In a real application, you'd upload this file to cloud storage 
      // (e.g., S3, Google Cloud Storage)
      // and get a public URL. For now, we'll simulate it.
      const simulatedUrl =
        `/uploads/${propertyId}/${Date.now()}-${file.originalname}`;
      const simulatedThumbnailUrl =
        `/uploads/${propertyId}/thumbnails/${Date.now()}-${file.originalname}`;

      const newImage = this.propertyImageRepository.create({
        propertyId: property.id,
        url: simulatedUrl,
        thumbnailUrl: simulatedThumbnailUrl, // In a real app, generate this
        altText: altText,
        orderIndex: currentMaxOrder + 1 + i, // Assign sequential order
        isPrimary: isPrimary,
        fileSize: file.size,
        fileType: file.mimetype,
        width: 0, // Placeholder, get actual dimensions after upload
        height: 0, // Placeholder, get actual dimensions after upload
      });
      uploadedImages.push(await this.propertyImageRepository.save(newImage));

      // If this is the primary image, update the property's main image reference (if applicable)
      if (isPrimary) {
        property.images = [
          simulatedUrl,
          ...property.images.filter(img => img !== simulatedUrl)
        ];
      } else {
        property.images.push(simulatedUrl);
      }
    }
    await this.propertyRepository.save(property); // Update property with new image URLs

    return uploadedImages;
  }

  async getImagesByPropertyId(propertyId: string): Promise<PropertyImage[]> {
    return this.propertyImageRepository.find({
      where: { propertyId },
      order: { orderIndex: 'ASC' },
    });
  }

  async deleteImage(
    propertyId: string, imageId: string, user: User
  ): Promise<void> {
    const property = await this.canManagePropertyImages(
      propertyId, user
    );

    const image = await this.propertyImageRepository.findOne({
      where: { id: imageId, propertyId },
    });

    if (!image) {
      throw new NotFoundException(
        `Image with ID ${imageId} not found for property ${propertyId}`
      );
    }

    // TODO: delete the file from cloud storage here
    await this.propertyImageRepository.remove(image);

    // Update property's images array
    property.images = property.images.filter(url => url !== image.url);
    await this.propertyRepository.save(property);
  }

  async reorderImages(
    propertyId: string,
    reorderDto: ReorderImagesDto,
    user: User
  ): Promise<PropertyImage[]> {
    const property = await this.canManagePropertyImages(propertyId, user);

    const existingImages = await this.propertyImageRepository.find({
      where: { propertyId },
    });

    if (existingImages.length !== reorderDto.imageIds.length) {
      throw new BadRequestException(
        'Provided image IDs do not match all existing images for the property.'
      );
    }

    const updatedImages: PropertyImage[] = [];
    for (let i = 0; i < reorderDto.imageIds.length; i++) {
      const imageId = reorderDto.imageIds[i];
      const image = existingImages.find(img => img.id === imageId);

      if (!image) {
        throw new NotFoundException(
          `Image with ID ${imageId} not found for property ${propertyId}`
        );
      }
      image.orderIndex = i; // Assign new order
      updatedImages.push(await this.propertyImageRepository.save(image));
    }

    // Update property's images array based on new order
    property.images = updatedImages.sort(
      (a, b) => a.orderIndex - b.orderIndex).map(img => img.url

      );
    await this.propertyRepository.save(property);

    return updatedImages;
  }
}