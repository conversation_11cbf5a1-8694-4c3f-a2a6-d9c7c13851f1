import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SavedSearch } from '@/api/v1/properties/entities';
import { User, UserRole } from '@/modules/users/entities/user.entity';
import { CreateSavedSearchDto,UpdateSavedSearchDto } from '@/api/v1/properties/dto';

@Injectable()
export class SavedSearchesService {
  constructor(
    @InjectRepository(SavedSearch)
    private readonly savedSearchRepository: Repository<SavedSearch>,
  ) {}

  async createSavedSearch(
    createDto: CreateSavedSearchDto, user: User
): Promise<SavedSearch> {
    const savedSearch = this.savedSearchRepository.create({
      userId: user.id,
      name: createDto.name,
      filters: createDto.filters,
      alertFrequency: createDto.alertFrequency || 'never',
      isActive: true,
      resultsCount: 0, // This would be calculated periodically by a background job
    });
    return this.savedSearchRepository.save(savedSearch);
  }

  async getSavedSearchesByUserId(
    userId: string, currentUser: User
): Promise<SavedSearch[]> {
    if (userId !== currentUser.id && currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException(
        'You can only view your own saved searches or you need admin privileges.'
    );
    }
    return this.savedSearchRepository.find({ where: { userId } });
  }

  async getSavedSearchById(
    id: string, currentUser: User
): Promise<SavedSearch> {
    const savedSearch = await this.savedSearchRepository.findOne(
        { where: { id } }
    );
    if (!savedSearch) {
      throw new NotFoundException(`Saved search with ID ${id} not found`);
    }
    if (savedSearch.userId !== currentUser.id && currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('You do not have permission to view this saved search.');
    }
    return savedSearch;
  }

  async updateSavedSearch(
    id: string,
    updateDto: UpdateSavedSearchDto,
    currentUser: User
): Promise<SavedSearch> {
    const savedSearch = await this.getSavedSearchById(id, currentUser); // Re-use check for ownership/admin

    Object.assign(savedSearch, updateDto);
    savedSearch.updatedAt = new Date(); // Update timestamp
    return this.savedSearchRepository.save(savedSearch);
  }

  async deleteSavedSearch(id: string, currentUser: User): Promise<void> {
    const savedSearch = await this.getSavedSearchById(id, currentUser); // Re-use check for ownership/admin
    await this.savedSearchRepository.remove(savedSearch);
  }

  async toggleSavedSearchActive(
    id: string, isActive: boolean, currentUser: User
): Promise<SavedSearch> {
    const savedSearch = await this.getSavedSearchById(id, currentUser);
    savedSearch.isActive = isActive;
    return this.savedSearchRepository.save(savedSearch);
  }
}