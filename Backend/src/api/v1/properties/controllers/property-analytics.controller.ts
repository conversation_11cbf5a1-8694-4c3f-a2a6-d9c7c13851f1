import {
    <PERSON>, Get, Param, 
    Query, UseGuards, Logger, ParseUUIDPipe
} from '@nestjs/common';
import {
    ApiTags, ApiOperation,
    ApiResponse, ApiBearerAuth
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserRole } from '@/modules/users/entities/user.entity';
import { Public } from '@/modules/auth/decorators/public.decorator'; // If market data is public
import { PropertyAnalyticsService } from '../services/property-analytics.service';
import {
    PropertyAnalytics,
    PropertyMarketData,
    PropertyRecommendation
} from '@/api/v1/properties/entities'; // Using these as DTOs for swagger

@ApiTags('Property Analytics')
@Controller('properties')
export class PropertyAnalyticsController {
  private readonly logger = new Logger(PropertyAnalyticsController.name);

  constructor(
    private readonly propertyAnalyticsService: PropertyAnalyticsService
) {}

  @Get(':id/analytics')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get detailed analytics for a specific property' })
  @ApiResponse({
    status: 200, description: 'Property analytics retrieved successfully',
    type: PropertyAnalytics
})
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Property not found' })
  getPropertyAnalytics(@Param('id', ParseUUIDPipe) id: string) {
    this.logger.debug(`Fetching analytics for property ${id}`);
    return this.propertyAnalyticsService.getPropertyAnalytics(id);
  }

  @Get('market-data')
  @Public() // Or restrict based on subscription/roles
  @ApiOperation({
    summary: 'Get market data for a specific location and property type'
})
  @ApiResponse({
    status: 200, description: 'Market data retrieved successfully',
    type: PropertyMarketData
})
  @ApiResponse({ status: 400, description: 'Bad Request' })
  getMarketData(
    @Query('location') location: string,
    @Query('propertyType') propertyType?: string,
  ) {
    this.logger.debug(
        `Fetching market data for location: ${location}, type: ${propertyType}`
    );
    return this.propertyAnalyticsService.getPropertyMarketData(location, propertyType);
  }

  @Get(':id/recommendations')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get property recommendations based on user preferences/history'
})
  @ApiResponse({
    status: 200, description: 'Property recommendations retrieved successfully',
    type: [PropertyRecommendation]
})
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  getPropertyRecommendations(
    @Param('id', ParseUUIDPipe) userId: string,
    @Query('limit') limit: number = 10,
  ) {
    this.logger.debug(
        `Fetching recommendations for user ${userId} with limit ${limit}`
    );
    return this.propertyAnalyticsService.getPropertyRecommendations(
        userId, limit
    );
  }
}   