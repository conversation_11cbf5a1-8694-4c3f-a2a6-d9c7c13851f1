import {
    Controller,
    Post,
    Body,
    Param,
    Get,
    Patch,
    UseGuards,
    HttpCode,
    HttpStatus,
    Logger,
  } from '@nestjs/common';
  import {
    ApiTags, ApiOperation, ApiResponse, ApiBearerAuth
} from '@nestjs/swagger';
  import {
    PropertyVerificationService
} from '../services/property-verification.service';
  import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
  import { RolesGuard } from '@/modules/auth/guards/roles.guard';
  import { Roles } from '@/modules/auth/decorators/roles.decorator';
  import { GetUser } from '@/common/decorators/user.decorator';
  import { User, UserRole } from '@/modules/users/entities/user.entity';
  import {
    SubmitVerificationStepsDto,
    UpdateVerificationStatusDto,
    ScheduleInspectionDto,
    CompleteInspectionDto,
} from '@/api/v1/properties/dto';
  import { VerificationStep } from '../entities/verification-step.entity';
  import { ParseUUIDPipe } from '@/common/pipes/parse-uuid.pipe';
  
  @ApiTags('Property Verification')
  @Controller('properties/:id/verification')
  export class PropertyVerificationController {
    private readonly logger = new Logger(PropertyVerificationController.name);
  
    constructor(private readonly verificationService: PropertyVerificationService) {}
  
    @Get('steps')
    @UseGuards(JwtAuthGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({ summary: 'Get all verification steps for a property' })
    @ApiResponse({
        status: 200, description: 'Verification steps retrieved successfully',
        type: [VerificationStep]
    })
    @ApiResponse({ status: 404, description: 'Property not found' })
    async getVerificationSteps(@Param('id', ParseUUIDPipe) id: string) {
      this.logger.log(`Fetching verification steps for property ID: ${id}`);
      return this.verificationService.getVerificationSteps(id);
    }
  
    @Post('submit-step')
    @UseGuards(JwtAuthGuard, RolesGuard)
    @Roles(UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({ summary: 'Submit a new verification step for a property' })
    @ApiResponse({
        status: 201, description: 'Verification step submitted successfully',
        type: VerificationStep
    })
    @ApiResponse({ status: 403, description: 'Forbidden - User does not have permission' })
    @ApiResponse({ status: 404, description: 'Property not found' })
    async submitVerificationStep(
      @Param('id', ParseUUIDPipe) id: string,
      @Body() submitDto: SubmitVerificationStepsDto,
      @GetUser() user: User,
    ) {
      this.logger.log(`Submitting verification step for property ID: ${id}`);
      return this.verificationService.submitVerificationStep(id, submitDto, user);
    }
  
    @Patch('status/:stepId')
    @UseGuards(JwtAuthGuard, RolesGuard)
    @Roles(UserRole.ADMIN)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({ summary: 'Update the status of a verification step (Admin only)' })
    @ApiResponse({
        status: 200, description: 'Verification step status updated successfully',
        type: VerificationStep
    })
    @ApiResponse({ status: 403, description: 'Forbidden - Not an admin' })
    @ApiResponse({ status: 404, description: 'Property or step not found' })
    async updateVerificationStatus(
      @Param('stepId', ParseUUIDPipe) stepId: string,
      @Body() statusDto: UpdateVerificationStatusDto,
      @GetUser() user: User,
    ) {
      this.logger.log(`Updating verification status for step ID: ${stepId}`);
      return this.verificationService.updateVerificationStatus(stepId, statusDto, user);
    }

    @Post('schedule-inspection')
    @UseGuards(JwtAuthGuard, RolesGuard)
    @Roles(UserRole.ADMIN)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({ summary: 'Schedule a new site inspection (Admin only)' })
    @ApiResponse({ status: 201, description: 'Inspection scheduled successfully', type: VerificationStep })
    @ApiResponse({ status: 403, description: 'Forbidden - Not an admin' })
    @ApiResponse({ status: 404, description: 'Property not found' })
    async scheduleInspection(
      @Param('id', ParseUUIDPipe) id: string,
      @Body() scheduleDto: ScheduleInspectionDto,
      @GetUser() user: User,
    ) {
      this.logger.log(`Scheduling inspection for property ID: ${id}`);
      return this.verificationService.scheduleInspection(id, scheduleDto, user);
    }

    @Get('progress')
    @UseGuards(JwtAuthGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({ summary: 'Get overall verification progress for a property' })
    @ApiResponse({ status: 200, description: 'Verification progress retrieved successfully' })
    @ApiResponse({ status: 404, description: 'Property not found' })
    async getVerificationProgress(@Param('id', ParseUUIDPipe) id: string) {
      return this.verificationService.getVerificationProgress(id);
    }

    @Patch('complete-inspection/:stepId')
    @UseGuards(JwtAuthGuard, RolesGuard)
    @Roles(UserRole.ADMIN)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({ summary: 'Complete a scheduled inspection (Admin only)' })
    @ApiResponse({ status: 200, description: 'Inspection completed successfully', type: VerificationStep })
    @ApiResponse({ status: 403, description: 'Forbidden - Not an admin' })
    @ApiResponse({ status: 404, description: 'Inspection step not found' })
    async completeInspection(
      @Param('stepId', ParseUUIDPipe) stepId: string,
      @Body() completeDto: CompleteInspectionDto,
      @GetUser() user: User,
    ) {
      this.logger.log(`Completing inspection for step ID: ${stepId}`);
      return this.verificationService.completeInspection(stepId, completeDto, user);
    }
  }
  