import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { Property, PropertyStatus } from './entities/property.entity';
import { User, UserRole } from '../../../modules/users/entities/user.entity';
import { CreatePropertyDto } from './dto/create-property.dto';
import { UpdatePropertyDto } from './dto/update-property.dto';
import { SearchPropertiesDto } from './dto/search-properties.dto';
import { PaginationUtil, PaginationResult } from '../../../common/utils/pagination.util';

@Injectable()
export class PropertiesService {
  constructor(
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(createPropertyDto: CreatePropertyDto, user: User): Promise<Property> {
    // Only landlords and agents can create properties
    if (![UserRole.LANDLORD, UserRole.AGENT, UserRole.ADMIN].includes(user.role)) {
      throw new ForbiddenException('Only landlords, agents, and admins can create properties');
    }

    // Calculate monthly price from annual price
    const pricePerMonth = createPropertyDto.pricePerYear / 12;

    // Validate agent if provided
    if (createPropertyDto.agentId) {
      const agent = await this.userRepository.findOne({
        where: { id: createPropertyDto.agentId, role: UserRole.AGENT },
      });
      if (!agent) {
        throw new BadRequestException('Invalid agent ID provided');
      }
    }

    // Create property
    const property = this.propertyRepository.create({
      ...createPropertyDto,
      pricePerMonth,
      landlordId: user.role === UserRole.LANDLORD ? user.id : createPropertyDto.agentId ? undefined : user.id,
      agentId: user.role === UserRole.AGENT ? user.id : createPropertyDto.agentId,
      city: createPropertyDto.city || 'Port Harcourt',
      state: createPropertyDto.state || 'Rivers',
      country: createPropertyDto.country || 'Nigeria',
      amenities: createPropertyDto.amenities || [],
      images: createPropertyDto.images || [],
    });

    return this.propertyRepository.save(property);
  }

  async findAll(searchDto: SearchPropertiesDto): Promise<PaginationResult<Property>> {
    const queryBuilder = this.createSearchQuery(searchDto);
    
    return PaginationUtil.paginate(queryBuilder, {
      page: searchDto.page || 1,
      limit: searchDto.limit || 20,
      sortBy: searchDto.sortBy || 'createdAt',
      sortOrder: searchDto.sortOrder || 'DESC',
    });
  }

  async findOne(id: string, incrementViews: boolean = true): Promise<Property> {
    const property = await this.propertyRepository.findOne({
      where: { id },
      relations: ['landlord', 'agent'],
    });

    if (!property) {
      throw new NotFoundException(`Property with ID ${id} not found`);
    }

    // Increment views count
    if (incrementViews) {
      await this.propertyRepository.increment({ id }, 'viewsCount', 1);
      property.viewsCount += 1;
    }

    return property;
  }

  async update(id: string, updatePropertyDto: UpdatePropertyDto, user: User): Promise<Property> {
    const property = await this.findOne(id, false);

    // Check permissions
    if (!this.canModifyProperty(property, user)) {
      throw new ForbiddenException('You do not have permission to update this property');
    }

    // Recalculate monthly price if annual price is updated
    if (updatePropertyDto.pricePerYear) {
      updatePropertyDto['pricePerMonth'] = updatePropertyDto.pricePerYear / 12;
    }

    // Validate agent if provided
    if (updatePropertyDto.agentId) {
      const agent = await this.userRepository.findOne({
        where: { id: updatePropertyDto.agentId, role: UserRole.AGENT },
      });
      if (!agent) {
        throw new BadRequestException('Invalid agent ID provided');
      }
    }

    Object.assign(property, updatePropertyDto);
    return this.propertyRepository.save(property);
  }

  async remove(id: string, user: User): Promise<void> {
    const property = await this.findOne(id, false);

    // Check permissions
    if (!this.canModifyProperty(property, user)) {
      throw new ForbiddenException('You do not have permission to delete this property');
    }

    await this.propertyRepository.remove(property);
  }

  async updateStatus(id: string, status: PropertyStatus, user: User): Promise<Property> {
    const property = await this.findOne(id, false);

    // Check permissions
    if (!this.canModifyProperty(property, user)) {
      throw new ForbiddenException('You do not have permission to update this property status');
    }

    property.status = status;
    return this.propertyRepository.save(property);
  }

  async toggleFeatured(id: string, user: User): Promise<Property> {
    // Only admins and agents can feature properties
    if (![UserRole.ADMIN, UserRole.AGENT].includes(user.role)) {
      throw new ForbiddenException('Only admins and agents can feature properties');
    }

    const property = await this.findOne(id, false);
    property.isFeatured = !property.isFeatured;
    return this.propertyRepository.save(property);
  }

  async toggleVerified(id: string, user: User): Promise<Property> {
    // Only admins can verify properties
    if (user.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can verify properties');
    }

    const property = await this.findOne(id, false);
    property.isVerified = !property.isVerified;
    return this.propertyRepository.save(property);
  }

  async incrementInquiries(id: string): Promise<void> {
    await this.propertyRepository.increment({ id }, 'inquiriesCount', 1);
  }

  async getMyProperties(user: User, searchDto: SearchPropertiesDto): Promise<PaginationResult<Property>> {
    const queryBuilder = this.createSearchQuery(searchDto);

    // Filter by user's properties
    if (user.role === UserRole.LANDLORD) {
      queryBuilder.andWhere('property.landlordId = :userId', { userId: user.id });
    } else if (user.role === UserRole.AGENT) {
      queryBuilder.andWhere('property.agentId = :userId', { userId: user.id });
    } else {
      // For other roles, return empty result
      queryBuilder.andWhere('1 = 0');
    }

    return PaginationUtil.paginate(queryBuilder, {
      page: searchDto.page || 1,
      limit: searchDto.limit || 20,
      sortBy: searchDto.sortBy || 'createdAt',
      sortOrder: searchDto.sortOrder || 'DESC',
    });
  }

  async getPropertyStats(): Promise<any> {
    const [
      totalProperties,
      availableProperties,
      rentedProperties,
      featuredProperties,
      verifiedProperties,
    ] = await Promise.all([
      this.propertyRepository.count(),
      this.propertyRepository.count({ where: { status: PropertyStatus.AVAILABLE } }),
      this.propertyRepository.count({ where: { status: PropertyStatus.RENTED } }),
      this.propertyRepository.count({ where: { isFeatured: true } }),
      this.propertyRepository.count({ where: { isVerified: true } }),
    ]);

    return {
      totalProperties,
      availableProperties,
      rentedProperties,
      featuredProperties,
      verifiedProperties,
      occupancyRate: totalProperties > 0 ? (rentedProperties / totalProperties) * 100 : 0,
    };
  }

  private createSearchQuery(searchDto: SearchPropertiesDto): SelectQueryBuilder<Property> {
    const queryBuilder = this.propertyRepository
      .createQueryBuilder('property')
      .leftJoinAndSelect('property.landlord', 'landlord')
      .leftJoinAndSelect('property.agent', 'agent');

    // Apply filters
    if (searchDto.propertyType) {
      queryBuilder.andWhere('property.propertyType = :propertyType', { propertyType: searchDto.propertyType });
    }

    if (searchDto.status) {
      queryBuilder.andWhere('property.status = :status', { status: searchDto.status });
    }

    if (searchDto.minPrice) {
      queryBuilder.andWhere('property.pricePerYear >= :minPrice', { minPrice: searchDto.minPrice });
    }

    if (searchDto.maxPrice) {
      queryBuilder.andWhere('property.pricePerYear <= :maxPrice', { maxPrice: searchDto.maxPrice });
    }

    if (searchDto.minBedrooms) {
      queryBuilder.andWhere('property.bedrooms >= :minBedrooms', { minBedrooms: searchDto.minBedrooms });
    }

    if (searchDto.maxBedrooms) {
      queryBuilder.andWhere('property.bedrooms <= :maxBedrooms', { maxBedrooms: searchDto.maxBedrooms });
    }

    if (searchDto.minBathrooms) {
      queryBuilder.andWhere('property.bathrooms >= :minBathrooms', { minBathrooms: searchDto.minBathrooms });
    }

    if (searchDto.maxBathrooms) {
      queryBuilder.andWhere('property.bathrooms <= :maxBathrooms', { maxBathrooms: searchDto.maxBathrooms });
    }

    if (searchDto.location) {
      queryBuilder.andWhere('property.location ILIKE :location', { location: `%${searchDto.location}%` });
    }

    if (searchDto.city) {
      queryBuilder.andWhere('property.city ILIKE :city', { city: `%${searchDto.city}%` });
    }

    if (searchDto.state) {
      queryBuilder.andWhere('property.state ILIKE :state', { state: `%${searchDto.state}%` });
    }

    if (searchDto.furnishingStatus) {
      queryBuilder.andWhere('property.furnishingStatus = :furnishingStatus', { furnishingStatus: searchDto.furnishingStatus });
    }

    if (searchDto.isFeatured !== undefined) {
      queryBuilder.andWhere('property.isFeatured = :isFeatured', { isFeatured: searchDto.isFeatured });
    }

    if (searchDto.isVerified !== undefined) {
      queryBuilder.andWhere('property.isVerified = :isVerified', { isVerified: searchDto.isVerified });
    }

    if (searchDto.landlordId) {
      queryBuilder.andWhere('property.landlordId = :landlordId', { landlordId: searchDto.landlordId });
    }

    if (searchDto.agentId) {
      queryBuilder.andWhere('property.agentId = :agentId', { agentId: searchDto.agentId });
    }

    if (searchDto.amenities) {
      const amenitiesList = searchDto.amenities.split(',').map(a => a.trim());
      queryBuilder.andWhere('property.amenities && :amenities', { amenities: amenitiesList });
    }

    if (searchDto.search) {
      queryBuilder.andWhere(
        '(property.title ILIKE :search OR property.description ILIKE :search OR property.location ILIKE :search)',
        { search: `%${searchDto.search}%` }
      );
    }

    return queryBuilder;
  }

  private canModifyProperty(property: Property, user: User): boolean {
    // Admin can modify any property
    if (user.role === UserRole.ADMIN) {
      return true;
    }

    // Landlord can modify their own properties
    if (user.role === UserRole.LANDLORD && property.landlordId === user.id) {
      return true;
    }

    // Agent can modify properties they manage
    if (user.role === UserRole.AGENT && property.agentId === user.id) {
      return true;
    }

    return false;
  }
}
