import { IsEnum, IsString, IsOptional } from 'class-validator';
import { VerificationStepStatus } from '../entities/verification-step.entity';

/**
 * DTO for updating the status of a verification step.
 * This is used by admins to review and approve/reject steps.
 */
export class UpdateVerificationStatusDto {
  @IsEnum(VerificationStepStatus)
  status: VerificationStepStatus;

  @IsString()
  @IsOptional()
  comments?: string;
}
