import { ApiProperty } from '@nestjs/swagger';
import {
    IsUUID, IsArray, ArrayMinSize, 
    IsOptional, IsString, IsNumber, IsInt, Min
} from 'class-validator';

export class UploadImageDto {
  @ApiProperty({
    type: 'string', format: 'binary',
    isArray: true, description: 'Array of image files to upload'
})
  @IsArray()
  @ArrayMinSize(1)
  files: Express.Multer.File[]; // <PERSON><PERSON> will populate this

  @ApiProperty({
    type: 'string', format: 'uuid',
    description: 'ID of the property to associate images with'
})
  @IsUUID()
  propertyId: string;

  @ApiProperty({
    type: [String],
    description: 'Optional alt texts for each image, matching file order',
    required: false
})
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  altTexts?: string[];

  @ApiProperty({
    type: 'number',
    description: 'Optional index of the primary image (0-based)',
    required: false
})
  @IsOptional()
  @IsInt()
  @Min(0)
  isPrimaryIndex?: number;
}
