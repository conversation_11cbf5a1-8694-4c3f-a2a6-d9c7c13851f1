import { ApiProperty } from '@nestjs/swagger';
import {
    IsString, IsOptional, IsIn, IsObject, IsBoolean
} from 'class-validator';
import { SearchPropertiesDto } from './search-properties.dto';

export class UpdateSavedSearchDto {
  @ApiProperty({
    description: 'New name for the saved search', required: false
})
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Updated search filters',
    type: SearchPropertiesDto, required: false
})
  @IsOptional()
  @IsObject()
  filters?: SearchPropertiesDto;

  @ApiProperty({
    description: 'Updated frequency for email alerts',
    enum: ['daily', 'weekly', 'monthly', 'never'],
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['daily', 'weekly', 'monthly', 'never'])
  alertFrequency?: 'daily' | 'weekly' | 'monthly' | 'never';

  @ApiProperty({
    description: 'Whether the saved search is active', required: false
})
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}