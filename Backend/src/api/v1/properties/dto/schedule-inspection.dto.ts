import { IsOptional, IsString, IsDateString, IsEnum } from 'class-validator';
import { VerificationStepStatus } from '../entities/verification-step.entity';

/**
 * DTO for scheduling a site inspection.
 * Used by admins or authorized personnel.
 */
export class ScheduleInspectionDto {
  @IsDateString()
  scheduledDate: string;

  @IsString()
  @IsOptional()
  notes?: string;
}

/**
 * DTO for completing an inspection step.
 * Used by admins to mark an inspection as approved or rejected.
 */
export class CompleteInspectionDto {
  @IsEnum(VerificationStepStatus)
  status: VerificationStepStatus;

  @IsString()
  @IsOptional()
  report?: string;
}