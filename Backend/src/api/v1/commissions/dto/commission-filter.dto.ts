import { IsOptional, IsEnum, IsDateString } from 'class-validator';
import { CommissionStatus } from '../entities/commission.entity';
import { PaginationDto } from '../../../../common/dto/pagination.dto';

export class CommissionFilterDto extends PaginationDto {
  @IsOptional()
  @IsEnum(CommissionStatus)
  status?: CommissionStatus;

  @IsOptional()
  @IsDateString()
  from_date?: string;

  @IsOptional()
  @IsDateString()
  to_date?: string;
}