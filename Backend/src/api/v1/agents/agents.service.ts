import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserRole, UserStatus } from '../../../modules/users/entities/user.entity';
import { Property, PropertyStatus } from '../properties/entities/property.entity';
import { PaginationUtil, PaginationResult } from '../../../common/utils/pagination.util';
import { PaginationDto } from '../../../common/dto/pagination.dto';

@Injectable()
export class AgentsService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
  ) {}

  async findAll(paginationDto: PaginationDto): Promise<PaginationResult<User>> {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .where('user.role = :role', { role: UserRole.AGENT })
      .andWhere('user.isActive = :isActive', { isActive: true });

    if (paginationDto.search) {
      queryBuilder.andWhere(
        '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)',
        { search: `%${paginationDto.search}%` }
      );
    }

    return PaginationUtil.paginate(queryBuilder, {
      page: paginationDto.page || 1,
      limit: paginationDto.limit || 20,
      sortBy: paginationDto.sortBy || 'createdAt',
      sortOrder: paginationDto.sortOrder || 'DESC',
    });
  }

  async findOne(id: string): Promise<User> {
    const agent = await this.userRepository.findOne({
      where: { id, role: UserRole.AGENT },
    });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${id} not found`);
    }

    return agent;
  }

  async getAgentProperties(agentId: string, paginationDto: PaginationDto): Promise<PaginationResult<Property>> {
    // Verify agent exists
    await this.findOne(agentId);

    const queryBuilder = this.propertyRepository
      .createQueryBuilder('property')
      .leftJoinAndSelect('property.landlord', 'landlord')
      .where('property.agentId = :agentId', { agentId });

    if (paginationDto.search) {
      queryBuilder.andWhere(
        '(property.title ILIKE :search OR property.description ILIKE :search OR property.location ILIKE :search)',
        { search: `%${paginationDto.search}%` }
      );
    }

    return PaginationUtil.paginate(queryBuilder, {
      page: paginationDto.page || 1,
      limit: paginationDto.limit || 20,
      sortBy: paginationDto.sortBy || 'createdAt',
      sortOrder: paginationDto.sortOrder || 'DESC',
    });
  }

  async getAgentStats(agentId: string): Promise<any> {
    // Verify agent exists
    await this.findOne(agentId);

    const [
      totalProperties,
      availableProperties,
      rentedProperties,
      totalViews,
      totalInquiries,
    ] = await Promise.all([
      this.propertyRepository.count({ where: { agentId } }),
      this.propertyRepository.count({ where: { agentId, status: PropertyStatus.AVAILABLE } }),
      this.propertyRepository.count({ where: { agentId, status: PropertyStatus.RENTED } }),
      this.propertyRepository
        .createQueryBuilder('property')
        .select('SUM(property.viewsCount)', 'totalViews')
        .where('property.agentId = :agentId', { agentId })
        .getRawOne()
        .then(result => parseInt(result.totalViews) || 0),
      this.propertyRepository
        .createQueryBuilder('property')
        .select('SUM(property.inquiriesCount)', 'totalInquiries')
        .where('property.agentId = :agentId', { agentId })
        .getRawOne()
        .then(result => parseInt(result.totalInquiries) || 0),
    ]);

    return {
      totalProperties,
      availableProperties,
      rentedProperties,
      totalViews,
      totalInquiries,
      conversionRate: totalViews > 0 ? (totalInquiries / totalViews) * 100 : 0,
      occupancyRate: totalProperties > 0 ? (rentedProperties / totalProperties) * 100 : 0,
    };
  }

  async activateAgent(id: string, currentUser: User): Promise<User> {
    // Only admins can activate agents
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can activate agents');
    }

    const agent = await this.findOne(id);
    agent.status = UserStatus.ACTIVE;
    agent.isActive = true;

    return this.userRepository.save(agent);
  }

  async deactivateAgent(id: string, currentUser: User): Promise<User> {
    // Only admins can deactivate agents
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can deactivate agents');
    }

    const agent = await this.findOne(id);
    agent.status = UserStatus.INACTIVE;
    agent.isActive = false;

    return this.userRepository.save(agent);
  }

  async getTopAgents(limit: number = 10): Promise<any[]> {
    const agents = await this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.agentProperties', 'property')
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'user.phone',
        'user.avatar',
        'COUNT(property.id) as propertyCount',
        'SUM(property.viewsCount) as totalViews',
        'SUM(property.inquiriesCount) as totalInquiries',
      ])
      .where('user.role = :role', { role: UserRole.AGENT })
      .andWhere('user.isActive = :isActive', { isActive: true })
      .groupBy('user.id')
      .orderBy('propertyCount', 'DESC')
      .addOrderBy('totalViews', 'DESC')
      .limit(limit)
      .getRawMany();

    return agents.map(agent => ({
      id: agent.user_id,
      firstName: agent.user_firstName,
      lastName: agent.user_lastName,
      email: agent.user_email,
      phone: agent.user_phone,
      avatar: agent.user_avatar,
      propertyCount: parseInt(agent.propertyCount) || 0,
      totalViews: parseInt(agent.totalViews) || 0,
      totalInquiries: parseInt(agent.totalInquiries) || 0,
      conversionRate: agent.totalViews > 0 ? (agent.totalInquiries / agent.totalViews) * 100 : 0,
    }));
  }

  async searchAgents(query: string, limit: number = 10): Promise<User[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .where('user.role = :role', { role: UserRole.AGENT })
      .andWhere('user.isActive = :isActive', { isActive: true })
      .andWhere(
        '(user.firstName ILIKE :query OR user.lastName ILIKE :query OR user.email ILIKE :query)',
        { query: `%${query}%` }
      )
      .limit(limit)
      .getMany();
  }
}
