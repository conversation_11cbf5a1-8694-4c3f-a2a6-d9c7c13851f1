import {
  Controller,
  Get,
  Param,
  Query,
  Patch,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { AgentsService } from './agents.service';
import { User, UserRole } from '../../../modules/users/entities/user.entity';
import { Property } from '../properties/entities/property.entity';
import { PaginationDto } from '../../../common/dto/pagination.dto';

import { JwtAuthGuard } from '../../../modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../modules/auth/guards/roles.guard';
import { Roles } from '../../../modules/auth/decorators/roles.decorator';
import { GetUser } from '../../../common/decorators/user.decorator';
import { ParseUUIDPipe } from '../../../common/pipes/parse-uuid.pipe';
import { ApiPaginatedResponse } from '../../../common/decorators/api-paginated-response.decorator';
import { Public } from '../../../modules/auth/decorators/public.decorator';

@ApiTags('Agents')
@Controller('agents')
export class AgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all active agents' })
  @ApiPaginatedResponse(User)
  @ApiResponse({
    status: 200,
    description: 'Agents retrieved successfully',
  })
  findAll(@Query() paginationDto: PaginationDto) {
    return this.agentsService.findAll(paginationDto);
  }

  @Get('top')
  @Public()
  @ApiOperation({ summary: 'Get top performing agents' })
  @ApiResponse({
    status: 200,
    description: 'Top agents retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
          firstName: { type: 'string', example: 'John' },
          lastName: { type: 'string', example: 'Doe' },
          email: { type: 'string', example: '<EMAIL>' },
          phone: { type: 'string', example: '+2348012345678' },
          avatar: { type: 'string', example: 'https://example.com/avatar.jpg' },
          propertyCount: { type: 'number', example: 25 },
          totalViews: { type: 'number', example: 1500 },
          totalInquiries: { type: 'number', example: 150 },
          conversionRate: { type: 'number', example: 10.5 },
        },
      },
    },
  })
  getTopAgents(@Query('limit') limit?: number) {
    return this.agentsService.getTopAgents(limit);
  }

  @Get('search')
  @Public()
  @ApiOperation({ summary: 'Search agents by name or email' })
  @ApiResponse({
    status: 200,
    description: 'Agents search results',
    type: [User],
  })
  searchAgents(
    @Query('q') query: string,
    @Query('limit') limit?: number,
  ) {
    return this.agentsService.searchAgents(query, limit);
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get agent by ID' })
  @ApiResponse({
    status: 200,
    description: 'Agent retrieved successfully',
    type: User,
  })
  @ApiResponse({
    status: 404,
    description: 'Agent not found',
  })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.agentsService.findOne(id);
  }

  @Get(':id/properties')
  @Public()
  @ApiOperation({ summary: 'Get properties managed by agent' })
  @ApiPaginatedResponse(Property)
  @ApiResponse({
    status: 200,
    description: 'Agent properties retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Agent not found',
  })
  getAgentProperties(
    @Param('id', ParseUUIDPipe) id: string,
    @Query() paginationDto: PaginationDto,
  ) {
    return this.agentsService.getAgentProperties(id, paginationDto);
  }

  @Get(':id/stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.AGENT)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get agent performance statistics' })
  @ApiResponse({
    status: 200,
    description: 'Agent statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalProperties: { type: 'number', example: 25 },
        availableProperties: { type: 'number', example: 20 },
        rentedProperties: { type: 'number', example: 5 },
        totalViews: { type: 'number', example: 1500 },
        totalInquiries: { type: 'number', example: 150 },
        conversionRate: { type: 'number', example: 10.5 },
        occupancyRate: { type: 'number', example: 20.0 },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Agent not found',
  })
  getAgentStats(@Param('id', ParseUUIDPipe) id: string) {
    return this.agentsService.getAgentStats(id);
  }

  @Patch(':id/activate')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Activate agent account' })
  @ApiResponse({
    status: 200,
    description: 'Agent activated successfully',
    type: User,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Only admins can activate agents',
  })
  @ApiResponse({
    status: 404,
    description: 'Agent not found',
  })
  activateAgent(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() currentUser: User,
  ) {
    return this.agentsService.activateAgent(id, currentUser);
  }

  @Patch(':id/deactivate')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Deactivate agent account' })
  @ApiResponse({
    status: 200,
    description: 'Agent deactivated successfully',
    type: User,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Only admins can deactivate agents',
  })
  @ApiResponse({
    status: 404,
    description: 'Agent not found',
  })
  deactivateAgent(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() currentUser: User,
  ) {
    return this.agentsService.deactivateAgent(id, currentUser);
  }
}
