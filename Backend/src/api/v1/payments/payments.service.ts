import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Payment, PaymentStatus, PaymentType, PaymentGateway } from './entities/payment.entity';
import { User, UserRole } from '../../../modules/users/entities/user.entity';
import { Property } from '../properties/entities/property.entity';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaginationUtil, PaginationResult } from '../../../common/utils/pagination.util';
import { PaginationDto } from '../../../common/dto/pagination.dto';

@Injectable()
export class PaymentsService {
  constructor(
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    private readonly configService: ConfigService,
  ) {}

  async create(createPaymentDto: CreatePaymentDto, user: User): Promise<Payment> {
    // Validate property if provided
    if (createPaymentDto.propertyId) {
      const property = await this.propertyRepository.findOne({
        where: { id: createPaymentDto.propertyId },
      });
      if (!property) {
        throw new BadRequestException('Property not found');
      }
    }

    // Validate recipient if provided
    if (createPaymentDto.recipientId) {
      const recipient = await this.userRepository.findOne({
        where: { id: createPaymentDto.recipientId },
      });
      if (!recipient) {
        throw new BadRequestException('Recipient not found');
      }
    }

    // Generate payment reference
    const reference = await this.generatePaymentReference();

    // Create payment
    const payment = this.paymentRepository.create({
      ...createPaymentDto,
      reference,
      userId: user.id,
      currency: createPaymentDto.currency || 'NGN',
      dueDate: createPaymentDto.dueDate ? new Date(createPaymentDto.dueDate) : null,
    });

    return this.paymentRepository.save(payment);
  }

  async findAll(paginationDto: PaginationDto, user?: User): Promise<PaginationResult<Payment>> {
    const queryBuilder = this.paymentRepository
      .createQueryBuilder('payment')
      .leftJoinAndSelect('payment.user', 'user')
      .leftJoinAndSelect('payment.property', 'property')
      .leftJoinAndSelect('payment.recipient', 'recipient');

    // Apply user-based filtering
    if (user && user.role !== UserRole.ADMIN) {
      if (user.role === UserRole.AGENT) {
        // Agents can see payments for properties they manage
        queryBuilder.andWhere(
          '(payment.userId = :userId OR property.agentId = :userId OR payment.recipientId = :userId)',
          { userId: user.id }
        );
      } else {
        // Other users can only see their own payments
        queryBuilder.andWhere(
          '(payment.userId = :userId OR payment.recipientId = :userId)',
          { userId: user.id }
        );
      }
    }

    if (paginationDto.search) {
      queryBuilder.andWhere(
        '(payment.reference ILIKE :search OR payment.description ILIKE :search)',
        { search: `%${paginationDto.search}%` }
      );
    }

    return PaginationUtil.paginate(queryBuilder, {
      page: paginationDto.page || 1,
      limit: paginationDto.limit || 20,
      sortBy: paginationDto.sortBy || 'createdAt',
      sortOrder: paginationDto.sortOrder || 'DESC',
    });
  }

  async findOne(id: string, user?: User): Promise<Payment> {
    const queryBuilder = this.paymentRepository
      .createQueryBuilder('payment')
      .leftJoinAndSelect('payment.user', 'user')
      .leftJoinAndSelect('payment.property', 'property')
      .leftJoinAndSelect('payment.recipient', 'recipient')
      .where('payment.id = :id', { id });

    // Apply user-based filtering
    if (user && user.role !== UserRole.ADMIN) {
      if (user.role === UserRole.AGENT) {
        queryBuilder.andWhere(
          '(payment.userId = :userId OR property.agentId = :userId OR payment.recipientId = :userId)',
          { userId: user.id }
        );
      } else {
        queryBuilder.andWhere(
          '(payment.userId = :userId OR payment.recipientId = :userId)',
          { userId: user.id }
        );
      }
    }

    const payment = await queryBuilder.getOne();

    if (!payment) {
      throw new NotFoundException(`Payment with ID ${id} not found`);
    }

    return payment;
  }

  async updateStatus(id: string, status: PaymentStatus, user: User): Promise<Payment> {
    // Only admins and agents can update payment status
    if (![UserRole.ADMIN, UserRole.AGENT].includes(user.role)) {
      throw new ForbiddenException('Only admins and agents can update payment status');
    }

    const payment = await this.findOne(id, user);

    payment.status = status;
    if (status === PaymentStatus.COMPLETED) {
      payment.paidAt = new Date();
    }

    return this.paymentRepository.save(payment);
  }

  async processPayment(id: string, gatewayResponse: any, user: User): Promise<Payment> {
    const payment = await this.findOne(id, user);

    payment.gatewayResponse = gatewayResponse;
    payment.gatewayReference = gatewayResponse.reference || gatewayResponse.tx_ref;
    
    if (gatewayResponse.status === 'success') {
      payment.status = PaymentStatus.COMPLETED;
      payment.paidAt = new Date();
    } else {
      payment.status = PaymentStatus.FAILED;
      payment.failureReason = gatewayResponse.message || 'Payment failed';
    }

    return this.paymentRepository.save(payment);
  }

  async getPaymentStats(user?: User): Promise<any> {
    let queryBuilder = this.paymentRepository.createQueryBuilder('payment');

    // Apply user-based filtering
    if (user && user.role !== UserRole.ADMIN) {
      if (user.role === UserRole.AGENT) {
        queryBuilder = queryBuilder
          .leftJoin('payment.property', 'property')
          .where(
            '(payment.userId = :userId OR property.agentId = :userId OR payment.recipientId = :userId)',
            { userId: user.id }
          );
      } else {
        queryBuilder = queryBuilder.where(
          '(payment.userId = :userId OR payment.recipientId = :userId)',
          { userId: user.id }
        );
      }
    }

    const [
      totalPayments,
      completedPayments,
      pendingPayments,
      failedPayments,
      totalAmount,
      completedAmount,
    ] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('payment.status = :status', { status: PaymentStatus.COMPLETED }).getCount(),
      queryBuilder.clone().andWhere('payment.status = :status', { status: PaymentStatus.PENDING }).getCount(),
      queryBuilder.clone().andWhere('payment.status = :status', { status: PaymentStatus.FAILED }).getCount(),
      queryBuilder.clone().select('SUM(payment.amount)', 'total').getRawOne().then(result => parseFloat(result.total) || 0),
      queryBuilder.clone()
        .andWhere('payment.status = :status', { status: PaymentStatus.COMPLETED })
        .select('SUM(payment.amount)', 'total')
        .getRawOne()
        .then(result => parseFloat(result.total) || 0),
    ]);

    return {
      totalPayments,
      completedPayments,
      pendingPayments,
      failedPayments,
      totalAmount,
      completedAmount,
      successRate: totalPayments > 0 ? (completedPayments / totalPayments) * 100 : 0,
    };
  }

  async getOverduePayments(user?: User): Promise<Payment[]> {
    let queryBuilder = this.paymentRepository
      .createQueryBuilder('payment')
      .leftJoinAndSelect('payment.user', 'user')
      .leftJoinAndSelect('payment.property', 'property')
      .where('payment.status = :status', { status: PaymentStatus.PENDING })
      .andWhere('payment.dueDate < :now', { now: new Date() });

    // Apply user-based filtering
    if (user && user.role !== UserRole.ADMIN) {
      if (user.role === UserRole.AGENT) {
        queryBuilder = queryBuilder.andWhere(
          '(payment.userId = :userId OR property.agentId = :userId)',
          { userId: user.id }
        );
      } else {
        queryBuilder = queryBuilder.andWhere('payment.userId = :userId', { userId: user.id });
      }
    }

    return queryBuilder.getMany();
  }

  async getPaymentsByType(paymentType: PaymentType, user?: User): Promise<Payment[]> {
    let queryBuilder = this.paymentRepository
      .createQueryBuilder('payment')
      .leftJoinAndSelect('payment.user', 'user')
      .leftJoinAndSelect('payment.property', 'property')
      .where('payment.paymentType = :paymentType', { paymentType });

    // Apply user-based filtering
    if (user && user.role !== UserRole.ADMIN) {
      if (user.role === UserRole.AGENT) {
        queryBuilder = queryBuilder.andWhere(
          '(payment.userId = :userId OR property.agentId = :userId OR payment.recipientId = :userId)',
          { userId: user.id }
        );
      } else {
        queryBuilder = queryBuilder.andWhere(
          '(payment.userId = :userId OR payment.recipientId = :userId)',
          { userId: user.id }
        );
      }
    }

    return queryBuilder.getMany();
  }

  private async generatePaymentReference(): Promise<string> {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    
    // Get count of payments today
    const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);
    
    const count = await this.paymentRepository
      .createQueryBuilder('payment')
      .where('payment.createdAt >= :startOfDay AND payment.createdAt < :endOfDay', {
        startOfDay,
        endOfDay,
      })
      .getCount();

    return `PAY_${dateStr}_${String(count + 1).padStart(3, '0')}`;
  }
}
