import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { Payment, PaymentStatus, PaymentType } from './entities/payment.entity';
import { User, UserRole } from '../../../modules/users/entities/user.entity';
import { PaginationDto } from '../../../common/dto/pagination.dto';

import { JwtAuthGuard } from '../../../modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../modules/auth/guards/roles.guard';
import { Roles } from '../../../modules/auth/decorators/roles.decorator';
import { GetUser } from '../../../common/decorators/user.decorator';
import { ParseUUIDPipe } from '../../../common/pipes/parse-uuid.pipe';
import { ApiPaginatedResponse } from '../../../common/decorators/api-paginated-response.decorator';

@ApiTags('Payments')
@Controller('payments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new payment' })
  @ApiResponse({
    status: 201,
    description: 'Payment created successfully',
    type: Payment,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  create(@Body() createPaymentDto: CreatePaymentDto, @GetUser() user: User) {
    return this.paymentsService.create(createPaymentDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'Get all payments' })
  @ApiPaginatedResponse(Payment)
  @ApiResponse({
    status: 200,
    description: 'Payments retrieved successfully',
  })
  findAll(@Query() paginationDto: PaginationDto, @GetUser() user: User) {
    return this.paymentsService.findAll(paginationDto, user);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get payment statistics' })
  @ApiResponse({
    status: 200,
    description: 'Payment statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalPayments: { type: 'number', example: 150 },
        completedPayments: { type: 'number', example: 120 },
        pendingPayments: { type: 'number', example: 25 },
        failedPayments: { type: 'number', example: 5 },
        totalAmount: { type: 'number', example: 50000000 },
        completedAmount: { type: 'number', example: 45000000 },
        successRate: { type: 'number', example: 80.5 },
      },
    },
  })
  getStats(@GetUser() user: User) {
    return this.paymentsService.getPaymentStats(user);
  }

  @Get('overdue')
  @ApiOperation({ summary: 'Get overdue payments' })
  @ApiResponse({
    status: 200,
    description: 'Overdue payments retrieved successfully',
    type: [Payment],
  })
  getOverduePayments(@GetUser() user: User) {
    return this.paymentsService.getOverduePayments(user);
  }

  @Get('by-type/:type')
  @ApiOperation({ summary: 'Get payments by type' })
  @ApiResponse({
    status: 200,
    description: 'Payments by type retrieved successfully',
    type: [Payment],
  })
  getPaymentsByType(@Param('type') type: PaymentType, @GetUser() user: User) {
    return this.paymentsService.getPaymentsByType(type, user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get payment by ID' })
  @ApiResponse({
    status: 200,
    description: 'Payment retrieved successfully',
    type: Payment,
  })
  @ApiResponse({
    status: 404,
    description: 'Payment not found',
  })
  findOne(@Param('id', ParseUUIDPipe) id: string, @GetUser() user: User) {
    return this.paymentsService.findOne(id, user);
  }

  @Patch(':id/status')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.AGENT)
  @ApiOperation({ summary: 'Update payment status' })
  @ApiResponse({
    status: 200,
    description: 'Payment status updated successfully',
    type: Payment,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Only admins and agents can update payment status',
  })
  updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('status') status: PaymentStatus,
    @GetUser() user: User,
  ) {
    return this.paymentsService.updateStatus(id, status, user);
  }

  @Post(':id/process')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Process payment with gateway response' })
  @ApiResponse({
    status: 200,
    description: 'Payment processed successfully',
    type: Payment,
  })
  @ApiResponse({
    status: 404,
    description: 'Payment not found',
  })
  processPayment(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() gatewayResponse: any,
    @GetUser() user: User,
  ) {
    return this.paymentsService.processPayment(id, gatewayResponse, user);
  }
}
