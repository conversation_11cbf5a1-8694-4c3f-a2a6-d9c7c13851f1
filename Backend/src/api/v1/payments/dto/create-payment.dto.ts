import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNumber,
  IsString,
  IsOptional,
  IsUUID,
  IsDateString,
  Min,
  Max,
  MinLength,
  <PERSON>Length,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentType, PaymentGateway } from '../entities/payment.entity';

export class CreatePaymentDto {
  @ApiProperty({
    description: 'Payment type',
    enum: PaymentType,
    example: PaymentType.RENT,
  })
  @IsEnum(PaymentType, { message: 'Payment type must be a valid type' })
  paymentType: PaymentType;

  @ApiProperty({
    description: 'Payment gateway',
    enum: PaymentGateway,
    example: PaymentGateway.PAYSTACK,
  })
  @IsEnum(PaymentGateway, { message: 'Payment gateway must be a valid gateway' })
  gateway: PaymentGateway;

  @ApiProperty({
    description: 'Payment amount in Naira',
    example: 1200000,
    minimum: 100,
    maximum: 100000000,
  })
  @IsNumber({}, { message: 'Amount must be a valid number' })
  @Min(100, { message: 'Amount must be at least ₦100' })
  @Max(100000000, { message: 'Amount must not exceed ₦100,000,000' })
  @Type(() => Number)
  amount: number;

  @ApiProperty({
    description: 'Payment description',
    example: 'Annual rent payment for 3-bedroom apartment',
    minLength: 10,
    maxLength: 500,
  })
  @IsString()
  @MinLength(10, { message: 'Description must be at least 10 characters long' })
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description: string;

  @ApiProperty({
    description: 'Property ID (for rent/deposit payments)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'Property ID must be a valid UUID' })
  propertyId?: string;

  @ApiProperty({
    description: 'Recipient user ID (for commission payments)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'Recipient ID must be a valid UUID' })
  recipientId?: string;

  @ApiProperty({
    description: 'Payment due date',
    example: '2023-12-31T23:59:59Z',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Due date must be a valid date string' })
  dueDate?: string;

  @ApiProperty({
    description: 'Currency code',
    example: 'NGN',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiProperty({
    description: 'Payment metadata',
    example: { 
      period: '2024-01',
      notes: 'First payment of the year'
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
