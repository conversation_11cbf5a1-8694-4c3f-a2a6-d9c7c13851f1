import {
  Controller,
  Get,
  Post,
  Patch,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { NotificationsService } from './notifications.service';
import { Notification, NotificationType } from './entities/notification.entity';
import { User, UserRole } from '../../../modules/users/entities/user.entity';
import { PaginationDto } from '../../../common/dto/pagination.dto';

import { JwtAuthGuard } from '../../../modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../modules/auth/guards/roles.guard';
import { Roles } from '../../../modules/auth/decorators/roles.decorator';
import { GetUser } from '../../../common/decorators/user.decorator';
import { ParseUUI<PERSON>ipe } from '../../../common/pipes/parse-uuid.pipe';
import { ApiPaginatedResponse } from '../../../common/decorators/api-paginated-response.decorator';

@ApiTags('Notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all notifications' })
  @ApiPaginatedResponse(Notification)
  @ApiResponse({
    status: 200,
    description: 'Notifications retrieved successfully',
  })
  findAll(@Query() paginationDto: PaginationDto, @GetUser() user: User) {
    return this.notificationsService.findAll(paginationDto, user);
  }

  @Get('my-notifications')
  @ApiOperation({ summary: 'Get current user notifications' })
  @ApiPaginatedResponse(Notification)
  @ApiResponse({
    status: 200,
    description: 'User notifications retrieved successfully',
  })
  getMyNotifications(@GetUser() user: User, @Query() paginationDto: PaginationDto) {
    return this.notificationsService.getUserNotifications(user.id, paginationDto);
  }

  @Get('unread-count')
  @ApiOperation({ summary: 'Get unread notifications count' })
  @ApiResponse({
    status: 200,
    description: 'Unread count retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        count: { type: 'number', example: 5 },
      },
    },
  })
  async getUnreadCount(@GetUser() user: User) {
    const count = await this.notificationsService.getUnreadCount(user.id);
    return { count };
  }

  @Get('stats')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.AGENT)
  @ApiOperation({ summary: 'Get notification statistics' })
  @ApiResponse({
    status: 200,
    description: 'Notification statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalNotifications: { type: 'number', example: 1000 },
        sentNotifications: { type: 'number', example: 950 },
        deliveredNotifications: { type: 'number', example: 900 },
        readNotifications: { type: 'number', example: 750 },
        failedNotifications: { type: 'number', example: 50 },
        deliveryRate: { type: 'number', example: 95.5 },
        readRate: { type: 'number', example: 83.3 },
      },
    },
  })
  getStats(@GetUser() user: User) {
    return this.notificationsService.getNotificationStats(user);
  }

  @Get('by-type/:type')
  @ApiOperation({ summary: 'Get notifications by type' })
  @ApiResponse({
    status: 200,
    description: 'Notifications by type retrieved successfully',
    type: [Notification],
  })
  getNotificationsByType(@Param('type') type: NotificationType, @GetUser() user: User) {
    return this.notificationsService.getNotificationsByType(type, user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get notification by ID' })
  @ApiResponse({
    status: 200,
    description: 'Notification retrieved successfully',
    type: Notification,
  })
  @ApiResponse({
    status: 404,
    description: 'Notification not found',
  })
  findOne(@Param('id', ParseUUIDPipe) id: string, @GetUser() user: User) {
    return this.notificationsService.findOne(id, user);
  }

  @Patch(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({
    status: 200,
    description: 'Notification marked as read successfully',
    type: Notification,
  })
  @ApiResponse({
    status: 404,
    description: 'Notification not found',
  })
  markAsRead(@Param('id', ParseUUIDPipe) id: string, @GetUser() user: User) {
    return this.notificationsService.markAsRead(id, user);
  }

  @Post('mark-all-read')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({
    status: 204,
    description: 'All notifications marked as read successfully',
  })
  markAllAsRead(@GetUser() user: User) {
    return this.notificationsService.markAllAsRead(user.id);
  }

  @Post('retry-failed')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Retry failed notifications (Admin only)' })
  @ApiResponse({
    status: 204,
    description: 'Failed notifications queued for retry',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
  })
  retryFailedNotifications() {
    return this.notificationsService.retryFailedNotifications();
  }
}
