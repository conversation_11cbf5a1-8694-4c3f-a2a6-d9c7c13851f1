import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../../../modules/users/entities/user.entity';

export enum NotificationType {
  PROPERTY_INQUIRY = 'property_inquiry',
  PAYMENT_RECEIVED = 'payment_received',
  PAYMENT_DUE = 'payment_due',
  PROPERTY_APPROVED = 'property_approved',
  PROPERTY_REJECTED = 'property_rejected',
  USER_REGISTERED = 'user_registered',
  SYSTEM_ALERT = 'system_alert',
  MARKETING = 'marketing',
}

export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app',
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  READ = 'read',
}

@Entity('notifications')
export class Notification {
  @ApiProperty({
    description: 'Unique identifier for the notification',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Notification type',
    enum: NotificationType,
    example: NotificationType.PROPERTY_INQUIRY,
  })
  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  @Index()
  type: NotificationType;

  @ApiProperty({
    description: 'Notification channel',
    enum: NotificationChannel,
    example: NotificationChannel.EMAIL,
  })
  @Column({
    type: 'enum',
    enum: NotificationChannel,
  })
  @Index()
  channel: NotificationChannel;

  @ApiProperty({
    description: 'Notification status',
    enum: NotificationStatus,
    example: NotificationStatus.SENT,
  })
  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING,
  })
  @Index()
  status: NotificationStatus;

  @ApiProperty({
    description: 'Notification title',
    example: 'New Property Inquiry',
  })
  @Column()
  title: string;

  @ApiProperty({
    description: 'Notification message',
    example: 'You have received a new inquiry for your property listing.',
  })
  @Column('text')
  message: string;

  @ApiProperty({
    description: 'Recipient email (for email notifications)',
    example: '<EMAIL>',
    required: false,
  })
  @Column({ nullable: true })
  recipientEmail?: string;

  @ApiProperty({
    description: 'Recipient phone (for SMS notifications)',
    example: '+2348012345678',
    required: false,
  })
  @Column({ nullable: true })
  recipientPhone?: string;

  @ApiProperty({
    description: 'Push notification token',
    required: false,
  })
  @Column({ nullable: true })
  pushToken?: string;

  @ApiProperty({
    description: 'Notification data/payload',
    required: false,
  })
  @Column({ type: 'jsonb', nullable: true })
  data?: Record<string, any>;

  @ApiProperty({
    description: 'Action URL for the notification',
    example: '/properties/123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @Column({ nullable: true })
  actionUrl?: string;

  @ApiProperty({
    description: 'When the notification was sent',
    required: false,
  })
  @Column({ nullable: true })
  sentAt?: Date;

  @ApiProperty({
    description: 'When the notification was delivered',
    required: false,
  })
  @Column({ nullable: true })
  deliveredAt?: Date;

  @ApiProperty({
    description: 'When the notification was read',
    required: false,
  })
  @Column({ nullable: true })
  readAt?: Date;

  @ApiProperty({
    description: 'Number of retry attempts',
    example: 0,
  })
  @Column({ default: 0 })
  retryCount: number;

  @ApiProperty({
    description: 'Error message if notification failed',
    required: false,
  })
  @Column({ nullable: true })
  errorMessage?: string;

  @ApiProperty({
    description: 'Notification recipient user',
    type: () => User,
  })
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'user_id' })
  @Index()
  userId: string;

  @ApiProperty({
    description: 'When the notification was created',
    example: '2023-12-01T10:00:00Z',
  })
  @CreateDateColumn()
  @Index()
  createdAt: Date;

  @ApiProperty({
    description: 'When the notification was last updated',
    example: '2023-12-01T10:00:00Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties
  @ApiProperty({
    description: 'Whether notification is read',
    example: false,
  })
  get isRead(): boolean {
    return this.status === NotificationStatus.READ;
  }

  @ApiProperty({
    description: 'Whether notification was successfully delivered',
    example: true,
  })
  get isDelivered(): boolean {
    return [NotificationStatus.DELIVERED, NotificationStatus.READ].includes(this.status);
  }
}
