import { User } from '../../../../modules/users/entities/user.entity';
import { Property } from '../../../v1/properties/entities/property.entity';
import { Commission } from '../../../v1/commissions/entities/commission.entity';
import { Client } from '../../../v1/clients/entities/client.entity';
import { Goal } from '../../../v1/goals/entities/goal.entity';

export class DashboardDto {
  agent: User;
  properties: Property[];
  commissions: {
    totalEarned: number;
    pendingAmount: number;
    cancelledAmount: number;
    averageCommission: number;
    dealsCompleted: number;
  };
  clients: Client[];
  goals: Goal[];
  stats: {
    totalProperties: number;
    availableProperties: number;
    rentedProperties: number;
    totalViews: number;
    totalInquiries: number;
    conversionRate: number;
    occupancyRate: number;
  };
  lastUpdated: string;
}