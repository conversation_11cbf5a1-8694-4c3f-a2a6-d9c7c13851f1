import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserRole, UserStatus } from '../../../modules/users/entities/user.entity';
import { Property, PropertyStatus } from '../properties/entities/property.entity';
import { Payment, PaymentStatus } from '../payments/entities/payment.entity';
import { PaginationUtil, PaginationResult } from '../../../common/utils/pagination.util';
import { PaginationDto } from '../../../common/dto/pagination.dto';

@Injectable()
export class AdminService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Property)
    private readonly propertyRepository: Repository<Property>,
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
  ) {}

  async getSystemOverview(): Promise<any> {
    const [
      totalUsers,
      totalProperties,
      totalPayments,
      pendingVerifications,
      recentActivity,
      systemHealth,
    ] = await Promise.all([
      this.getUserStats(),
      this.getPropertyStats(),
      this.getPaymentStats(),
      this.getPendingVerifications(),
      this.getRecentActivity(),
      this.getSystemHealth(),
    ]);

    return {
      users: totalUsers,
      properties: totalProperties,
      payments: totalPayments,
      pendingVerifications,
      recentActivity,
      systemHealth,
    };
  }

  async getAllUsers(paginationDto: PaginationDto): Promise<PaginationResult<User>> {
    const queryBuilder = this.userRepository.createQueryBuilder('user');

    if (paginationDto.search) {
      queryBuilder.andWhere(
        '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)',
        { search: `%${paginationDto.search}%` }
      );
    }

    return PaginationUtil.paginate(queryBuilder, {
      page: paginationDto.page || 1,
      limit: paginationDto.limit || 20,
      sortBy: paginationDto.sortBy || 'createdAt',
      sortOrder: paginationDto.sortOrder || 'DESC',
    });
  }

  async getAllProperties(paginationDto: PaginationDto): Promise<PaginationResult<Property>> {
    const queryBuilder = this.propertyRepository
      .createQueryBuilder('property')
      .leftJoinAndSelect('property.landlord', 'landlord')
      .leftJoinAndSelect('property.agent', 'agent');

    if (paginationDto.search) {
      queryBuilder.andWhere(
        '(property.title ILIKE :search OR property.description ILIKE :search OR property.location ILIKE :search)',
        { search: `%${paginationDto.search}%` }
      );
    }

    return PaginationUtil.paginate(queryBuilder, {
      page: paginationDto.page || 1,
      limit: paginationDto.limit || 20,
      sortBy: paginationDto.sortBy || 'createdAt',
      sortOrder: paginationDto.sortOrder || 'DESC',
    });
  }

  async getAllPayments(paginationDto: PaginationDto): Promise<PaginationResult<Payment>> {
    const queryBuilder = this.paymentRepository
      .createQueryBuilder('payment')
      .leftJoinAndSelect('payment.user', 'user')
      .leftJoinAndSelect('payment.property', 'property')
      .leftJoinAndSelect('payment.recipient', 'recipient');

    if (paginationDto.search) {
      queryBuilder.andWhere(
        '(payment.reference ILIKE :search OR payment.description ILIKE :search)',
        { search: `%${paginationDto.search}%` }
      );
    }

    return PaginationUtil.paginate(queryBuilder, {
      page: paginationDto.page || 1,
      limit: paginationDto.limit || 20,
      sortBy: paginationDto.sortBy || 'createdAt',
      sortOrder: paginationDto.sortOrder || 'DESC',
    });
  }

  async suspendUser(userId: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.status = UserStatus.SUSPENDED;
    user.isActive = false;
    return this.userRepository.save(user);
  }

  async activateUser(userId: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    user.status = UserStatus.ACTIVE;
    user.isActive = true;
    return this.userRepository.save(user);
  }

  async verifyProperty(propertyId: string): Promise<Property> {
    const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
    if (!property) {
      throw new NotFoundException('Property not found');
    }

    property.isVerified = true;
    return this.propertyRepository.save(property);
  }

  async unverifyProperty(propertyId: string): Promise<Property> {
    const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
    if (!property) {
      throw new NotFoundException('Property not found');
    }

    property.isVerified = false;
    return this.propertyRepository.save(property);
  }

  async featureProperty(propertyId: string): Promise<Property> {
    const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
    if (!property) {
      throw new NotFoundException('Property not found');
    }

    property.isFeatured = true;
    return this.propertyRepository.save(property);
  }

  async unfeatureProperty(propertyId: string): Promise<Property> {
    const property = await this.propertyRepository.findOne({ where: { id: propertyId } });
    if (!property) {
      throw new NotFoundException('Property not found');
    }

    property.isFeatured = false;
    return this.propertyRepository.save(property);
  }

  async approvePayment(paymentId: string): Promise<Payment> {
    const payment = await this.paymentRepository.findOne({ where: { id: paymentId } });
    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    payment.status = PaymentStatus.COMPLETED;
    payment.paidAt = new Date();
    return this.paymentRepository.save(payment);
  }

  async rejectPayment(paymentId: string, reason: string): Promise<Payment> {
    const payment = await this.paymentRepository.findOne({ where: { id: paymentId } });
    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    payment.status = PaymentStatus.FAILED;
    payment.failureReason = reason;
    return this.paymentRepository.save(payment);
  }

  async getPendingApprovals(): Promise<any> {
    const [pendingProperties, pendingPayments, suspendedUsers] = await Promise.all([
      this.propertyRepository.find({
        where: { isVerified: false },
        relations: ['landlord', 'agent'],
        take: 10,
        order: { createdAt: 'DESC' },
      }),
      this.paymentRepository.find({
        where: { status: PaymentStatus.PENDING },
        relations: ['user', 'property'],
        take: 10,
        order: { createdAt: 'DESC' },
      }),
      this.userRepository.find({
        where: { status: UserStatus.SUSPENDED },
        take: 10,
        order: { updatedAt: 'DESC' },
      }),
    ]);

    return {
      pendingProperties,
      pendingPayments,
      suspendedUsers,
    };
  }

  async getRecentActivity(): Promise<any[]> {
    const [recentUsers, recentProperties, recentPayments] = await Promise.all([
      this.userRepository.find({
        take: 5,
        order: { createdAt: 'DESC' },
        select: ['id', 'firstName', 'lastName', 'email', 'role', 'createdAt'],
      }),
      this.propertyRepository.find({
        take: 5,
        order: { createdAt: 'DESC' },
        select: ['id', 'title', 'location', 'pricePerYear', 'createdAt'],
        relations: ['landlord'],
      }),
      this.paymentRepository.find({
        take: 5,
        order: { createdAt: 'DESC' },
        select: ['id', 'reference', 'amount', 'status', 'createdAt'],
        relations: ['user'],
      }),
    ]);

    const activities = [];

    recentUsers.forEach(user => {
      activities.push({
        type: 'user_registration',
        description: `New ${user.role} registered: ${user.firstName} ${user.lastName}`,
        timestamp: user.createdAt,
        data: user,
      });
    });

    recentProperties.forEach(property => {
      activities.push({
        type: 'property_listing',
        description: `New property listed: ${property.title}`,
        timestamp: property.createdAt,
        data: property,
      });
    });

    recentPayments.forEach(payment => {
      activities.push({
        type: 'payment',
        description: `Payment ${payment.status}: ${payment.reference}`,
        timestamp: payment.createdAt,
        data: payment,
      });
    });

    return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 20);
  }

  async bulkAction(action: string, ids: string[], data?: any): Promise<any> {
    switch (action) {
      case 'activate_users':
        await this.userRepository.update(ids, { status: UserStatus.ACTIVE, isActive: true });
        break;
      case 'suspend_users':
        await this.userRepository.update(ids, { status: UserStatus.SUSPENDED, isActive: false });
        break;
      case 'verify_properties':
        await this.propertyRepository.update(ids, { isVerified: true });
        break;
      case 'unverify_properties':
        await this.propertyRepository.update(ids, { isVerified: false });
        break;
      case 'feature_properties':
        await this.propertyRepository.update(ids, { isFeatured: true });
        break;
      case 'unfeature_properties':
        await this.propertyRepository.update(ids, { isFeatured: false });
        break;
      case 'approve_payments':
        await this.paymentRepository.update(ids, { status: PaymentStatus.COMPLETED, paidAt: new Date() });
        break;
      case 'reject_payments':
        await this.paymentRepository.update(ids, { status: PaymentStatus.FAILED, failureReason: data?.reason || 'Rejected by admin' });
        break;
      default:
        throw new ForbiddenException('Invalid bulk action');
    }

    return { success: true, affectedCount: ids.length };
  }

  private async getUserStats(): Promise<any> {
    const [total, active, suspended, byRole] = await Promise.all([
      this.userRepository.count(),
      this.userRepository.count({ where: { isActive: true } }),
      this.userRepository.count({ where: { status: UserStatus.SUSPENDED } }),
      this.userRepository
        .createQueryBuilder('user')
        .select('user.role', 'role')
        .addSelect('COUNT(*)', 'count')
        .groupBy('user.role')
        .getRawMany(),
    ]);

    return { total, active, suspended, byRole };
  }

  private async getPropertyStats(): Promise<any> {
    const [total, verified, featured, available, rented] = await Promise.all([
      this.propertyRepository.count(),
      this.propertyRepository.count({ where: { isVerified: true } }),
      this.propertyRepository.count({ where: { isFeatured: true } }),
      this.propertyRepository.count({ where: { status: PropertyStatus.AVAILABLE } }),
      this.propertyRepository.count({ where: { status: PropertyStatus.RENTED } }),
    ]);

    return { total, verified, featured, available, rented };
  }

  private async getPaymentStats(): Promise<any> {
    const [total, completed, pending, failed, totalAmount] = await Promise.all([
      this.paymentRepository.count(),
      this.paymentRepository.count({ where: { status: PaymentStatus.COMPLETED } }),
      this.paymentRepository.count({ where: { status: PaymentStatus.PENDING } }),
      this.paymentRepository.count({ where: { status: PaymentStatus.FAILED } }),
      this.paymentRepository
        .createQueryBuilder('payment')
        .select('SUM(payment.amount)', 'total')
        .where('payment.status = :status', { status: PaymentStatus.COMPLETED })
        .getRawOne()
        .then(result => parseFloat(result.total) || 0),
    ]);

    return { total, completed, pending, failed, totalAmount };
  }

  private async getPendingVerifications(): Promise<any> {
    const [unverifiedProperties, pendingPayments, inactiveUsers] = await Promise.all([
      this.propertyRepository.count({ where: { isVerified: false } }),
      this.paymentRepository.count({ where: { status: PaymentStatus.PENDING } }),
      this.userRepository.count({ where: { isActive: false } }),
    ]);

    return { unverifiedProperties, pendingPayments, inactiveUsers };
  }

  private async getSystemHealth(): Promise<any> {
    // This would typically check various system metrics
    // For now, we'll return basic health indicators
    return {
      status: 'healthy',
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      timestamp: new Date(),
    };
  }
}
