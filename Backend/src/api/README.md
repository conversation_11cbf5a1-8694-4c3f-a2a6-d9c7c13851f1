# PHCityRent API Documentation

## API Structure

The PHCityRent API is organized into versioned modules under `/api/v1/` for better maintainability and future compatibility.

### Current API Version: v1

Base URL: `http://localhost:3001/api/v1`

## Available Modules

### ✅ **Implemented Modules**

#### 1. **Authentication** (`/auth`)
Complete JWT-based authentication system with refresh tokens and RBAC.

**Endpoints:**
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh access token
- `POST /auth/logout` - User logout
- `PATCH /auth/change-password` - Change password
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password with token
- `GET /auth/me` - Get current user profile

#### 2. **Users** (`/users`)
User management with role-based access control.

**Endpoints:**
- `GET /users` - Get all users (Admin/Agent only)
- `GET /users/profile` - Get current user profile
- `GET /users/:id` - Get user by ID (Admin/Agent only)
- `PATCH /users/profile` - Update current user profile
- `PATCH /users/:id` - Update user by ID (Admin only)
- `PATCH /users/:id/activate` - Activate user (Admin only)
- `PATCH /users/:id/deactivate` - Deactivate user (Admin only)
- `DELETE /users/:id` - Delete user (Admin only)

#### 3. **Properties** (`/properties`)
Comprehensive property management system with advanced search and filtering.

**Endpoints:**
- `POST /properties` - Create new property (Landlord/Agent/Admin)
- `GET /properties` - Search and filter properties (Public)
- `GET /properties/my-properties` - Get current user properties
- `GET /properties/stats` - Get property statistics (Admin/Agent)
- `GET /properties/:id` - Get property by ID (Public)
- `PATCH /properties/:id` - Update property (Owner/Agent/Admin)
- `PATCH /properties/:id/status` - Update property status
- `PATCH /properties/:id/toggle-featured` - Toggle featured status (Admin/Agent)
- `PATCH /properties/:id/toggle-verified` - Toggle verified status (Admin)
- `POST /properties/:id/inquire` - Record property inquiry (Public)
- `DELETE /properties/:id` - Delete property (Owner/Agent/Admin)

**Features:**
- Advanced search with multiple filters
- Property type filtering (apartment, house, duplex, etc.)
- Price range filtering
- Location-based search
- Bedroom/bathroom filtering
- Amenities filtering
- Featured and verified properties
- Property statistics and analytics
- View and inquiry tracking

#### 4. **Agents** (`/agents`)
Agent management and performance tracking system.

**Endpoints:**
- `GET /agents` - Get all active agents (Public)
- `GET /agents/top` - Get top performing agents (Public)
- `GET /agents/search` - Search agents by name/email (Public)
- `GET /agents/:id` - Get agent by ID (Public)
- `GET /agents/:id/properties` - Get properties managed by agent (Public)
- `GET /agents/:id/stats` - Get agent performance statistics (Admin/Agent)
- `PATCH /agents/:id/activate` - Activate agent (Admin only)
- `PATCH /agents/:id/deactivate` - Deactivate agent (Admin only)

**Features:**
- Agent performance metrics
- Property management tracking
- Top agents ranking
- Agent search functionality
- Performance statistics (conversion rates, occupancy rates)

#### 5. **Health** (`/health`)
System health monitoring and diagnostics.

**Endpoints:**
- `GET /health` - Comprehensive health check
- `GET /health/database` - Database health check
- `GET /health/memory` - Memory health check
- `GET /health/disk` - Disk health check

### 🚧 **Placeholder Modules** (Ready for Implementation)

#### 6. **Payments** (`/payments`)
Payment processing with Nigerian payment gateways.

**Planned Features:**
- Paystack integration
- Flutterwave integration
- Payment history tracking
- Subscription management
- Invoice generation
- Payment analytics

#### 7. **Analytics** (`/analytics`)
Advanced analytics and reporting system.

**Planned Features:**
- Property market analytics
- User behavior analytics
- Revenue analytics
- Performance dashboards
- Custom reports
- Data visualization

#### 8. **Admin** (`/admin`)
Administrative dashboard and management tools.

**Planned Features:**
- System configuration
- User management dashboard
- Property moderation
- Analytics dashboard
- System monitoring
- Audit logs

#### 9. **Files** (`/files`)
File upload and management system.

**Planned Features:**
- Image upload for properties
- Image optimization and resizing
- Video upload support
- Document management
- CDN integration
- File security and access control

#### 10. **Notifications** (`/notifications`)
Multi-channel notification system.

**Planned Features:**
- Email notifications
- SMS notifications
- Push notifications
- Notification preferences
- Notification history
- Template management

## API Features

### 🔐 **Security**
- JWT authentication with refresh tokens
- Role-based access control (RBAC)
- Rate limiting and throttling
- Input validation and sanitization
- SQL injection protection
- CORS configuration

### 📊 **Performance**
- Database connection pooling
- Redis caching
- Query optimization
- Pagination for large datasets
- Response compression
- Health monitoring

### 📚 **Documentation**
- OpenAPI 3.0 specification
- Swagger UI at `/api/docs`
- Comprehensive endpoint documentation
- Request/response examples
- Error code documentation

### 🔍 **Search & Filtering**
- Advanced property search
- Multiple filter combinations
- Location-based filtering
- Price range filtering
- Amenities filtering
- Sorting options

### 📈 **Analytics**
- Property view tracking
- Inquiry tracking
- Performance metrics
- User activity monitoring
- System health metrics

## Data Models

### **User Roles**
- `admin` - System administrator
- `agent` - Real estate agent
- `landlord` - Property owner
- `tenant` - Property seeker

### **Property Types**
- `apartment` - Apartment
- `house` - House
- `duplex` - Duplex
- `bungalow` - Bungalow
- `flat` - Flat
- `room` - Single room
- `self_contain` - Self-contained room
- `shop` - Commercial shop
- `office` - Office space
- `warehouse` - Warehouse

### **Property Status**
- `available` - Available for rent
- `rented` - Currently rented
- `maintenance` - Under maintenance
- `inactive` - Not available

## Error Handling

The API uses standard HTTP status codes and returns structured error responses:

```json
{
  "statusCode": 400,
  "timestamp": "2023-12-01T10:00:00Z",
  "path": "/api/v1/properties",
  "errorId": "123e4567-e89b-12d3-a456-426614174000",
  "message": "Validation failed",
  "error": "Bad Request"
}
```

## Rate Limiting

- **Default**: 100 requests per minute per IP
- **Authentication endpoints**: Additional throttling for security
- **File uploads**: Separate limits for large file operations

## Pagination

All list endpoints support pagination with the following query parameters:

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `search` - Search query
- `sortBy` - Sort field
- `sortOrder` - Sort direction (ASC/DESC)

## Getting Started

1. **Authentication**: Register or login to get access tokens
2. **Explore**: Use Swagger UI at `/api/docs` for interactive testing
3. **Properties**: Start with property search and filtering
4. **Agents**: Explore agent listings and performance data
5. **Management**: Use role-based endpoints for property management

## Next Steps

The API is designed for easy extension. Priority implementations:

1. **Payment Integration** - Paystack/Flutterwave for Nigerian market
2. **File Upload System** - Property images and documents
3. **Notification System** - Email/SMS/Push notifications
4. **Advanced Analytics** - Market insights and reporting
5. **Admin Dashboard** - Comprehensive management tools

This modular architecture ensures scalability and maintainability as the platform grows.
