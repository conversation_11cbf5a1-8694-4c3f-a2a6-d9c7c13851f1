import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const errorResponse = exception.getResponse();

    const errorId = uuidv4();
    const timestamp = new Date().toISOString();
    const path = request.url;
    const method = request.method;
    const ip = request.ip || request.connection.remoteAddress;
    const userAgent = request.headers['user-agent'];

    // Determine if this is a validation error
    const isValidationError = 
      status === HttpStatus.BAD_REQUEST && 
      typeof errorResponse === 'object' &&
      'message' in errorResponse &&
      Array.isArray(errorResponse['message']);

    // Format the error response
    const formattedResponse = {
      statusCode: status,
      timestamp,
      path,
      errorId,
      ...(typeof errorResponse === 'object' 
        ? errorResponse 
        : { message: errorResponse }),
    };

    // Log the error with appropriate level based on status code
    const logMessage = `[${errorId}] ${method} ${path} ${status} - ${
      typeof errorResponse === 'object' && 'message' in errorResponse
        ? Array.isArray(errorResponse['message'])
          ? errorResponse['message'].join(', ')
          : errorResponse['message']
        : errorResponse
    }`;

    if (status >= 500) {
      this.logger.error(logMessage, exception.stack);
      
      // For 500 errors, don't expose internal details to the client
      formattedResponse['message'] = 'Internal server error';
      delete formattedResponse['error'];
      
      // Add error ID for tracking
      this.logger.error({
        errorId,
        path,
        method,
        ip,
        userAgent,
        status,
        timestamp,
        exception: exception.stack,
      });
    } else if (status >= 400 && status < 500) {
      if (isValidationError) {
        this.logger.debug(logMessage);
      } else {
        this.logger.warn(logMessage);
      }
    }

    response.status(status).json(formattedResponse);
  }
}
