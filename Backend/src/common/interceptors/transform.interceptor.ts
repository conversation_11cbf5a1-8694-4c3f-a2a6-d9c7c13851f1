import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { classToPlain } from 'class-transformer';

export interface Response<T> {
  statusCode: number;
  message: string;
  data: T;
  timestamp: string;
  path: string;
}

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, Response<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest();
    const response = ctx.getResponse();

    return next.handle().pipe(
      map((data) => {
        // Don't transform if response is already in the correct format
        if (data && typeof data === 'object' && 'statusCode' in data) {
          return data;
        }

        // Transform class instances to plain objects
        const transformedData = data ? classToPlain(data) : data;

        return {
          statusCode: response.statusCode,
          message: this.getSuccessMessage(response.statusCode),
          data: transformedData,
          timestamp: new Date().toISOString(),
          path: request.url,
        };
      }),
    );
  }

  private getSuccessMessage(statusCode: number): string {
    switch (statusCode) {
      case 200:
        return 'Success';
      case 201:
        return 'Created successfully';
      case 204:
        return 'No content';
      default:
        return 'Success';
    }
  }
}
