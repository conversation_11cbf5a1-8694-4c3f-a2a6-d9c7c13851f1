import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  constructor(private readonly configService: ConfigService) {}

  getAppInfo() {
    return {
      name: this.configService.get('app.name'),
      version: this.configService.get('app.version'),
      description: this.configService.get('app.description'),
      environment: this.configService.get('app.nodeEnv'),
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }

  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: this.configService.get('app.version'),
    };
  }
}
