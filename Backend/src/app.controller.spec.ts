import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const mockAppService = {
      getAppInfo: jest.fn().mockReturnValue({
        name: 'PHCityRent API',
        version: '1.0.0',
        description: 'Test API',
        environment: 'test'
      }),
      getHealth: jest.fn().mockReturnValue({
        status: 'ok',
        timestamp: new Date().toISOString()
      })
    };

    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        {
          provide: AppService,
          useValue: mockAppService,
        },
      ],
    }).compile();

    appController = app.get<AppController>(AppController);
    appService = app.get<AppService>(AppService);
  });

  it('should be defined', () => {
    expect(appController).toBeDefined();
  });

  describe('getAppInfo', () => {
    it('should return app information', () => {
      const result = appController.getAppInfo();
      expect(result).toBeDefined();
      expect(result.name).toBe('PHCityRent API');
      expect(appService.getAppInfo).toHaveBeenCalled();
    });
  });

  describe('getHealth', () => {
    it('should return health status', () => {
      const result = appController.getHealth();
      expect(result).toBeDefined();
      expect(result.status).toBe('ok');
      expect(appService.getHealth).toHaveBeenCalled();
    });
  });
});
