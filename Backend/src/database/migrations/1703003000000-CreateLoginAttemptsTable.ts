import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateLoginAttemptsTable1703003000000 implements MigrationInterface {
  name = 'CreateLoginAttemptsTable1703003000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create login_attempts table
    await queryRunner.createTable(
      new Table({
        name: 'login_attempts',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()',
          },
          {
            name: 'email',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'ipAddress',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'success',
            type: 'boolean',
            default: false,
          },
          {
            name: 'userAgent',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'failureReason',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create indexes using raw SQL
    await queryRunner.query(`CREATE INDEX "IDX_login_attempts_email" ON "login_attempts" ("email")`);
    await queryRunner.query(`CREATE INDEX "IDX_login_attempts_ipAddress" ON "login_attempts" ("ipAddress")`);
    await queryRunner.query(`CREATE INDEX "IDX_login_attempts_email_ipAddress" ON "login_attempts" ("email", "ipAddress")`);
    await queryRunner.query(`CREATE INDEX "IDX_login_attempts_createdAt" ON "login_attempts" ("createdAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_login_attempts_success" ON "login_attempts" ("success")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('login_attempts');
  }
}
