import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class SupabaseMigrationService {
  private readonly logger = new Logger(SupabaseMigrationService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Convert Supabase migrations to TypeORM format
   */
  async convertSupabaseMigrations(): Promise<void> {
    this.logger.log('🔄 Starting Supabase to TypeORM migration conversion...');

    const supabaseMigrationsPath = path.join(process.cwd(), 'migrations', 'migrations');
    const typeormMigrationsPath = path.join(process.cwd(), 'src', 'database', 'migrations');

    // Ensure TypeORM migrations directory exists
    if (!fs.existsSync(typeormMigrationsPath)) {
      fs.mkdirSync(typeormMigrationsPath, { recursive: true });
    }

    // Get all Supabase migration files
    const migrationFiles = fs.readdirSync(supabaseMigrationsPath)
      .filter(file => file.endsWith('.sql'))
      .sort();

    this.logger.log(`📁 Found ${migrationFiles.length} Supabase migration files`);

    for (const file of migrationFiles) {
      await this.convertMigrationFile(file, supabaseMigrationsPath, typeormMigrationsPath);
    }

    this.logger.log('✅ Migration conversion completed!');
  }

  /**
   * Convert individual migration file
   */
  private async convertMigrationFile(
    filename: string,
    sourcePath: string,
    targetPath: string,
  ): Promise<void> {
    const sourceFile = path.join(sourcePath, filename);
    const sqlContent = fs.readFileSync(sourceFile, 'utf8');

    // Extract timestamp and name from filename
    const match = filename.match(/^(\d{14})-(.+)\.sql$/);
    if (!match) {
      this.logger.warn(`⚠️ Skipping file with invalid format: ${filename}`);
      return;
    }

    const [, timestamp, name] = match;
    const className = this.toCamelCase(name.replace(/-/g, '_'));
    const targetFile = path.join(targetPath, `${timestamp}-${className}.ts`);

    // Convert SQL to TypeORM migration format
    const migrationContent = this.generateTypeOrmMigration(className, timestamp, sqlContent);

    fs.writeFileSync(targetFile, migrationContent);
    this.logger.log(`✅ Converted: ${filename} -> ${path.basename(targetFile)}`);
  }

  /**
   * Generate TypeORM migration class
   */
  private generateTypeOrmMigration(className: string, timestamp: string, sqlContent: string): string {
    // Clean and prepare SQL content
    const cleanedSql = this.cleanSqlContent(sqlContent);
    const upQueries = this.extractUpQueries(cleanedSql);
    const downQueries = this.extractDownQueries(cleanedSql);

    return `import { MigrationInterface, QueryRunner } from 'typeorm';

export class ${className}${timestamp} implements MigrationInterface {
  name = '${className}${timestamp}';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Converted from Supabase migration: ${className}
    ${upQueries.map(query => `await queryRunner.query(\`${query}\`);`).join('\n    ')}
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rollback queries (auto-generated)
    ${downQueries.map(query => `await queryRunner.query(\`${query}\`);`).join('\n    ')}
  }
}
`;
  }

  /**
   * Clean SQL content for TypeORM
   */
  private cleanSqlContent(sql: string): string {
    return sql
      // Remove comments
      .replace(/--.*$/gm, '')
      // Remove empty lines
      .replace(/^\s*$/gm, '')
      // Remove Supabase-specific commands
      .replace(/ALTER PUBLICATION supabase_realtime ADD TABLE.*;/g, '')
      // Replace auth.users with users (if needed)
      .replace(/auth\.users/g, 'users')
      // Clean up whitespace
      .trim();
  }

  /**
   * Extract UP queries from SQL
   */
  private extractUpQueries(sql: string): string[] {
    const queries = sql
      .split(';')
      .map(query => query.trim())
      .filter(query => query.length > 0)
      .map(query => query.replace(/`/g, '\\`')); // Escape backticks

    return queries;
  }

  /**
   * Extract DOWN queries (generate basic rollback)
   */
  private extractDownQueries(sql: string): string[] {
    const downQueries: string[] = [];
    const lines = sql.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      
      // Generate DROP statements for CREATE TABLE
      if (trimmed.startsWith('CREATE TABLE')) {
        const match = trimmed.match(/CREATE TABLE\s+(?:IF NOT EXISTS\s+)?(\w+\.\w+|\w+)/i);
        if (match) {
          downQueries.push(`DROP TABLE IF EXISTS ${match[1]} CASCADE`);
        }
      }
      
      // Generate DROP statements for CREATE INDEX
      if (trimmed.startsWith('CREATE INDEX') || trimmed.startsWith('CREATE UNIQUE INDEX')) {
        const match = trimmed.match(/CREATE\s+(?:UNIQUE\s+)?INDEX\s+(?:CONCURRENTLY\s+)?(?:IF NOT EXISTS\s+)?(\w+)/i);
        if (match) {
          downQueries.push(`DROP INDEX IF EXISTS ${match[1]}`);
        }
      }
      
      // Generate DROP statements for CREATE FUNCTION
      if (trimmed.startsWith('CREATE OR REPLACE FUNCTION') || trimmed.startsWith('CREATE FUNCTION')) {
        const match = trimmed.match(/CREATE\s+(?:OR REPLACE\s+)?FUNCTION\s+(\w+)/i);
        if (match) {
          downQueries.push(`DROP FUNCTION IF EXISTS ${match[1]} CASCADE`);
        }
      }
    }

    return downQueries.reverse(); // Reverse order for proper rollback
  }

  /**
   * Convert string to CamelCase
   */
  private toCamelCase(str: string): string {
    return str
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }

  /**
   * Run all converted migrations
   */
  async runConvertedMigrations(): Promise<void> {
    this.logger.log('🚀 Running converted TypeORM migrations...');

    try {
      await this.dataSource.runMigrations();
      this.logger.log('✅ All migrations executed successfully!');
    } catch (error) {
      this.logger.error('❌ Migration execution failed:', error);
      throw error;
    }
  }

  /**
   * Create comprehensive database schema from Supabase
   */
  async createComprehensiveSchema(): Promise<void> {
    this.logger.log('🏗️ Creating comprehensive database schema...');

    const schemaQueries = [
      // Enable UUID extension
      `CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,
      
      // Enable PostGIS for geolocation (if needed)
      `CREATE EXTENSION IF NOT EXISTS postgis;`,
      
      // Create updated_at trigger function
      `
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';
      `,
    ];

    for (const query of schemaQueries) {
      try {
        await this.dataSource.query(query);
        this.logger.log('✅ Schema setup query executed');
      } catch (error) {
        this.logger.warn(`⚠️ Schema query failed (may already exist): ${error.message}`);
      }
    }

    this.logger.log('✅ Comprehensive schema creation completed!');
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<any> {
    const supabaseMigrationsPath = path.join(process.cwd(), 'migrations', 'migrations');
    const typeormMigrationsPath = path.join(process.cwd(), 'src', 'database', 'migrations');

    const supabaseFiles = fs.existsSync(supabaseMigrationsPath) 
      ? fs.readdirSync(supabaseMigrationsPath).filter(f => f.endsWith('.sql'))
      : [];

    const typeormFiles = fs.existsSync(typeormMigrationsPath)
      ? fs.readdirSync(typeormMigrationsPath).filter(f => f.endsWith('.ts'))
      : [];

    const executedMigrations = await this.dataSource.query(
      `SELECT * FROM migrations ORDER BY timestamp DESC`
    ).catch(() => []);

    return {
      supabase: {
        available: supabaseFiles.length,
        files: supabaseFiles,
      },
      typeorm: {
        converted: typeormFiles.length,
        files: typeormFiles,
      },
      executed: {
        count: executedMigrations.length,
        latest: executedMigrations[0]?.name || 'None',
      },
      status: supabaseFiles.length > 0 ? 'ready-for-conversion' : 'no-supabase-migrations',
    };
  }

  /**
   * Generate seed data from Supabase schema
   */
  async generateSeedData(): Promise<void> {
    this.logger.log('🌱 Generating seed data based on Supabase schema...');

    // This would analyze the schema and generate appropriate seed data
    // For now, we'll use the existing property seeder
    this.logger.log('✅ Seed data generation completed! Use existing seeders.');
  }
}
