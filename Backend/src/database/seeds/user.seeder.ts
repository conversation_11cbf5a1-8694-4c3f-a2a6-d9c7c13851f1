import { DataSource } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User, UserRole, UserStatus } from '../../modules/users/entities/user.entity';

export class UserSeeder {
  public static async run(dataSource: DataSource): Promise<void> {
    const userRepository = dataSource.getRepository(User);

    // Helper to create or skip user
    async function createUserIfNotExists(userData: Partial<User>) {
      const existing = await userRepository.findOne({ where: { email: userData.email } });
      if (existing) {
        console.log(`User ${userData.email} already exists, skipping.`);
        return;
      }
      const user = userRepository.create(userData);
      await userRepository.save(user);
      console.log(`User ${userData.email} created.`);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('Admin123!@#', 12);

    // Create admin user
    await createUserIfNotExists({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator',
      phone: '+2348000000001',
      role: UserRole.ADMIN,
      status: UserStatus.ACTIVE,
      isActive: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      city: 'Port Harcourt',
      state: 'Rivers',
      country: 'Nigeria',
    });

    // Create sample agent user
    await createUserIfNotExists({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Sample',
      lastName: 'Agent',
      phone: '+2348000000002',
      role: UserRole.AGENT,
      status: UserStatus.ACTIVE,
      isActive: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      city: 'Port Harcourt',
      state: 'Rivers',
      country: 'Nigeria',
      occupation: 'Real Estate Agent',
    });

    // Create sample landlord user
    await createUserIfNotExists({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Sample',
      lastName: 'Landlord',
      phone: '+2348000000003',
      role: UserRole.LANDLORD,
      status: UserStatus.ACTIVE,
      isActive: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      city: 'Port Harcourt',
      state: 'Rivers',
      country: 'Nigeria',
      occupation: 'Property Owner',
    });

    // Create sample tenant user
    await createUserIfNotExists({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Sample',
      lastName: 'Tenant',
      phone: '+2348000000004',
      role: UserRole.TENANT,
      status: UserStatus.ACTIVE,
      isActive: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      city: 'Port Harcourt',
      state: 'Rivers',
      country: 'Nigeria',
      occupation: 'Software Engineer',
      monthlyIncome: 500000,
    });

    console.log('✅ User seeding completed successfully');
    console.log('📧 Admin credentials: <EMAIL> / Admin123!@#');
    console.log('📧 Agent credentials: <EMAIL> / Admin123!@#');
    console.log('📧 Landlord credentials: <EMAIL> / Admin123!@#');
    console.log('📧 Tenant credentials: <EMAIL> / Admin123!@#');
  }
}
