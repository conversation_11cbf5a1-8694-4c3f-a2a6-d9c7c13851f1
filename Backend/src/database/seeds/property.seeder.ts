import { DataSource, Repository } from 'typeorm';
import { Property, PropertyType, PropertyStatus, FurnishingStatus } from '../../api/v1/properties/entities/property.entity';
import { User, UserRole } from '../../modules/users/entities/user.entity';

export class PropertySeeder {
  private static readonly PROPERTY_COUNT = 12000; // Generate 12k properties for load testing

  private static readonly PROPERTY_TYPES = [
    PropertyType.APARTMENT,
    PropertyType.HOUSE,
    PropertyType.DUPLEX,
    PropertyType.BUNGALOW,
    PropertyType.FLAT,
    PropertyType.ROOM,
    PropertyType.SELF_CONTAIN,
    PropertyType.SHOP,
    PropertyType.OFFICE,
    PropertyType.WAREHOUSE,
  ];

  private static readonly LOCATIONS = [
    'GRA Phase 1, Port Harcourt',
    'GRA Phase 2, Port Harcourt',
    'GRA Phase 3, Port Harcourt',
    'Old GRA, Port Harcourt',
    'New GRA, Port Harcourt',
    'Trans Amadi, Port Harcourt',
    'D-Line, Port Harcourt',
    'W<PERSON>ji, Port Harcourt',
    'Eliozu, Port Harcourt',
    'Rumuokwurushi, Port Harcourt',
    'Rumuola, Port Harcourt',
    'Rumuigbo, Port Harcourt',
    'Rumuokoro, Port Harcourt',
    'Rumuobiakani, Port Harcourt',
    'Rumueme, Port Harcourt',
    'Rumuokuta, Port Harcourt',
    'Rumualogu, Port Harcourt',
    'Rumuepirikom, Port Harcourt',
    'Rumuokwuta, Port Harcourt',
    'Rumuodara, Port Harcourt',
    'Ada George, Port Harcourt',
    'Aba Road, Port Harcourt',
    'East West Road, Port Harcourt',
    'Stadium Road, Port Harcourt',
    'Ikwerre Road, Port Harcourt',
    'Aggrey Road, Port Harcourt',
    'Forces Avenue, Port Harcourt',
    'Tombia Street, Port Harcourt',
    'Circular Road, Port Harcourt',
    'Azikiwe Road, Port Harcourt',
    'Creek Road, Port Harcourt',
    'Harbour Road, Port Harcourt',
    'Marine Base, Port Harcourt',
    'Mile 1, Port Harcourt',
    'Mile 2, Port Harcourt',
    'Mile 3, Port Harcourt',
    'Mile 4, Port Harcourt',
    'Choba, Port Harcourt',
    'Aluu, Port Harcourt',
    'Ozuoba, Port Harcourt',
  ];

  private static readonly AMENITIES = [
    'parking',
    'generator',
    'security',
    'water',
    'electricity',
    'internet',
    'air_conditioning',
    'swimming_pool',
    'gym',
    'elevator',
    'balcony',
    'garden',
    'garage',
    'cctv',
    'fence',
    'gate',
    'borehole',
    'solar_power',
    'backup_generator',
    'servant_quarters',
    'study_room',
    'dining_room',
    'family_lounge',
    'visitors_toilet',
    'store_room',
    'laundry_room',
    'kitchen_cabinet',
    'wardrobe',
    'tiles',
    'pop_ceiling',
    'chandelier',
    'intercom',
    'fire_alarm',
    'smoke_detector',
    'water_heater',
    'inverter',
    'prepaid_meter',
  ];

  private static readonly PROPERTY_TITLES = [
    'Beautiful {bedrooms}-bedroom {type} in {area}',
    'Luxury {bedrooms}-bedroom {type} for rent in {area}',
    'Modern {bedrooms}-bedroom {type} in serene {area}',
    'Spacious {bedrooms}-bedroom {type} in prime {area}',
    'Executive {bedrooms}-bedroom {type} in {area}',
    'Newly built {bedrooms}-bedroom {type} in {area}',
    'Furnished {bedrooms}-bedroom {type} in {area}',
    'Well-finished {bedrooms}-bedroom {type} in {area}',
    'Tastefully finished {bedrooms}-bedroom {type} in {area}',
    'Exquisite {bedrooms}-bedroom {type} in {area}',
  ];

  public static async run(dataSource: DataSource): Promise<void> {
    console.log('🏠 Starting property seeding...');

    const propertyRepository = dataSource.getRepository(Property);
    const userRepository = dataSource.getRepository(User);

    // Check if properties already exist
    const existingCount = await propertyRepository.count();
    if (existingCount > 1000) {
      console.log(`Properties already seeded (${existingCount} found), skipping...`);
      return;
    }

    // Get landlords and agents
    const landlords = await userRepository.find({
      where: { role: UserRole.LANDLORD },
    });

    const agents = await userRepository.find({
      where: { role: UserRole.AGENT },
    });

    if (landlords.length === 0) {
      console.log('No landlords found, creating sample landlords...');
      await this.createSampleLandlords(userRepository);
      const newLandlords = await userRepository.find({
        where: { role: UserRole.LANDLORD },
      });
      landlords.push(...newLandlords);
    }

    if (agents.length === 0) {
      console.log('No agents found, creating sample agents...');
      await this.createSampleAgents(userRepository);
      const newAgents = await userRepository.find({
        where: { role: UserRole.AGENT },
      });
      agents.push(...newAgents);
    }

    console.log(`Found ${landlords.length} landlords and ${agents.length} agents`);

    // Generate properties in batches
    const batchSize = 500;
    const totalBatches = Math.ceil(this.PROPERTY_COUNT / batchSize);

    for (let batch = 0; batch < totalBatches; batch++) {
      const properties = [];
      const startIndex = batch * batchSize;
      const endIndex = Math.min(startIndex + batchSize, this.PROPERTY_COUNT);

      console.log(`Generating batch ${batch + 1}/${totalBatches} (${startIndex + 1}-${endIndex})...`);

      for (let i = startIndex; i < endIndex; i++) {
        const property = this.generateProperty(landlords, agents, i);
        properties.push(property);
      }

      // Save batch
      await propertyRepository.save(properties);
      console.log(`✅ Saved batch ${batch + 1}/${totalBatches}`);
    }

    console.log(`🎉 Property seeding completed! Generated ${this.PROPERTY_COUNT} properties.`);
  }

  private static generateProperty(landlords: User[], agents: User[], index: number): Partial<Property> {
    const propertyType = this.getRandomElement(this.PROPERTY_TYPES);
    const location = this.getRandomElement(this.LOCATIONS);
    const area = location.split(',')[0];
    
    // Generate realistic bedroom/bathroom counts based on property type
    const { bedrooms, bathrooms, toilets } = this.generateRoomCounts(propertyType);
    
    // Generate realistic price based on property type and location
    const pricePerYear = this.generatePrice(propertyType, bedrooms, location);
    const pricePerMonth = pricePerYear / 12;

    // Generate title
    const titleTemplate = this.getRandomElement(this.PROPERTY_TITLES);
    const title = titleTemplate
      .replace('{bedrooms}', bedrooms.toString())
      .replace('{type}', propertyType.replace('_', ' '))
      .replace('{area}', area);

    // Generate description
    const description = this.generateDescription(propertyType, bedrooms, bathrooms, area);

    // Select random landlord and optionally an agent
    const landlord = this.getRandomElement(landlords);
    const agent = Math.random() < 0.3 ? this.getRandomElement(agents) : null; // 30% chance of having an agent

    // Generate amenities
    const amenities = this.generateAmenities(propertyType);

    // Generate coordinates for Port Harcourt area
    const { latitude, longitude } = this.generateCoordinates();

    // Generate other properties
    const furnishingStatus = Math.random() < 0.4 ? this.getRandomElement(Object.values(FurnishingStatus)) : null;
    const sizeInSqm = this.generateSize(propertyType, bedrooms);
    const securityDeposit = Math.random() < 0.8 ? pricePerYear * 0.1 : null; // 80% have security deposit
    const agentCommission = agent ? Math.random() * 15 + 5 : null; // 5-20% commission

    // Generate status (90% available, 10% rented)
    const status = Math.random() < 0.9 ? PropertyStatus.AVAILABLE : PropertyStatus.RENTED;

    // Generate verification and featured status
    const isVerified = Math.random() < 0.7; // 70% verified
    const isFeatured = Math.random() < 0.1; // 10% featured

    // Generate view and inquiry counts
    const viewsCount = Math.floor(Math.random() * 500);
    const inquiriesCount = Math.floor(viewsCount * (Math.random() * 0.2)); // 0-20% conversion rate

    // Generate creation date (last 2 years)
    const createdAt = new Date();
    createdAt.setDate(createdAt.getDate() - Math.floor(Math.random() * 730));

    return {
      title,
      description,
      propertyType,
      status,
      pricePerYear,
      pricePerMonth,
      securityDeposit,
      agentCommission,
      location,
      city: 'Port Harcourt',
      state: 'Rivers',
      country: 'Nigeria',
      bedrooms,
      bathrooms,
      toilets,
      sizeInSqm,
      furnishingStatus,
      amenities,
      images: this.generateImageUrls(index),
      latitude,
      longitude,
      isFeatured,
      isVerified,
      viewsCount,
      inquiriesCount,
      landlordId: landlord.id,
      agentId: agent?.id,
      createdAt,
      updatedAt: createdAt,
    };
  }

  private static generateRoomCounts(propertyType: PropertyType): { bedrooms: number; bathrooms: number; toilets: number } {
    switch (propertyType) {
      case PropertyType.ROOM:
        return { bedrooms: 1, bathrooms: 1, toilets: 1 };
      case PropertyType.SELF_CONTAIN:
        return { bedrooms: 1, bathrooms: 1, toilets: 1 };
      case PropertyType.FLAT:
        const flatBedrooms = Math.random() < 0.7 ? 2 : 3;
        return { bedrooms: flatBedrooms, bathrooms: flatBedrooms, toilets: flatBedrooms + 1 };
      case PropertyType.APARTMENT:
        const aptBedrooms = Math.random() < 0.4 ? 2 : Math.random() < 0.8 ? 3 : 4;
        return { bedrooms: aptBedrooms, bathrooms: aptBedrooms, toilets: aptBedrooms + 1 };
      case PropertyType.BUNGALOW:
        const bungalowBedrooms = Math.random() < 0.3 ? 3 : Math.random() < 0.7 ? 4 : 5;
        return { bedrooms: bungalowBedrooms, bathrooms: bungalowBedrooms, toilets: bungalowBedrooms + 1 };
      case PropertyType.HOUSE:
        const houseBedrooms = Math.random() < 0.2 ? 3 : Math.random() < 0.6 ? 4 : Math.random() < 0.9 ? 5 : 6;
        return { bedrooms: houseBedrooms, bathrooms: houseBedrooms, toilets: houseBedrooms + 2 };
      case PropertyType.DUPLEX:
        const duplexBedrooms = Math.random() < 0.3 ? 4 : Math.random() < 0.7 ? 5 : 6;
        return { bedrooms: duplexBedrooms, bathrooms: duplexBedrooms, toilets: duplexBedrooms + 2 };
      case PropertyType.SHOP:
        return { bedrooms: 0, bathrooms: 1, toilets: 1 };
      case PropertyType.OFFICE:
        return { bedrooms: 0, bathrooms: Math.floor(Math.random() * 3) + 1, toilets: Math.floor(Math.random() * 3) + 1 };
      case PropertyType.WAREHOUSE:
        return { bedrooms: 0, bathrooms: Math.floor(Math.random() * 2) + 1, toilets: Math.floor(Math.random() * 2) + 1 };
      default:
        return { bedrooms: 3, bathrooms: 3, toilets: 4 };
    }
  }

  private static generatePrice(propertyType: PropertyType, bedrooms: number, location: string): number {
    let basePrice = 0;

    // Base prices by property type (annual rent in Naira)
    switch (propertyType) {
      case PropertyType.ROOM:
        basePrice = 200000;
        break;
      case PropertyType.SELF_CONTAIN:
        basePrice = 300000;
        break;
      case PropertyType.FLAT:
        basePrice = 600000;
        break;
      case PropertyType.APARTMENT:
        basePrice = 800000;
        break;
      case PropertyType.BUNGALOW:
        basePrice = 1500000;
        break;
      case PropertyType.HOUSE:
        basePrice = 1200000;
        break;
      case PropertyType.DUPLEX:
        basePrice = 2000000;
        break;
      case PropertyType.SHOP:
        basePrice = 500000;
        break;
      case PropertyType.OFFICE:
        basePrice = 800000;
        break;
      case PropertyType.WAREHOUSE:
        basePrice = 1000000;
        break;
    }

    // Adjust by number of bedrooms
    if (bedrooms > 0) {
      basePrice += (bedrooms - 1) * 200000;
    }

    // Location multiplier
    let locationMultiplier = 1;
    if (location.includes('GRA') || location.includes('Trans Amadi')) {
      locationMultiplier = 1.5;
    } else if (location.includes('D-Line') || location.includes('Woji')) {
      locationMultiplier = 1.3;
    } else if (location.includes('Eliozu') || location.includes('Ada George')) {
      locationMultiplier = 1.2;
    }

    // Add some randomness (±20%)
    const randomMultiplier = 0.8 + Math.random() * 0.4;

    return Math.round(basePrice * locationMultiplier * randomMultiplier);
  }

  private static generateDescription(propertyType: PropertyType, bedrooms: number, bathrooms: number, area: string): string {
    const descriptions = [
      `Spacious ${bedrooms}-bedroom ${propertyType.replace('_', ' ')} located in the heart of ${area}. This property features ${bathrooms} bathrooms and comes with modern amenities. Perfect for families looking for comfort and convenience.`,
      `Beautiful ${bedrooms}-bedroom ${propertyType.replace('_', ' ')} in a serene environment in ${area}. Well-finished with quality fittings and fixtures. Ideal for professionals and families.`,
      `Executive ${bedrooms}-bedroom ${propertyType.replace('_', ' ')} in prime ${area} location. Features include modern kitchen, spacious living areas, and ${bathrooms} bathrooms. Close to major amenities.`,
      `Luxury ${bedrooms}-bedroom ${propertyType.replace('_', ' ')} in ${area}. Tastefully finished with high-quality materials. Offers privacy, security, and easy access to major roads.`,
      `Modern ${bedrooms}-bedroom ${propertyType.replace('_', ' ')} in ${area}. Newly built with contemporary design and ${bathrooms} bathrooms. Perfect for those seeking comfort and style.`,
    ];

    return this.getRandomElement(descriptions);
  }

  private static generateAmenities(propertyType: PropertyType): string[] {
    const commonAmenities = ['parking', 'security', 'water', 'electricity'];
    const residentialAmenities = ['generator', 'fence', 'gate', 'tiles', 'pop_ceiling'];
    const luxuryAmenities = ['air_conditioning', 'swimming_pool', 'gym', 'elevator', 'cctv'];
    const commercialAmenities = ['backup_generator', 'fire_alarm', 'elevator', 'cctv'];

    let availableAmenities = [...commonAmenities];

    if ([PropertyType.SHOP, PropertyType.OFFICE, PropertyType.WAREHOUSE].includes(propertyType)) {
      availableAmenities.push(...commercialAmenities);
    } else {
      availableAmenities.push(...residentialAmenities);
      
      // Add luxury amenities for high-end properties
      if ([PropertyType.DUPLEX, PropertyType.HOUSE].includes(propertyType) && Math.random() < 0.3) {
        availableAmenities.push(...luxuryAmenities);
      }
    }

    // Randomly select 3-8 amenities
    const amenityCount = Math.floor(Math.random() * 6) + 3;
    const selectedAmenities = [];

    for (let i = 0; i < amenityCount && availableAmenities.length > 0; i++) {
      const randomIndex = Math.floor(Math.random() * availableAmenities.length);
      selectedAmenities.push(availableAmenities.splice(randomIndex, 1)[0]);
    }

    return selectedAmenities;
  }

  private static generateCoordinates(): { latitude: number; longitude: number } {
    // Port Harcourt coordinates: approximately 4.8156° N, 7.0498° E
    const baseLat = 4.8156;
    const baseLng = 7.0498;
    
    // Add random offset within ~20km radius
    const latOffset = (Math.random() - 0.5) * 0.4; // ±0.2 degrees
    const lngOffset = (Math.random() - 0.5) * 0.4; // ±0.2 degrees

    return {
      latitude: Math.round((baseLat + latOffset) * 100000) / 100000,
      longitude: Math.round((baseLng + lngOffset) * 100000) / 100000,
    };
  }

  private static generateSize(propertyType: PropertyType, bedrooms: number): number {
    let baseSize = 0;

    switch (propertyType) {
      case PropertyType.ROOM:
        baseSize = 20;
        break;
      case PropertyType.SELF_CONTAIN:
        baseSize = 35;
        break;
      case PropertyType.FLAT:
        baseSize = 80;
        break;
      case PropertyType.APARTMENT:
        baseSize = 100;
        break;
      case PropertyType.BUNGALOW:
        baseSize = 150;
        break;
      case PropertyType.HOUSE:
        baseSize = 180;
        break;
      case PropertyType.DUPLEX:
        baseSize = 250;
        break;
      case PropertyType.SHOP:
        baseSize = 50;
        break;
      case PropertyType.OFFICE:
        baseSize = 80;
        break;
      case PropertyType.WAREHOUSE:
        baseSize = 200;
        break;
    }

    // Adjust by bedrooms
    if (bedrooms > 0) {
      baseSize += (bedrooms - 1) * 25;
    }

    // Add randomness (±30%)
    const randomMultiplier = 0.7 + Math.random() * 0.6;

    return Math.round(baseSize * randomMultiplier);
  }

  private static generateImageUrls(index: number): string[] {
    const imageCount = Math.floor(Math.random() * 8) + 3; // 3-10 images
    const images = [];

    for (let i = 0; i < imageCount; i++) {
      images.push(`https://picsum.photos/800/600?random=${index * 10 + i}`);
    }

    return images;
  }

  private static getRandomElement<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  private static async createSampleLandlords(userRepository: Repository<User>): Promise<void> {
    const landlords = [];
    for (let i = 1; i <= 50; i++) {
      landlords.push({
        email: `landlord${i}@phcityrent.com`,
        password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL/.LVtpO', // password123
        firstName: `Landlord${i}`,
        lastName: `Owner${i}`,
        phone: `+23480${String(i).padStart(8, '0')}`,
        role: UserRole.LANDLORD,
        isActive: true,
        isEmailVerified: true,
        city: 'Port Harcourt',
        state: 'Rivers',
        country: 'Nigeria',
      });
    }
    await userRepository.save(landlords);
  }

  private static async createSampleAgents(userRepository: Repository<User>): Promise<void> {
    const agents = [];
    for (let i = 1; i <= 20; i++) {
      agents.push({
        email: `agent${i}@phcityrent.com`,
        password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL/.LVtpO', // password123
        firstName: `Agent${i}`,
        lastName: `Realtor${i}`,
        phone: `+23481${String(i).padStart(8, '0')}`,
        role: UserRole.AGENT,
        isActive: true,
        isEmailVerified: true,
        city: 'Port Harcourt',
        state: 'Rivers',
        country: 'Nigeria',
        occupation: 'Real Estate Agent',
      });
    }
    await userRepository.save(agents);
  }
}
