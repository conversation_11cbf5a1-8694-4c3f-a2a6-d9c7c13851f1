-- Database Optimization Indexes for PHCityRent
-- High-volume real estate data optimization

-- =====================================================
-- USERS TABLE OPTIMIZATION
-- =====================================================

-- Composite index for user authentication queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active 
ON users (email, "isActive") 
WHERE "isActive" = true;

-- Index for role-based queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role_active 
ON users (role, "isActive") 
WHERE "isActive" = true;

-- Index for user search functionality
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_search 
ON users USING gin(to_tsvector('english', "firstName" || ' ' || "lastName" || ' ' || email));

-- Partial index for verified users
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_verified 
ON users ("isEmailVerified", "isPhoneVerified") 
WHERE "isEmailVerified" = true OR "isPhoneVerified" = true;

-- =====================================================
-- PROPERTIES TABLE OPTIMIZATION
-- =====================================================

-- Composite index for property search (most common query pattern)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_search_main 
ON properties (status, "propertyType", "pricePerYear", city, "isVerified") 
WHERE status = 'available';

-- Composite index for location-based searches
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_location_search 
ON properties (city, state, location, status) 
WHERE status = 'available';

-- Composite index for bedroom/bathroom filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_rooms_price 
ON properties (bedrooms, bathrooms, "pricePerYear", status) 
WHERE status = 'available';

-- Index for price range queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_price_range 
ON properties ("pricePerYear", "pricePerMonth", status) 
WHERE status = 'available';

-- Spatial index for location-based queries (if using PostGIS)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_coordinates 
ON properties (latitude, longitude) 
WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Index for featured properties
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_featured 
ON properties ("isFeatured", status, "createdAt") 
WHERE "isFeatured" = true AND status = 'available';

-- Index for verified properties
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_verified 
ON properties ("isVerified", status, "createdAt") 
WHERE "isVerified" = true AND status = 'available';

-- Index for landlord properties
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_landlord 
ON properties ("landlord_id", status, "createdAt");

-- Index for agent properties
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_agent 
ON properties ("agent_id", status, "createdAt") 
WHERE "agent_id" IS NOT NULL;

-- Full-text search index for property content
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_fulltext 
ON properties USING gin(to_tsvector('english', title || ' ' || description || ' ' || location));

-- Index for amenities search (GIN index for array operations)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_amenities 
ON properties USING gin(amenities);

-- Index for property analytics
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_analytics 
ON properties ("viewsCount", "inquiriesCount", "createdAt");

-- =====================================================
-- PAYMENTS TABLE OPTIMIZATION
-- =====================================================

-- Composite index for payment queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_user_status 
ON payments ("user_id", status, "createdAt");

-- Index for payment processing
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_gateway_status 
ON payments (gateway, status, "createdAt");

-- Index for payment type analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_type_status 
ON payments ("paymentType", status, amount);

-- Index for property payments
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_property 
ON payments ("property_id", status, "createdAt") 
WHERE "property_id" IS NOT NULL;

-- Index for recipient payments (commissions)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_recipient 
ON payments ("recipient_id", status, "createdAt") 
WHERE "recipient_id" IS NOT NULL;

-- Index for overdue payments
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_overdue 
ON payments ("dueDate", status) 
WHERE status = 'pending' AND "dueDate" IS NOT NULL;

-- Index for payment reference lookup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_reference_unique 
ON payments (reference);

-- =====================================================
-- NOTIFICATIONS TABLE OPTIMIZATION
-- =====================================================

-- Composite index for user notifications
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_status 
ON notifications ("user_id", status, "createdAt");

-- Index for notification processing
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_channel_status 
ON notifications (channel, status, "createdAt");

-- Index for notification type analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_type_status 
ON notifications (type, status, "createdAt");

-- Index for failed notifications retry
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_retry 
ON notifications (status, "retryCount", "createdAt") 
WHERE status = 'failed' AND "retryCount" < 3;

-- Index for unread notifications
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_unread 
ON notifications ("user_id", status) 
WHERE status IN ('delivered', 'sent');

-- =====================================================
-- REFRESH TOKENS TABLE OPTIMIZATION
-- =====================================================

-- Index for token cleanup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_refresh_tokens_expires 
ON refresh_tokens ("expiresAt") 
WHERE "expiresAt" < NOW();

-- Index for user token lookup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_refresh_tokens_user 
ON refresh_tokens ("user_id", "expiresAt");

-- =====================================================
-- LOGIN ATTEMPTS TABLE OPTIMIZATION
-- =====================================================

-- Index for rate limiting
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_login_attempts_ip_time 
ON login_attempts ("ipAddress", "createdAt") 
WHERE "createdAt" > NOW() - INTERVAL '1 hour';

-- Index for user login tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_login_attempts_email_time 
ON login_attempts (email, "createdAt") 
WHERE "createdAt" > NOW() - INTERVAL '24 hours';

-- Index for failed login attempts
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_login_attempts_failed 
ON login_attempts (email, "ipAddress", success, "createdAt") 
WHERE success = false;

-- =====================================================
-- PERFORMANCE MONITORING QUERIES
-- =====================================================

-- View for monitoring index usage
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'Unused'
        WHEN idx_scan < 100 THEN 'Low Usage'
        WHEN idx_scan < 1000 THEN 'Medium Usage'
        ELSE 'High Usage'
    END as usage_level
FROM pg_stat_user_indexes 
ORDER BY idx_scan DESC;

-- View for monitoring table statistics
CREATE OR REPLACE VIEW table_stats AS
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    CASE 
        WHEN n_live_tup = 0 THEN 0
        ELSE ROUND((n_dead_tup::float / n_live_tup::float) * 100, 2)
    END as dead_tuple_percentage
FROM pg_stat_user_tables 
ORDER BY n_live_tup DESC;

-- =====================================================
-- MAINTENANCE PROCEDURES
-- =====================================================

-- Function to analyze table statistics
CREATE OR REPLACE FUNCTION analyze_tables()
RETURNS void AS $$
BEGIN
    ANALYZE users;
    ANALYZE properties;
    ANALYZE payments;
    ANALYZE notifications;
    ANALYZE refresh_tokens;
    ANALYZE login_attempts;
    
    RAISE NOTICE 'Table analysis completed';
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old data
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Clean up expired refresh tokens
    DELETE FROM refresh_tokens WHERE "expiresAt" < NOW();
    
    -- Clean up old login attempts (older than 30 days)
    DELETE FROM login_attempts WHERE "createdAt" < NOW() - INTERVAL '30 days';
    
    -- Clean up old failed notifications (older than 7 days)
    DELETE FROM notifications 
    WHERE status = 'failed' 
    AND "retryCount" >= 3 
    AND "createdAt" < NOW() - INTERVAL '7 days';
    
    RAISE NOTICE 'Old data cleanup completed';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- QUERY OPTIMIZATION HINTS
-- =====================================================

/*
QUERY OPTIMIZATION GUIDELINES:

1. Property Search Queries:
   - Always include status = 'available' in WHERE clause
   - Use price range filters before other filters
   - Limit results with OFFSET/LIMIT for pagination

2. User Queries:
   - Filter by isActive = true for active users
   - Use role-based filtering early in WHERE clause

3. Payment Queries:
   - Include user_id in WHERE clause when possible
   - Filter by status for better performance

4. Notification Queries:
   - Always include user_id for user-specific queries
   - Use status filtering for processing queues

5. General Guidelines:
   - Use EXPLAIN ANALYZE to check query plans
   - Avoid SELECT * in production queries
   - Use appropriate LIMIT clauses
   - Consider using prepared statements for repeated queries
*/
