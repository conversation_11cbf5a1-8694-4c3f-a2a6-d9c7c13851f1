import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Import test controller and documentation controllers
import { TestController } from './test.controller';
import {
  <PERSON>AuthController,
  DocUsersController,
  DocPropertiesController,
  DocAgentsController,
  DocPaymentsController,
  DocAnalyticsController,
  DocAdminController,
  DocFilesController,
  DocNotificationsController,
  DocHealthController,
} from './documentation-controllers';

// No services needed - documentation controllers are self-contained

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
  ],
  controllers: [
    TestController,
    DocAuthController,
    DocUsersController,
    DocPropertiesController,
    DocAgentsController,
    DocPaymentsController,
    DocAnalyticsController,
    DocAdminController,
    DocFilesController,
    DocNotificationsController,
    DocHealthController,
  ],
  providers: [],
})
export class DocumentationModule {}
