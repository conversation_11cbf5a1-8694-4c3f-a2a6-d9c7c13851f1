# PHCityRent Backend API

Enterprise-grade NestJS backend for the PHCityRent platform with comprehensive authentication, authorization, and database management.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm 8+
- PostgreSQL 15+
- Redis 6+

### Installation

```bash
# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Edit .env with your configuration
nano .env

# Run database migrations
npm run migration:run

# Seed the database
npm run seed

# Start development server
npm run start:dev
```

The API will be available at `http://localhost:3001` with Swagger documentation at `http://localhost:3001/api/docs`.

## 📁 Project Structure

```
Backend/
├── src/
│   ├── api/              # API versioning structure
│   │   └── v1/          # API version 1
│   │       ├── properties/  # ✅ Property management (IMPLEMENTED)
│   │       ├── agents/      # ✅ Agent management (IMPLEMENTED)
│   │       ├── payments/    # 🚧 Payment processing (placeholder)
│   │       ├── analytics/   # 🚧 Analytics & reporting (placeholder)
│   │       ├── admin/       # 🚧 Admin functionality (placeholder)
│   │       ├── files/       # 🚧 File management (placeholder)
│   │       ├── notifications/ # 🚧 Notification system (placeholder)
│   │       └── v1.module.ts # API v1 module
│   ├── modules/          # Core system modules
│   │   ├── auth/        # ✅ Authentication & authorization (IMPLEMENTED)
│   │   ├── users/       # ✅ User management (IMPLEMENTED)
│   │   └── health/      # ✅ Health checks (IMPLEMENTED)
│   ├── common/          # Shared utilities
│   │   ├── decorators/  # Custom decorators
│   │   ├── filters/     # Exception filters
│   │   ├── interceptors/ # Request/response interceptors
│   │   ├── pipes/       # Validation pipes
│   │   ├── utils/       # Utility functions
│   │   └── logger/      # Logging service
│   ├── config/          # Configuration files
│   ├── database/        # Database migrations and seeds
│   └── main.ts          # Application entry point
├── database/            # Database optimization scripts
├── docs/                # Documentation
├── test/                # Test files
└── package.json         # Dependencies and scripts
```

## 🔧 Key Features Implemented

### ✅ **Authentication & Authorization** (COMPLETE)
- **JWT Authentication** with access and refresh tokens
- **Role-Based Access Control (RBAC)** with 4 roles: Admin, Agent, Landlord, Tenant
- **Password Security** with bcrypt hashing and complexity requirements
- **Account Security** with login attempt tracking and account lockout
- **Password Reset** with secure token-based reset flow
- **Email/Phone Verification** infrastructure

### ✅ **Property Management System** (COMPLETE)
- **Comprehensive Property CRUD** with role-based permissions
- **Advanced Search & Filtering** by type, price, location, bedrooms, amenities
- **Property Types** supporting all Nigerian real estate categories
- **Property Status Management** (available, rented, maintenance, inactive)
- **Featured & Verified Properties** with admin controls
- **Property Analytics** with view and inquiry tracking
- **Location-Based Search** with coordinate support
- **Image & Media Support** with multiple image URLs and virtual tours

### ✅ **Agent Management System** (COMPLETE)
- **Agent Directory** with public agent listings
- **Agent Performance Tracking** with comprehensive statistics
- **Property-Agent Relationships** with commission tracking
- **Top Agents Ranking** based on performance metrics
- **Agent Search & Discovery** functionality
- **Admin Controls** for agent activation/deactivation

### ✅ **Database Architecture**
- **PostgreSQL** with TypeORM and connection pooling
- **Database Migrations** with version control
- **Database Seeding** with sample data
- **Optimized Indexes** for performance
- **Foreign Key Constraints** for data integrity

### ✅ **API Documentation**
- **OpenAPI 3.0** specification with Swagger UI
- **Comprehensive Documentation** for all endpoints
- **Request/Response Examples** with validation schemas
- **Authentication Documentation** with bearer token support

### ✅ **Error Handling & Logging**
- **Global Exception Filter** with structured error responses
- **Request/Response Logging** with performance tracking
- **Winston Logger** with file rotation and structured logging
- **Error Tracking** with unique error IDs

### ✅ **Security Features**
- **Helmet** for security headers
- **Rate Limiting** with Redis-based throttling
- **Input Validation** with class-validator
- **CORS Configuration** with environment-based origins
- **SQL Injection Protection** with parameterized queries

### ✅ **Performance & Monitoring**
- **Redis Caching** for session and data caching
- **Connection Pooling** for database optimization
- **Health Checks** with Terminus for monitoring
- **Request Compression** with gzip
- **Performance Interceptors** for slow query detection

## 🛠️ Available Scripts

```bash
# Development
npm run start:dev          # Start development server with hot reload
npm run start:debug        # Start with debugging enabled

# Production
npm run build              # Build for production
npm run start:prod         # Start production server

# Database
npm run migration:generate # Generate new migration
npm run migration:run      # Run pending migrations
npm run migration:revert   # Revert last migration
npm run seed              # Seed database with sample data
npm run db:reset          # Reset database (revert, run, seed)

# Testing
npm run test              # Run unit tests
npm run test:watch        # Run tests in watch mode
npm run test:cov          # Run tests with coverage
npm run test:e2e          # Run end-to-end tests

# Code Quality
npm run lint              # Lint code
npm run format            # Format code with Prettier
```

## 🔐 Authentication Flow

### Registration
```bash
POST /api/v1/auth/register
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+*************",
  "role": "tenant"
}
```

### Login
```bash
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

### Protected Routes
```bash
GET /api/v1/users/profile
Authorization: Bearer <access_token>
```

## 👥 Default User Accounts

After running `npm run seed`, the following accounts are available:

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| Admin | <EMAIL> | Admin123!@# | System administrator |
| Agent | <EMAIL> | Admin123!@# | Real estate agent |
| Landlord | <EMAIL> | Admin123!@# | Property owner |
| Tenant | <EMAIL> | Admin123!@# | Property seeker |

## 🌐 API Endpoints

### Authentication (`/api/v1/auth`)
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh access token
- `POST /auth/logout` - User logout
- `PATCH /auth/change-password` - Change password
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password with token
- `GET /auth/me` - Get current user profile

### Users (`/api/v1/users`)
- `GET /users` - Get all users (Admin/Agent only)
- `GET /users/profile` - Get current user profile
- `GET /users/:id` - Get user by ID (Admin/Agent only)
- `PATCH /users/profile` - Update current user profile
- `PATCH /users/:id` - Update user by ID (Admin only)
- `PATCH /users/:id/activate` - Activate user (Admin only)
- `PATCH /users/:id/deactivate` - Deactivate user (Admin only)
- `DELETE /users/:id` - Delete user (Admin only)

### Properties (`/api/v1/properties`) ✅ **NEW**
- `POST /properties` - Create new property (Landlord/Agent/Admin)
- `GET /properties` - Search and filter properties (Public)
- `GET /properties/my-properties` - Get current user properties
- `GET /properties/stats` - Get property statistics (Admin/Agent)
- `GET /properties/:id` - Get property by ID (Public)
- `PATCH /properties/:id` - Update property (Owner/Agent/Admin)
- `PATCH /properties/:id/status` - Update property status
- `PATCH /properties/:id/toggle-featured` - Toggle featured status (Admin/Agent)
- `PATCH /properties/:id/toggle-verified` - Toggle verified status (Admin)
- `POST /properties/:id/inquire` - Record property inquiry (Public)
- `DELETE /properties/:id` - Delete property (Owner/Agent/Admin)

### Agents (`/api/v1/agents`) ✅ **NEW**
- `GET /agents` - Get all active agents (Public)
- `GET /agents/top` - Get top performing agents (Public)
- `GET /agents/search` - Search agents by name/email (Public)
- `GET /agents/:id` - Get agent by ID (Public)
- `GET /agents/:id/properties` - Get properties managed by agent (Public)
- `GET /agents/:id/stats` - Get agent performance statistics (Admin/Agent)
- `PATCH /agents/:id/activate` - Activate agent (Admin only)
- `PATCH /agents/:id/deactivate` - Deactivate agent (Admin only)

### Health Checks (`/api/v1/health`)
- `GET /health` - Comprehensive health check
- `GET /health/database` - Database health check
- `GET /health/memory` - Memory health check
- `GET /health/disk` - Disk health check

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Environment | development | No |
| `PORT` | Server port | 3001 | No |
| `DB_HOST` | Database host | localhost | Yes |
| `DB_PORT` | Database port | 5432 | No |
| `DB_USERNAME` | Database username | postgres | Yes |
| `DB_PASSWORD` | Database password | - | Yes |
| `DB_NAME` | Database name | phcityrent | Yes |
| `REDIS_HOST` | Redis host | localhost | No |
| `REDIS_PORT` | Redis port | 6379 | No |
| `JWT_SECRET` | JWT secret key | - | Yes |
| `JWT_REFRESH_SECRET` | JWT refresh secret | - | Yes |

See `.env.example` for complete configuration options.

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:cov

# Run E2E tests
npm run test:e2e

# Run tests in watch mode
npm run test:watch
```

## 📊 Monitoring & Health Checks

The API includes comprehensive health checks accessible at:

- `/health` - Overall system health
- `/health/database` - Database connectivity
- `/health/memory` - Memory usage
- `/health/disk` - Disk space

## 🚀 Deployment

### Docker Deployment
```bash
# Build Docker image
docker build -t phcityrent-api .

# Run with Docker Compose
docker-compose up -d
```

### Production Deployment
```bash
# Build for production
npm run build

# Start production server
npm run start:prod
```

## 🔄 Database Migrations

```bash
# Generate migration
npm run migration:generate -- --name=CreateNewTable

# Run migrations
npm run migration:run

# Revert migration
npm run migration:revert
```

## 📚 Additional Documentation

- **API Documentation**: Available at `/api/docs` when running
- **Database Schema**: See `src/database/migrations/`
- **Configuration**: See `src/config/`
- **Authentication**: See `src/modules/auth/`

## 🤝 Contributing

1. Follow TypeScript and NestJS best practices
2. Add tests for new features
3. Update documentation
4. Follow the existing code style
5. Use conventional commits

## 📝 License

This project is proprietary software for PHCityRent platform.

---

**Note**: This is an enterprise-grade backend with production-ready authentication, security, and monitoring features. The modular architecture allows for easy expansion of additional features like property management, payments, and analytics.
