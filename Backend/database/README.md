# PHCityRent Production Database Setup

This directory contains production-grade database optimization, monitoring, and deployment scripts for PHCityRent.

## 🚀 Quick Start

### 1. Production Deployment

```bash
# Set required environment variables
export DB_PASSWORD="your_secure_password_here"
export ADMIN_EMAIL="<EMAIL>"

# Run the deployment script
sudo ./scripts/deploy_production_db.sh
```

### 2. Setup Read Replica

```bash
# Set environment variables
export PRIMARY_HOST="your-primary-server"
export REPLICATION_PASSWORD="your_replication_password"

# Run as postgres user
sudo -u postgres ./scripts/setup_read_replica.sh
```

### 3. Monitor Database

```bash
# Basic monitoring
./scripts/db_monitor.sh

# Full performance report
./scripts/db_monitor.sh --full-report

# Backup database
./scripts/db_monitor.sh --backup
```

## 📁 File Structure

```
database/
├── README.md                           # This file
├── postgresql.production.conf          # Production PostgreSQL configuration
├── scripts/
│   ├── deploy_production_db.sh         # Complete production deployment
│   ├── db_monitor.sh                   # Database monitoring and maintenance
│   ├── setup_read_replica.sh           # Read replica setup
│   └── performance_tuning.sql          # Performance analysis queries
└── migrations/
    └── 20250710000001_production_database_optimization.sql
```

## 🔧 Features Implemented

### ✅ Advanced Indexing
- **Full-text search indexes** using GIN and trigram operators
- **Composite indexes** for common query patterns
- **Spatial indexes** for location-based queries
- **Array indexes** for amenities and tags
- **Partial indexes** for filtered queries

### ✅ Database Partitioning
- **Monthly partitioning** for payment_transactions table
- **Monthly partitioning** for user_behavior table
- **Automated partition creation** for future months
- **Partition pruning** for improved query performance

### ✅ Materialized Views
- **Property search view** with aggregated metrics
- **Agent performance view** with KPIs
- **Automated refresh** procedures
- **Concurrent refresh** for zero downtime

### ✅ Stored Procedures
- **Intelligent property search** with ranking algorithm
- **Batch property updates** with optimistic locking
- **Performance metrics collection**
- **Database maintenance automation**

### ✅ Backup & Recovery
- **Automated backup procedures** with compression
- **Point-in-time recovery** setup
- **WAL archiving** configuration
- **Backup verification** and cleanup

### ✅ Read Replica Support
- **Streaming replication** setup
- **Automated failover** preparation
- **Replication monitoring** and lag detection
- **Load balancing** configuration

### ✅ Performance Monitoring
- **Real-time metrics** collection
- **Slow query detection** and analysis
- **Connection pool monitoring**
- **Cache hit ratio tracking**
- **Automated alerting** via email and Slack

## 📊 Performance Optimizations

### Database Configuration
- **Memory settings** optimized for available RAM
- **WAL settings** for high availability
- **Query planner** tuned for SSD storage
- **Connection pooling** configuration
- **Autovacuum** optimization

### Query Optimization
- **pg_stat_statements** for query analysis
- **Index usage** monitoring
- **Query plan** analysis tools
- **Performance budgets** and thresholds

### System-Level Tuning
- **Kernel parameters** optimization
- **I/O scheduler** configuration
- **Memory management** tuning
- **Network optimization**

## 🔒 Security Features

### Authentication & Authorization
- **Role-based access control** (RBAC)
- **SSL/TLS encryption** for connections
- **Password policies** enforcement
- **Connection restrictions** by IP

### Data Protection
- **Row-level security** (RLS) policies
- **Audit logging** for sensitive operations
- **Data encryption** at rest
- **Backup encryption**

## 📈 Monitoring & Alerting

### Automated Monitoring
- **Database health checks** every 5 minutes
- **Performance metrics** collection hourly
- **Backup verification** daily
- **Security audit** weekly

### Alert Conditions
- **High connection usage** (>80%)
- **Low cache hit ratio** (<95%)
- **High disk usage** (>85%)
- **Slow queries** (>5 seconds)
- **Replication lag** (>60 seconds)

### Notification Channels
- **Email alerts** for critical issues
- **Slack integration** for team notifications
- **Log aggregation** for analysis
- **Dashboard metrics** for visualization

## 🛠️ Maintenance Procedures

### Daily Maintenance
```bash
# Automated via cron
0 2 * * * /usr/local/bin/db_monitor.sh --backup
*/5 * * * * /usr/local/bin/db_monitor.sh
```

### Weekly Maintenance
```bash
# Performance analysis
./scripts/db_monitor.sh --full-report

# Materialized view refresh
sudo -u postgres psql -d phcityrent -c "SELECT refresh_performance_views();"
```

### Monthly Maintenance
```bash
# Index maintenance
sudo -u postgres psql -d phcityrent -c "SELECT perform_database_maintenance();"

# Cleanup old logs
find /var/log/phcityrent -name "*.log" -mtime +30 -delete
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | Database host | localhost |
| `DB_PORT` | Database port | 5432 |
| `DB_NAME` | Database name | phcityrent |
| `DB_USER` | Database user | phcityrent_user |
| `DB_PASSWORD` | Database password | *required* |
| `BACKUP_DIR` | Backup directory | /var/backups/postgresql |
| `LOG_DIR` | Log directory | /var/log/phcityrent |
| `ALERT_EMAIL` | Email for alerts | <EMAIL> |
| `SLACK_WEBHOOK` | Slack webhook URL | *optional* |

### PostgreSQL Settings

Key production settings in `postgresql.production.conf`:

```ini
# Memory
shared_buffers = 512MB          # 25% of RAM
effective_cache_size = 1536MB   # 75% of RAM
work_mem = 8MB                  # Per connection

# WAL
wal_level = replica
max_wal_senders = 5
archive_mode = on

# Performance
random_page_cost = 1.1          # SSD optimized
effective_io_concurrency = 200
max_parallel_workers = 8
```

## 🚨 Troubleshooting

### Common Issues

1. **High CPU Usage**
   ```bash
   # Check slow queries
   sudo -u postgres psql -d phcityrent -f scripts/performance_tuning.sql
   ```

2. **High Memory Usage**
   ```bash
   # Check connection count
   sudo -u postgres psql -d phcityrent -c "SELECT count(*) FROM pg_stat_activity;"
   ```

3. **Replication Lag**
   ```bash
   # Check replication status
   ./scripts/setup_read_replica.sh verify
   ```

4. **Backup Failures**
   ```bash
   # Check backup logs
   tail -f /var/log/phcityrent/db_monitor.log
   ```

### Performance Analysis

```sql
-- Find slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;

-- Check index usage
SELECT schemaname, tablename, indexname, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE idx_tup_read = 0;

-- Analyze table bloat
SELECT schemaname, tablename, n_dead_tup, n_live_tup
FROM pg_stat_user_tables 
WHERE n_dead_tup > 1000;
```

## 📚 Additional Resources

- [PostgreSQL Performance Tuning](https://wiki.postgresql.org/wiki/Performance_Optimization)
- [Monitoring PostgreSQL](https://www.postgresql.org/docs/current/monitoring.html)
- [Backup and Recovery](https://www.postgresql.org/docs/current/backup.html)
- [High Availability](https://www.postgresql.org/docs/current/high-availability.html)

## 🤝 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the logs in `/var/log/phcityrent/`
3. Contact the development team
4. Create an issue in the project repository

---

**Note**: This setup is designed for production use with enterprise-grade performance, security, and reliability features. Always test in a staging environment before deploying to production.
