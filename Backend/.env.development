# Development Environment Configuration
NODE_ENV=development
PORT=3001
APP_NAME=PHCityRent API (Development)
APP_VERSION=1.0.0
APP_DESCRIPTION=Enterprise-grade API for Port Harcourt Real Estate Platform - Development
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:8081,http://localhost:5173
API_PREFIX=api/v1

# Database Configuration (Development)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=phcityrent_dev
DB_MAX_CONNECTIONS=10
DB_MIN_CONNECTIONS=2
DB_LOGGING=true
DB_SYNCHRONIZE=true

# Redis Configuration (Development)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_QUEUE_DB=1
REDIS_SESSION_DB=2

# Authentication Configuration (Development)
JWT_SECRET=dev-jwt-secret-key-for-development-only-change-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=dev-refresh-secret-key-for-development-only-change-in-production
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=10
PASSWORD_RESET_EXPIRY=3600000
EMAIL_VERIFICATION_EXPIRY=86400000
MAX_LOGIN_ATTEMPTS=10
LOCKOUT_DURATION=300000

# File Upload Configuration (Development)
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp,image/gif
UPLOAD_PATH=./uploads/dev
STATIC_FILES_PATH=/uploads

# Email Configuration (Development)
EMAIL_FROM=<EMAIL>
EMAIL_HOST=smtp.mailtrap.io
EMAIL_PORT=2525
EMAIL_USER=your_mailtrap_user
EMAIL_PASSWORD=your_mailtrap_password
EMAIL_SECURE=false

# Payment Gateway Configuration (Development)
# Paystack Test Keys
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key
PAYSTACK_WEBHOOK_SECRET=your_paystack_webhook_secret
PAYSTACK_BASE_URL=https://api.paystack.co

# Flutterwave Test Keys
FLUTTERWAVE_SECRET_KEY=FLWSECK_TEST-your_flutterwave_secret_key
FLUTTERWAVE_PUBLIC_KEY=FLWPUBK_TEST-your_flutterwave_public_key
FLUTTERWAVE_WEBHOOK_SECRET=your_flutterwave_webhook_secret
FLUTTERWAVE_BASE_URL=https://api.flutterwave.com/v3

# Rate Limiting Configuration (Development - More Lenient)
THROTTLE_TTL=60
THROTTLE_LIMIT=1000
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_MAX=1000

# Caching Configuration (Development)
CACHE_TTL=60
CACHE_MAX_ITEMS=100

# Pagination Configuration
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Monitoring Configuration (Development)
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_SWAGGER=true
ENABLE_CORS=true

# Logging Configuration (Development)
LOG_LEVEL=debug
LOG_FORMAT=dev
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_STACK_TRACE=true

# Development Tools
ENABLE_HOT_RELOAD=true
ENABLE_DEBUG_MODE=true
ENABLE_PROFILING=false

# External Services (Development)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# WhatsApp Business API (Development)
WHATSAPP_BUSINESS_PHONE_ID=your_phone_id
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_verify_token

# SMS Configuration (Development)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Security Configuration (Development)
HELMET_ENABLED=true
CSRF_ENABLED=false
SESSION_SECRET=dev-session-secret-change-in-production
COOKIE_SECURE=false
COOKIE_SAME_SITE=lax

# Database Seeding (Development)
ENABLE_SEEDING=true
SEED_ADMIN_EMAIL=<EMAIL>
SEED_ADMIN_PASSWORD=Admin123!@#
