# Staging Environment Configuration
NODE_ENV=staging
PORT=3001
APP_NAME=PHCityRent API (Staging)
APP_VERSION=1.0.0
APP_DESCRIPTION=Enterprise-grade API for Port Harcourt Real Estate Platform - Staging
CORS_ORIGINS=https://staging.phcityrent.com,https://staging-admin.phcityrent.com
API_PREFIX=api/v1

# Database Configuration (Staging)
DB_HOST=staging-db.phcityrent.com
DB_PORT=5432
DB_USERNAME=phcityrent_staging
DB_PASSWORD=${STAGING_DB_PASSWORD}
DB_NAME=phcityrent_staging
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5
DB_LOGGING=false
DB_SYNCHRONIZE=false
DB_SSL=true

# Redis Configuration (Staging)
REDIS_HOST=staging-redis.phcityrent.com
REDIS_PORT=6379
REDIS_PASSWORD=${STAGING_REDIS_PASSWORD}
REDIS_DB=0
REDIS_QUEUE_DB=1
REDIS_SESSION_DB=2
REDIS_TLS=true

# Authentication Configuration (Staging)
JWT_SECRET=${STAGING_JWT_SECRET}
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=${STAGING_JWT_REFRESH_SECRET}
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=12
PASSWORD_RESET_EXPIRY=3600000
EMAIL_VERIFICATION_EXPIRY=86400000
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000

# File Upload Configuration (Staging)
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp
UPLOAD_PATH=/app/uploads/staging
STATIC_FILES_PATH=/uploads

# Email Configuration (Staging)
EMAIL_FROM=<EMAIL>
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USER=apikey
EMAIL_PASSWORD=${STAGING_SENDGRID_API_KEY}
EMAIL_SECURE=true

# Payment Gateway Configuration (Staging)
# Paystack Test Keys (Staging)
PAYSTACK_SECRET_KEY=${STAGING_PAYSTACK_SECRET_KEY}
PAYSTACK_PUBLIC_KEY=${STAGING_PAYSTACK_PUBLIC_KEY}
PAYSTACK_WEBHOOK_SECRET=${STAGING_PAYSTACK_WEBHOOK_SECRET}
PAYSTACK_BASE_URL=https://api.paystack.co

# Flutterwave Test Keys (Staging)
FLUTTERWAVE_SECRET_KEY=${STAGING_FLUTTERWAVE_SECRET_KEY}
FLUTTERWAVE_PUBLIC_KEY=${STAGING_FLUTTERWAVE_PUBLIC_KEY}
FLUTTERWAVE_WEBHOOK_SECRET=${STAGING_FLUTTERWAVE_WEBHOOK_SECRET}
FLUTTERWAVE_BASE_URL=https://api.flutterwave.com/v3

# Rate Limiting Configuration (Staging)
THROTTLE_TTL=60
THROTTLE_LIMIT=200
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_MAX=500

# Caching Configuration (Staging)
CACHE_TTL=300
CACHE_MAX_ITEMS=1000

# Pagination Configuration
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Monitoring Configuration (Staging)
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_SWAGGER=true
ENABLE_CORS=true

# Logging Configuration (Staging)
LOG_LEVEL=info
LOG_FORMAT=combined
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_STACK_TRACE=false

# External Services (Staging)
GOOGLE_MAPS_API_KEY=${STAGING_GOOGLE_MAPS_API_KEY}
CLOUDINARY_CLOUD_NAME=${STAGING_CLOUDINARY_CLOUD_NAME}
CLOUDINARY_API_KEY=${STAGING_CLOUDINARY_API_KEY}
CLOUDINARY_API_SECRET=${STAGING_CLOUDINARY_API_SECRET}

# WhatsApp Business API (Staging)
WHATSAPP_BUSINESS_PHONE_ID=${STAGING_WHATSAPP_PHONE_ID}
WHATSAPP_ACCESS_TOKEN=${STAGING_WHATSAPP_ACCESS_TOKEN}
WHATSAPP_WEBHOOK_VERIFY_TOKEN=${STAGING_WHATSAPP_VERIFY_TOKEN}

# SMS Configuration (Staging)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=${STAGING_TWILIO_ACCOUNT_SID}
TWILIO_AUTH_TOKEN=${STAGING_TWILIO_AUTH_TOKEN}
TWILIO_PHONE_NUMBER=${STAGING_TWILIO_PHONE_NUMBER}

# Security Configuration (Staging)
HELMET_ENABLED=true
CSRF_ENABLED=true
SESSION_SECRET=${STAGING_SESSION_SECRET}
COOKIE_SECURE=true
COOKIE_SAME_SITE=strict

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health
HEALTH_CHECK_TIMEOUT=5000

# Backup Configuration (Staging)
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=${STAGING_BACKUP_S3_BUCKET}
BACKUP_S3_REGION=${STAGING_BACKUP_S3_REGION}

# Error Tracking (Staging)
SENTRY_DSN=${STAGING_SENTRY_DSN}
SENTRY_ENVIRONMENT=staging
SENTRY_RELEASE=${APP_VERSION}

# Performance Monitoring
NEW_RELIC_LICENSE_KEY=${STAGING_NEW_RELIC_LICENSE_KEY}
NEW_RELIC_APP_NAME=PHCityRent-API-Staging
