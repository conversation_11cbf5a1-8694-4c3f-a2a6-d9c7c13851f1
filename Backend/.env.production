# Production Environment Configuration
NODE_ENV=production
PORT=3001
APP_NAME=PHCityRent API
APP_VERSION=1.0.0
APP_DESCRIPTION=Enterprise-grade API for Port Harcourt Real Estate Platform
CORS_ORIGINS=https://phcityrent.com,https://admin.phcityrent.com,https://www.phcityrent.com
API_PREFIX=api/v1

# Database Configuration (Production)
DB_HOST=${PROD_DB_HOST}
DB_PORT=5432
DB_USERNAME=${PROD_DB_USERNAME}
DB_PASSWORD=${PROD_DB_PASSWORD}
DB_NAME=${PROD_DB_NAME}
DB_MAX_CONNECTIONS=50
DB_MIN_CONNECTIONS=10
DB_LOGGING=false
DB_SYNCHRONIZE=false
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=true

# Redis Configuration (Production)
REDIS_HOST=${PROD_REDIS_HOST}
REDIS_PORT=6379
REDIS_PASSWORD=${PROD_REDIS_PASSWORD}
REDIS_DB=0
REDIS_QUEUE_DB=1
REDIS_SESSION_DB=2
REDIS_TLS=true

# Authentication Configuration (Production)
JWT_SECRET=${PROD_JWT_SECRET}
JWT_EXPIRES_IN=15m
JWT_REFRESH_SECRET=${PROD_JWT_REFRESH_SECRET}
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=14
PASSWORD_RESET_EXPIRY=3600000
EMAIL_VERIFICATION_EXPIRY=86400000
MAX_LOGIN_ATTEMPTS=3
LOCKOUT_DURATION=1800000

# File Upload Configuration (Production)
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp
UPLOAD_PATH=/app/uploads/production
STATIC_FILES_PATH=/uploads

# Email Configuration (Production)
EMAIL_FROM=<EMAIL>
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USER=apikey
EMAIL_PASSWORD=${PROD_SENDGRID_API_KEY}
EMAIL_SECURE=true

# Payment Gateway Configuration (Production)
# Paystack Live Keys
PAYSTACK_SECRET_KEY=${PROD_PAYSTACK_SECRET_KEY}
PAYSTACK_PUBLIC_KEY=${PROD_PAYSTACK_PUBLIC_KEY}
PAYSTACK_WEBHOOK_SECRET=${PROD_PAYSTACK_WEBHOOK_SECRET}
PAYSTACK_BASE_URL=https://api.paystack.co

# Flutterwave Live Keys
FLUTTERWAVE_SECRET_KEY=${PROD_FLUTTERWAVE_SECRET_KEY}
FLUTTERWAVE_PUBLIC_KEY=${PROD_FLUTTERWAVE_PUBLIC_KEY}
FLUTTERWAVE_WEBHOOK_SECRET=${PROD_FLUTTERWAVE_WEBHOOK_SECRET}
FLUTTERWAVE_BASE_URL=https://api.flutterwave.com/v3

# Rate Limiting Configuration (Production)
THROTTLE_TTL=60
THROTTLE_LIMIT=100
API_RATE_LIMIT_WINDOW=900000
API_RATE_LIMIT_MAX=200

# Caching Configuration (Production)
CACHE_TTL=600
CACHE_MAX_ITEMS=5000

# Pagination Configuration
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=50

# Monitoring Configuration (Production)
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_SWAGGER=false
ENABLE_CORS=true

# Logging Configuration (Production)
LOG_LEVEL=warn
LOG_FORMAT=json
ENABLE_REQUEST_LOGGING=false
ENABLE_ERROR_STACK_TRACE=false

# External Services (Production)
GOOGLE_MAPS_API_KEY=${PROD_GOOGLE_MAPS_API_KEY}
CLOUDINARY_CLOUD_NAME=${PROD_CLOUDINARY_CLOUD_NAME}
CLOUDINARY_API_KEY=${PROD_CLOUDINARY_API_KEY}
CLOUDINARY_API_SECRET=${PROD_CLOUDINARY_API_SECRET}

# WhatsApp Business API (Production)
WHATSAPP_BUSINESS_PHONE_ID=${PROD_WHATSAPP_PHONE_ID}
WHATSAPP_ACCESS_TOKEN=${PROD_WHATSAPP_ACCESS_TOKEN}
WHATSAPP_WEBHOOK_VERIFY_TOKEN=${PROD_WHATSAPP_VERIFY_TOKEN}

# SMS Configuration (Production)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=${PROD_TWILIO_ACCOUNT_SID}
TWILIO_AUTH_TOKEN=${PROD_TWILIO_AUTH_TOKEN}
TWILIO_PHONE_NUMBER=${PROD_TWILIO_PHONE_NUMBER}

# Security Configuration (Production)
HELMET_ENABLED=true
CSRF_ENABLED=true
SESSION_SECRET=${PROD_SESSION_SECRET}
COOKIE_SECURE=true
COOKIE_SAME_SITE=strict
TRUST_PROXY=true

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health
HEALTH_CHECK_TIMEOUT=3000

# Backup Configuration (Production)
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=90
BACKUP_S3_BUCKET=${PROD_BACKUP_S3_BUCKET}
BACKUP_S3_REGION=${PROD_BACKUP_S3_REGION}
BACKUP_S3_ACCESS_KEY=${PROD_BACKUP_S3_ACCESS_KEY}
BACKUP_S3_SECRET_KEY=${PROD_BACKUP_S3_SECRET_KEY}

# Error Tracking (Production)
SENTRY_DSN=${PROD_SENTRY_DSN}
SENTRY_ENVIRONMENT=production
SENTRY_RELEASE=${APP_VERSION}
SENTRY_TRACES_SAMPLE_RATE=0.1

# Performance Monitoring
NEW_RELIC_LICENSE_KEY=${PROD_NEW_RELIC_LICENSE_KEY}
NEW_RELIC_APP_NAME=PHCityRent-API-Production

# SSL Configuration
SSL_CERT_PATH=${PROD_SSL_CERT_PATH}
SSL_KEY_PATH=${PROD_SSL_KEY_PATH}
SSL_CA_PATH=${PROD_SSL_CA_PATH}

# CDN Configuration
CDN_URL=${PROD_CDN_URL}
STATIC_ASSETS_CDN=${PROD_STATIC_ASSETS_CDN}

# Load Balancer Configuration
BEHIND_LOAD_BALANCER=true
LOAD_BALANCER_IP_HEADER=x-forwarded-for

# Clustering Configuration
CLUSTER_ENABLED=true
CLUSTER_WORKERS=0
