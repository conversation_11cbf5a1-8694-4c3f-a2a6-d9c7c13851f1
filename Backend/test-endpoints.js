const axios = require('axios');

console.log('🧪 PHCityRent Backend - Endpoint Testing');
console.log('==========================================\n');

const BASE_URL = 'http://localhost:3001/api/v1';

// Test endpoints that don't require database
const testEndpoints = [
  {
    name: 'Test Health Check',
    method: 'GET',
    url: `${BASE_URL}/test/health`,
    expectedStatus: 200,
  },
  {
    name: 'Test Endpoints List',
    method: 'GET',
    url: `${BASE_URL}/test/endpoints`,
    expectedStatus: 200,
  },
];

async function testEndpoint(test) {
  try {
    console.log(`Testing: ${test.name}`);
    
    const response = await axios({
      method: test.method,
      url: test.url,
      timeout: 5000,
      validateStatus: () => true, // Don't throw on any status
    });

    const success = response.status === test.expectedStatus;
    const statusIcon = success ? '✅' : '❌';
    
    console.log(`  ${statusIcon} ${test.method} ${test.url}`);
    console.log(`     Status: ${response.status} (expected: ${test.expectedStatus})`);
    
    if (response.data) {
      if (typeof response.data === 'object') {
        console.log(`     Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
      } else {
        console.log(`     Response: ${response.data.toString().substring(0, 100)}...`);
      }
    }
    
    return success;
  } catch (error) {
    console.log(`  ❌ ${test.method} ${test.url}`);
    console.log(`     Error: ${error.message}`);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting endpoint tests...\n');
  
  let passed = 0;
  let total = testEndpoints.length;
  
  for (const test of testEndpoints) {
    const success = await testEndpoint(test);
    if (success) passed++;
    console.log('');
  }
  
  console.log('📊 Test Results:');
  console.log(`   Passed: ${passed}/${total}`);
  console.log(`   Success Rate: ${Math.round((passed/total) * 100)}%`);
  
  if (passed === total) {
    console.log('\n🎉 All endpoint tests passed!');
  } else {
    console.log('\n⚠️  Some tests failed (expected if server is not running)');
  }
  
  return passed === total;
}

// Check if server is running first
async function checkServerStatus() {
  try {
    const response = await axios.get(`${BASE_URL}/test/health`, { timeout: 2000 });
    return response.status === 200;
  } catch (error) {
    return false;
  }
}

async function main() {
  const serverRunning = await checkServerStatus();
  
  if (!serverRunning) {
    console.log('⚠️  Server is not running. Starting test server...');
    console.log('   Run: npm run test:server');
    console.log('   Then run this test again.\n');
    
    // Show what we would test
    console.log('📋 Endpoints that would be tested:');
    testEndpoints.forEach(test => {
      console.log(`   ${test.method} ${test.url}`);
    });
    
    return;
  }
  
  console.log('✅ Server is running. Proceeding with tests...\n');
  await runTests();
}

main().catch(console.error);
