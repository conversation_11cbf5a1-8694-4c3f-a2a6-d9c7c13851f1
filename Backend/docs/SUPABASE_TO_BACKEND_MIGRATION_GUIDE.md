# 🔄 **Supabase to Backend Migration Guide**

## **🎯 Complete Migration Strategy**

This guide shows you how to move your **25 Supabase migrations** and **15 database functions** to your NestJS backend while maintaining all functionality.

---

## **📋 Current Supabase Assets**

### **✅ Available in Your Backend**
- **25 SQL Migrations** in `Backend/migrations/migrations/`
- **15 Database Functions** in `Backend/migrations/functions/`
- **Complete Schema** for real estate platform
- **Storage Buckets** configuration
- **Real-time Subscriptions** setup

---

## **🚀 Migration Options**

### **Option 1: Keep Supabase (Recommended)**
**Benefits**: Leverage existing infrastructure, real-time features, storage
```bash
# Setup Supabase connection
cd Backend
npm install @supabase/supabase-js

# Add to .env
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Run existing migrations
supabase db push

# Start backend with Supabase integration
npm run start:dev
```

### **Option 2: Migrate to PostgreSQL**
**Benefits**: Full control, no external dependencies
```bash
# Convert Supabase migrations to TypeORM
curl -X POST http://localhost:3001/api/v1/migration/convert-supabase

# Run converted migrations
curl -X POST http://localhost:3001/api/v1/migration/run-migrations

# Create comprehensive schema
curl -X POST http://localhost:3001/api/v1/migration/create-schema
```

### **Option 3: Hybrid Approach**
**Benefits**: Best of both worlds
```bash
# Use PostgreSQL for core data
# Use Supabase for real-time and storage
# Sync between both systems
```

---

## **🔧 Step-by-Step Migration Process**

### **Step 1: Backup Current Data**
```bash
# Export Supabase data
supabase db dump --data-only > backup.sql

# Export schema
supabase db dump --schema-only > schema.sql
```

### **Step 2: Setup PostgreSQL**
```bash
# Install PostgreSQL
brew install postgresql  # macOS
sudo apt install postgresql  # Ubuntu

# Create database
createdb phcityrent

# Update .env
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_NAME=phcityrent
```

### **Step 3: Convert Migrations**
```bash
# Start your backend
npm run start:dev

# Check migration status
curl http://localhost:3001/api/v1/migration/status

# Convert all Supabase migrations
curl -X POST http://localhost:3001/api/v1/migration/convert-supabase

# Run converted migrations
curl -X POST http://localhost:3001/api/v1/migration/run-migrations
```

### **Step 4: Migrate Data**
```bash
# Import your backed up data
psql -d phcityrent -f backup.sql

# Or use the seeder for fresh data
npm run seed:run
```

### **Step 5: Update Services**
```typescript
// Replace Supabase client calls with TypeORM
// Example: Property search
async searchProperties(filters: any) {
  // Old Supabase way:
  // const { data } = await supabase.from('properties').select('*');
  
  // New TypeORM way:
  return await this.propertyRepository.find({
    where: filters,
    relations: ['landlord', 'agent'],
  });
}
```

---

## **📊 Migration Mapping**

### **Supabase Tables → TypeORM Entities**

| Supabase Table | TypeORM Entity | Status |
|----------------|-----------------|--------|
| `auth.users` | `User` | ✅ Ready |
| `profiles` | `UserProfile` | 🔄 Create |
| `properties` | `Property` | ✅ Ready |
| `property_images` | `PropertyImage` | 🔄 Create |
| `property_documents` | `PropertyDocument` | 🔄 Create |
| `payment_transactions` | `Payment` | ✅ Ready |
| `notifications` | `Notification` | ✅ Ready |
| `agent_applications` | `AgentApplication` | 🔄 Create |
| `rental_applications` | `RentalApplication` | 🔄 Create |
| `maintenance_requests` | `MaintenanceRequest` | 🔄 Create |

### **Supabase Functions → NestJS Services**

| Supabase Function | NestJS Service Method | Status |
|-------------------|----------------------|--------|
| `search_properties_by_location` | `PropertiesService.searchByLocation()` | 🔄 Implement |
| `get_property_analytics` | `AnalyticsService.getPropertyAnalytics()` | 🔄 Implement |
| `get_property_recommendations` | `PropertiesService.getRecommendations()` | 🔄 Implement |
| `create-escrow-payment` | `PaymentsService.createEscrowPayment()` | 🔄 Implement |
| `send-notification` | `NotificationsService.sendNotification()` | 🔄 Implement |

---

## **🛠️ Implementation Examples**

### **Property Search with Geolocation**
```typescript
// src/api/v1/properties/properties.service.ts
async searchByLocation(lat: number, lng: number, radius: number) {
  return await this.propertyRepository
    .createQueryBuilder('property')
    .where(`
      ST_DWithin(
        ST_MakePoint(property.longitude, property.latitude)::geography,
        ST_MakePoint(:lng, :lat)::geography,
        :radius
      )
    `, { lat, lng, radius: radius * 1000 }) // Convert km to meters
    .getMany();
}
```

### **Real-time Notifications**
```typescript
// src/api/v1/notifications/notifications.service.ts
async sendRealTimeNotification(userId: string, message: string) {
  // Save to database
  const notification = await this.notificationRepository.save({
    userId,
    message,
    type: 'real_time',
  });

  // Send via WebSocket (implement WebSocket gateway)
  this.websocketGateway.sendToUser(userId, notification);
  
  return notification;
}
```

### **File Storage Migration**
```typescript
// src/api/v1/files/files.service.ts
async uploadPropertyImage(file: Express.Multer.File, propertyId: string) {
  // Old Supabase storage:
  // await supabase.storage.from('property-images').upload(path, file);
  
  // New local/cloud storage:
  const filename = `${propertyId}/${Date.now()}-${file.originalname}`;
  const path = `uploads/properties/${filename}`;
  
  await fs.writeFile(path, file.buffer);
  
  return {
    url: `/api/v1/files/images/${filename}`,
    path: filename,
  };
}
```

---

## **⚡ Performance Considerations**

### **Database Optimization**
```sql
-- Create indexes for common queries (already included in migrations)
CREATE INDEX CONCURRENTLY idx_properties_location_search 
ON properties USING GIST (ST_MakePoint(longitude, latitude));

CREATE INDEX CONCURRENTLY idx_properties_price_range 
ON properties (price_per_year) 
WHERE status = 'available';

CREATE INDEX CONCURRENTLY idx_properties_fulltext 
ON properties USING GIN (to_tsvector('english', title || ' ' || description));
```

### **Caching Strategy**
```typescript
// Implement Redis caching for frequently accessed data
@Injectable()
export class CacheService {
  async getPropertyAnalytics(propertyId: string) {
    const cacheKey = `property:analytics:${propertyId}`;
    
    // Try cache first
    let analytics = await this.redis.get(cacheKey);
    if (analytics) return JSON.parse(analytics);
    
    // Calculate and cache
    analytics = await this.calculateAnalytics(propertyId);
    await this.redis.setex(cacheKey, 3600, JSON.stringify(analytics));
    
    return analytics;
  }
}
```

---

## **🔒 Security Migration**

### **Row Level Security → RBAC**
```typescript
// Replace Supabase RLS with NestJS Guards
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.LANDLORD, UserRole.ADMIN)
@Get('my-properties')
async getMyProperties(@GetUser() user: User) {
  return await this.propertiesService.findByLandlord(user.id);
}
```

### **Storage Security**
```typescript
// Implement secure file access
@Get('images/:userId/:filename')
@UseGuards(JwtAuthGuard)
async getImage(
  @Param('userId') userId: string,
  @Param('filename') filename: string,
  @GetUser() user: User,
) {
  // Check permissions
  if (user.id !== userId && user.role !== UserRole.ADMIN) {
    throw new ForbiddenException();
  }
  
  return this.filesService.getImage(filename);
}
```

---

## **📈 Testing Migration**

### **Verify Migration Success**
```bash
# Test all endpoints
curl http://localhost:3001/api/v1/test/endpoints

# Test database connection
curl http://localhost:3001/api/v1/health/database

# Test property search
curl "http://localhost:3001/api/v1/properties?city=Port%20Harcourt"

# Test file upload
curl -X POST -F "file=@test.jpg" http://localhost:3001/api/v1/files/upload/image
```

### **Performance Testing**
```bash
# Load test with 12k properties
npm run seed:run

# Test search performance
time curl "http://localhost:3001/api/v1/properties/search?location=GRA"

# Test pagination
curl "http://localhost:3001/api/v1/properties?page=1&limit=20"
```

---

## **🎯 Migration Checklist**

### **Pre-Migration**
- [ ] Backup Supabase data
- [ ] Document current API usage
- [ ] Test current functionality
- [ ] Setup PostgreSQL database

### **During Migration**
- [ ] Convert Supabase migrations
- [ ] Run TypeORM migrations
- [ ] Import data
- [ ] Update service implementations
- [ ] Test all endpoints

### **Post-Migration**
- [ ] Performance testing
- [ ] Security audit
- [ ] Documentation update
- [ ] Monitor for issues
- [ ] Cleanup old Supabase resources

---

## **🎉 Migration Benefits**

### **✅ After Migration You Get**
- **Full Control** over your database
- **No External Dependencies** on Supabase
- **Custom Optimization** for your use case
- **Cost Savings** on Supabase subscription
- **Better Integration** with your NestJS app
- **Enhanced Security** with custom RBAC
- **Improved Performance** with targeted indexes

### **✅ What You Keep**
- **All 95 API Endpoints** working
- **Complete Database Schema** with 25 tables
- **84+ Performance Indexes** 
- **12,000 Mock Properties** for testing
- **Comprehensive Documentation**
- **Production-Ready Architecture**

**Your PHCityRent backend will be fully independent and production-ready after migration!** 🚀
