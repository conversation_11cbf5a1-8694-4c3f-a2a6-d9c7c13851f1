# Auth Module Integration Guide

## Overview

This document outlines the integration of the authentication module from `phcityrent-backend` to the frontend in `p-398841`. The integration replaces the existing Supabase authentication with a custom JWT-based authentication system.

## Architecture

### Backend (phcityrent-backend)
- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with refresh tokens
- **Security**: Rate limiting, password hashing, role-based access control

### Frontend (p-398841)
- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **State Management**: React Context for auth state
- **HTTP Client**: Fetch API with retry logic

## Key Features Integrated

### 1. User Registration
- Email and password validation
- Phone number requirement
- Role selection (TENANT, LANDLORD, AGENT)
- Password strength requirements
- Email verification flow

### 2. User Authentication
- JWT-based login
- Token refresh mechanism
- Session persistence
- Automatic token refresh on 401 errors

### 3. Security Features
- Password hashing with bcrypt
- Rate limiting on auth endpoints
- Token expiration handling
- Secure token storage in localStorage

### 4. Role-Based Access Control
- User roles: TENANT, LANDLORD, AGENT, ADMIN
- Protected routes with role requirements
- Admin dashboard access control

## Implementation Details

### 1. Auth Service (`src/services/authService.ts`)

The auth service handles all authentication-related API calls:

```typescript
// Key methods
- register(registerDto: RegisterDto): Promise<AuthResponse>
- login(loginDto: LoginDto): Promise<AuthResponse>
- refreshAccessToken(): Promise<AuthResponse>
- logout(): Promise<void>
- getProfile(): Promise<any>
```

**Features:**
- Automatic token refresh on 401 errors
- Retry logic with exponential backoff
- Secure token storage and management
- Error handling with user-friendly messages

### 2. Auth Hook (`src/hooks/useAuth.tsx`)

The auth hook provides a React context for authentication state:

```typescript
interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAdmin: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: any }>;
  signUp: (email: string, password: string, fullName: string, phone: string, role: 'TENANT' | 'LANDLORD' | 'AGENT') => Promise<{ error?: any }>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}
```

### 3. Auth Modal (`src/components/auth/AuthModal.tsx`)

Enhanced registration form with:
- Full name input
- Phone number validation
- Role selection dropdown
- Password strength validation
- Real-time form validation

### 4. Protected Routes (`src/components/auth/ProtectedRoute.tsx`)

Advanced route protection with:
- Authentication checks
- Role-based access control
- Email verification requirements
- Account status validation
- Graceful error handling

## Configuration

### Environment Variables

Create a `.env` file in the frontend directory:

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3000

# App Configuration
VITE_APP_NAME=PHCityRent
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development
```

### API Configuration (`src/config/api.ts`)

Centralized API configuration:

```typescript
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;
```

## Migration Steps

### 1. Backend Setup

1. **Start the phcityrent-backend server:**
   ```bash
   cd phcityrent-backend
   npm install
   npm run dev
   ```

2. **Set up the database:**
   ```bash
   npm run db:generate
   npm run db:migrate
   npm run db:seed
   ```

3. **Configure environment variables:**
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/phcityrent"
   JWT_SECRET="your-secret-key"
   JWT_REFRESH_SECRET="your-refresh-secret-key"
   ```

### 2. Frontend Setup

1. **Install dependencies:**
   ```bash
   cd p-398841/Frontend
   npm install jwt-decode
   ```

2. **Configure environment variables:**
   ```env
   VITE_API_BASE_URL=http://localhost:3000
   ```

3. **Update the AuthProvider in your app:**
   ```tsx
   import { AuthProvider } from '@/hooks/useAuth';
   
   function App() {
     return (
       <AuthProvider>
         {/* Your app components */}
       </AuthProvider>
     );
   }
   ```

### 3. Testing the Integration

1. **Test Registration:**
   - Open the auth modal
   - Fill in all required fields
   - Submit the form
   - Verify user is created and logged in

2. **Test Login:**
   - Use existing credentials
   - Verify token storage
   - Check user state updates

3. **Test Protected Routes:**
   - Navigate to protected pages
   - Verify access control works
   - Test role-based restrictions

## API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/auth/register` | Register new user |
| POST | `/auth/login` | Login user |
| POST | `/auth/refresh` | Refresh access token |
| POST | `/auth/logout` | Logout user |
| PATCH | `/auth/change-password` | Change password |
| POST | `/auth/forgot-password` | Request password reset |
| POST | `/auth/reset-password` | Reset password with token |
| POST | `/auth/verify-email` | Verify email with token |
| GET | `/auth/me` | Get current user profile |

### Request/Response Examples

#### Register Request
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+2348012345678",
  "role": "TENANT"
}
```

#### Login Request
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

#### Auth Response
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "TENANT",
    "isEmailVerified": false,
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "accessToken": "jwt-token",
  "refreshToken": "refresh-token",
  "expiresIn": 900,
  "tokenType": "Bearer"
}
```

## Security Considerations

### 1. Token Security
- Access tokens expire after 15 minutes
- Refresh tokens are stored securely
- Automatic token refresh on expiration
- Secure token storage in localStorage

### 2. Password Security
- Minimum 8 characters
- Must contain uppercase, lowercase, number, and special character
- Bcrypt hashing with salt rounds
- Password strength validation

### 3. Rate Limiting
- Configurable rate limits on auth endpoints
- Prevents brute force attacks
- IP-based rate limiting

### 4. Input Validation
- Server-side validation with class-validator
- Client-side validation for better UX
- Sanitized inputs to prevent injection attacks

## Error Handling

### Common Error Scenarios

1. **Network Errors:**
   - Automatic retry with exponential backoff
   - User-friendly error messages
   - Graceful degradation

2. **Authentication Errors:**
   - Clear error messages for invalid credentials
   - Account status validation
   - Email verification prompts

3. **Token Expiration:**
   - Automatic refresh on 401 errors
   - Seamless user experience
   - Fallback to login on refresh failure

## Troubleshooting

### Common Issues

1. **CORS Errors:**
   - Ensure backend CORS is configured for frontend domain
   - Check API base URL configuration

2. **Token Issues:**
   - Clear localStorage and re-login
   - Check token expiration settings
   - Verify JWT secret configuration

3. **Database Connection:**
   - Verify database is running
   - Check connection string
   - Run database migrations

### Debug Tools

1. **Browser DevTools:**
   - Check Network tab for API calls
   - Monitor localStorage for tokens
   - Review console for errors

2. **Backend Logs:**
   - Monitor server logs for errors
   - Check database connection
   - Verify environment variables

## Future Enhancements

### Planned Features

1. **Social Authentication:**
   - Google OAuth integration
   - Facebook login
   - Apple Sign-In

2. **Advanced Security:**
   - Two-factor authentication
   - Device management
   - Session tracking

3. **User Management:**
   - Profile management
   - Account settings
   - Notification preferences

4. **Analytics:**
   - Login analytics
   - User behavior tracking
   - Security event logging

## Support

For issues or questions about the auth integration:

1. Check the troubleshooting section
2. Review backend logs
3. Verify configuration settings
4. Test with minimal setup

## Conclusion

This integration provides a robust, secure authentication system that replaces the previous Supabase implementation. The system includes comprehensive error handling, security features, and a smooth user experience.

The modular design allows for easy maintenance and future enhancements while maintaining backward compatibility during the transition period. 