-- =====================================================
-- COMPREHENSIVE SECURITY HARDENING SCHEMA
-- Database schema for security monitoring and protection
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- SECURITY EVENTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS security_events (
    id BIGSERIAL PRIMARY KEY,
    event_id UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    source VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    session_id VARCHAR(100),
    ip_address INET NOT NULL,
    user_agent TEXT,
    tags TEXT[] DEFAULT '{}',
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for security_events
CREATE INDEX idx_security_events_type ON security_events(event_type);
CREATE INDEX idx_security_events_severity ON security_events(severity);
CREATE INDEX idx_security_events_ip ON security_events(ip_address);
CREATE INDEX idx_security_events_user ON security_events(user_id);
CREATE INDEX idx_security_events_timestamp ON security_events(timestamp);
CREATE INDEX idx_security_events_resolved ON security_events(resolved);
CREATE INDEX idx_security_events_composite ON security_events(event_type, severity, timestamp);

-- =====================================================
-- SECURITY ALERTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS security_alerts (
    id BIGSERIAL PRIMARY KEY,
    alert_id UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    event_id UUID REFERENCES security_events(event_id) ON DELETE CASCADE,
    alert_type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'false_positive')),
    assigned_to UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    escalation_level INTEGER DEFAULT 1,
    automated_response TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for security_alerts
CREATE INDEX idx_security_alerts_status ON security_alerts(status);
CREATE INDEX idx_security_alerts_severity ON security_alerts(severity);
CREATE INDEX idx_security_alerts_assigned ON security_alerts(assigned_to);
CREATE INDEX idx_security_alerts_created ON security_alerts(created_at);

-- =====================================================
-- RATE LIMIT VIOLATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS rate_limit_violations (
    id BIGSERIAL PRIMARY KEY,
    rate_limit_key VARCHAR(200) NOT NULL,
    rule_id VARCHAR(100) NOT NULL,
    rule_name VARCHAR(200) NOT NULL,
    violation_type VARCHAR(50) NOT NULL,
    request_count INTEGER NOT NULL,
    limit_value INTEGER NOT NULL,
    window_ms INTEGER NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    ip_address INET NOT NULL,
    user_agent TEXT,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for rate_limit_violations
CREATE INDEX idx_rate_limit_violations_key ON rate_limit_violations(rate_limit_key);
CREATE INDEX idx_rate_limit_violations_ip ON rate_limit_violations(ip_address);
CREATE INDEX idx_rate_limit_violations_timestamp ON rate_limit_violations(timestamp);

-- =====================================================
-- RATE LIMIT ALERTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS rate_limit_alerts (
    id BIGSERIAL PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    rate_limit_key VARCHAR(200) NOT NULL,
    request_count INTEGER NOT NULL,
    time_window INTEGER NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    ip_address INET NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for rate_limit_alerts
CREATE INDEX idx_rate_limit_alerts_ip ON rate_limit_alerts(ip_address);
CREATE INDEX idx_rate_limit_alerts_timestamp ON rate_limit_alerts(timestamp);

-- =====================================================
-- CSRF ATTACK LOGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS csrf_attack_logs (
    id BIGSERIAL PRIMARY KEY,
    attack_id UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    attack_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    ip_address INET NOT NULL,
    user_agent TEXT,
    referer TEXT,
    origin TEXT,
    blocked BOOLEAN DEFAULT TRUE,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for csrf_attack_logs
CREATE INDEX idx_csrf_attacks_ip ON csrf_attack_logs(ip_address);
CREATE INDEX idx_csrf_attacks_type ON csrf_attack_logs(attack_type);
CREATE INDEX idx_csrf_attacks_timestamp ON csrf_attack_logs(timestamp);

-- =====================================================
-- ENCRYPTION KEYS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS encryption_keys (
    id BIGSERIAL PRIMARY KEY,
    key_id UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    algorithm VARCHAR(50) NOT NULL,
    key_data TEXT NOT NULL, -- In production, this should be encrypted with a master key
    purpose VARCHAR(50) NOT NULL CHECK (purpose IN ('data', 'file', 'communication', 'backup')),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'revoked', 'expired')),
    rotation_schedule INTEGER, -- Days between rotations
    usage_count BIGINT DEFAULT 0,
    last_used TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for encryption_keys
CREATE INDEX idx_encryption_keys_purpose ON encryption_keys(purpose);
CREATE INDEX idx_encryption_keys_status ON encryption_keys(status);
CREATE INDEX idx_encryption_keys_expires ON encryption_keys(expires_at);

-- =====================================================
-- KEY ROTATION LOGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS key_rotation_logs (
    id BIGSERIAL PRIMARY KEY,
    old_key_id UUID NOT NULL,
    new_key_id UUID REFERENCES encryption_keys(key_id) ON DELETE CASCADE,
    affected_records INTEGER DEFAULT 0,
    rotated_at TIMESTAMPTZ DEFAULT NOW(),
    rotation_reason TEXT
);

-- Indexes for key_rotation_logs
CREATE INDEX idx_key_rotation_logs_old_key ON key_rotation_logs(old_key_id);
CREATE INDEX idx_key_rotation_logs_new_key ON key_rotation_logs(new_key_id);
CREATE INDEX idx_key_rotation_logs_date ON key_rotation_logs(rotated_at);

-- =====================================================
-- KEY REVOCATION LOGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS key_revocation_logs (
    id BIGSERIAL PRIMARY KEY,
    key_id UUID NOT NULL,
    reason TEXT NOT NULL,
    revoked_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    revoked_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- ENCRYPTED DATA TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS encrypted_data (
    id BIGSERIAL PRIMARY KEY,
    data_id UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    encrypted_data JSONB NOT NULL,
    key_id UUID REFERENCES encryption_keys(key_id) ON DELETE RESTRICT,
    encryption_type VARCHAR(20) NOT NULL CHECK (encryption_type IN ('deterministic', 'probabilistic')),
    searchable_hash TEXT, -- For searchable encryption
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for encrypted_data
CREATE INDEX idx_encrypted_data_table_field ON encrypted_data(table_name, field_name);
CREATE INDEX idx_encrypted_data_record ON encrypted_data(record_id);
CREATE INDEX idx_encrypted_data_key ON encrypted_data(key_id);
CREATE INDEX idx_encrypted_data_hash ON encrypted_data(searchable_hash);

-- =====================================================
-- BLOCKED IPS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS blocked_ips (
    id BIGSERIAL PRIMARY KEY,
    ip_address INET NOT NULL,
    reason TEXT NOT NULL,
    blocked_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    blocked_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    block_count INTEGER DEFAULT 1,
    last_attempt TIMESTAMPTZ
);

-- Indexes for blocked_ips
CREATE UNIQUE INDEX idx_blocked_ips_unique ON blocked_ips(ip_address) WHERE is_active = TRUE;
CREATE INDEX idx_blocked_ips_expires ON blocked_ips(expires_at);
CREATE INDEX idx_blocked_ips_active ON blocked_ips(is_active);

-- =====================================================
-- ACCOUNT SUSPENSIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS account_suspensions (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reason TEXT NOT NULL,
    suspended_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    suspended_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    suspension_count INTEGER DEFAULT 1
);

-- Indexes for account_suspensions
CREATE INDEX idx_account_suspensions_user ON account_suspensions(user_id);
CREATE INDEX idx_account_suspensions_expires ON account_suspensions(expires_at);
CREATE INDEX idx_account_suspensions_active ON account_suspensions(is_active);

-- =====================================================
-- THREAT INTELLIGENCE TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS threat_intelligence (
    id BIGSERIAL PRIMARY KEY,
    ip_address INET NOT NULL,
    threat_type VARCHAR(50) NOT NULL,
    risk_score INTEGER NOT NULL CHECK (risk_score >= 0 AND risk_score <= 100),
    source VARCHAR(100) NOT NULL,
    details JSONB DEFAULT '{}',
    first_seen TIMESTAMPTZ DEFAULT NOW(),
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    confidence_level INTEGER DEFAULT 50 CHECK (confidence_level >= 0 AND confidence_level <= 100)
);

-- Indexes for threat_intelligence
CREATE UNIQUE INDEX idx_threat_intelligence_ip ON threat_intelligence(ip_address);
CREATE INDEX idx_threat_intelligence_type ON threat_intelligence(threat_type);
CREATE INDEX idx_threat_intelligence_risk ON threat_intelligence(risk_score);
CREATE INDEX idx_threat_intelligence_active ON threat_intelligence(is_active);

-- =====================================================
-- ADMIN NOTIFICATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS admin_notifications (
    id BIGSERIAL PRIMARY KEY,
    event_id UUID REFERENCES security_events(event_id) ON DELETE CASCADE,
    urgency VARCHAR(20) NOT NULL CHECK (urgency IN ('low', 'medium', 'high', 'immediate')),
    message TEXT NOT NULL,
    notification_type VARCHAR(50) DEFAULT 'security_alert',
    sent_to UUID[] DEFAULT '{}',
    sent_at TIMESTAMPTZ DEFAULT NOW(),
    acknowledged_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    acknowledged_at TIMESTAMPTZ
);

-- Indexes for admin_notifications
CREATE INDEX idx_admin_notifications_urgency ON admin_notifications(urgency);
CREATE INDEX idx_admin_notifications_sent ON admin_notifications(sent_at);
CREATE INDEX idx_admin_notifications_ack ON admin_notifications(acknowledged_at);

-- =====================================================
-- SECURITY AUDIT TRAIL TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS security_audit_trail (
    id BIGSERIAL PRIMARY KEY,
    audit_id UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    ip_address INET,
    user_agent TEXT,
    old_values JSONB,
    new_values JSONB,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for security_audit_trail
CREATE INDEX idx_audit_trail_action ON security_audit_trail(action);
CREATE INDEX idx_audit_trail_resource ON security_audit_trail(resource_type, resource_id);
CREATE INDEX idx_audit_trail_user ON security_audit_trail(user_id);
CREATE INDEX idx_audit_trail_timestamp ON security_audit_trail(timestamp);

-- =====================================================
-- VULNERABILITY SCANS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS vulnerability_scans (
    id BIGSERIAL PRIMARY KEY,
    scan_id UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    scan_type VARCHAR(50) NOT NULL,
    target VARCHAR(200) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    vulnerabilities_found INTEGER DEFAULT 0,
    critical_count INTEGER DEFAULT 0,
    high_count INTEGER DEFAULT 0,
    medium_count INTEGER DEFAULT 0,
    low_count INTEGER DEFAULT 0,
    scan_results JSONB DEFAULT '{}',
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    next_scan TIMESTAMPTZ
);

-- Indexes for vulnerability_scans
CREATE INDEX idx_vulnerability_scans_type ON vulnerability_scans(scan_type);
CREATE INDEX idx_vulnerability_scans_status ON vulnerability_scans(status);
CREATE INDEX idx_vulnerability_scans_started ON vulnerability_scans(started_at);

-- =====================================================
-- SECURITY CONFIGURATION TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS security_configuration (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    is_sensitive BOOLEAN DEFAULT FALSE,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert default security configurations
INSERT INTO security_configuration (config_key, config_value, description) VALUES
('rate_limiting_enabled', 'true', 'Enable rate limiting globally'),
('csrf_protection_enabled', 'true', 'Enable CSRF protection'),
('encryption_enabled', 'true', 'Enable data encryption'),
('security_monitoring_enabled', 'true', 'Enable security event monitoring'),
('max_login_attempts', '5', 'Maximum login attempts before account lockout'),
('session_timeout', '3600', 'Session timeout in seconds'),
('password_min_length', '8', 'Minimum password length'),
('require_2fa', 'false', 'Require two-factor authentication'),
('allowed_file_types', '["image/jpeg", "image/png", "image/gif", "application/pdf"]', 'Allowed file upload types'),
('max_file_size', '********', 'Maximum file upload size in bytes (10MB)')
ON CONFLICT (config_key) DO NOTHING;

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_security_events_updated_at BEFORE UPDATE ON security_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_security_alerts_updated_at BEFORE UPDATE ON security_alerts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_encryption_keys_updated_at BEFORE UPDATE ON encryption_keys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_encrypted_data_updated_at BEFORE UPDATE ON encrypted_data FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically expire blocked IPs
CREATE OR REPLACE FUNCTION expire_blocked_ips()
RETURNS void AS $$
BEGIN
    UPDATE blocked_ips 
    SET is_active = FALSE 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW() 
    AND is_active = TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically expire account suspensions
CREATE OR REPLACE FUNCTION expire_account_suspensions()
RETURNS void AS $$
BEGIN
    UPDATE account_suspensions 
    SET is_active = FALSE 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW() 
    AND is_active = TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old security events
CREATE OR REPLACE FUNCTION cleanup_old_security_events()
RETURNS void AS $$
BEGIN
    DELETE FROM security_events 
    WHERE timestamp < NOW() - INTERVAL '90 days'
    AND resolved = TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on sensitive tables
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE encryption_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_audit_trail ENABLE ROW LEVEL SECURITY;

-- RLS Policies for security_events (admin and security team only)
CREATE POLICY "Security events viewable by admins" ON security_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' IN ('admin', 'security_admin')
        )
    );

-- RLS Policies for security_alerts (admin and security team only)
CREATE POLICY "Security alerts viewable by admins" ON security_alerts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' IN ('admin', 'security_admin')
        )
    );

-- RLS Policies for encryption_keys (system only)
CREATE POLICY "Encryption keys restricted" ON encryption_keys
    FOR ALL USING (FALSE); -- No direct access via RLS

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Composite indexes for common queries
CREATE INDEX idx_security_events_user_time ON security_events(user_id, timestamp);
CREATE INDEX idx_security_events_ip_type ON security_events(ip_address, event_type);
CREATE INDEX idx_security_alerts_severity_status ON security_alerts(severity, status);

-- Partial indexes for active records
CREATE INDEX idx_blocked_ips_active_expires ON blocked_ips(expires_at) WHERE is_active = TRUE;
CREATE INDEX idx_account_suspensions_active_expires ON account_suspensions(expires_at) WHERE is_active = TRUE;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE security_events IS 'Comprehensive security event logging';
COMMENT ON TABLE security_alerts IS 'Security alerts generated from events';
COMMENT ON TABLE rate_limit_violations IS 'Rate limiting violation logs';
COMMENT ON TABLE csrf_attack_logs IS 'CSRF attack attempt logs';
COMMENT ON TABLE encryption_keys IS 'Encryption key management';
COMMENT ON TABLE encrypted_data IS 'Encrypted sensitive data storage';
COMMENT ON TABLE blocked_ips IS 'IP address blocking management';
COMMENT ON TABLE account_suspensions IS 'User account suspension management';
COMMENT ON TABLE threat_intelligence IS 'Threat intelligence data';
COMMENT ON TABLE security_audit_trail IS 'Comprehensive audit trail';
COMMENT ON TABLE vulnerability_scans IS 'Vulnerability scan results';
COMMENT ON TABLE security_configuration IS 'Security system configuration';
