-- =====================================================
-- THIRD-PARTY INTEGRATIONS DATABASE SCHEMA
-- Support for Google Maps, Social Media, Documents, and Backup services
-- =====================================================

-- Property Locations Table (Google Maps Integration)
CREATE TABLE IF NOT EXISTS public.property_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  address TEXT,
  place_id TEXT,
  formatted_address TEXT,
  neighborhood TEXT,
  city TEXT DEFAULT 'Port Harcourt',
  state TEXT DEFAULT 'Rivers',
  country TEXT DEFAULT 'Nigeria',
  postal_code TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(property_id)
);

-- Property Amenities Table (Nearby amenities from Google Places)
CREATE TABLE IF NOT EXISTS public.property_amenities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
  amenity_id TEXT NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('school', 'hospital', 'shopping', 'restaurant', 'transport', 'bank', 'recreation')),
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  distance DECIMAL(8, 2) NOT NULL, -- Distance in kilometers
  rating DECIMAL(3, 2),
  open_now BOOLEAN,
  place_details JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Social Media Platforms Configuration
CREATE TABLE IF NOT EXISTS public.social_platforms (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  icon TEXT NOT NULL,
  color TEXT NOT NULL,
  share_url TEXT NOT NULL,
  api_endpoint TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  configuration JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Social Profiles
CREATE TABLE IF NOT EXISTS public.user_social_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  provider TEXT NOT NULL,
  provider_id TEXT NOT NULL,
  profile_data JSONB NOT NULL DEFAULT '{}',
  access_token TEXT,
  refresh_token TEXT,
  expires_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, provider)
);

-- Social Sharing Logs
CREATE TABLE IF NOT EXISTS public.social_sharing_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  property_id UUID REFERENCES public.properties(id),
  platform TEXT NOT NULL,
  content_title TEXT NOT NULL,
  content_url TEXT NOT NULL,
  shares INTEGER DEFAULT 0,
  likes INTEGER DEFAULT 0,
  comments INTEGER DEFAULT 0,
  reach INTEGER DEFAULT 0,
  engagement INTEGER DEFAULT 0,
  clicks INTEGER DEFAULT 0,
  success BOOLEAN DEFAULT TRUE,
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Generated Content
CREATE TABLE IF NOT EXISTS public.user_generated_content (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
  platform TEXT NOT NULL,
  content_type TEXT NOT NULL CHECK (content_type IN ('review', 'photo', 'video', 'story')),
  content TEXT NOT NULL,
  media_urls TEXT[] DEFAULT '{}',
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  likes INTEGER DEFAULT 0,
  shares INTEGER DEFAULT 0,
  comments INTEGER DEFAULT 0,
  is_verified BOOLEAN DEFAULT FALSE,
  moderation_status TEXT DEFAULT 'pending' CHECK (moderation_status IN ('pending', 'approved', 'rejected')),
  moderated_by UUID REFERENCES auth.users(id),
  moderated_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Social Campaigns
CREATE TABLE IF NOT EXISTS public.social_campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  platforms TEXT[] NOT NULL DEFAULT '{}',
  content JSONB NOT NULL DEFAULT '{}',
  schedule JSONB NOT NULL DEFAULT '{}',
  targeting JSONB DEFAULT '{}',
  budget DECIMAL(12, 2),
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'active', 'completed', 'paused')),
  metrics JSONB DEFAULT '{}',
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Document Templates
CREATE TABLE IF NOT EXISTS public.document_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  type TEXT NOT NULL CHECK (type IN ('rental_agreement', 'lease_contract', 'property_inspection', 'receipt', 'invoice', 'legal_notice')),
  category TEXT NOT NULL CHECK (category IN ('legal', 'financial', 'administrative')),
  template TEXT NOT NULL,
  variables JSONB NOT NULL DEFAULT '[]',
  is_active BOOLEAN DEFAULT TRUE,
  version TEXT NOT NULL DEFAULT '1.0',
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Generated Documents
CREATE TABLE IF NOT EXISTS public.generated_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id UUID NOT NULL REFERENCES public.document_templates(id),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  pdf_url TEXT,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'generated', 'signed', 'executed', 'archived')),
  version INTEGER DEFAULT 1,
  metadata JSONB DEFAULT '{}',
  property_id UUID REFERENCES public.properties(id),
  tenant_id UUID REFERENCES auth.users(id),
  landlord_id UUID REFERENCES auth.users(id),
  agent_id UUID REFERENCES auth.users(id),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Document Signatures
CREATE TABLE IF NOT EXISTS public.document_signatures (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES public.generated_documents(id) ON DELETE CASCADE,
  signer_id UUID NOT NULL REFERENCES auth.users(id),
  signer_name TEXT NOT NULL,
  signer_email TEXT NOT NULL,
  signer_role TEXT NOT NULL CHECK (signer_role IN ('tenant', 'landlord', 'agent', 'witness')),
  signature_data TEXT,
  signed_at TIMESTAMPTZ,
  ip_address INET,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'signed', 'declined')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Document Versions
CREATE TABLE IF NOT EXISTS public.document_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES public.generated_documents(id) ON DELETE CASCADE,
  version INTEGER NOT NULL,
  content TEXT NOT NULL,
  changes TEXT[] DEFAULT '{}',
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Backup Configurations
CREATE TABLE IF NOT EXISTS public.backup_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  tables TEXT[] NOT NULL DEFAULT '{}',
  schedule JSONB NOT NULL DEFAULT '{}',
  retention JSONB NOT NULL DEFAULT '{}',
  encryption BOOLEAN DEFAULT FALSE,
  compression BOOLEAN DEFAULT TRUE,
  destination JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Backup Jobs
CREATE TABLE IF NOT EXISTS public.backup_jobs (
  id TEXT PRIMARY KEY,
  config_id UUID NOT NULL REFERENCES public.backup_configs(id),
  status TEXT NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  duration INTEGER, -- Duration in milliseconds
  size BIGINT, -- Size in bytes
  record_count INTEGER,
  error TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Sync Configurations
CREATE TABLE IF NOT EXISTS public.sync_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  source_table TEXT NOT NULL,
  target_table TEXT NOT NULL,
  sync_mode TEXT NOT NULL CHECK (sync_mode IN ('full', 'incremental', 'delta')),
  conflict_resolution TEXT NOT NULL CHECK (conflict_resolution IN ('source_wins', 'target_wins', 'manual', 'timestamp')),
  filters JSONB DEFAULT '{}',
  transformations JSONB DEFAULT '[]',
  schedule JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Sync Jobs
CREATE TABLE IF NOT EXISTS public.sync_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  config_id UUID NOT NULL REFERENCES public.sync_configs(id),
  status TEXT NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'conflict')),
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  records_processed INTEGER DEFAULT 0,
  records_inserted INTEGER DEFAULT 0,
  records_updated INTEGER DEFAULT 0,
  records_deleted INTEGER DEFAULT 0,
  conflicts JSONB DEFAULT '[]',
  error TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data Recovery Points
CREATE TABLE IF NOT EXISTS public.data_recovery_points (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  timestamp TIMESTAMPTZ NOT NULL,
  tables TEXT[] NOT NULL DEFAULT '{}',
  size BIGINT NOT NULL,
  checksum TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- API Keys Management
CREATE TABLE IF NOT EXISTS public.api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  service_name TEXT NOT NULL,
  key_name TEXT NOT NULL,
  encrypted_key TEXT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMPTZ,
  usage_count INTEGER DEFAULT 0,
  rate_limit INTEGER,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(service_name, key_name)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Property Locations
CREATE INDEX idx_property_locations_property_id ON public.property_locations(property_id);
CREATE INDEX idx_property_locations_coordinates ON public.property_locations(latitude, longitude);
CREATE INDEX idx_property_locations_city ON public.property_locations(city);

-- Property Amenities
CREATE INDEX idx_property_amenities_property_id ON public.property_amenities(property_id);
CREATE INDEX idx_property_amenities_type ON public.property_amenities(type);
CREATE INDEX idx_property_amenities_distance ON public.property_amenities(distance);

-- Social Media
CREATE INDEX idx_user_social_profiles_user_id ON public.user_social_profiles(user_id);
CREATE INDEX idx_user_social_profiles_provider ON public.user_social_profiles(provider);
CREATE INDEX idx_social_sharing_logs_property_id ON public.social_sharing_logs(property_id);
CREATE INDEX idx_social_sharing_logs_platform ON public.social_sharing_logs(platform, created_at DESC);
CREATE INDEX idx_user_generated_content_property_id ON public.user_generated_content(property_id);
CREATE INDEX idx_user_generated_content_user_id ON public.user_generated_content(user_id);

-- Documents
CREATE INDEX idx_generated_documents_template_id ON public.generated_documents(template_id);
CREATE INDEX idx_generated_documents_property_id ON public.generated_documents(property_id);
CREATE INDEX idx_generated_documents_status ON public.generated_documents(status);
CREATE INDEX idx_document_signatures_document_id ON public.document_signatures(document_id);
CREATE INDEX idx_document_signatures_signer_id ON public.document_signatures(signer_id);

-- Backup & Sync
CREATE INDEX idx_backup_jobs_config_id ON public.backup_jobs(config_id);
CREATE INDEX idx_backup_jobs_status ON public.backup_jobs(status, start_time DESC);
CREATE INDEX idx_sync_jobs_config_id ON public.sync_jobs(config_id);
CREATE INDEX idx_sync_jobs_status ON public.sync_jobs(status, start_time DESC);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Property Locations
ALTER TABLE public.property_locations ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view property locations" ON public.property_locations
  FOR SELECT USING (true);
CREATE POLICY "Property owners can manage locations" ON public.property_locations
  FOR ALL TO authenticated
  USING (property_id IN (
    SELECT id FROM public.properties WHERE created_by = auth.uid()
  ));

-- User Social Profiles
ALTER TABLE public.user_social_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage their own social profiles" ON public.user_social_profiles
  FOR ALL TO authenticated
  USING (user_id = auth.uid());

-- User Generated Content
ALTER TABLE public.user_generated_content ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view approved content" ON public.user_generated_content
  FOR SELECT USING (moderation_status = 'approved');
CREATE POLICY "Users can manage their own content" ON public.user_generated_content
  FOR ALL TO authenticated
  USING (user_id = auth.uid());

-- Generated Documents
ALTER TABLE public.generated_documents ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can access their documents" ON public.generated_documents
  FOR ALL TO authenticated
  USING (
    created_by = auth.uid() OR
    tenant_id = auth.uid() OR
    landlord_id = auth.uid() OR
    agent_id = auth.uid()
  );

-- Document Signatures
ALTER TABLE public.document_signatures ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can access their signatures" ON public.document_signatures
  FOR ALL TO authenticated
  USING (signer_id = auth.uid());

-- Backup Configurations
ALTER TABLE public.backup_configs ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Admins can manage backup configs" ON public.backup_configs
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Updated at triggers
CREATE TRIGGER set_updated_at_property_locations
  BEFORE UPDATE ON public.property_locations
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_property_amenities
  BEFORE UPDATE ON public.property_amenities
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_user_social_profiles
  BEFORE UPDATE ON public.user_social_profiles
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_user_generated_content
  BEFORE UPDATE ON public.user_generated_content
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_social_campaigns
  BEFORE UPDATE ON public.social_campaigns
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_document_templates
  BEFORE UPDATE ON public.document_templates
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_generated_documents
  BEFORE UPDATE ON public.generated_documents
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_document_signatures
  BEFORE UPDATE ON public.document_signatures
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_backup_configs
  BEFORE UPDATE ON public.backup_configs
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_sync_configs
  BEFORE UPDATE ON public.sync_configs
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_api_keys
  BEFORE UPDATE ON public.api_keys
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- =====================================================
-- INITIAL DATA
-- =====================================================

-- Insert default social platforms
INSERT INTO public.social_platforms (id, name, icon, color, share_url, api_endpoint) VALUES
('facebook', 'Facebook', '📘', '#1877F2', 'https://www.facebook.com/sharer/sharer.php', 'https://graph.facebook.com/v18.0'),
('twitter', 'Twitter', '🐦', '#1DA1F2', 'https://twitter.com/intent/tweet', 'https://api.twitter.com/2'),
('instagram', 'Instagram', '📷', '#E4405F', 'https://www.instagram.com', 'https://graph.instagram.com'),
('linkedin', 'LinkedIn', '💼', '#0A66C2', 'https://www.linkedin.com/sharing/share-offsite', 'https://api.linkedin.com/v2'),
('whatsapp', 'WhatsApp', '💬', '#25D366', 'https://wa.me', 'https://graph.facebook.com/v18.0'),
('telegram', 'Telegram', '✈️', '#0088CC', 'https://t.me/share/url', NULL)
ON CONFLICT (id) DO NOTHING;

-- Insert default document templates
INSERT INTO public.document_templates (id, name, type, category, template, variables) VALUES
(
  gen_random_uuid(),
  'Nigerian Rental Agreement',
  'rental_agreement',
  'legal',
  '<div class="document"><h1>RENTAL AGREEMENT</h1><p>Date: {{current_date}}</p></div>',
  '[{"name": "landlord_name", "type": "text", "label": "Landlord Name", "required": true}]'::jsonb
),
(
  gen_random_uuid(),
  'Property Inspection Report',
  'property_inspection',
  'administrative',
  '<div class="document"><h1>PROPERTY INSPECTION REPORT</h1><p>Date: {{current_date}}</p></div>',
  '[{"name": "property_address", "type": "text", "label": "Property Address", "required": true}]'::jsonb
)
ON CONFLICT (name) DO NOTHING;

-- Insert default backup configuration
INSERT INTO public.backup_configs (id, name, description, tables, schedule, retention, destination) VALUES
(
  gen_random_uuid(),
  'Daily Full Backup',
  'Complete daily backup of all critical tables',
  ARRAY['properties', 'profiles', 'rental_applications', 'payments', 'messages'],
  '{"frequency": "daily", "time": "02:00", "timezone": "Africa/Lagos"}'::jsonb,
  '{"keepDaily": 7, "keepWeekly": 4, "keepMonthly": 12, "keepYearly": 2}'::jsonb,
  '{"type": "supabase", "config": {}}'::jsonb
)
ON CONFLICT (name) DO NOTHING;
