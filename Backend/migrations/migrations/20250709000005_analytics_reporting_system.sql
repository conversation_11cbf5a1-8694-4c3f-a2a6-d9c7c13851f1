-- =====================================================
-- ANALYTICS & REPORTING SYSTEM DATABASE SCHEMA
-- Comprehensive database support for analytics and reporting
-- =====================================================

-- Custom Dashboards Table
CREATE TABLE IF NOT EXISTS public.custom_dashboards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  layout JSONB NOT NULL DEFAULT '{"widgets": []}',
  filters JSONB DEFAULT '{}',
  refresh_interval INTEGER DEFAULT 300, -- seconds
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Alert Rules Table
CREATE TABLE IF NOT EXISTS public.alert_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  metric TEXT NOT NULL,
  condition TEXT NOT NULL CHECK (condition IN ('greater_than', 'less_than', 'equals', 'percentage_change')),
  threshold DECIMAL NOT NULL,
  timeframe TEXT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  notification_channels TEXT[] DEFAULT '{}',
  recipients TEXT[] DEFAULT '{}',
  last_triggered TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification Alerts Table
CREATE TABLE IF NOT EXISTS public.notification_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_rule_id UUID NOT NULL REFERENCES public.alert_rules(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  metric_value DECIMAL NOT NULL,
  threshold_value DECIMAL NOT NULL,
  triggered_at TIMESTAMPTZ DEFAULT NOW(),
  acknowledged BOOLEAN DEFAULT FALSE,
  acknowledged_by UUID REFERENCES auth.users(id),
  acknowledged_at TIMESTAMPTZ
);

-- Report Configurations Table
CREATE TABLE IF NOT EXISTS public.report_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK (type IN ('agent_performance', 'property_analysis', 'market_trends', 'financial', 'custom')),
  template_id UUID,
  data_sources TEXT[] NOT NULL,
  filters JSONB NOT NULL DEFAULT '{}',
  visualizations JSONB NOT NULL DEFAULT '[]',
  schedule JSONB,
  sharing JSONB DEFAULT '{"is_public": false, "shared_with": [], "access_level": "view"}',
  created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Generated Reports Table
CREATE TABLE IF NOT EXISTS public.generated_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  config_id UUID NOT NULL REFERENCES public.report_configs(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  format TEXT NOT NULL CHECK (format IN ('pdf', 'excel', 'csv', 'html')),
  file_url TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  generation_time INTEGER NOT NULL, -- milliseconds
  data_points INTEGER NOT NULL,
  generated_at TIMESTAMPTZ DEFAULT NOW(),
  generated_by UUID NOT NULL REFERENCES auth.users(id),
  download_count INTEGER DEFAULT 0,
  expires_at TIMESTAMPTZ
);

-- Report Templates Table
CREATE TABLE IF NOT EXISTS public.report_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL,
  layout JSONB NOT NULL,
  default_filters JSONB DEFAULT '{}',
  is_system_template BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Performance Metrics Table
CREATE TABLE IF NOT EXISTS public.performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  entity_id UUID NOT NULL,
  entity_type TEXT NOT NULL CHECK (entity_type IN ('agent', 'property', 'market', 'user')),
  metric_name TEXT NOT NULL,
  metric_value DECIMAL NOT NULL,
  metric_unit TEXT,
  calculation_date DATE NOT NULL,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  calculation_method TEXT,
  confidence_level DECIMAL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Analytics Events Table
CREATE TABLE IF NOT EXISTS public.analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id UUID NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  properties JSONB NOT NULL DEFAULT '{}',
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  session_id TEXT,
  ip_address INET,
  user_agent TEXT
);

-- KPI Definitions Table
CREATE TABLE IF NOT EXISTS public.kpi_definitions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  category TEXT NOT NULL,
  data_type TEXT NOT NULL CHECK (data_type IN ('number', 'percentage', 'currency', 'duration', 'count')),
  calculation_method TEXT NOT NULL,
  unit TEXT,
  format TEXT,
  is_kpi BOOLEAN DEFAULT TRUE,
  target_value DECIMAL,
  benchmark_value DECIMAL,
  trend_direction TEXT CHECK (trend_direction IN ('higher_better', 'lower_better', 'neutral')),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Performance Goals Table
CREATE TABLE IF NOT EXISTS public.performance_goals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  entity_id UUID NOT NULL,
  entity_type TEXT NOT NULL CHECK (entity_type IN ('agent', 'property', 'team', 'company')),
  metric TEXT NOT NULL,
  target_value DECIMAL NOT NULL,
  current_value DECIMAL DEFAULT 0,
  progress_percentage DECIMAL DEFAULT 0,
  target_date DATE NOT NULL,
  status TEXT DEFAULT 'on_track' CHECK (status IN ('on_track', 'at_risk', 'behind', 'achieved')),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Report Schedules Table
CREATE TABLE IF NOT EXISTS public.report_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  report_config_id UUID NOT NULL REFERENCES public.report_configs(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  frequency TEXT NOT NULL CHECK (frequency IN ('daily', 'weekly', 'monthly', 'quarterly')),
  schedule_time TIME NOT NULL,
  timezone TEXT DEFAULT 'UTC',
  recipients JSONB NOT NULL DEFAULT '[]',
  format TEXT NOT NULL CHECK (format IN ('pdf', 'excel', 'csv', 'email')),
  is_active BOOLEAN DEFAULT TRUE,
  last_run TIMESTAMPTZ,
  next_run TIMESTAMPTZ,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Analytics Audit Log Table
CREATE TABLE IF NOT EXISTS public.analytics_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  action TEXT NOT NULL CHECK (action IN ('view', 'create', 'update', 'delete', 'export', 'share')),
  resource_type TEXT NOT NULL CHECK (resource_type IN ('dashboard', 'report', 'chart', 'data')),
  resource_id UUID NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  details JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Market Trend Data Table
CREATE TABLE IF NOT EXISTS public.market_trend_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location TEXT NOT NULL,
  property_type TEXT,
  date DATE NOT NULL,
  average_price DECIMAL,
  median_price DECIMAL,
  volume INTEGER,
  days_on_market_avg DECIMAL,
  demand_score DECIMAL,
  supply_score DECIMAL,
  price_per_sqft DECIMAL,
  absorption_rate DECIMAL,
  inventory_level INTEGER,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(location, property_type, date)
);

-- Enable RLS on all tables
ALTER TABLE public.custom_dashboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alert_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.report_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.generated_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.report_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kpi_definitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.performance_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.report_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.market_trend_data ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- RLS POLICIES
-- =====================================================

-- Custom Dashboards Policies
CREATE POLICY "Users can manage their own dashboards" ON public.custom_dashboards
  FOR ALL TO authenticated
  USING (user_id = auth.uid() OR is_public = true);

-- Alert Rules Policies
CREATE POLICY "Users can manage their own alert rules" ON public.alert_rules
  FOR ALL TO authenticated
  USING (true); -- Adjust based on your access control needs

-- Notification Alerts Policies
CREATE POLICY "Users can view relevant alerts" ON public.notification_alerts
  FOR SELECT TO authenticated
  USING (
    alert_rule_id IN (
      SELECT id FROM public.alert_rules 
      WHERE recipients @> ARRAY[auth.email()]
    )
  );

-- Report Configs Policies
CREATE POLICY "Users can manage their own report configs" ON public.report_configs
  FOR ALL TO authenticated
  USING (created_by = auth.uid() OR sharing->>'is_public' = 'true');

-- Generated Reports Policies
CREATE POLICY "Users can access their own reports" ON public.generated_reports
  FOR SELECT TO authenticated
  USING (
    generated_by = auth.uid() OR 
    config_id IN (
      SELECT id FROM public.report_configs 
      WHERE created_by = auth.uid() OR sharing->>'is_public' = 'true'
    )
  );

-- Report Templates Policies
CREATE POLICY "Anyone can view templates" ON public.report_templates
  FOR SELECT USING (true);

CREATE POLICY "Users can create custom templates" ON public.report_templates
  FOR INSERT TO authenticated
  WITH CHECK (created_by = auth.uid());

-- Performance Metrics Policies
CREATE POLICY "Users can view relevant performance metrics" ON public.performance_metrics
  FOR SELECT TO authenticated
  USING (true); -- Adjust based on your access control needs

-- Analytics Events Policies
CREATE POLICY "Users can view their own events" ON public.analytics_events
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "System can insert events" ON public.analytics_events
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- KPI Definitions Policies
CREATE POLICY "Anyone can view KPI definitions" ON public.kpi_definitions
  FOR SELECT USING (true);

CREATE POLICY "Admins can manage KPI definitions" ON public.kpi_definitions
  FOR ALL TO authenticated
  USING (public.is_admin());

-- Performance Goals Policies
CREATE POLICY "Users can manage relevant goals" ON public.performance_goals
  FOR ALL TO authenticated
  USING (created_by = auth.uid() OR entity_id = auth.uid());

-- Report Schedules Policies
CREATE POLICY "Users can manage their own schedules" ON public.report_schedules
  FOR ALL TO authenticated
  USING (created_by = auth.uid());

-- Analytics Audit Log Policies
CREATE POLICY "Users can view their own audit logs" ON public.analytics_audit_log
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "System can insert audit logs" ON public.analytics_audit_log
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- Market Trend Data Policies
CREATE POLICY "Anyone can view market trend data" ON public.market_trend_data
  FOR SELECT USING (true);

CREATE POLICY "System can manage market trend data" ON public.market_trend_data
  FOR ALL TO authenticated
  USING (public.is_admin());

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Custom Dashboards Indexes
CREATE INDEX idx_custom_dashboards_user_id ON public.custom_dashboards(user_id);
CREATE INDEX idx_custom_dashboards_public ON public.custom_dashboards(is_public) WHERE is_public = true;

-- Alert Rules Indexes
CREATE INDEX idx_alert_rules_active ON public.alert_rules(is_active) WHERE is_active = true;
CREATE INDEX idx_alert_rules_metric ON public.alert_rules(metric);

-- Notification Alerts Indexes
CREATE INDEX idx_notification_alerts_rule_id ON public.notification_alerts(alert_rule_id);
CREATE INDEX idx_notification_alerts_triggered ON public.notification_alerts(triggered_at DESC);
CREATE INDEX idx_notification_alerts_unacknowledged ON public.notification_alerts(acknowledged) WHERE acknowledged = false;

-- Report Configs Indexes
CREATE INDEX idx_report_configs_created_by ON public.report_configs(created_by);
CREATE INDEX idx_report_configs_type ON public.report_configs(type);
CREATE INDEX idx_report_configs_updated ON public.report_configs(updated_at DESC);

-- Generated Reports Indexes
CREATE INDEX idx_generated_reports_config_id ON public.generated_reports(config_id);
CREATE INDEX idx_generated_reports_generated_by ON public.generated_reports(generated_by);
CREATE INDEX idx_generated_reports_generated_at ON public.generated_reports(generated_at DESC);

-- Performance Metrics Indexes
CREATE INDEX idx_performance_metrics_entity ON public.performance_metrics(entity_id, entity_type);
CREATE INDEX idx_performance_metrics_metric ON public.performance_metrics(metric_name);
CREATE INDEX idx_performance_metrics_date ON public.performance_metrics(calculation_date DESC);
CREATE INDEX idx_performance_metrics_period ON public.performance_metrics(period_start, period_end);

-- Analytics Events Indexes
CREATE INDEX idx_analytics_events_user_id ON public.analytics_events(user_id, timestamp DESC);
CREATE INDEX idx_analytics_events_entity ON public.analytics_events(entity_id, entity_type);
CREATE INDEX idx_analytics_events_type ON public.analytics_events(event_type, timestamp DESC);
CREATE INDEX idx_analytics_events_session ON public.analytics_events(session_id);

-- Performance Goals Indexes
CREATE INDEX idx_performance_goals_entity ON public.performance_goals(entity_id, entity_type);
CREATE INDEX idx_performance_goals_created_by ON public.performance_goals(created_by);
CREATE INDEX idx_performance_goals_status ON public.performance_goals(status);
CREATE INDEX idx_performance_goals_target_date ON public.performance_goals(target_date);

-- Market Trend Data Indexes
CREATE INDEX idx_market_trend_data_location ON public.market_trend_data(location, property_type);
CREATE INDEX idx_market_trend_data_date ON public.market_trend_data(date DESC);
CREATE INDEX idx_market_trend_data_unique ON public.market_trend_data(location, property_type, date);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Updated at triggers
CREATE TRIGGER set_updated_at_custom_dashboards
  BEFORE UPDATE ON public.custom_dashboards
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_report_configs
  BEFORE UPDATE ON public.report_configs
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_performance_goals
  BEFORE UPDATE ON public.performance_goals
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- =====================================================
-- REALTIME SUBSCRIPTIONS
-- =====================================================

-- Enable realtime for analytics features
ALTER PUBLICATION supabase_realtime ADD TABLE public.notification_alerts;
ALTER PUBLICATION supabase_realtime ADD TABLE public.performance_metrics;
ALTER PUBLICATION supabase_realtime ADD TABLE public.analytics_events;

-- =====================================================
-- ANALYTICS FUNCTIONS
-- =====================================================

-- Function to get property overview metrics
CREATE OR REPLACE FUNCTION get_property_overview(
  start_date TEXT,
  end_date TEXT,
  location_filters TEXT[] DEFAULT NULL,
  property_type_filters TEXT[] DEFAULT NULL
)
RETURNS TABLE (
  total_properties INTEGER,
  active_properties INTEGER,
  total_inquiries INTEGER,
  conversion_rate DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  WITH property_stats AS (
    SELECT
      COUNT(*) as total_props,
      COUNT(CASE WHEN is_available = true THEN 1 END) as active_props
    FROM properties p
    WHERE (location_filters IS NULL OR p.location = ANY(location_filters))
      AND (property_type_filters IS NULL OR p.property_type = ANY(property_type_filters))
  ),
  inquiry_stats AS (
    SELECT
      COUNT(*) as total_inq,
      COUNT(CASE WHEN status = 'converted' THEN 1 END) as converted_inq
    FROM property_inquiries pi
    JOIN properties p ON pi.property_id = p.id
    WHERE pi.created_at >= start_date::TIMESTAMPTZ
      AND pi.created_at <= end_date::TIMESTAMPTZ
      AND (location_filters IS NULL OR p.location = ANY(location_filters))
      AND (property_type_filters IS NULL OR p.property_type = ANY(property_type_filters))
  )
  SELECT
    ps.total_props::INTEGER,
    ps.active_props::INTEGER,
    COALESCE(iq.total_inq, 0)::INTEGER,
    CASE
      WHEN COALESCE(iq.total_inq, 0) > 0
      THEN ROUND((iq.converted_inq::DECIMAL / iq.total_inq * 100), 2)
      ELSE 0
    END
  FROM property_stats ps
  CROSS JOIN inquiry_stats iq;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get agent overview metrics
CREATE OR REPLACE FUNCTION get_agent_overview(
  start_date TEXT,
  end_date TEXT,
  agent_filters TEXT[] DEFAULT NULL
)
RETURNS TABLE (
  total_agents INTEGER,
  active_agents INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::INTEGER as total_agents,
    COUNT(CASE WHEN is_active = true THEN 1 END)::INTEGER as active_agents
  FROM agent_profiles ap
  WHERE (agent_filters IS NULL OR ap.agent_id = ANY(agent_filters::UUID[]));
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get financial overview metrics
CREATE OR REPLACE FUNCTION get_financial_overview(
  start_date TEXT,
  end_date TEXT
)
RETURNS TABLE (
  revenue_this_month DECIMAL,
  revenue_growth DECIMAL
) AS $$
DECLARE
  current_revenue DECIMAL := 0;
  previous_revenue DECIMAL := 0;
BEGIN
  -- Get current period revenue
  SELECT COALESCE(SUM(amount), 0) INTO current_revenue
  FROM payments
  WHERE created_at >= start_date::TIMESTAMPTZ
    AND created_at <= end_date::TIMESTAMPTZ
    AND status = 'completed';

  -- Get previous period revenue for comparison
  SELECT COALESCE(SUM(amount), 0) INTO previous_revenue
  FROM payments
  WHERE created_at >= (start_date::TIMESTAMPTZ - INTERVAL '1 month')
    AND created_at <= (end_date::TIMESTAMPTZ - INTERVAL '1 month')
    AND status = 'completed';

  RETURN QUERY
  SELECT
    current_revenue,
    CASE
      WHEN previous_revenue > 0
      THEN ROUND(((current_revenue - previous_revenue) / previous_revenue * 100), 2)
      ELSE 0
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get real-time metrics
CREATE OR REPLACE FUNCTION get_realtime_metrics()
RETURNS TABLE (
  active_users INTEGER,
  properties_viewed_today INTEGER,
  inquiries_today INTEGER,
  new_registrations_today INTEGER,
  response_time_avg DECIMAL,
  error_rate DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  WITH today_stats AS (
    SELECT
      COUNT(DISTINCT user_id) FILTER (WHERE timestamp >= CURRENT_DATE) as active_users_today,
      COUNT(*) FILTER (WHERE event_type = 'property_view' AND timestamp >= CURRENT_DATE) as views_today,
      COUNT(*) FILTER (WHERE event_type = 'inquiry_created' AND timestamp >= CURRENT_DATE) as inquiries_today
    FROM analytics_events
    WHERE timestamp >= CURRENT_DATE
  ),
  user_stats AS (
    SELECT COUNT(*) as new_users_today
    FROM auth.users
    WHERE created_at >= CURRENT_DATE
  )
  SELECT
    COALESCE(ts.active_users_today, 0)::INTEGER,
    COALESCE(ts.views_today, 0)::INTEGER,
    COALESCE(ts.inquiries_today, 0)::INTEGER,
    COALESCE(us.new_users_today, 0)::INTEGER,
    500.0::DECIMAL, -- Placeholder for response time
    0.01::DECIMAL   -- Placeholder for error rate
  FROM today_stats ts
  CROSS JOIN user_stats us;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get trend metrics
CREATE OR REPLACE FUNCTION get_trend_metrics(
  start_date TEXT,
  end_date TEXT,
  filters JSONB DEFAULT '{}'
)
RETURNS TABLE (
  property_views JSONB,
  inquiries JSONB,
  revenue JSONB,
  user_growth JSONB
) AS $$
BEGIN
  RETURN QUERY
  WITH daily_views AS (
    SELECT
      DATE(timestamp) as date,
      COUNT(*) as count
    FROM analytics_events
    WHERE event_type = 'property_view'
      AND timestamp >= start_date::TIMESTAMPTZ
      AND timestamp <= end_date::TIMESTAMPTZ
    GROUP BY DATE(timestamp)
    ORDER BY date
  ),
  daily_inquiries AS (
    SELECT
      DATE(created_at) as date,
      COUNT(*) as count
    FROM property_inquiries
    WHERE created_at >= start_date::TIMESTAMPTZ
      AND created_at <= end_date::TIMESTAMPTZ
    GROUP BY DATE(created_at)
    ORDER BY date
  ),
  daily_revenue AS (
    SELECT
      DATE(created_at) as date,
      SUM(amount) as amount
    FROM payments
    WHERE created_at >= start_date::TIMESTAMPTZ
      AND created_at <= end_date::TIMESTAMPTZ
      AND status = 'completed'
    GROUP BY DATE(created_at)
    ORDER BY date
  ),
  daily_users AS (
    SELECT
      DATE(created_at) as date,
      COUNT(*) as count
    FROM auth.users
    WHERE created_at >= start_date::TIMESTAMPTZ
      AND created_at <= end_date::TIMESTAMPTZ
    GROUP BY DATE(created_at)
    ORDER BY date
  )
  SELECT
    COALESCE(jsonb_agg(jsonb_build_object('date', dv.date, 'count', dv.count)), '[]'::jsonb),
    COALESCE(jsonb_agg(jsonb_build_object('date', di.date, 'count', di.count)), '[]'::jsonb),
    COALESCE(jsonb_agg(jsonb_build_object('date', dr.date, 'amount', dr.amount)), '[]'::jsonb),
    COALESCE(jsonb_agg(jsonb_build_object('date', du.date, 'count', du.count)), '[]'::jsonb)
  FROM daily_views dv
  FULL OUTER JOIN daily_inquiries di ON dv.date = di.date
  FULL OUTER JOIN daily_revenue dr ON dv.date = dr.date
  FULL OUTER JOIN daily_users du ON dv.date = du.date;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate property performance
CREATE OR REPLACE FUNCTION calculate_property_performance(
  property_id UUID,
  start_date TEXT,
  end_date TEXT
)
RETURNS TABLE (
  total_views INTEGER,
  unique_views INTEGER,
  view_to_inquiry_rate DECIMAL,
  inquiry_to_visit_rate DECIMAL,
  visit_to_application_rate DECIMAL,
  application_to_lease_rate DECIMAL,
  overall_conversion_rate DECIMAL,
  average_time_on_market INTEGER,
  price_competitiveness DECIMAL,
  engagement_score DECIMAL,
  quality_score DECIMAL,
  comparable_performance JSONB
) AS $$
DECLARE
  views_count INTEGER := 0;
  unique_views_count INTEGER := 0;
  inquiries_count INTEGER := 0;
  visits_count INTEGER := 0;
  applications_count INTEGER := 0;
  leases_count INTEGER := 0;
BEGIN
  -- Get view metrics
  SELECT
    COUNT(*),
    COUNT(DISTINCT user_id)
  INTO views_count, unique_views_count
  FROM analytics_events
  WHERE entity_id = property_id
    AND event_type = 'property_view'
    AND timestamp >= start_date::TIMESTAMPTZ
    AND timestamp <= end_date::TIMESTAMPTZ;

  -- Get inquiry metrics
  SELECT COUNT(*) INTO inquiries_count
  FROM property_inquiries
  WHERE property_id = calculate_property_performance.property_id
    AND created_at >= start_date::TIMESTAMPTZ
    AND created_at <= end_date::TIMESTAMPTZ;

  -- Calculate conversion rates
  RETURN QUERY
  SELECT
    COALESCE(views_count, 0),
    COALESCE(unique_views_count, 0),
    CASE WHEN views_count > 0 THEN ROUND((inquiries_count::DECIMAL / views_count * 100), 2) ELSE 0 END,
    CASE WHEN inquiries_count > 0 THEN ROUND((visits_count::DECIMAL / inquiries_count * 100), 2) ELSE 0 END,
    CASE WHEN visits_count > 0 THEN ROUND((applications_count::DECIMAL / visits_count * 100), 2) ELSE 0 END,
    CASE WHEN applications_count > 0 THEN ROUND((leases_count::DECIMAL / applications_count * 100), 2) ELSE 0 END,
    CASE WHEN views_count > 0 THEN ROUND((leases_count::DECIMAL / views_count * 100), 2) ELSE 0 END,
    30, -- Placeholder for average time on market
    0.85::DECIMAL, -- Placeholder for price competitiveness
    CASE WHEN views_count > 0 THEN LEAST(100, views_count * 10) ELSE 0 END::DECIMAL,
    75.0::DECIMAL, -- Placeholder for quality score
    '{"vs_similar_properties": 0, "vs_location_average": 0, "vs_type_average": 0}'::JSONB;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate market performance
CREATE OR REPLACE FUNCTION calculate_market_performance(
  location_filter TEXT,
  property_type_filter TEXT DEFAULT NULL,
  start_date TEXT DEFAULT NULL,
  end_date TEXT DEFAULT NULL
)
RETURNS TABLE (
  total_properties INTEGER,
  active_listings INTEGER,
  average_price DECIMAL,
  median_price DECIMAL,
  price_per_sqft DECIMAL,
  days_on_market_avg DECIMAL,
  absorption_rate DECIMAL,
  inventory_level INTEGER,
  demand_supply_ratio DECIMAL,
  market_velocity DECIMAL,
  price_growth DECIMAL,
  volume_growth DECIMAL,
  demand_growth DECIMAL,
  seasonal_factors JSONB
) AS $$
BEGIN
  RETURN QUERY
  WITH market_stats AS (
    SELECT
      COUNT(*) as total_props,
      COUNT(CASE WHEN is_available = true THEN 1 END) as active_props,
      AVG(price_per_year) as avg_price,
      PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY price_per_year) as med_price,
      AVG(CASE WHEN area_sqft > 0 THEN price_per_year / area_sqft ELSE NULL END) as price_sqft
    FROM properties
    WHERE location ILIKE '%' || location_filter || '%'
      AND (property_type_filter IS NULL OR property_type = property_type_filter)
  )
  SELECT
    ms.total_props::INTEGER,
    ms.active_props::INTEGER,
    ROUND(COALESCE(ms.avg_price, 0), 2),
    ROUND(COALESCE(ms.med_price, 0), 2),
    ROUND(COALESCE(ms.price_sqft, 0), 2),
    30.0::DECIMAL, -- Placeholder for days on market
    0.15::DECIMAL, -- Placeholder for absorption rate
    ms.active_props::INTEGER,
    1.2::DECIMAL,  -- Placeholder for demand/supply ratio
    0.8::DECIMAL,  -- Placeholder for market velocity
    5.2::DECIMAL,  -- Placeholder for price growth
    12.5::DECIMAL, -- Placeholder for volume growth
    8.3::DECIMAL,  -- Placeholder for demand growth
    '{}'::JSONB    -- Placeholder for seasonal factors
  FROM market_stats ms;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to execute report queries
CREATE OR REPLACE FUNCTION execute_report_query(
  query TEXT,
  filters JSONB DEFAULT '{}'
)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  -- This is a simplified version - in production, you'd want more sophisticated query execution
  -- For now, return sample data based on query type
  IF query ILIKE '%property%performance%' THEN
    result := '[{"property_id": "123", "views": 150, "inquiries": 12, "conversion_rate": 8.0}]'::JSONB;
  ELSIF query ILIKE '%agent%performance%' THEN
    result := '[{"agent_id": "456", "properties_managed": 25, "revenue": 50000, "satisfaction": 4.5}]'::JSONB;
  ELSE
    result := '[]'::JSONB;
  END IF;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get metric value for alerts
CREATE OR REPLACE FUNCTION get_metric_value(
  metric_name TEXT,
  timeframe TEXT
)
RETURNS TABLE (
  value DECIMAL,
  previous_value DECIMAL
) AS $$
BEGIN
  -- Simplified metric calculation - expand based on your needs
  CASE metric_name
    WHEN 'daily_inquiries' THEN
      RETURN QUERY
      SELECT
        COUNT(*)::DECIMAL as current_val,
        0::DECIMAL as prev_val
      FROM property_inquiries
      WHERE created_at >= CURRENT_DATE;
    WHEN 'conversion_rate' THEN
      RETURN QUERY
      SELECT
        15.5::DECIMAL as current_val,
        14.2::DECIMAL as prev_val;
    ELSE
      RETURN QUERY
      SELECT
        0::DECIMAL as current_val,
        0::DECIMAL as prev_val;
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions on analytics functions
GRANT EXECUTE ON FUNCTION get_property_overview(TEXT, TEXT, TEXT[], TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION get_agent_overview(TEXT, TEXT, TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION get_financial_overview(TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_realtime_metrics() TO authenticated;
GRANT EXECUTE ON FUNCTION get_trend_metrics(TEXT, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_property_performance(UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_market_performance(TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION execute_report_query(TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION get_metric_value(TEXT, TEXT) TO authenticated;

-- Grant permissions on analytics tables
GRANT SELECT, INSERT, UPDATE ON public.custom_dashboards TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.alert_rules TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.notification_alerts TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.report_configs TO authenticated;
GRANT SELECT ON public.generated_reports TO authenticated;
GRANT SELECT ON public.report_templates TO authenticated;
GRANT SELECT ON public.performance_metrics TO authenticated;
GRANT SELECT, INSERT ON public.analytics_events TO authenticated;
GRANT SELECT ON public.kpi_definitions TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.performance_goals TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.report_schedules TO authenticated;
GRANT SELECT, INSERT ON public.analytics_audit_log TO authenticated;
GRANT SELECT ON public.market_trend_data TO authenticated;
