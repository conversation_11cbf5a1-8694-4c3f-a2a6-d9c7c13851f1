-- =====================================================
-- PERFORMANCE OPTIMIZATION DATABASE SCHEMA
-- Advanced indexing, query optimization, and monitoring
-- =====================================================

-- Query Performance Monitoring Table
CREATE TABLE IF NOT EXISTS public.query_performance_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  query_hash TEXT NOT NULL,
  query_text TEXT NOT NULL,
  execution_time_ms INTEGER NOT NULL,
  rows_examined INTEGER,
  rows_returned INTEGER,
  index_usage JSONB DEFAULT '{}',
  user_id UUID REFERENCES auth.users(id),
  endpoint TEXT,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  slow_query BOOLEAN GENERATED ALWAYS AS (execution_time_ms > 1000) STORED
);

-- Database Connection Pool Monitoring
CREATE TABLE IF NOT EXISTS public.connection_pool_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  pool_name TEXT NOT NULL,
  active_connections INTEGER NOT NULL,
  idle_connections INTEGER NOT NULL,
  waiting_connections INTEGER NOT NULL,
  max_connections INTEGER NOT NULL,
  cpu_usage DECIMAL(5,2),
  memory_usage DECIMAL(5,2),
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Cache Performance Metrics
CREATE TABLE IF NOT EXISTS public.cache_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cache_type TEXT NOT NULL, -- 'redis', 'browser', 'cdn', 'database'
  cache_key TEXT NOT NULL,
  operation TEXT NOT NULL, -- 'hit', 'miss', 'set', 'delete', 'expire'
  response_time_ms INTEGER,
  data_size_bytes INTEGER,
  ttl_seconds INTEGER,
  user_id UUID REFERENCES auth.users(id),
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Performance Budgets
CREATE TABLE IF NOT EXISTS public.performance_budgets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name TEXT NOT NULL,
  budget_value INTEGER NOT NULL,
  current_value INTEGER,
  threshold_warning INTEGER,
  threshold_critical INTEGER,
  page_path TEXT,
  device_type TEXT CHECK (device_type IN ('mobile', 'desktop', 'tablet')),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Core Web Vitals Tracking
CREATE TABLE IF NOT EXISTS public.web_vitals_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  session_id TEXT NOT NULL,
  page_url TEXT NOT NULL,
  device_type TEXT,
  connection_type TEXT,
  
  -- Core Web Vitals
  lcp_value DECIMAL(8,2), -- Largest Contentful Paint
  fid_value DECIMAL(8,2), -- First Input Delay
  cls_value DECIMAL(8,4), -- Cumulative Layout Shift
  
  -- Additional Metrics
  fcp_value DECIMAL(8,2), -- First Contentful Paint
  ttfb_value DECIMAL(8,2), -- Time to First Byte
  
  -- Performance Scores
  performance_score INTEGER CHECK (performance_score >= 0 AND performance_score <= 100),
  
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Image Optimization Tracking
CREATE TABLE IF NOT EXISTS public.image_optimization_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  original_url TEXT NOT NULL,
  optimized_url TEXT NOT NULL,
  original_size_bytes INTEGER NOT NULL,
  optimized_size_bytes INTEGER NOT NULL,
  compression_ratio DECIMAL(5,2) GENERATED ALWAYS AS (
    ROUND((1.0 - (optimized_size_bytes::DECIMAL / original_size_bytes::DECIMAL)) * 100, 2)
  ) STORED,
  format_original TEXT NOT NULL,
  format_optimized TEXT NOT NULL,
  optimization_type TEXT NOT NULL, -- 'compression', 'format_conversion', 'resize'
  processing_time_ms INTEGER,
  cdn_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- ADVANCED PERFORMANCE INDEXES
-- =====================================================

-- Properties table optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_search_optimized 
ON public.properties USING GIN (
  to_tsvector('english', title || ' ' || description || ' ' || location)
);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_location_price 
ON public.properties (location, price) 
WHERE status = 'available';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_type_bedrooms_price 
ON public.properties (property_type, bedrooms, price) 
WHERE status = 'available';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_created_status 
ON public.properties (created_at DESC, status) 
WHERE status IN ('available', 'pending');

-- Composite index for property filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_properties_filter_composite 
ON public.properties (status, property_type, location, price, bedrooms, bathrooms)
WHERE status = 'available';

-- Rental applications optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rental_applications_property_status 
ON public.rental_applications (property_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rental_applications_user_status 
ON public.rental_applications (user_id, status, created_at DESC);

-- Messages optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_conversation_timestamp 
ON public.messages (conversation_id, created_at DESC)
WHERE is_deleted = FALSE;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_unread 
ON public.messages (recipient_id, is_read, created_at DESC)
WHERE is_read = FALSE AND is_deleted = FALSE;

-- Payments optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_user_status_date 
ON public.payments (user_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_property_status 
ON public.payments (property_id, status, payment_date DESC);

-- Property views optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_property_views_property_date 
ON public.property_views (property_id, viewed_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_property_views_user_date 
ON public.property_views (user_id, viewed_at DESC);

-- Notifications optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_unread 
ON public.notifications (user_id, is_read, created_at DESC)
WHERE is_read = FALSE;

-- Performance monitoring indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_query_performance_slow 
ON public.query_performance_logs (timestamp DESC, execution_time_ms DESC)
WHERE slow_query = TRUE;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_query_performance_hash 
ON public.query_performance_logs (query_hash, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_web_vitals_page_device 
ON public.web_vitals_metrics (page_url, device_type, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cache_metrics_type_operation 
ON public.cache_metrics (cache_type, operation, timestamp DESC);

-- =====================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- =====================================================

-- Property statistics materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS public.property_stats_mv AS
SELECT 
  location,
  property_type,
  COUNT(*) as total_properties,
  COUNT(*) FILTER (WHERE status = 'available') as available_properties,
  AVG(price) as avg_price,
  MIN(price) as min_price,
  MAX(price) as max_price,
  AVG(bedrooms) as avg_bedrooms,
  COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as new_this_month
FROM public.properties 
GROUP BY location, property_type;

CREATE UNIQUE INDEX ON public.property_stats_mv (location, property_type);

-- User activity summary
CREATE MATERIALIZED VIEW IF NOT EXISTS public.user_activity_summary_mv AS
SELECT 
  u.id as user_id,
  u.email,
  p.role,
  COUNT(DISTINCT pv.property_id) as properties_viewed,
  COUNT(DISTINCT ra.property_id) as applications_submitted,
  COUNT(DISTINCT msg.id) as messages_sent,
  MAX(pv.viewed_at) as last_property_view,
  MAX(ra.created_at) as last_application,
  MAX(msg.created_at) as last_message
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
LEFT JOIN public.property_views pv ON u.id = pv.user_id
LEFT JOIN public.rental_applications ra ON u.id = ra.user_id
LEFT JOIN public.messages msg ON u.id = msg.sender_id
GROUP BY u.id, u.email, p.role;

CREATE UNIQUE INDEX ON public.user_activity_summary_mv (user_id);

-- Popular properties view
CREATE MATERIALIZED VIEW IF NOT EXISTS public.popular_properties_mv AS
SELECT 
  p.id,
  p.title,
  p.location,
  p.price,
  p.property_type,
  COUNT(pv.id) as view_count,
  COUNT(ra.id) as application_count,
  COUNT(DISTINCT pv.user_id) as unique_viewers,
  AVG(CASE WHEN r.rating IS NOT NULL THEN r.rating END) as avg_rating,
  COUNT(r.id) as review_count
FROM public.properties p
LEFT JOIN public.property_views pv ON p.id = pv.property_id 
  AND pv.viewed_at >= NOW() - INTERVAL '30 days'
LEFT JOIN public.rental_applications ra ON p.id = ra.property_id 
  AND ra.created_at >= NOW() - INTERVAL '30 days'
LEFT JOIN public.reviews r ON p.id = r.property_id
WHERE p.status = 'available'
GROUP BY p.id, p.title, p.location, p.price, p.property_type
HAVING COUNT(pv.id) > 0;

CREATE UNIQUE INDEX ON public.popular_properties_mv (id);
CREATE INDEX ON public.popular_properties_mv (view_count DESC, application_count DESC);

-- =====================================================
-- PERFORMANCE MONITORING FUNCTIONS
-- =====================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION public.refresh_performance_views()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY public.property_stats_mv;
  REFRESH MATERIALIZED VIEW CONCURRENTLY public.user_activity_summary_mv;
  REFRESH MATERIALIZED VIEW CONCURRENTLY public.popular_properties_mv;
END;
$$;

-- Function to log slow queries
CREATE OR REPLACE FUNCTION public.log_slow_query(
  p_query_text TEXT,
  p_execution_time INTEGER,
  p_rows_examined INTEGER DEFAULT NULL,
  p_rows_returned INTEGER DEFAULT NULL,
  p_user_id UUID DEFAULT NULL,
  p_endpoint TEXT DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_query_hash TEXT;
BEGIN
  -- Generate hash for query deduplication
  v_query_hash := encode(digest(p_query_text, 'sha256'), 'hex');
  
  INSERT INTO public.query_performance_logs (
    query_hash,
    query_text,
    execution_time_ms,
    rows_examined,
    rows_returned,
    user_id,
    endpoint
  ) VALUES (
    v_query_hash,
    p_query_text,
    p_execution_time,
    p_rows_examined,
    p_rows_returned,
    p_user_id,
    p_endpoint
  );
END;
$$;

-- Function to get performance recommendations
CREATE OR REPLACE FUNCTION public.get_performance_recommendations()
RETURNS TABLE (
  recommendation_type TEXT,
  description TEXT,
  priority INTEGER,
  estimated_impact TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  
  -- Slow query recommendations
  SELECT 
    'slow_queries'::TEXT,
    'Query: ' || LEFT(query_text, 100) || '... (avg: ' || 
    ROUND(AVG(execution_time_ms)) || 'ms)' as description,
    1 as priority,
    'High - Optimize this query'::TEXT as estimated_impact
  FROM public.query_performance_logs
  WHERE timestamp >= NOW() - INTERVAL '24 hours'
    AND slow_query = TRUE
  GROUP BY query_hash, query_text
  HAVING COUNT(*) > 5
  ORDER BY AVG(execution_time_ms) DESC
  LIMIT 5
  
  UNION ALL
  
  -- Missing index recommendations
  SELECT 
    'missing_indexes'::TEXT,
    'Consider adding indexes for frequently filtered columns'::TEXT,
    2,
    'Medium - Improve query performance'::TEXT
  WHERE EXISTS (
    SELECT 1 FROM public.query_performance_logs 
    WHERE timestamp >= NOW() - INTERVAL '24 hours'
      AND execution_time_ms > 500
  )
  
  UNION ALL
  
  -- Cache hit rate recommendations
  SELECT 
    'cache_optimization'::TEXT,
    'Cache hit rate below 80% - consider cache warming'::TEXT,
    2,
    'Medium - Reduce database load'::TEXT
  WHERE (
    SELECT 
      CASE 
        WHEN COUNT(*) = 0 THEN 0
        ELSE (COUNT(*) FILTER (WHERE operation = 'hit')::FLOAT / COUNT(*)) * 100
      END
    FROM public.cache_metrics 
    WHERE timestamp >= NOW() - INTERVAL '1 hour'
  ) < 80;
END;
$$;

-- =====================================================
-- AUTOMATED MAINTENANCE JOBS
-- =====================================================

-- Function to clean old performance logs
CREATE OR REPLACE FUNCTION public.cleanup_performance_logs()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Keep only last 7 days of query logs
  DELETE FROM public.query_performance_logs 
  WHERE timestamp < NOW() - INTERVAL '7 days';
  
  -- Keep only last 24 hours of cache metrics
  DELETE FROM public.cache_metrics 
  WHERE timestamp < NOW() - INTERVAL '24 hours';
  
  -- Keep only last 30 days of web vitals
  DELETE FROM public.web_vitals_metrics 
  WHERE timestamp < NOW() - INTERVAL '30 days';
  
  -- Keep only last 7 days of connection pool metrics
  DELETE FROM public.connection_pool_metrics 
  WHERE timestamp < NOW() - INTERVAL '7 days';
END;
$$;

-- =====================================================
-- RLS POLICIES FOR PERFORMANCE TABLES
-- =====================================================

ALTER TABLE public.query_performance_logs ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Admins can view query performance logs" ON public.query_performance_logs
  FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

ALTER TABLE public.web_vitals_metrics ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can insert their own web vitals" ON public.web_vitals_metrics
  FOR INSERT TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Admins can view all web vitals" ON public.web_vitals_metrics
  FOR SELECT TO authenticated
  USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- =====================================================
-- PERFORMANCE MONITORING TRIGGERS
-- =====================================================

-- Trigger to update materialized views
CREATE OR REPLACE FUNCTION public.trigger_refresh_stats()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  -- Refresh stats every hour (simplified for demo)
  PERFORM pg_notify('refresh_stats', 'properties_updated');
  RETURN COALESCE(NEW, OLD);
END;
$$;

-- Add trigger to properties table
DROP TRIGGER IF EXISTS trigger_refresh_property_stats ON public.properties;
CREATE TRIGGER trigger_refresh_property_stats
  AFTER INSERT OR UPDATE OR DELETE ON public.properties
  FOR EACH STATEMENT
  EXECUTE FUNCTION public.trigger_refresh_stats();

-- =====================================================
-- INITIAL PERFORMANCE BUDGETS
-- =====================================================

INSERT INTO public.performance_budgets (metric_name, budget_value, threshold_warning, threshold_critical, page_path, device_type) VALUES
('lcp', 2500, 2000, 3000, '/', 'mobile'),
('lcp', 2000, 1500, 2500, '/', 'desktop'),
('fid', 100, 80, 150, '/', 'mobile'),
('fid', 50, 40, 100, '/', 'desktop'),
('cls', 100, 80, 150, '/', 'mobile'), -- CLS * 1000 for integer storage
('cls', 100, 80, 150, '/', 'desktop'),
('bundle_size', 250000, 200000, 300000, '/properties', 'mobile'), -- 250KB
('bundle_size', 500000, 400000, 600000, '/properties', 'desktop'), -- 500KB
('api_response_time', 500, 400, 800, '/api/properties', 'mobile'),
('api_response_time', 300, 250, 500, '/api/properties', 'desktop')
ON CONFLICT DO NOTHING;
