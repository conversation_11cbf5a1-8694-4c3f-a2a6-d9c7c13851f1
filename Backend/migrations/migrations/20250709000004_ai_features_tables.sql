-- =====================================================
-- AI FEATURES DATABASE TABLES AND FUNCTIONS
-- Comprehensive database support for AI-powered features
-- =====================================================

-- User Preferences Table
CREATE TABLE IF NOT EXISTS public.user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  budget_min INTEGER NOT NULL,
  budget_max INTEGER NOT NULL,
  property_types TEXT[] NOT NULL DEFAULT '{}',
  locations TEXT[] NOT NULL DEFAULT '{}',
  bedrooms INTEGER[],
  bathrooms INTEGER[],
  amenities TEXT[] DEFAULT '{}',
  lifestyle_preferences TEXT[] DEFAULT '{}',
  commute_requirements JSONB DEFAULT '[]',
  move_in_date DATE,
  lease_duration_months INTEGER,
  pet_friendly BOOLEAN DEFAULT FALSE,
  furnished BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id)
);

-- User Behavior Tracking Table
CREATE TABLE IF NOT EXISTS public.user_behavior (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  session_id TEXT,
  event_type TEXT NOT NULL,
  event_data JSONB NOT NULL,
  property_id UUID REFERENCES public.properties(id),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT,
  device_type TEXT,
  source TEXT
);

-- Property Interactions Table
CREATE TABLE IF NOT EXISTS public.property_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
  interaction_type TEXT NOT NULL CHECK (interaction_type IN ('view', 'save', 'inquiry', 'contact', 'share', 'compare')),
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  duration_seconds INTEGER,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Recommendations Table
CREATE TABLE IF NOT EXISTS public.ai_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
  recommendation_type TEXT NOT NULL,
  score DECIMAL NOT NULL,
  confidence_level DECIMAL NOT NULL,
  explanation TEXT[],
  match_factors JSONB,
  generated_at TIMESTAMPTZ DEFAULT NOW(),
  clicked BOOLEAN DEFAULT FALSE,
  clicked_at TIMESTAMPTZ,
  converted BOOLEAN DEFAULT FALSE,
  converted_at TIMESTAMPTZ
);

-- Price Predictions Table
CREATE TABLE IF NOT EXISTS public.price_predictions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE,
  location TEXT NOT NULL,
  property_type TEXT NOT NULL,
  predicted_price DECIMAL NOT NULL,
  confidence_score DECIMAL NOT NULL,
  price_range_min DECIMAL NOT NULL,
  price_range_max DECIMAL NOT NULL,
  prediction_factors JSONB,
  market_trends JSONB,
  comparable_properties JSONB,
  model_version TEXT,
  generated_at TIMESTAMPTZ DEFAULT NOW(),
  actual_price DECIMAL,
  accuracy_score DECIMAL
);

-- Market Analytics Table
CREATE TABLE IF NOT EXISTS public.market_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location TEXT NOT NULL,
  property_type TEXT,
  analysis_type TEXT NOT NULL,
  insights JSONB NOT NULL,
  metrics JSONB NOT NULL,
  confidence_level DECIMAL NOT NULL,
  data_sources TEXT[],
  generated_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  UNIQUE(location, property_type, analysis_type, generated_at)
);

-- Lead Scoring Table
CREATE TABLE IF NOT EXISTS public.lead_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  score INTEGER NOT NULL CHECK (score >= 0 AND score <= 100),
  quality_grade TEXT NOT NULL CHECK (quality_grade IN ('A', 'B', 'C', 'D')),
  conversion_probability DECIMAL NOT NULL,
  priority_level TEXT NOT NULL CHECK (priority_level IN ('high', 'medium', 'low')),
  scoring_factors JSONB NOT NULL,
  recommended_actions TEXT[],
  follow_up_schedule JSONB,
  generated_at TIMESTAMPTZ DEFAULT NOW(),
  actual_conversion BOOLEAN,
  conversion_date TIMESTAMPTZ
);

-- Property Matches Table
CREATE TABLE IF NOT EXISTS public.property_matches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
  match_score DECIMAL NOT NULL,
  confidence_level DECIMAL NOT NULL,
  match_factors JSONB NOT NULL,
  reasons TEXT[],
  potential_issues TEXT[],
  recommendation_strength TEXT NOT NULL,
  generated_at TIMESTAMPTZ DEFAULT NOW(),
  user_feedback INTEGER CHECK (user_feedback >= 1 AND user_feedback <= 5),
  feedback_date TIMESTAMPTZ
);

-- AI Model Performance Table
CREATE TABLE IF NOT EXISTS public.ai_model_performance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  model_type TEXT NOT NULL,
  model_version TEXT NOT NULL,
  accuracy DECIMAL NOT NULL,
  precision_score DECIMAL,
  recall_score DECIMAL,
  f1_score DECIMAL,
  mae DECIMAL,
  rmse DECIMAL,
  training_data_size INTEGER,
  evaluation_date TIMESTAMPTZ DEFAULT NOW(),
  feature_importance JSONB,
  performance_notes TEXT
);

-- Enable RLS on all tables
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_behavior ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.property_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.price_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.market_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lead_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.property_matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_model_performance ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- RLS POLICIES
-- =====================================================

-- User Preferences Policies
CREATE POLICY "Users can manage their own preferences" ON public.user_preferences
  FOR ALL TO authenticated
  USING (user_id = auth.uid());

-- User Behavior Policies
CREATE POLICY "Users can view their own behavior" ON public.user_behavior
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "System can insert behavior data" ON public.user_behavior
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- Property Interactions Policies
CREATE POLICY "Users can manage their own interactions" ON public.property_interactions
  FOR ALL TO authenticated
  USING (user_id = auth.uid());

-- AI Recommendations Policies
CREATE POLICY "Users can view their own recommendations" ON public.ai_recommendations
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "System can manage recommendations" ON public.ai_recommendations
  FOR INSERT TO authenticated
  WITH CHECK (true);

CREATE POLICY "Users can update recommendation feedback" ON public.ai_recommendations
  FOR UPDATE TO authenticated
  USING (user_id = auth.uid());

-- Price Predictions Policies
CREATE POLICY "Anyone can view price predictions" ON public.price_predictions
  FOR SELECT USING (true);

CREATE POLICY "System can manage price predictions" ON public.price_predictions
  FOR ALL TO authenticated
  USING (true);

-- Market Analytics Policies
CREATE POLICY "Anyone can view market analytics" ON public.market_analytics
  FOR SELECT USING (true);

CREATE POLICY "System can manage market analytics" ON public.market_analytics
  FOR ALL TO authenticated
  USING (true);

-- Lead Scores Policies
CREATE POLICY "Agents can view lead scores for their leads" ON public.lead_scores
  FOR SELECT TO authenticated
  USING (
    lead_id IN (
      SELECT id FROM public.property_inquiries 
      WHERE agent_id = (SELECT agent_id FROM public.profiles WHERE id = auth.uid())
    )
  );

CREATE POLICY "System can manage lead scores" ON public.lead_scores
  FOR ALL TO authenticated
  USING (true);

-- Property Matches Policies
CREATE POLICY "Users can view their own property matches" ON public.property_matches
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can provide feedback on matches" ON public.property_matches
  FOR UPDATE TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "System can create property matches" ON public.property_matches
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- AI Model Performance Policies
CREATE POLICY "Admins can view model performance" ON public.ai_model_performance
  FOR SELECT TO authenticated
  USING (public.is_admin());

CREATE POLICY "System can manage model performance" ON public.ai_model_performance
  FOR ALL TO authenticated
  USING (public.is_admin());

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User Preferences Indexes
CREATE INDEX idx_user_preferences_user_id ON public.user_preferences(user_id);

-- User Behavior Indexes
CREATE INDEX idx_user_behavior_user_id ON public.user_behavior(user_id, timestamp DESC);
CREATE INDEX idx_user_behavior_property_id ON public.user_behavior(property_id, timestamp DESC);
CREATE INDEX idx_user_behavior_event_type ON public.user_behavior(event_type, timestamp DESC);
CREATE INDEX idx_user_behavior_session ON public.user_behavior(session_id, timestamp);

-- Property Interactions Indexes
CREATE INDEX idx_property_interactions_user_id ON public.property_interactions(user_id, created_at DESC);
CREATE INDEX idx_property_interactions_property_id ON public.property_interactions(property_id, created_at DESC);
CREATE INDEX idx_property_interactions_type ON public.property_interactions(interaction_type, created_at DESC);

-- AI Recommendations Indexes
CREATE INDEX idx_ai_recommendations_user_id ON public.ai_recommendations(user_id, generated_at DESC);
CREATE INDEX idx_ai_recommendations_property_id ON public.ai_recommendations(property_id);
CREATE INDEX idx_ai_recommendations_score ON public.ai_recommendations(score DESC);
CREATE INDEX idx_ai_recommendations_type ON public.ai_recommendations(recommendation_type);

-- Price Predictions Indexes
CREATE INDEX idx_price_predictions_property_id ON public.price_predictions(property_id);
CREATE INDEX idx_price_predictions_location ON public.price_predictions(location, property_type);
CREATE INDEX idx_price_predictions_generated ON public.price_predictions(generated_at DESC);

-- Market Analytics Indexes
CREATE INDEX idx_market_analytics_location ON public.market_analytics(location, property_type);
CREATE INDEX idx_market_analytics_type ON public.market_analytics(analysis_type, generated_at DESC);
CREATE INDEX idx_market_analytics_expires ON public.market_analytics(expires_at) WHERE expires_at IS NOT NULL;

-- Lead Scores Indexes
CREATE INDEX idx_lead_scores_lead_id ON public.lead_scores(lead_id);
CREATE INDEX idx_lead_scores_score ON public.lead_scores(score DESC);
CREATE INDEX idx_lead_scores_grade ON public.lead_scores(quality_grade, score DESC);
CREATE INDEX idx_lead_scores_priority ON public.lead_scores(priority_level, generated_at DESC);

-- Property Matches Indexes
CREATE INDEX idx_property_matches_user_id ON public.property_matches(user_id, generated_at DESC);
CREATE INDEX idx_property_matches_property_id ON public.property_matches(property_id);
CREATE INDEX idx_property_matches_score ON public.property_matches(match_score DESC);

-- AI Model Performance Indexes
CREATE INDEX idx_ai_model_performance_type ON public.ai_model_performance(model_type, model_version);
CREATE INDEX idx_ai_model_performance_date ON public.ai_model_performance(evaluation_date DESC);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================

CREATE TRIGGER set_updated_at_user_preferences
  BEFORE UPDATE ON public.user_preferences
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- =====================================================
-- REALTIME SUBSCRIPTIONS
-- =====================================================

-- Enable realtime for AI features
ALTER PUBLICATION supabase_realtime ADD TABLE public.ai_recommendations;
ALTER PUBLICATION supabase_realtime ADD TABLE public.property_matches;
ALTER PUBLICATION supabase_realtime ADD TABLE public.lead_scores;
ALTER PUBLICATION supabase_realtime ADD TABLE public.price_predictions;

-- =====================================================
-- AI FUNCTIONS
-- =====================================================

-- Function to calculate user similarity for collaborative filtering
CREATE OR REPLACE FUNCTION calculate_user_similarity(
  user1_id UUID,
  user2_id UUID
)
RETURNS DECIMAL AS $$
DECLARE
  similarity_score DECIMAL := 0;
  common_interactions INTEGER := 0;
  total_interactions INTEGER := 0;
BEGIN
  -- Calculate similarity based on property interactions
  SELECT
    COUNT(CASE WHEN pi1.property_id = pi2.property_id THEN 1 END) as common,
    COUNT(DISTINCT pi1.property_id) + COUNT(DISTINCT pi2.property_id) as total
  INTO common_interactions, total_interactions
  FROM property_interactions pi1
  FULL OUTER JOIN property_interactions pi2 ON pi1.property_id = pi2.property_id
  WHERE pi1.user_id = user1_id AND pi2.user_id = user2_id;

  -- Calculate Jaccard similarity
  IF total_interactions > 0 THEN
    similarity_score := common_interactions::DECIMAL / (total_interactions - common_interactions);
  END IF;

  RETURN COALESCE(similarity_score, 0);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get property recommendations using collaborative filtering
CREATE OR REPLACE FUNCTION get_collaborative_recommendations(
  target_user_id UUID,
  limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
  property_id UUID,
  score DECIMAL,
  similar_users UUID[]
) AS $$
BEGIN
  RETURN QUERY
  WITH similar_users AS (
    SELECT
      u.id as user_id,
      calculate_user_similarity(target_user_id, u.id) as similarity
    FROM auth.users u
    WHERE u.id != target_user_id
      AND calculate_user_similarity(target_user_id, u.id) > 0.1
    ORDER BY similarity DESC
    LIMIT 50
  ),
  recommended_properties AS (
    SELECT
      pi.property_id,
      AVG(pi.rating * su.similarity) as weighted_score,
      array_agg(DISTINCT su.user_id) as recommending_users
    FROM property_interactions pi
    JOIN similar_users su ON pi.user_id = su.user_id
    WHERE pi.property_id NOT IN (
      SELECT property_id
      FROM property_interactions
      WHERE user_id = target_user_id
    )
    AND pi.rating IS NOT NULL
    GROUP BY pi.property_id
    HAVING COUNT(*) >= 2
    ORDER BY weighted_score DESC
    LIMIT limit_count
  )
  SELECT
    rp.property_id,
    rp.weighted_score,
    rp.recommending_users
  FROM recommended_properties rp;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate lead score
CREATE OR REPLACE FUNCTION calculate_lead_score(
  lead_data JSONB
)
RETURNS INTEGER AS $$
DECLARE
  score INTEGER := 0;
  budget_score INTEGER := 0;
  timeline_score INTEGER := 0;
  engagement_score INTEGER := 0;
  completeness_score INTEGER := 0;
BEGIN
  -- Budget qualification (0-25 points)
  IF (lead_data->>'budget_max')::INTEGER > 500000 THEN
    budget_score := 25;
  ELSIF (lead_data->>'budget_max')::INTEGER > 200000 THEN
    budget_score := 20;
  ELSIF (lead_data->>'budget_max')::INTEGER > 100000 THEN
    budget_score := 15;
  ELSE
    budget_score := 10;
  END IF;

  -- Timeline urgency (0-20 points)
  CASE lead_data->>'move_in_timeline'
    WHEN 'immediate' THEN timeline_score := 20;
    WHEN '1_month' THEN timeline_score := 18;
    WHEN '3_months' THEN timeline_score := 15;
    WHEN '6_months' THEN timeline_score := 10;
    ELSE timeline_score := 5;
  END CASE;

  -- Engagement level (0-25 points)
  IF (lead_data->>'phone_provided')::BOOLEAN THEN
    engagement_score := engagement_score + 10;
  END IF;
  IF (lead_data->>'email_verified')::BOOLEAN THEN
    engagement_score := engagement_score + 10;
  END IF;
  IF (lead_data->>'viewed_multiple_properties')::BOOLEAN THEN
    engagement_score := engagement_score + 5;
  END IF;

  -- Information completeness (0-20 points)
  completeness_score := (
    CASE WHEN lead_data ? 'full_name' THEN 3 ELSE 0 END +
    CASE WHEN lead_data ? 'phone' THEN 3 ELSE 0 END +
    CASE WHEN lead_data ? 'email' THEN 3 ELSE 0 END +
    CASE WHEN lead_data ? 'budget_min' THEN 3 ELSE 0 END +
    CASE WHEN lead_data ? 'budget_max' THEN 3 ELSE 0 END +
    CASE WHEN lead_data ? 'preferred_locations' THEN 2 ELSE 0 END +
    CASE WHEN lead_data ? 'property_type' THEN 2 ELSE 0 END +
    CASE WHEN lead_data ? 'move_in_date' THEN 1 ELSE 0 END
  );

  -- Source quality (0-10 points)
  CASE lead_data->>'source'
    WHEN 'referral' THEN score := score + 10;
    WHEN 'website_form' THEN score := score + 8;
    WHEN 'social_media' THEN score := score + 6;
    WHEN 'advertisement' THEN score := score + 4;
    ELSE score := score + 2;
  END CASE;

  score := budget_score + timeline_score + engagement_score + completeness_score + score;

  RETURN LEAST(100, score);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get price trends
CREATE OR REPLACE FUNCTION get_price_trends(
  location_filter TEXT,
  property_type_filter TEXT DEFAULT NULL,
  months_back INTEGER DEFAULT 12
)
RETURNS TABLE (
  time_period TEXT,
  average_price DECIMAL,
  price_change_percentage DECIMAL,
  volume_change_percentage DECIMAL,
  demand_score DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  WITH monthly_data AS (
    SELECT
      DATE_TRUNC('month', created_at) as month,
      AVG(price_per_year) as avg_price,
      COUNT(*) as volume
    FROM properties
    WHERE location ILIKE '%' || location_filter || '%'
      AND (property_type_filter IS NULL OR property_type = property_type_filter)
      AND created_at >= NOW() - INTERVAL '1 month' * months_back
    GROUP BY DATE_TRUNC('month', created_at)
    ORDER BY month
  ),
  trend_data AS (
    SELECT
      TO_CHAR(month, 'YYYY-MM') as period,
      avg_price,
      LAG(avg_price) OVER (ORDER BY month) as prev_price,
      volume,
      LAG(volume) OVER (ORDER BY month) as prev_volume
    FROM monthly_data
  )
  SELECT
    td.period,
    td.avg_price,
    CASE
      WHEN td.prev_price > 0 THEN
        ROUND(((td.avg_price - td.prev_price) / td.prev_price * 100)::DECIMAL, 2)
      ELSE 0
    END as price_change_pct,
    CASE
      WHEN td.prev_volume > 0 THEN
        ROUND(((td.volume - td.prev_volume) / td.prev_volume * 100)::DECIMAL, 2)
      ELSE 0
    END as volume_change_pct,
    LEAST(1.0, GREATEST(0.0, (td.volume::DECIMAL / 100))) as demand_score
  FROM trend_data td
  WHERE td.prev_price IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update AI model performance
CREATE OR REPLACE FUNCTION update_model_performance(
  model_type_param TEXT,
  model_version_param TEXT,
  accuracy_param DECIMAL,
  metrics_param JSONB
)
RETURNS void AS $$
BEGIN
  INSERT INTO ai_model_performance (
    model_type,
    model_version,
    accuracy,
    precision_score,
    recall_score,
    f1_score,
    mae,
    rmse,
    feature_importance
  ) VALUES (
    model_type_param,
    model_version_param,
    accuracy_param,
    (metrics_param->>'precision')::DECIMAL,
    (metrics_param->>'recall')::DECIMAL,
    (metrics_param->>'f1_score')::DECIMAL,
    (metrics_param->>'mae')::DECIMAL,
    (metrics_param->>'rmse')::DECIMAL,
    metrics_param->'feature_importance'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions on AI functions
GRANT EXECUTE ON FUNCTION calculate_user_similarity(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_collaborative_recommendations(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_lead_score(JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION get_price_trends(TEXT, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION update_model_performance(TEXT, TEXT, DECIMAL, JSONB) TO authenticated;

-- Grant permissions on AI tables
GRANT SELECT, INSERT, UPDATE ON public.user_preferences TO authenticated;
GRANT SELECT, INSERT ON public.user_behavior TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.property_interactions TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.ai_recommendations TO authenticated;
GRANT SELECT ON public.price_predictions TO authenticated;
GRANT SELECT ON public.market_analytics TO authenticated;
GRANT SELECT ON public.lead_scores TO authenticated;
GRANT SELECT, UPDATE ON public.property_matches TO authenticated;
