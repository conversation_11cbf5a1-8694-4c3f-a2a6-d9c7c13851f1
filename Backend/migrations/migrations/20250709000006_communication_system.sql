-- =====================================================
-- COMMUNICATION SYSTEM DATABASE SCHEMA
-- Comprehensive database support for WhatsApp, Email, SMS, and In-App messaging
-- =====================================================

-- WhatsApp Contacts Table
CREATE TABLE IF NOT EXISTS public.whatsapp_contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  profile_picture TEXT,
  is_business BOOLEAN DEFAULT FALSE,
  last_seen TIMESTAMPTZ,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'blocked')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- WhatsApp Messages Table
CREATE TABLE IF NOT EXISTS public.whatsapp_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contact_id UUID NOT NULL REFERENCES public.whatsapp_contacts(id) ON DELETE CASCADE,
  conversation_id UUID NOT NULL,
  message_type TEXT NOT NULL CHECK (message_type IN ('text', 'image', 'document', 'audio', 'video', 'template', 'interactive')),
  content JSONB NOT NULL DEFAULT '{}',
  direction TEXT NOT NULL CHECK (direction IN ('inbound', 'outbound')),
  status TEXT DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed', 'pending')),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  delivered_at TIMESTAMPTZ,
  read_at TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}',
  property_id UUID REFERENCES public.properties(id),
  user_id UUID REFERENCES auth.users(id)
);

-- WhatsApp Templates Table
CREATE TABLE IF NOT EXISTS public.whatsapp_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  category TEXT NOT NULL CHECK (category IN ('marketing', 'utility', 'authentication')),
  language TEXT DEFAULT 'en',
  status TEXT DEFAULT 'pending' CHECK (status IN ('approved', 'pending', 'rejected')),
  components JSONB NOT NULL DEFAULT '[]',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- WhatsApp Campaigns Table
CREATE TABLE IF NOT EXISTS public.whatsapp_campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  template_id TEXT NOT NULL,
  target_audience JSONB NOT NULL DEFAULT '{}',
  schedule JSONB NOT NULL DEFAULT '{"send_immediately": true}',
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'completed', 'failed')),
  metrics JSONB DEFAULT '{"total_recipients": 0, "sent_count": 0, "delivered_count": 0, "read_count": 0, "failed_count": 0, "response_count": 0}',
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  sent_at TIMESTAMPTZ
);

-- WhatsApp Chatbot Flows Table
CREATE TABLE IF NOT EXISTS public.whatsapp_chatbot_flows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  trigger_keywords TEXT[] NOT NULL DEFAULT '{}',
  flow_steps JSONB NOT NULL DEFAULT '[]',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Email Templates Table
CREATE TABLE IF NOT EXISTS public.email_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  subject TEXT NOT NULL,
  html_content TEXT NOT NULL,
  text_content TEXT NOT NULL,
  template_type TEXT NOT NULL CHECK (template_type IN ('transactional', 'marketing', 'system')),
  variables JSONB DEFAULT '[]',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Email Campaigns Table
CREATE TABLE IF NOT EXISTS public.email_campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  template_id UUID NOT NULL REFERENCES public.email_templates(id),
  subject TEXT NOT NULL,
  sender_name TEXT NOT NULL,
  sender_email TEXT NOT NULL,
  target_audience JSONB NOT NULL DEFAULT '{}',
  schedule JSONB NOT NULL DEFAULT '{"send_immediately": true}',
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'completed', 'paused', 'failed')),
  metrics JSONB DEFAULT '{"total_recipients": 0, "sent_count": 0, "delivered_count": 0, "opened_count": 0, "clicked_count": 0, "bounced_count": 0, "unsubscribed_count": 0, "spam_count": 0}',
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  sent_at TIMESTAMPTZ
);

-- Email Automations Table
CREATE TABLE IF NOT EXISTS public.email_automations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  trigger JSONB NOT NULL,
  workflow_steps JSONB NOT NULL DEFAULT '[]',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Email Deliveries Table
CREATE TABLE IF NOT EXISTS public.email_deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id UUID REFERENCES public.email_campaigns(id),
  automation_id UUID REFERENCES public.email_automations(id),
  template_id UUID NOT NULL REFERENCES public.email_templates(id),
  recipient_email TEXT NOT NULL,
  recipient_name TEXT,
  subject TEXT NOT NULL,
  status TEXT DEFAULT 'queued' CHECK (status IN ('queued', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'spam', 'unsubscribed')),
  sent_at TIMESTAMPTZ,
  delivered_at TIMESTAMPTZ,
  opened_at TIMESTAMPTZ,
  clicked_at TIMESTAMPTZ,
  bounce_reason TEXT,
  tracking_data JSONB DEFAULT '{"opens": 0, "clicks": 0}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Email Subscriptions Table
CREATE TABLE IF NOT EXISTS public.email_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  subscription_types TEXT[] NOT NULL DEFAULT '{}',
  status TEXT DEFAULT 'subscribed' CHECK (status IN ('subscribed', 'unsubscribed', 'bounced', 'spam')),
  preferences JSONB DEFAULT '{"frequency": "immediate", "categories": []}',
  unsubscribe_token TEXT NOT NULL UNIQUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(email, user_id)
);

-- Conversations Table
CREATE TABLE IF NOT EXISTS public.conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  participants UUID[] NOT NULL,
  conversation_type TEXT NOT NULL CHECK (conversation_type IN ('direct', 'group', 'support')),
  title TEXT,
  description TEXT,
  property_id UUID REFERENCES public.properties(id),
  last_activity TIMESTAMPTZ DEFAULT NOW(),
  is_archived BOOLEAN DEFAULT FALSE,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Messages Table
CREATE TABLE IF NOT EXISTS public.messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id),
  message_type TEXT NOT NULL CHECK (message_type IN ('text', 'image', 'document', 'audio', 'video', 'location', 'system')),
  content JSONB NOT NULL DEFAULT '{}',
  reply_to UUID REFERENCES public.messages(id),
  thread_id UUID,
  status TEXT DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed')),
  is_edited BOOLEAN DEFAULT FALSE,
  is_deleted BOOLEAN DEFAULT FALSE,
  reactions JSONB DEFAULT '[]',
  mentions UUID[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  delivered_at TIMESTAMPTZ,
  read_at TIMESTAMPTZ
);

-- Message Delivery Status Table
CREATE TABLE IF NOT EXISTS public.message_delivery_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  status TEXT NOT NULL CHECK (status IN ('sent', 'delivered', 'read')),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(message_id, user_id)
);

-- File Uploads Table
CREATE TABLE IF NOT EXISTS public.file_uploads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  file_url TEXT NOT NULL,
  uploaded_by UUID NOT NULL REFERENCES auth.users(id),
  conversation_id UUID NOT NULL REFERENCES public.conversations(id),
  message_id UUID REFERENCES public.messages(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- SMS Messages Table
CREATE TABLE IF NOT EXISTS public.sms_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number TEXT NOT NULL,
  message TEXT NOT NULL,
  message_type TEXT NOT NULL CHECK (message_type IN ('alert', 'otp', 'reminder', 'confirmation', 'emergency', 'marketing')),
  status TEXT DEFAULT 'queued' CHECK (status IN ('queued', 'sent', 'delivered', 'failed', 'expired')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  scheduled_at TIMESTAMPTZ,
  sent_at TIMESTAMPTZ,
  delivered_at TIMESTAMPTZ,
  failed_reason TEXT,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 1,
  user_id UUID REFERENCES auth.users(id),
  reference_id TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- SMS Templates Table
CREATE TABLE IF NOT EXISTS public.sms_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  message_type TEXT NOT NULL CHECK (message_type IN ('alert', 'otp', 'reminder', 'confirmation', 'emergency', 'marketing')),
  content TEXT NOT NULL,
  variables JSONB DEFAULT '[]',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- OTP Verifications Table
CREATE TABLE IF NOT EXISTS public.otp_verifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number TEXT NOT NULL,
  code TEXT NOT NULL,
  purpose TEXT NOT NULL CHECK (purpose IN ('registration', 'login', 'password_reset', 'phone_verification', 'transaction')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'verified', 'expired', 'failed')),
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  expires_at TIMESTAMPTZ NOT NULL,
  verified_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- SMS Campaigns Table
CREATE TABLE IF NOT EXISTS public.sms_campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  template_id UUID NOT NULL REFERENCES public.sms_templates(id),
  target_audience JSONB NOT NULL DEFAULT '{}',
  schedule JSONB NOT NULL DEFAULT '{"send_immediately": true}',
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'completed', 'failed')),
  metrics JSONB DEFAULT '{"total_recipients": 0, "sent_count": 0, "delivered_count": 0, "failed_count": 0}',
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  sent_at TIMESTAMPTZ
);

-- Communication Preferences Table
CREATE TABLE IF NOT EXISTS public.communication_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  channels JSONB NOT NULL DEFAULT '{}',
  quiet_hours JSONB DEFAULT '{"enabled": false}',
  language TEXT DEFAULT 'en',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Message Queue Table
CREATE TABLE IF NOT EXISTS public.message_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  channel_type TEXT NOT NULL CHECK (channel_type IN ('whatsapp', 'email', 'sms', 'push')),
  recipient TEXT NOT NULL,
  message_data JSONB NOT NULL,
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  status TEXT DEFAULT 'queued' CHECK (status IN ('queued', 'processing', 'sent', 'failed', 'cancelled')),
  scheduled_at TIMESTAMPTZ,
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  last_attempt_at TIMESTAMPTZ,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Communication Logs Table
CREATE TABLE IF NOT EXISTS public.communication_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  channel_type TEXT NOT NULL,
  message_type TEXT NOT NULL,
  recipient TEXT NOT NULL,
  subject TEXT,
  content_preview TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed')),
  delivery_time TIMESTAMPTZ,
  open_time TIMESTAMPTZ,
  click_time TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE public.whatsapp_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.whatsapp_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.whatsapp_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.whatsapp_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.whatsapp_chatbot_flows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_automations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_deliveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_delivery_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.file_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sms_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sms_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.otp_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sms_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.communication_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.communication_logs ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- RLS POLICIES
-- =====================================================

-- WhatsApp Contacts Policies
CREATE POLICY "Anyone can view WhatsApp contacts" ON public.whatsapp_contacts
  FOR SELECT USING (true);

CREATE POLICY "System can manage WhatsApp contacts" ON public.whatsapp_contacts
  FOR ALL TO authenticated
  USING (true);

-- WhatsApp Messages Policies
CREATE POLICY "Users can view relevant WhatsApp messages" ON public.whatsapp_messages
  FOR SELECT TO authenticated
  USING (user_id = auth.uid() OR contact_id IN (
    SELECT id FROM public.whatsapp_contacts WHERE phone_number IN (
      SELECT phone FROM public.profiles WHERE id = auth.uid()
    )
  ));

-- Email Campaigns Policies
CREATE POLICY "Users can manage their own email campaigns" ON public.email_campaigns
  FOR ALL TO authenticated
  USING (created_by = auth.uid());

-- Conversations Policies
CREATE POLICY "Users can access their conversations" ON public.conversations
  FOR ALL TO authenticated
  USING (auth.uid() = ANY(participants) OR created_by = auth.uid());

-- Messages Policies
CREATE POLICY "Users can access messages in their conversations" ON public.messages
  FOR ALL TO authenticated
  USING (
    conversation_id IN (
      SELECT id FROM public.conversations 
      WHERE auth.uid() = ANY(participants)
    )
  );

-- SMS Messages Policies
CREATE POLICY "Users can view their own SMS messages" ON public.sms_messages
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

-- Communication Preferences Policies
CREATE POLICY "Users can manage their own communication preferences" ON public.communication_preferences
  FOR ALL TO authenticated
  USING (user_id = auth.uid());

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- WhatsApp indexes
CREATE INDEX idx_whatsapp_messages_contact_id ON public.whatsapp_messages(contact_id, timestamp DESC);
CREATE INDEX idx_whatsapp_messages_conversation ON public.whatsapp_messages(conversation_id, timestamp);
CREATE INDEX idx_whatsapp_messages_user_id ON public.whatsapp_messages(user_id, timestamp DESC);

-- Email indexes
CREATE INDEX idx_email_deliveries_campaign_id ON public.email_deliveries(campaign_id);
CREATE INDEX idx_email_deliveries_recipient ON public.email_deliveries(recipient_email);
CREATE INDEX idx_email_deliveries_status ON public.email_deliveries(status, created_at DESC);

-- Conversation indexes
CREATE INDEX idx_conversations_participants ON public.conversations USING GIN(participants);
CREATE INDEX idx_conversations_property_id ON public.conversations(property_id);
CREATE INDEX idx_conversations_last_activity ON public.conversations(last_activity DESC);

-- Message indexes
CREATE INDEX idx_messages_conversation_id ON public.messages(conversation_id, created_at DESC);
CREATE INDEX idx_messages_sender_id ON public.messages(sender_id, created_at DESC);
CREATE INDEX idx_messages_thread_id ON public.messages(thread_id, created_at);

-- SMS indexes
CREATE INDEX idx_sms_messages_phone ON public.sms_messages(phone_number, created_at DESC);
CREATE INDEX idx_sms_messages_user_id ON public.sms_messages(user_id, created_at DESC);
CREATE INDEX idx_sms_messages_status ON public.sms_messages(status, priority);

-- OTP indexes
CREATE INDEX idx_otp_verifications_phone ON public.otp_verifications(phone_number, purpose, status);
CREATE INDEX idx_otp_verifications_expires ON public.otp_verifications(expires_at) WHERE status = 'pending';

-- Queue indexes
CREATE INDEX idx_message_queue_status ON public.message_queue(status, priority, scheduled_at);
CREATE INDEX idx_message_queue_channel ON public.message_queue(channel_type, status);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Updated at triggers
CREATE TRIGGER set_updated_at_whatsapp_contacts
  BEFORE UPDATE ON public.whatsapp_contacts
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_email_templates
  BEFORE UPDATE ON public.email_templates
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_conversations
  BEFORE UPDATE ON public.conversations
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_messages
  BEFORE UPDATE ON public.messages
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_sms_messages
  BEFORE UPDATE ON public.sms_messages
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_updated_at_communication_preferences
  BEFORE UPDATE ON public.communication_preferences
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- =====================================================
-- REALTIME SUBSCRIPTIONS
-- =====================================================

-- Enable realtime for communication features
ALTER PUBLICATION supabase_realtime ADD TABLE public.whatsapp_messages;
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;
ALTER PUBLICATION supabase_realtime ADD TABLE public.message_delivery_status;
ALTER PUBLICATION supabase_realtime ADD TABLE public.sms_messages;
