# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Database files
*.sqlite
*.sqlite3
*.db
phcityrent.db
phcityrent_*.db

# Upload directories
uploads/
temp/
tmp/

# Build outputs
build/
coverage/
test-results/

# Docker
.dockerignore
docker-compose.override.yml

# Security files
*.pem
*.key
*.crt
*.p12
*.pfx

# Backup files
*.backup
*.bak
*.tmp

# Local configuration
config/local.*
.env.override

# Deployment
.vercel
.netlify

# Monitoring
.pm2/
ecosystem.config.js.local

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Testing
cypress/videos/
cypress/screenshots/
playwright-report/
.jest-cache/

# Storybook
storybook-static/

# Bundle analysis
bundle-analyzer-report.html
stats.json
