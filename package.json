{"name": "phcityrent", "version": "1.0.0", "description": "Port Harcourt Real Estate Platform - Full Stack Application", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd Backend && npm run start:dev", "dev:frontend": "cd Frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd Backend && npm run build", "build:frontend": "cd Frontend && npm run build", "start": "npm run start:backend", "start:backend": "cd Backend && npm run start:prod", "start:frontend": "cd Frontend && npm run preview", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd Backend && npm run test", "test:frontend": "cd Frontend && npm run test", "test:e2e": "cd Frontend && npm run test:e2e", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd Backend && npm run lint", "lint:frontend": "cd Frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd Backend && npm run lint:fix", "lint:fix:frontend": "cd Frontend && npm run lint:fix", "format": "npm run format:backend && npm run format:frontend", "format:backend": "cd Backend && npm run format", "format:frontend": "cd Frontend && npm run format", "setup": "./scripts/env-setup.sh", "setup:dev": "./scripts/env-setup.sh development", "setup:staging": "./scripts/env-setup.sh staging", "setup:prod": "./scripts/env-setup.sh production", "env:dev": "cp Backend/.env.development Backend/.env && cp Frontend/.env.development Frontend/.env", "env:staging": "cp Backend/.env.staging Backend/.env && cp Frontend/.env.staging Frontend/.env", "env:prod": "cp Backend/.env.production Backend/.env && cp Frontend/.env.production Frontend/.env", "db:migrate": "cd Backend && npm run migration:run", "db:seed": "cd Backend && npm run db:seed", "db:reset": "cd Backend && npm run db:reset", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "deploy:staging": "./scripts/deploy.sh staging", "deploy:prod": "./scripts/deploy.sh production", "health:check": "curl -f http://localhost:3001/api/v1/health || exit 1", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd Backend && rm -rf dist node_modules", "clean:frontend": "cd Frontend && rm -rf dist node_modules", "install:all": "npm install && cd Backend && npm install && cd ../Frontend && npm install", "update:deps": "npm update && cd Backend && npm update && cd ../Frontend && npm update", "security:audit": "npm audit && cd Backend && npm audit && cd ../Frontend && npm audit", "security:fix": "npm audit fix && cd Backend && npm audit fix && cd ../Frontend && npm audit fix"}, "keywords": ["real-estate", "property-management", "rental-platform", "port-harcourt", "nigeria", "<PERSON><PERSON><PERSON>", "react", "typescript"], "author": "PHCityRent Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/phcityrent/phcityrent.git"}, "bugs": {"url": "https://github.com/phcityrent/phcityrent/issues"}, "homepage": "https://phcityrent.com", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "lint-staged": {"Backend/**/*.{js,ts}": ["cd Backend && npm run lint:fix", "cd Backend && npm run format"], "Frontend/**/*.{js,ts,tsx}": ["cd Frontend && npm run lint:fix", "cd Frontend && npm run format"]}, "workspaces": ["Backend", "Frontend"]}