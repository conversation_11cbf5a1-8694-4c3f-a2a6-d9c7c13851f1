#!/bin/bash

# Environment Setup Script for PHCityRent
# This script sets up the development environment and manages environment configurations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        REQUIRED_VERSION="18.0.0"
        
        if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
            print_status "Node.js version $NODE_VERSION is compatible"
        else
            print_error "Node.js version $NODE_VERSION is not compatible. Required: $REQUIRED_VERSION or higher"
            exit 1
        fi
    else
        print_error "Node.js is not installed"
        exit 1
    fi
}

# Function to check npm version
check_npm_version() {
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_status "npm version $NPM_VERSION detected"
    else
        print_error "npm is not installed"
        exit 1
    fi
}

# Function to setup environment files
setup_env_files() {
    local env=$1
    print_header "Setting up environment files for $env"
    
    # Backend environment
    if [ -f "Backend/.env.$env" ]; then
        cp "Backend/.env.$env" "Backend/.env"
        print_status "Backend environment file set to $env"
    else
        print_warning "Backend/.env.$env not found"
    fi
    
    # Frontend environment
    if [ -f "Frontend/.env.$env" ]; then
        cp "Frontend/.env.$env" "Frontend/.env"
        print_status "Frontend environment file set to $env"
    else
        print_warning "Frontend/.env.$env not found"
    fi
}

# Function to install dependencies
install_dependencies() {
    print_header "Installing dependencies"
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd Backend
    npm install
    cd ..
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    cd Frontend
    npm install
    cd ..
    
    print_status "All dependencies installed successfully"
}

# Function to setup database
setup_database() {
    print_header "Setting up database"
    
    cd Backend
    
    # Check if database exists and create if not
    if [ ! -f "phcityrent.db" ]; then
        print_status "Creating SQLite database..."
        npm run db:create 2>/dev/null || print_warning "Database creation command not found"
    fi
    
    # Run migrations
    print_status "Running database migrations..."
    npm run migration:run 2>/dev/null || print_warning "Migration command not found"
    
    # Seed database if in development
    if [ "$1" = "development" ]; then
        print_status "Seeding database with sample data..."
        npm run db:seed 2>/dev/null || print_warning "Seed command not found"
    fi
    
    cd ..
}

# Function to setup development tools
setup_dev_tools() {
    print_header "Setting up development tools"
    
    # Install global tools if not present
    if ! command_exists nodemon; then
        print_status "Installing nodemon globally..."
        npm install -g nodemon
    fi
    
    if ! command_exists concurrently; then
        print_status "Installing concurrently globally..."
        npm install -g concurrently
    fi
    
    # Setup Git hooks
    if [ -d ".git" ]; then
        print_status "Setting up Git hooks..."
        
        # Create pre-commit hook
        cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "Running pre-commit checks..."

# Run linting
cd Frontend && npm run lint --silent
if [ $? -ne 0 ]; then
    echo "Frontend linting failed. Please fix the issues before committing."
    exit 1
fi

cd ../Backend && npm run lint --silent
if [ $? -ne 0 ]; then
    echo "Backend linting failed. Please fix the issues before committing."
    exit 1
fi

echo "Pre-commit checks passed!"
EOF
        
        chmod +x .git/hooks/pre-commit
        print_status "Git hooks configured"
    fi
}

# Function to create necessary directories
create_directories() {
    print_header "Creating necessary directories"
    
    # Backend directories
    mkdir -p Backend/uploads/dev
    mkdir -p Backend/uploads/staging
    mkdir -p Backend/uploads/production
    mkdir -p Backend/logs
    mkdir -p Backend/temp
    
    # Frontend directories
    mkdir -p Frontend/public/images
    mkdir -p Frontend/public/icons
    mkdir -p Frontend/dist
    
    print_status "Directories created successfully"
}

# Function to validate environment
validate_environment() {
    print_header "Validating environment setup"
    
    # Check if required files exist
    local required_files=(
        "Backend/package.json"
        "Frontend/package.json"
        "Backend/.env"
        "Frontend/.env"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            print_status "✓ $file exists"
        else
            print_error "✗ $file is missing"
            exit 1
        fi
    done
    
    # Test backend startup
    print_status "Testing backend startup..."
    cd Backend
    timeout 10s npm run start:dev > /dev/null 2>&1 &
    BACKEND_PID=$!
    sleep 5
    
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_status "✓ Backend starts successfully"
        kill $BACKEND_PID
    else
        print_warning "⚠ Backend startup test inconclusive"
    fi
    
    cd ..
    
    # Test frontend build
    print_status "Testing frontend build..."
    cd Frontend
    npm run build > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        print_status "✓ Frontend builds successfully"
    else
        print_warning "⚠ Frontend build test failed"
    fi
    
    cd ..
}

# Function to display help
show_help() {
    echo "Usage: $0 [ENVIRONMENT] [OPTIONS]"
    echo ""
    echo "ENVIRONMENT:"
    echo "  development  Set up development environment (default)"
    echo "  staging      Set up staging environment"
    echo "  production   Set up production environment"
    echo ""
    echo "OPTIONS:"
    echo "  --skip-deps     Skip dependency installation"
    echo "  --skip-db       Skip database setup"
    echo "  --skip-tools    Skip development tools setup"
    echo "  --skip-validation Skip environment validation"
    echo "  --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                          # Setup development environment"
    echo "  $0 development              # Setup development environment"
    echo "  $0 staging --skip-deps      # Setup staging without installing deps"
    echo "  $0 production --skip-tools  # Setup production without dev tools"
}

# Main function
main() {
    local environment="development"
    local skip_deps=false
    local skip_db=false
    local skip_tools=false
    local skip_validation=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            development|staging|production)
                environment="$1"
                shift
                ;;
            --skip-deps)
                skip_deps=true
                shift
                ;;
            --skip-db)
                skip_db=true
                shift
                ;;
            --skip-tools)
                skip_tools=true
                shift
                ;;
            --skip-validation)
                skip_validation=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_header "PHCityRent Environment Setup"
    print_status "Setting up $environment environment"
    
    # Check prerequisites
    check_node_version
    check_npm_version
    
    # Setup environment
    setup_env_files "$environment"
    create_directories
    
    if [ "$skip_deps" = false ]; then
        install_dependencies
    fi
    
    if [ "$skip_db" = false ]; then
        setup_database "$environment"
    fi
    
    if [ "$skip_tools" = false ] && [ "$environment" = "development" ]; then
        setup_dev_tools
    fi
    
    if [ "$skip_validation" = false ]; then
        validate_environment
    fi
    
    print_header "Setup Complete!"
    print_status "Environment: $environment"
    print_status "Backend: http://localhost:3001"
    print_status "Frontend: http://localhost:8081"
    print_status ""
    print_status "To start the application:"
    print_status "  npm run dev        # Start both frontend and backend"
    print_status "  npm run dev:backend # Start backend only"
    print_status "  npm run dev:frontend # Start frontend only"
}

# Run main function with all arguments
main "$@"
