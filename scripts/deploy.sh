#!/bin/bash

# Deployment Script for PHCityRent
# Supports deployment to staging and production environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOY_LOG="$PROJECT_ROOT/deploy.log"

# Default values
ENVIRONMENT=""
SKIP_TESTS=false
SKIP_BUILD=false
SKIP_BACKUP=false
DRY_RUN=false
FORCE_DEPLOY=false

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$DEPLOY_LOG"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$DEPLOY_LOG"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$DEPLOY_LOG"
}

print_header() {
    echo -e "${BLUE}[DEPLOY]${NC} $1" | tee -a "$DEPLOY_LOG"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking prerequisites"
    
    # Check if required commands exist
    local required_commands=("docker" "docker-compose" "git" "curl")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            print_error "$cmd is not installed or not in PATH"
            exit 1
        fi
    done
    
    # Check Docker daemon
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check Git status
    if [ -d ".git" ]; then
        if [ -n "$(git status --porcelain)" ] && [ "$FORCE_DEPLOY" = false ]; then
            print_error "Working directory is not clean. Commit or stash changes first."
            print_warning "Use --force to deploy anyway"
            exit 1
        fi
    fi
    
    print_status "Prerequisites check passed"
}

# Function to validate environment
validate_environment() {
    print_header "Validating environment: $ENVIRONMENT"
    
    case $ENVIRONMENT in
        staging|production)
            # Check if environment files exist
            if [ ! -f "Backend/.env.$ENVIRONMENT" ]; then
                print_error "Backend/.env.$ENVIRONMENT not found"
                exit 1
            fi
            
            if [ ! -f "Frontend/.env.$ENVIRONMENT" ]; then
                print_error "Frontend/.env.$ENVIRONMENT not found"
                exit 1
            fi
            
            # Check required environment variables
            source "Backend/.env.$ENVIRONMENT"
            
            local required_vars=("DB_HOST" "DB_PASSWORD" "JWT_SECRET" "JWT_REFRESH_SECRET")
            
            for var in "${required_vars[@]}"; do
                if [ -z "${!var}" ]; then
                    print_error "Required environment variable $var is not set"
                    exit 1
                fi
            done
            ;;
        *)
            print_error "Invalid environment: $ENVIRONMENT"
            print_error "Supported environments: staging, production"
            exit 1
            ;;
    esac
    
    print_status "Environment validation passed"
}

# Function to run tests
run_tests() {
    if [ "$SKIP_TESTS" = true ]; then
        print_warning "Skipping tests"
        return
    fi
    
    print_header "Running tests"
    
    # Backend tests
    print_status "Running backend tests..."
    cd "$PROJECT_ROOT/Backend"
    npm test
    
    # Frontend tests
    print_status "Running frontend tests..."
    cd "$PROJECT_ROOT/Frontend"
    npm test
    
    cd "$PROJECT_ROOT"
    print_status "All tests passed"
}

# Function to build application
build_application() {
    if [ "$SKIP_BUILD" = true ]; then
        print_warning "Skipping build"
        return
    fi
    
    print_header "Building application"
    
    # Set environment files
    cp "Backend/.env.$ENVIRONMENT" "Backend/.env"
    cp "Frontend/.env.$ENVIRONMENT" "Frontend/.env"
    
    # Build Docker images
    print_status "Building Docker images..."
    
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.production.yml build --no-cache
    else
        docker-compose -f docker-compose.yml build --no-cache
    fi
    
    print_status "Build completed successfully"
}

# Function to create backup
create_backup() {
    if [ "$SKIP_BACKUP" = true ] || [ "$ENVIRONMENT" = "staging" ]; then
        print_warning "Skipping backup"
        return
    fi
    
    print_header "Creating backup"
    
    local backup_dir="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Database backup
    print_status "Creating database backup..."
    docker-compose exec -T postgres pg_dump -U "$DB_USERNAME" "$DB_NAME" > "$backup_dir/database.sql"
    
    # Uploads backup
    print_status "Creating uploads backup..."
    if [ -d "$PROJECT_ROOT/Backend/uploads" ]; then
        tar -czf "$backup_dir/uploads.tar.gz" -C "$PROJECT_ROOT/Backend" uploads
    fi
    
    # Configuration backup
    print_status "Creating configuration backup..."
    cp "Backend/.env" "$backup_dir/backend.env"
    cp "Frontend/.env" "$backup_dir/frontend.env"
    
    print_status "Backup created at $backup_dir"
}

# Function to deploy application
deploy_application() {
    print_header "Deploying application to $ENVIRONMENT"
    
    if [ "$DRY_RUN" = true ]; then
        print_warning "DRY RUN MODE - No actual deployment will occur"
        return
    fi
    
    # Stop existing services
    print_status "Stopping existing services..."
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.production.yml down
    else
        docker-compose down
    fi
    
    # Start new services
    print_status "Starting new services..."
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d
    else
        docker-compose up -d
    fi
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Health check
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3001/api/v1/health >/dev/null 2>&1; then
            print_status "Backend health check passed"
            break
        fi
        
        print_status "Waiting for backend... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "Backend health check failed"
        exit 1
    fi
    
    print_status "Deployment completed successfully"
}

# Function to run post-deployment tasks
post_deployment() {
    print_header "Running post-deployment tasks"
    
    # Run database migrations
    print_status "Running database migrations..."
    docker-compose exec backend npm run migration:run
    
    # Clear caches
    print_status "Clearing caches..."
    docker-compose exec redis redis-cli FLUSHALL
    
    # Send deployment notification
    if [ "$ENVIRONMENT" = "production" ]; then
        print_status "Sending deployment notification..."
        # Add your notification logic here (Slack, email, etc.)
    fi
    
    print_status "Post-deployment tasks completed"
}

# Function to rollback deployment
rollback_deployment() {
    print_header "Rolling back deployment"
    
    # Get previous image tags
    local previous_backend_image=$(docker images --format "table {{.Repository}}:{{.Tag}}" | grep phcityrent-backend | sed -n '2p')
    local previous_frontend_image=$(docker images --format "table {{.Repository}}:{{.Tag}}" | grep phcityrent-frontend | sed -n '2p')
    
    if [ -n "$previous_backend_image" ] && [ -n "$previous_frontend_image" ]; then
        print_status "Rolling back to previous images..."
        
        # Update docker-compose to use previous images
        # This is a simplified rollback - in production, you'd want more sophisticated versioning
        
        docker-compose down
        docker-compose up -d
        
        print_status "Rollback completed"
    else
        print_error "No previous images found for rollback"
        exit 1
    fi
}

# Function to show deployment status
show_status() {
    print_header "Deployment Status"
    
    echo "Services:"
    docker-compose ps
    
    echo -e "\nLogs (last 20 lines):"
    docker-compose logs --tail=20
    
    echo -e "\nHealth Checks:"
    curl -s http://localhost:3001/api/v1/health | jq '.' || echo "Backend health check failed"
}

# Function to show help
show_help() {
    echo "Usage: $0 <environment> [options]"
    echo ""
    echo "Environments:"
    echo "  staging      Deploy to staging environment"
    echo "  production   Deploy to production environment"
    echo ""
    echo "Options:"
    echo "  --skip-tests      Skip running tests"
    echo "  --skip-build      Skip building application"
    echo "  --skip-backup     Skip creating backup"
    echo "  --dry-run         Show what would be deployed without actually deploying"
    echo "  --force           Force deployment even with uncommitted changes"
    echo "  --rollback        Rollback to previous deployment"
    echo "  --status          Show current deployment status"
    echo "  --help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 staging                    # Deploy to staging"
    echo "  $0 production --skip-tests    # Deploy to production without tests"
    echo "  $0 production --dry-run       # Show what would be deployed"
    echo "  $0 production --rollback      # Rollback production deployment"
}

# Main function
main() {
    # Initialize log file
    echo "Deployment started at $(date)" > "$DEPLOY_LOG"
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            staging|production)
                ENVIRONMENT="$1"
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --force)
                FORCE_DEPLOY=true
                shift
                ;;
            --rollback)
                rollback_deployment
                exit 0
                ;;
            --status)
                show_status
                exit 0
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Validate arguments
    if [ -z "$ENVIRONMENT" ]; then
        print_error "Environment is required"
        show_help
        exit 1
    fi
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Run deployment steps
    check_prerequisites
    validate_environment
    run_tests
    create_backup
    build_application
    deploy_application
    post_deployment
    
    print_header "Deployment Summary"
    print_status "Environment: $ENVIRONMENT"
    print_status "Deployment completed at $(date)"
    print_status "Log file: $DEPLOY_LOG"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        print_status "Production URL: https://phcityrent.com"
        print_status "Admin URL: https://admin.phcityrent.com"
    else
        print_status "Staging URL: https://staging.phcityrent.com"
        print_status "Admin URL: https://staging-admin.phcityrent.com"
    fi
}

# Run main function with all arguments
main "$@"
