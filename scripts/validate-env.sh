#!/bin/bash

# Environment Variables Validation Script
# Checks if all required production environment variables are set

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}[VALIDATE]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Function to check if environment variable is set
check_env_var() {
    local var_name="$1"
    local required="$2"
    local description="$3"
    
    if [ -n "${!var_name}" ]; then
        print_success "$var_name is set ($description)"
        return 0
    else
        if [ "$required" = "true" ]; then
            print_error "$var_name is REQUIRED but not set ($description)"
            return 1
        else
            print_warning "$var_name is optional but not set ($description)"
            return 0
        fi
    fi
}

print_header "PHCityRent Production Environment Validation"
echo "Checking all required and optional environment variables..."
echo ""

# Track validation results
ERRORS=0
WARNINGS=0

print_header "Database Configuration (REQUIRED)"
check_env_var "PROD_DB_HOST" "true" "Database host" || ((ERRORS++))
check_env_var "PROD_DB_USERNAME" "true" "Database username" || ((ERRORS++))
check_env_var "PROD_DB_PASSWORD" "true" "Database password" || ((ERRORS++))
check_env_var "PROD_DB_NAME" "true" "Database name" || ((ERRORS++))
echo ""

print_header "Redis Configuration (REQUIRED)"
check_env_var "PROD_REDIS_HOST" "true" "Redis host" || ((ERRORS++))
check_env_var "PROD_REDIS_PASSWORD" "true" "Redis password" || ((ERRORS++))
echo ""

print_header "JWT & Security Secrets (REQUIRED)"
check_env_var "PROD_JWT_SECRET" "true" "JWT secret key" || ((ERRORS++))
check_env_var "PROD_JWT_REFRESH_SECRET" "true" "JWT refresh secret" || ((ERRORS++))
check_env_var "PROD_SESSION_SECRET" "true" "Session secret" || ((ERRORS++))
echo ""

print_header "Payment Gateway Configuration (IMPORTANT)"
check_env_var "PROD_PAYSTACK_SECRET_KEY" "false" "Paystack secret key" || ((WARNINGS++))
check_env_var "PROD_PAYSTACK_PUBLIC_KEY" "false" "Paystack public key" || ((WARNINGS++))
check_env_var "PROD_PAYSTACK_WEBHOOK_SECRET" "false" "Paystack webhook secret" || ((WARNINGS++))
check_env_var "PROD_FLUTTERWAVE_SECRET_KEY" "false" "Flutterwave secret key" || ((WARNINGS++))
check_env_var "PROD_FLUTTERWAVE_PUBLIC_KEY" "false" "Flutterwave public key" || ((WARNINGS++))
check_env_var "PROD_FLUTTERWAVE_WEBHOOK_SECRET" "false" "Flutterwave webhook secret" || ((WARNINGS++))
echo ""

print_header "Email Configuration (IMPORTANT)"
check_env_var "PROD_SENDGRID_API_KEY" "false" "SendGrid API key" || ((WARNINGS++))
echo ""

print_header "External Services (OPTIONAL)"
check_env_var "PROD_GOOGLE_MAPS_API_KEY" "false" "Google Maps API key" || ((WARNINGS++))
check_env_var "PROD_CLOUDINARY_CLOUD_NAME" "false" "Cloudinary cloud name" || ((WARNINGS++))
check_env_var "PROD_CLOUDINARY_API_KEY" "false" "Cloudinary API key" || ((WARNINGS++))
check_env_var "PROD_CLOUDINARY_API_SECRET" "false" "Cloudinary API secret" || ((WARNINGS++))
echo ""

print_header "Monitoring & Analytics (OPTIONAL)"
check_env_var "PROD_SENTRY_DSN" "false" "Sentry DSN" || ((WARNINGS++))
check_env_var "PROD_NEW_RELIC_LICENSE_KEY" "false" "New Relic license key" || ((WARNINGS++))
check_env_var "PROD_GOOGLE_ANALYTICS_ID" "false" "Google Analytics ID" || ((WARNINGS++))
echo ""

# Summary
print_header "Validation Summary"
if [ $ERRORS -eq 0 ]; then
    print_success "All required environment variables are set!"
    if [ $WARNINGS -eq 0 ]; then
        print_success "All optional environment variables are also set!"
        echo -e "${GREEN}🎉 Ready for production deployment!${NC}"
    else
        print_warning "$WARNINGS optional environment variables are not set"
        echo -e "${YELLOW}⚠️  You can deploy, but some features may be limited${NC}"
    fi
    echo ""
    echo "Next steps:"
    echo "1. Test deployment: ./scripts/deploy.sh production --dry-run"
    echo "2. Deploy to production: ./scripts/deploy.sh production"
    exit 0
else
    print_error "$ERRORS required environment variables are missing!"
    echo -e "${RED}❌ Cannot deploy to production${NC}"
    echo ""
    echo "Please set the missing required variables and run this script again."
    echo "Use: ./scripts/setup-production-env.sh for guided setup"
    exit 1
fi
