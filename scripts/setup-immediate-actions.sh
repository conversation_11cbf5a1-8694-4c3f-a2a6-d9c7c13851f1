#!/bin/bash

# Setup script for immediate CI/CD actions
# This script implements the 4 immediate actions for better development workflow

set -e

echo "🚀 Setting up Immediate CI/CD Actions..."
echo "=================================================="

# Change to project root
cd "$(dirname "$0")/.."

# ==========================================
# 1. Install Pre-commit Dependencies
# ==========================================
echo ""
echo "📦 Installing pre-commit dependencies..."
cd frontend

# Install Husky and related tools
npm install --save-dev husky@^8.0.3 lint-staged@^15.2.0 @commitlint/cli@^18.4.3 @commitlint/config-conventional@^18.4.3 prettier@^3.1.1

# Initialize <PERSON>sky
npx husky install

# Make pre-commit hooks executable
chmod +x .husky/pre-commit
chmod +x .husky/commit-msg

echo "✅ Pre-commit tools installed and configured"

# ==========================================
# 2. Setup Git Hooks
# ==========================================
echo ""
echo "🔧 Setting up Git hooks..."

# Add prepare script to package.json if not exists
if ! grep -q '"prepare"' package.json; then
    # Add prepare script
    sed -i.bak 's/"scripts": {/"scripts": {\n    "prepare": "husky install",/' package.json
    rm package.json.bak
fi

# Add lint-staged configuration if not exists
if ! grep -q '"lint-staged"' package.json; then
    # Add lint-staged config to package.json
    cat >> package.json.tmp << 'EOF'
{
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{json,md,yml,yaml}": [
      "prettier --write"
    ]
  }
}
EOF
    
    # Merge with existing package.json
    jq -s '.[0] * .[1]' package.json package.json.tmp > package.json.new
    mv package.json.new package.json
    rm package.json.tmp
fi

echo "✅ Git hooks configured"

# ==========================================
# 3. Test the Setup
# ==========================================
echo ""
echo "🧪 Testing the setup..."

# Test linting
echo "Testing ESLint..."
npm run lint || echo "⚠️  Linting issues found - will be fixed by pre-commit hooks"

# Test type checking
echo "Testing TypeScript..."
npm run type-check || echo "⚠️  Type errors found - please fix before committing"

# Test build
echo "Testing build..."
npm run build || echo "⚠️  Build issues found - please fix before committing"

# Test unit tests
echo "Testing unit tests..."
npm run test:unit || echo "⚠️  Test failures found - please fix before committing"

echo "✅ Setup testing completed"

# ==========================================
# 4. Create GitHub Branch Protection Guide
# ==========================================
echo ""
echo "📋 Creating branch protection setup guide..."

cd ..

cat > BRANCH_PROTECTION_SETUP.md << 'EOF'
# Branch Protection Setup Guide

## 🛡️ Immediate Action Required

To complete the setup, you need to configure branch protection rules on GitHub:

### Step 1: Go to Repository Settings
1. Navigate to: https://github.com/Woldreamz-Inc/ptownmoving/settings/branches
2. Click "Add rule"

### Step 2: Configure Main Branch Protection
```
Branch name pattern: main

✅ Require a pull request before merging
  ✅ Require approvals: 1
  ✅ Dismiss stale PR approvals when new commits are pushed

✅ Require status checks to pass before merging
  ✅ Require branches to be up to date before merging
  Required status checks:
    - Lint & Type Check
    - Security Scan
    - Build Test
    - Run Tests

✅ Require conversation resolution before merging
✅ Include administrators
```

### Step 3: Configure Develop Branch Protection
```
Branch name pattern: develop

✅ Require a pull request before merging
  ✅ Require approvals: 1

✅ Require status checks to pass before merging
  Required status checks:
    - Lint & Type Check
    - Build Test
```

### Step 4: Test the Protection
1. Try to push directly to main (should be blocked)
2. Create a feature branch and PR (should require checks)
3. Verify CI pipeline runs on PR creation

## 🎯 What's Now Working

✅ Pre-commit hooks prevent bad code from being committed
✅ Conventional commit messages are enforced
✅ Code is automatically formatted and linted
✅ Type checking runs before commits
✅ Unit tests run in CI pipeline
✅ Security scanning is active
✅ Build verification happens automatically

## 🚀 Next Steps

1. Set up branch protection rules (above)
2. Add team members to appropriate GitHub teams
3. Configure Slack/email notifications for CI failures
4. Set up staging environment deployment
5. Add integration tests
6. Configure performance monitoring

EOF

echo "✅ Branch protection guide created: BRANCH_PROTECTION_SETUP.md"

# ==========================================
# 5. Summary and Next Steps
# ==========================================
echo ""
echo "🎉 Immediate Actions Setup Complete!"
echo "=================================================="
echo ""
echo "✅ COMPLETED:"
echo "   • Pre-commit hooks installed and configured"
echo "   • Conventional commit messages enforced"
echo "   • Code formatting and linting automated"
echo "   • Basic CI pipeline configured"
echo "   • Unit testing framework enhanced"
echo "   • Security scanning enabled"
echo ""
echo "🔧 MANUAL STEPS REQUIRED:"
echo "   1. Set up branch protection rules on GitHub"
echo "   2. Review and merge any pending changes"
echo "   3. Test the workflow with a feature branch"
echo ""
echo "📋 FILES CREATED/MODIFIED:"
echo "   • .github/workflows/basic-ci.yml"
echo "   • .github/CODEOWNERS"
echo "   • frontend/.husky/pre-commit"
echo "   • frontend/.husky/commit-msg"
echo "   • frontend/commitlint.config.js"
echo "   • frontend/.prettierrc"
echo "   • frontend/.lintstagedrc.json"
echo "   • Test files in frontend/src/tests/"
echo ""
echo "🚀 NEXT COMMIT WILL:"
echo "   • Run pre-commit checks automatically"
echo "   • Enforce conventional commit messages"
echo "   • Trigger CI pipeline on push"
echo "   • Run all tests and security scans"
echo ""
echo "Try making a commit now to test the new workflow!"
echo "Example: git add . && git commit -m 'feat: implement pre-commit hooks and CI pipeline'"
echo ""

# Return to original directory
cd frontend
echo "Current directory: $(pwd)"
echo "Ready for development! 🎯"
