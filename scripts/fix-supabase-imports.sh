#!/bin/bash

# Script to fix Supabase imports and replace with mock data service
# This script will systematically replace Supabase imports across the frontend

set -e

echo "🔧 Fixing Supabase imports in frontend..."

# Change to frontend directory
cd frontend

# List of files to fix
FILES=(
    "src/hooks/useProperty.ts"
    "src/hooks/usePayment.ts"
    "src/hooks/useVerificationStatus.ts"
    "src/hooks/useAgentApplication.ts"
    "src/hooks/useCommissions.ts"
    "src/hooks/useMaintenanceRequests.ts"
    "src/hooks/useDashboardStats.ts"
    "src/hooks/useImageUpload.ts"
    "src/hooks/useNotifications.ts"
    "src/hooks/useRentalAgreements.ts"
    "src/hooks/useMessages.ts"
    "src/hooks/useDocumentUpload.ts"
    "src/hooks/useEscrow.ts"
    "src/hooks/useSavedSearches.ts"
    "src/components/admin/AdminStats.tsx"
    "src/components/admin/ApplicationAnalytics.tsx"
    "src/components/admin/NotificationCenter.tsx"
    "src/components/admin/AdminPromotion.tsx"
    "src/components/admin/ApplicationDetailsModal.tsx"
    "src/components/rental/RentalApplicationForm.tsx"
    "src/components/tenant/QuickContactAgent.tsx"
    "src/services/whatsappService.ts"
    "src/services/paymentHistoryService.ts"
    "src/services/emailService.ts"
    "src/services/messagingService.ts"
    "src/services/smsService.ts"
    "src/services/marketTrendAnalysisService.ts"
    "src/services/dashboardService.ts"
    "src/services/performanceMetricsService.ts"
    "src/services/reportGenerationService.ts"
    "src/services/realTimeDataService.ts"
)

# Function to create backup
create_backup() {
    local file="$1"
    if [ -f "$file" ]; then
        cp "$file" "$file.backup"
        echo "  📋 Created backup: $file.backup"
    fi
}

# Function to replace Supabase import
replace_supabase_import() {
    local file="$1"
    if [ -f "$file" ]; then
        echo "  🔄 Processing: $file"
        
        # Replace the import statement
        sed -i.tmp "s|import { supabase } from '@/integrations/supabase/client';|import { MockDataService } from '@/services/mockDataService';|g" "$file"
        
        # Remove the temporary file
        rm -f "$file.tmp"
        
        echo "  ✅ Updated import in: $file"
    else
        echo "  ⚠️  File not found: $file"
    fi
}

# Function to add mock data service import if not present
add_mock_import() {
    local file="$1"
    if [ -f "$file" ]; then
        # Check if MockDataService import already exists
        if ! grep -q "MockDataService" "$file"; then
            # Add import after other imports
            sed -i.tmp "1i\\
import { MockDataService } from '@/services/mockDataService';" "$file"
            rm -f "$file.tmp"
            echo "  ➕ Added MockDataService import to: $file"
        fi
    fi
}

# Main processing
echo "📁 Processing files..."

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "🔧 Processing: $file"
        create_backup "$file"
        replace_supabase_import "$file"
    else
        echo "⚠️  File not found: $file"
    fi
done

echo ""
echo "🎯 Creating simplified versions of complex files..."

# Create simplified versions of hooks that are heavily dependent on Supabase
cat > src/hooks/useProperty.ts << 'EOF'
import { useQuery } from '@tanstack/react-query';
import { MockDataService, MockProperty } from '@/services/mockDataService';

export const useProperty = (id: string) => {
  return useQuery({
    queryKey: ['property', id],
    queryFn: async (): Promise<MockProperty | null> => {
      return await MockDataService.getProperty(id);
    },
    enabled: !!id,
  });
};

export const useProperties = () => {
  return useQuery({
    queryKey: ['properties'],
    queryFn: async (): Promise<MockProperty[]> => {
      return await MockDataService.getProperties();
    },
  });
};
EOF

cat > src/hooks/useDashboardStats.ts << 'EOF'
import { useQuery } from '@tanstack/react-query';
import { MockDataService } from '@/services/mockDataService';

export const useDashboardStats = () => {
  return useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      return await MockDataService.getStats();
    },
  });
};
EOF

cat > src/hooks/useNotifications.ts << 'EOF'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { MockDataService } from '@/services/mockDataService';

export const useNotifications = (userId: string) => {
  return useQuery({
    queryKey: ['notifications', userId],
    queryFn: async () => {
      return await MockDataService.getNotifications(userId);
    },
    enabled: !!userId,
  });
};

export const useMarkNotificationAsRead = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (notificationId: string) => {
      await MockDataService.markNotificationAsRead(notificationId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
};
EOF

echo "✅ Created simplified hook implementations"

echo ""
echo "🧹 Cleaning up..."

# Remove backup files if everything went well
read -p "Remove backup files? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    find . -name "*.backup" -delete
    echo "🗑️  Removed backup files"
else
    echo "📋 Backup files preserved"
fi

echo ""
echo "✅ Supabase import fixes completed!"
echo "🚀 You can now run the development server without Supabase dependencies"
echo ""
echo "Next steps:"
echo "1. Run 'npm run dev' to test the application"
echo "2. Check for any remaining import errors"
echo "3. Test the authentication UI components"
echo ""
EOF
