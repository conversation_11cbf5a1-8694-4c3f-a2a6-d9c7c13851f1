#!/bin/bash

# Quick fix for Supabase imports
echo "🔧 Quick fixing Supabase imports..."

cd frontend

# Replace Supabase imports with MockDataService in all files
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i.bak "s|import { supabase } from '@/integrations/supabase/client';|import { MockDataService } from '@/services/mockDataService';|g"

# Replace other problematic imports
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i.bak "s|from '@/services/performanceMonitoringService'|from '@/services/mockServices'|g"
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i.bak "s|from '@/services/validationService'|from '@/services/mockServices'|g"
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i.bak "s|from '@/middleware/rateLimitingMiddleware'|from '@/services/mockServices'|g"
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i.bak "s|from '@/middleware/csrfMiddleware'|from '@/services/mockServices'|g"

# Clean up backup files
find src -name "*.bak" -delete

echo "✅ Quick fix completed!"
