#!/bin/bash

# 🚀 Feature Integration Monitoring Script
# Usage: ./scripts/monitor-integration.sh

set -e

echo "🔍 FEATURE-AGENT INTEGRATION MONITORING"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check if we're in the right directory
if [ ! -d ".git" ]; then
    print_status $RED "❌ Error: Not in a git repository"
    exit 1
fi

print_status $BLUE "📊 BRANCH STATUS ANALYSIS"
echo "----------------------------------------"

# Fetch latest changes
print_status $YELLOW "🔄 Fetching latest changes..."
git fetch origin

# Show branch information
print_status $GREEN "📋 Branch Overview:"
echo "Current branch: $(git branch --show-current)"
echo "Available branches:"
git branch -a | grep -E "(feature-agent|staging|main)" | sed 's/^/  /'

echo ""
print_status $BLUE "📈 CHANGE ANALYSIS"
echo "----------------------------------------"

# Check if feature-agent exists
if git show-ref --verify --quiet refs/remotes/origin/feature-agent; then
    print_status $GREEN "✅ feature-agent branch found"
    
    # Show commit differences
    echo ""
    print_status $YELLOW "📝 Recent commits on feature-agent:"
    git log origin/feature-agent --oneline -5 | sed 's/^/  /'
    
    echo ""
    print_status $YELLOW "📊 Changes vs main branch:"
    git diff origin/main origin/feature-agent --stat | head -20
    
    echo ""
    print_status $YELLOW "🔢 Summary of changes:"
    CHANGES=$(git diff origin/main origin/feature-agent --stat | tail -1)
    echo "  $CHANGES"
    
else
    print_status $RED "❌ feature-agent branch not found"
fi

echo ""
print_status $BLUE "🔍 CI/CD PIPELINE STATUS"
echo "----------------------------------------"

# Check for GitHub CLI
if command -v gh &> /dev/null; then
    print_status $GREEN "✅ GitHub CLI available"
    
    echo ""
    print_status $YELLOW "🏃 Recent workflow runs:"
    gh run list --limit 5 --json status,conclusion,name,createdAt,headBranch | \
    jq -r '.[] | "  \(.createdAt | split("T")[0]) - \(.name) (\(.headBranch)) - \(.status)/\(.conclusion // "running")"'
    
    echo ""
    print_status $YELLOW "📋 Open Pull Requests:"
    gh pr list --json number,title,headRefName,baseRefName,state | \
    jq -r '.[] | "  #\(.number): \(.title) (\(.headRefName) → \(.baseRefName)) - \(.state)"'
    
else
    print_status $YELLOW "⚠️  GitHub CLI not available. Install with: brew install gh"
    print_status $BLUE "🔗 Manual check: https://github.com/Woldreamz-Inc/ptownmoving/actions"
fi

echo ""
print_status $BLUE "🎯 INTEGRATION CHECKLIST"
echo "----------------------------------------"

# Check if PR exists
if command -v gh &> /dev/null; then
    PR_COUNT=$(gh pr list --head feature-agent --json number | jq length)
    if [ "$PR_COUNT" -gt 0 ]; then
        print_status $GREEN "✅ Pull Request exists for feature-agent"
        gh pr list --head feature-agent --json number,title,url | \
        jq -r '.[] | "  PR #\(.number): \(.title)\n  URL: \(.url)"'
    else
        print_status $YELLOW "⏳ No Pull Request found for feature-agent"
        print_status $BLUE "   Next step: Developer should create PR feature-agent → staging"
    fi
fi

echo ""
print_status $YELLOW "📋 Manual Monitoring Tasks:"
echo "  1. 🔍 Check GitHub Actions: https://github.com/Woldreamz-Inc/ptownmoving/actions"
echo "  2. 📝 Review Pull Requests: https://github.com/Woldreamz-Inc/ptownmoving/pulls"
echo "  3. 🌿 Monitor Branches: https://github.com/Woldreamz-Inc/ptownmoving/branches"
echo "  4. 📊 Check Deployments: https://github.com/Woldreamz-Inc/ptownmoving/deployments"

echo ""
print_status $BLUE "🚀 NEXT STEPS"
echo "----------------------------------------"
print_status $GREEN "For Project Manager:"
echo "  1. Wait for developer to create PR: feature-agent → staging"
echo "  2. Monitor CI/CD pipeline results"
echo "  3. Review automated test results"
echo "  4. Coordinate manual QA testing"
echo "  5. Approve staging deployment"
echo "  6. Monitor staging environment"
echo "  7. Approve production deployment"

echo ""
print_status $GREEN "For Developer:"
echo "  1. Create PR: feature-agent → staging"
echo "  2. Fix any CI/CD failures"
echo "  3. Address code review feedback"
echo "  4. Support QA testing"
echo "  5. Monitor deployment health"

echo ""
print_status $BLUE "🔄 Run this script again to check for updates!"
echo "========================================"
