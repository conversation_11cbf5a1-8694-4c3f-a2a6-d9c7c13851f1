#!/bin/bash

# Backup Script for PHCityRent
# Creates automated backups of database, uploads, and configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_ROOT="$PROJECT_ROOT/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_ROOT/$TIMESTAMP"

# Default values
RETENTION_DAYS=30
COMPRESS=true
UPLOAD_TO_S3=false
S3_BUCKET=""
ENVIRONMENT="production"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[BACKUP]${NC} $1"
}

# Function to create backup directory
create_backup_dir() {
    print_header "Creating backup directory"
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$BACKUP_DIR/database"
    mkdir -p "$BACKUP_DIR/uploads"
    mkdir -p "$BACKUP_DIR/config"
    mkdir -p "$BACKUP_DIR/logs"
    
    print_status "Backup directory created: $BACKUP_DIR"
}

# Function to backup database
backup_database() {
    print_header "Backing up database"
    
    # Load environment variables
    if [ -f "Backend/.env" ]; then
        source Backend/.env
    else
        print_error "Backend/.env file not found"
        exit 1
    fi
    
    # Create database backup
    print_status "Creating PostgreSQL dump..."
    
    if docker-compose ps postgres | grep -q "Up"; then
        # Database is running in Docker
        docker-compose exec -T postgres pg_dump \
            -U "$DB_USERNAME" \
            -h localhost \
            -p 5432 \
            --verbose \
            --clean \
            --no-owner \
            --no-privileges \
            "$DB_NAME" > "$BACKUP_DIR/database/database.sql"
    else
        # Database is running locally
        PGPASSWORD="$DB_PASSWORD" pg_dump \
            -U "$DB_USERNAME" \
            -h "$DB_HOST" \
            -p "$DB_PORT" \
            --verbose \
            --clean \
            --no-owner \
            --no-privileges \
            "$DB_NAME" > "$BACKUP_DIR/database/database.sql"
    fi
    
    # Create schema-only backup
    print_status "Creating schema-only backup..."
    
    if docker-compose ps postgres | grep -q "Up"; then
        docker-compose exec -T postgres pg_dump \
            -U "$DB_USERNAME" \
            -h localhost \
            -p 5432 \
            --schema-only \
            --verbose \
            "$DB_NAME" > "$BACKUP_DIR/database/schema.sql"
    else
        PGPASSWORD="$DB_PASSWORD" pg_dump \
            -U "$DB_USERNAME" \
            -h "$DB_HOST" \
            -p "$DB_PORT" \
            --schema-only \
            --verbose \
            "$DB_NAME" > "$BACKUP_DIR/database/schema.sql"
    fi
    
    # Verify backup
    if [ -s "$BACKUP_DIR/database/database.sql" ]; then
        print_status "Database backup completed successfully"
        
        # Get backup size
        local backup_size=$(du -h "$BACKUP_DIR/database/database.sql" | cut -f1)
        print_status "Database backup size: $backup_size"
    else
        print_error "Database backup failed or is empty"
        exit 1
    fi
}

# Function to backup uploads
backup_uploads() {
    print_header "Backing up uploads"
    
    local uploads_dir="$PROJECT_ROOT/Backend/uploads"
    
    if [ -d "$uploads_dir" ] && [ "$(ls -A $uploads_dir)" ]; then
        print_status "Creating uploads archive..."
        
        if [ "$COMPRESS" = true ]; then
            tar -czf "$BACKUP_DIR/uploads/uploads.tar.gz" -C "$PROJECT_ROOT/Backend" uploads
        else
            cp -r "$uploads_dir" "$BACKUP_DIR/uploads/"
        fi
        
        # Get backup size
        if [ "$COMPRESS" = true ]; then
            local backup_size=$(du -h "$BACKUP_DIR/uploads/uploads.tar.gz" | cut -f1)
        else
            local backup_size=$(du -sh "$BACKUP_DIR/uploads/uploads" | cut -f1)
        fi
        
        print_status "Uploads backup completed successfully"
        print_status "Uploads backup size: $backup_size"
    else
        print_warning "No uploads directory found or directory is empty"
    fi
}

# Function to backup configuration
backup_config() {
    print_header "Backing up configuration"
    
    # Backup environment files (without sensitive data)
    print_status "Backing up environment configuration..."
    
    # Create sanitized environment files
    if [ -f "Backend/.env" ]; then
        # Remove sensitive values but keep structure
        sed 's/=.*/=***REDACTED***/g' "Backend/.env" > "$BACKUP_DIR/config/backend.env.template"
    fi
    
    if [ -f "Frontend/.env" ]; then
        sed 's/=.*/=***REDACTED***/g' "Frontend/.env" > "$BACKUP_DIR/config/frontend.env.template"
    fi
    
    # Backup package.json files
    print_status "Backing up package configurations..."
    
    if [ -f "Backend/package.json" ]; then
        cp "Backend/package.json" "$BACKUP_DIR/config/backend-package.json"
    fi
    
    if [ -f "Frontend/package.json" ]; then
        cp "Frontend/package.json" "$BACKUP_DIR/config/frontend-package.json"
    fi
    
    if [ -f "package.json" ]; then
        cp "package.json" "$BACKUP_DIR/config/root-package.json"
    fi
    
    # Backup Docker configuration
    print_status "Backing up Docker configuration..."
    
    if [ -f "docker-compose.yml" ]; then
        cp "docker-compose.yml" "$BACKUP_DIR/config/"
    fi
    
    if [ -f "docker-compose.production.yml" ]; then
        cp "docker-compose.production.yml" "$BACKUP_DIR/config/"
    fi
    
    # Backup nginx configuration if exists
    if [ -d "nginx" ]; then
        cp -r nginx "$BACKUP_DIR/config/"
    fi
    
    print_status "Configuration backup completed"
}

# Function to backup logs
backup_logs() {
    print_header "Backing up logs"
    
    local logs_dir="$PROJECT_ROOT/Backend/logs"
    
    if [ -d "$logs_dir" ] && [ "$(ls -A $logs_dir)" ]; then
        print_status "Creating logs archive..."
        
        if [ "$COMPRESS" = true ]; then
            tar -czf "$BACKUP_DIR/logs/logs.tar.gz" -C "$PROJECT_ROOT/Backend" logs
        else
            cp -r "$logs_dir" "$BACKUP_DIR/logs/"
        fi
        
        print_status "Logs backup completed"
    else
        print_warning "No logs directory found or directory is empty"
    fi
}

# Function to create backup manifest
create_manifest() {
    print_header "Creating backup manifest"
    
    local manifest_file="$BACKUP_DIR/manifest.json"
    
    cat > "$manifest_file" << EOF
{
  "backup_id": "$TIMESTAMP",
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "environment": "$ENVIRONMENT",
  "version": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "files": {
    "database": "$(ls -la $BACKUP_DIR/database/ 2>/dev/null | wc -l)",
    "uploads": "$(ls -la $BACKUP_DIR/uploads/ 2>/dev/null | wc -l)",
    "config": "$(ls -la $BACKUP_DIR/config/ 2>/dev/null | wc -l)",
    "logs": "$(ls -la $BACKUP_DIR/logs/ 2>/dev/null | wc -l)"
  },
  "sizes": {
    "total": "$(du -sh $BACKUP_DIR | cut -f1)",
    "database": "$(du -sh $BACKUP_DIR/database 2>/dev/null | cut -f1 || echo '0')",
    "uploads": "$(du -sh $BACKUP_DIR/uploads 2>/dev/null | cut -f1 || echo '0')",
    "config": "$(du -sh $BACKUP_DIR/config 2>/dev/null | cut -f1 || echo '0')",
    "logs": "$(du -sh $BACKUP_DIR/logs 2>/dev/null | cut -f1 || echo '0')"
  }
}
EOF
    
    print_status "Backup manifest created"
}

# Function to upload to S3
upload_to_s3() {
    if [ "$UPLOAD_TO_S3" = false ] || [ -z "$S3_BUCKET" ]; then
        return
    fi
    
    print_header "Uploading backup to S3"
    
    # Check if AWS CLI is installed
    if ! command -v aws >/dev/null 2>&1; then
        print_warning "AWS CLI not found, skipping S3 upload"
        return
    fi
    
    # Create archive
    print_status "Creating backup archive..."
    local archive_name="phcityrent-backup-$TIMESTAMP.tar.gz"
    tar -czf "$BACKUP_ROOT/$archive_name" -C "$BACKUP_ROOT" "$TIMESTAMP"
    
    # Upload to S3
    print_status "Uploading to S3 bucket: $S3_BUCKET"
    aws s3 cp "$BACKUP_ROOT/$archive_name" "s3://$S3_BUCKET/backups/$archive_name"
    
    # Clean up local archive
    rm "$BACKUP_ROOT/$archive_name"
    
    print_status "Backup uploaded to S3 successfully"
}

# Function to clean old backups
cleanup_old_backups() {
    print_header "Cleaning up old backups"
    
    print_status "Removing backups older than $RETENTION_DAYS days..."
    
    find "$BACKUP_ROOT" -type d -name "20*" -mtime +$RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
    
    # Clean up S3 backups if configured
    if [ "$UPLOAD_TO_S3" = true ] && [ -n "$S3_BUCKET" ] && command -v aws >/dev/null 2>&1; then
        print_status "Cleaning up old S3 backups..."
        
        # List and delete old backups from S3
        aws s3 ls "s3://$S3_BUCKET/backups/" | while read -r line; do
            local backup_date=$(echo "$line" | awk '{print $1}')
            local backup_file=$(echo "$line" | awk '{print $4}')
            
            if [ -n "$backup_date" ] && [ -n "$backup_file" ]; then
                local backup_timestamp=$(date -d "$backup_date" +%s)
                local cutoff_timestamp=$(date -d "$RETENTION_DAYS days ago" +%s)
                
                if [ "$backup_timestamp" -lt "$cutoff_timestamp" ]; then
                    aws s3 rm "s3://$S3_BUCKET/backups/$backup_file"
                    print_status "Deleted old S3 backup: $backup_file"
                fi
            fi
        done
    fi
    
    print_status "Cleanup completed"
}

# Function to verify backup
verify_backup() {
    print_header "Verifying backup"
    
    local errors=0
    
    # Check database backup
    if [ -f "$BACKUP_DIR/database/database.sql" ] && [ -s "$BACKUP_DIR/database/database.sql" ]; then
        print_status "✓ Database backup verified"
    else
        print_error "✗ Database backup verification failed"
        ((errors++))
    fi
    
    # Check manifest
    if [ -f "$BACKUP_DIR/manifest.json" ]; then
        print_status "✓ Manifest file verified"
    else
        print_error "✗ Manifest file verification failed"
        ((errors++))
    fi
    
    # Check total size
    local total_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    print_status "Total backup size: $total_size"
    
    if [ $errors -eq 0 ]; then
        print_status "Backup verification passed"
        return 0
    else
        print_error "Backup verification failed with $errors errors"
        return 1
    fi
}

# Function to show help
show_help() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --retention-days DAYS    Number of days to keep backups (default: 30)"
    echo "  --no-compress           Don't compress backup files"
    echo "  --s3-bucket BUCKET      Upload backup to S3 bucket"
    echo "  --environment ENV       Environment name (default: production)"
    echo "  --help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Create backup with default settings"
    echo "  $0 --retention-days 7                # Keep backups for 7 days"
    echo "  $0 --s3-bucket my-backup-bucket      # Upload to S3"
    echo "  $0 --no-compress --retention-days 14 # No compression, 14 days retention"
}

# Main function
main() {
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --retention-days)
                RETENTION_DAYS="$2"
                shift 2
                ;;
            --no-compress)
                COMPRESS=false
                shift
                ;;
            --s3-bucket)
                S3_BUCKET="$2"
                UPLOAD_TO_S3=true
                shift 2
                ;;
            --environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    print_header "PHCityRent Backup Started"
    print_status "Timestamp: $TIMESTAMP"
    print_status "Environment: $ENVIRONMENT"
    print_status "Retention: $RETENTION_DAYS days"
    print_status "Compression: $COMPRESS"
    print_status "S3 Upload: $UPLOAD_TO_S3"
    
    # Run backup steps
    create_backup_dir
    backup_database
    backup_uploads
    backup_config
    backup_logs
    create_manifest
    
    # Verify backup
    if verify_backup; then
        upload_to_s3
        cleanup_old_backups
        
        print_header "Backup Completed Successfully"
        print_status "Backup location: $BACKUP_DIR"
        print_status "Total size: $(du -sh $BACKUP_DIR | cut -f1)"
    else
        print_error "Backup verification failed"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
