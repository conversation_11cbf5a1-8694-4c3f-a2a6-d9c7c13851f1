#!/bin/bash

# Production Environment Variables Setup Script
# This script helps you configure all required production environment variables

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to generate secure random string
generate_secret() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Function to prompt for input with default
prompt_input() {
    local prompt="$1"
    local default="$2"
    local secret="$3"
    
    if [ "$secret" = "true" ]; then
        echo -n "$prompt: "
        read -s value
        echo
    else
        echo -n "$prompt${default:+ (default: $default)}: "
        read value
    fi
    
    echo "${value:-$default}"
}

print_header "PHCityRent Production Environment Setup"
echo "This script will help you configure all required production environment variables."
echo "Press Enter to continue or Ctrl+C to exit."
read

# Create production environment file
ENV_FILE="production.env"
echo "# Production Environment Variables - Generated $(date)" > $ENV_FILE
echo "# IMPORTANT: Keep this file secure and never commit to version control" >> $ENV_FILE
echo "" >> $ENV_FILE

print_header "Database Configuration"
DB_HOST=$(prompt_input "Database Host" "localhost")
DB_USERNAME=$(prompt_input "Database Username" "phcityrent_prod")
DB_PASSWORD=$(prompt_input "Database Password" "" "true")
DB_NAME=$(prompt_input "Database Name" "phcityrent_production")

echo "# Database Configuration" >> $ENV_FILE
echo "export PROD_DB_HOST=\"$DB_HOST\"" >> $ENV_FILE
echo "export PROD_DB_USERNAME=\"$DB_USERNAME\"" >> $ENV_FILE
echo "export PROD_DB_PASSWORD=\"$DB_PASSWORD\"" >> $ENV_FILE
echo "export PROD_DB_NAME=\"$DB_NAME\"" >> $ENV_FILE
echo "" >> $ENV_FILE

print_header "Redis Configuration"
REDIS_HOST=$(prompt_input "Redis Host" "localhost")
REDIS_PASSWORD=$(prompt_input "Redis Password" "" "true")

echo "# Redis Configuration" >> $ENV_FILE
echo "export PROD_REDIS_HOST=\"$REDIS_HOST\"" >> $ENV_FILE
echo "export PROD_REDIS_PASSWORD=\"$REDIS_PASSWORD\"" >> $ENV_FILE
echo "" >> $ENV_FILE

print_header "JWT Secrets (Auto-generating secure secrets)"
JWT_SECRET=$(generate_secret 64)
JWT_REFRESH_SECRET=$(generate_secret 64)
SESSION_SECRET=$(generate_secret 32)

echo "# JWT and Session Secrets" >> $ENV_FILE
echo "export PROD_JWT_SECRET=\"$JWT_SECRET\"" >> $ENV_FILE
echo "export PROD_JWT_REFRESH_SECRET=\"$JWT_REFRESH_SECRET\"" >> $ENV_FILE
echo "export PROD_SESSION_SECRET=\"$SESSION_SECRET\"" >> $ENV_FILE
echo "" >> $ENV_FILE

print_header "Payment Gateway Configuration"
echo "Setting up Paystack (Live Keys)..."
PAYSTACK_SECRET=$(prompt_input "Paystack Secret Key (sk_live_...)" "" "true")
PAYSTACK_PUBLIC=$(prompt_input "Paystack Public Key (pk_live_...)")
PAYSTACK_WEBHOOK=$(prompt_input "Paystack Webhook Secret" "" "true")

echo "Setting up Flutterwave (Live Keys)..."
FLUTTERWAVE_SECRET=$(prompt_input "Flutterwave Secret Key (FLWSECK-...)" "" "true")
FLUTTERWAVE_PUBLIC=$(prompt_input "Flutterwave Public Key (FLWPUBK-...)")
FLUTTERWAVE_WEBHOOK=$(prompt_input "Flutterwave Webhook Secret" "" "true")

echo "# Payment Gateway Configuration" >> $ENV_FILE
echo "export PROD_PAYSTACK_SECRET_KEY=\"$PAYSTACK_SECRET\"" >> $ENV_FILE
echo "export PROD_PAYSTACK_PUBLIC_KEY=\"$PAYSTACK_PUBLIC\"" >> $ENV_FILE
echo "export PROD_PAYSTACK_WEBHOOK_SECRET=\"$PAYSTACK_WEBHOOK\"" >> $ENV_FILE
echo "export PROD_FLUTTERWAVE_SECRET_KEY=\"$FLUTTERWAVE_SECRET\"" >> $ENV_FILE
echo "export PROD_FLUTTERWAVE_PUBLIC_KEY=\"$FLUTTERWAVE_PUBLIC\"" >> $ENV_FILE
echo "export PROD_FLUTTERWAVE_WEBHOOK_SECRET=\"$FLUTTERWAVE_WEBHOOK\"" >> $ENV_FILE
echo "" >> $ENV_FILE

print_header "Email Configuration"
SENDGRID_API_KEY=$(prompt_input "SendGrid API Key" "" "true")

echo "# Email Configuration" >> $ENV_FILE
echo "export PROD_SENDGRID_API_KEY=\"$SENDGRID_API_KEY\"" >> $ENV_FILE
echo "" >> $ENV_FILE

print_header "External Services (Optional - Press Enter to skip)"
GOOGLE_MAPS_KEY=$(prompt_input "Google Maps API Key" "")
CLOUDINARY_CLOUD=$(prompt_input "Cloudinary Cloud Name" "")
CLOUDINARY_API_KEY=$(prompt_input "Cloudinary API Key" "")
CLOUDINARY_API_SECRET=$(prompt_input "Cloudinary API Secret" "" "true")

if [ -n "$GOOGLE_MAPS_KEY" ]; then
    echo "# External Services" >> $ENV_FILE
    echo "export PROD_GOOGLE_MAPS_API_KEY=\"$GOOGLE_MAPS_KEY\"" >> $ENV_FILE
    [ -n "$CLOUDINARY_CLOUD" ] && echo "export PROD_CLOUDINARY_CLOUD_NAME=\"$CLOUDINARY_CLOUD\"" >> $ENV_FILE
    [ -n "$CLOUDINARY_API_KEY" ] && echo "export PROD_CLOUDINARY_API_KEY=\"$CLOUDINARY_API_KEY\"" >> $ENV_FILE
    [ -n "$CLOUDINARY_API_SECRET" ] && echo "export PROD_CLOUDINARY_API_SECRET=\"$CLOUDINARY_API_SECRET\"" >> $ENV_FILE
    echo "" >> $ENV_FILE
fi

print_success "Production environment file created: $ENV_FILE"
print_warning "IMPORTANT SECURITY NOTES:"
echo "1. Keep the $ENV_FILE file secure and never commit it to version control"
echo "2. Set appropriate file permissions: chmod 600 $ENV_FILE"
echo "3. Source this file before deployment: source $ENV_FILE"
echo ""
print_header "Next Steps:"
echo "1. Review and verify all values in $ENV_FILE"
echo "2. Run: chmod 600 $ENV_FILE"
echo "3. Run: source $ENV_FILE"
echo "4. Test deployment: ./scripts/deploy.sh staging --dry-run"
echo "5. Deploy to production: ./scripts/deploy.sh production"

# Set secure permissions
chmod 600 $ENV_FILE
print_success "Set secure permissions (600) on $ENV_FILE"
