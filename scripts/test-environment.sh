#!/bin/bash

# Environment Configuration Test Script
# Tests all environment configurations and verifies setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_LOG="$PROJECT_ROOT/environment-test.log"

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# Function to print colored output
print_status() {
    echo -e "${GREEN}[PASS]${NC} $1" | tee -a "$TEST_LOG"
    ((TESTS_PASSED++))
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "$TEST_LOG"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1" | tee -a "$TEST_LOG"
    ((TESTS_FAILED++))
    FAILED_TESTS+=("$1")
}

print_header() {
    echo -e "${BLUE}[TEST]${NC} $1" | tee -a "$TEST_LOG"
}

# Function to test file existence
test_file_exists() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        print_status "$description exists: $file"
        return 0
    else
        print_error "$description missing: $file"
        return 1
    fi
}

# Function to test directory existence
test_directory_exists() {
    local dir="$1"
    local description="$2"
    
    if [ -d "$dir" ]; then
        print_status "$description exists: $dir"
        return 0
    else
        print_error "$description missing: $dir"
        return 1
    fi
}

# Function to test environment file
test_env_file() {
    local env_file="$1"
    local env_name="$2"
    
    print_header "Testing $env_name environment file"
    
    if test_file_exists "$env_file" "$env_name environment file"; then
        # Check for required variables
        local required_vars=()
        
        if [[ "$env_file" == *"Backend"* ]]; then
            required_vars=("NODE_ENV" "PORT" "DB_HOST" "DB_USERNAME" "JWT_SECRET")
        else
            required_vars=("VITE_NODE_ENV" "VITE_API_BASE_URL")
        fi
        
        for var in "${required_vars[@]}"; do
            if grep -q "^$var=" "$env_file"; then
                print_status "$env_name has required variable: $var"
            else
                print_error "$env_name missing required variable: $var"
            fi
        done
        
        # Check file permissions
        if [ -r "$env_file" ]; then
            print_status "$env_name environment file is readable"
        else
            print_error "$env_name environment file is not readable"
        fi
    fi
}

# Function to test package.json files
test_package_json() {
    local package_file="$1"
    local component="$2"
    
    print_header "Testing $component package.json"
    
    if test_file_exists "$package_file" "$component package.json"; then
        # Check if it's valid JSON
        if jq empty "$package_file" 2>/dev/null; then
            print_status "$component package.json is valid JSON"
        else
            print_error "$component package.json is invalid JSON"
        fi
        
        # Check for required fields
        local required_fields=("name" "version" "scripts")
        
        for field in "${required_fields[@]}"; do
            if jq -e ".$field" "$package_file" >/dev/null 2>&1; then
                print_status "$component package.json has required field: $field"
            else
                print_error "$component package.json missing required field: $field"
            fi
        done
        
        # Check for development scripts
        if [[ "$component" == "Backend" ]]; then
            local scripts=("start" "start:dev" "build" "test")
        else
            local scripts=("dev" "build" "test" "lint")
        fi
        
        for script in "${scripts[@]}"; do
            if jq -e ".scripts.\"$script\"" "$package_file" >/dev/null 2>&1; then
                print_status "$component has required script: $script"
            else
                print_error "$component missing required script: $script"
            fi
        done
    fi
}

# Function to test Docker configuration
test_docker_config() {
    print_header "Testing Docker configuration"
    
    # Test main docker-compose file
    test_file_exists "docker-compose.yml" "Main docker-compose file"
    
    # Test environment-specific docker-compose files
    test_file_exists "docker-compose.development.yml" "Development docker-compose file"
    test_file_exists "docker-compose.production.yml" "Production docker-compose file"
    
    # Test Dockerfiles
    test_file_exists "Backend/Dockerfile" "Backend Dockerfile"
    test_file_exists "Frontend/Dockerfile" "Frontend Dockerfile"
    
    # Validate docker-compose files
    if command -v docker-compose >/dev/null 2>&1; then
        if docker-compose config >/dev/null 2>&1; then
            print_status "Docker Compose configuration is valid"
        else
            print_error "Docker Compose configuration is invalid"
        fi
    else
        print_warning "Docker Compose not installed, skipping validation"
    fi
}

# Function to test scripts
test_scripts() {
    print_header "Testing deployment scripts"
    
    # Test script files
    local scripts=("env-setup.sh" "deploy.sh" "backup.sh")
    
    for script in "${scripts[@]}"; do
        local script_path="scripts/$script"
        if test_file_exists "$script_path" "Script: $script"; then
            # Check if executable
            if [ -x "$script_path" ]; then
                print_status "Script is executable: $script"
            else
                print_error "Script is not executable: $script"
            fi
        fi
    done
}

# Function to test CI/CD configuration
test_cicd_config() {
    print_header "Testing CI/CD configuration"
    
    # Test GitHub Actions workflows
    test_file_exists ".github/workflows/ci.yml" "CI workflow"
    test_file_exists ".github/workflows/cd.yml" "CD workflow"
    
    # Validate YAML files
    if command -v yamllint >/dev/null 2>&1; then
        for workflow in .github/workflows/*.yml; do
            if yamllint "$workflow" >/dev/null 2>&1; then
                print_status "Valid YAML: $(basename "$workflow")"
            else
                print_error "Invalid YAML: $(basename "$workflow")"
            fi
        done
    else
        print_warning "yamllint not installed, skipping YAML validation"
    fi
}

# Function to test environment configuration
test_environment_config() {
    print_header "Testing environment configuration"
    
    # Test backend environment files
    test_env_file "Backend/.env.development" "Backend Development"
    test_env_file "Backend/.env.staging" "Backend Staging"
    test_env_file "Backend/.env.production" "Backend Production"
    
    # Test frontend environment files
    test_env_file "Frontend/.env.development" "Frontend Development"
    test_env_file "Frontend/.env.staging" "Frontend Staging"
    test_env_file "Frontend/.env.production" "Frontend Production"
    
    # Test environment configuration utility
    test_file_exists "Frontend/src/config/environment.ts" "Environment configuration utility"
}

# Function to test project structure
test_project_structure() {
    print_header "Testing project structure"
    
    # Test main directories
    test_directory_exists "Backend" "Backend directory"
    test_directory_exists "Frontend" "Frontend directory"
    test_directory_exists "scripts" "Scripts directory"
    test_directory_exists "docs" "Documentation directory"
    test_directory_exists ".github" "GitHub directory"
    
    # Test backend structure
    test_directory_exists "Backend/src" "Backend source directory"
    test_directory_exists "Backend/test" "Backend test directory"
    
    # Test frontend structure
    test_directory_exists "Frontend/src" "Frontend source directory"
    test_directory_exists "Frontend/public" "Frontend public directory"
    
    # Test documentation
    test_file_exists "docs/SETUP.md" "Setup documentation"
    test_file_exists "docs/DEPLOYMENT.md" "Deployment documentation"
    test_file_exists "docs/ERROR_HANDLING.md" "Error handling documentation"
}

# Function to test dependencies
test_dependencies() {
    print_header "Testing dependencies"
    
    # Test Node.js version
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version | cut -d'v' -f2)
        local required_version="18.0.0"
        
        if [ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" = "$required_version" ]; then
            print_status "Node.js version is compatible: $node_version"
        else
            print_error "Node.js version is incompatible: $node_version (required: $required_version+)"
        fi
    else
        print_error "Node.js is not installed"
    fi
    
    # Test npm version
    if command -v npm >/dev/null 2>&1; then
        local npm_version=$(npm --version)
        print_status "npm version: $npm_version"
    else
        print_error "npm is not installed"
    fi
    
    # Test Docker
    if command -v docker >/dev/null 2>&1; then
        if docker info >/dev/null 2>&1; then
            print_status "Docker is installed and running"
        else
            print_error "Docker is installed but not running"
        fi
    else
        print_warning "Docker is not installed"
    fi
    
    # Test Docker Compose
    if command -v docker-compose >/dev/null 2>&1; then
        local compose_version=$(docker-compose --version)
        print_status "Docker Compose is installed: $compose_version"
    else
        print_warning "Docker Compose is not installed"
    fi
}

# Function to test package installations
test_package_installations() {
    print_header "Testing package installations"
    
    # Test backend dependencies
    if [ -d "Backend/node_modules" ]; then
        print_status "Backend dependencies are installed"
    else
        print_warning "Backend dependencies are not installed"
    fi
    
    # Test frontend dependencies
    if [ -d "Frontend/node_modules" ]; then
        print_status "Frontend dependencies are installed"
    else
        print_warning "Frontend dependencies are not installed"
    fi
    
    # Test root dependencies
    if [ -d "node_modules" ]; then
        print_status "Root dependencies are installed"
    else
        print_warning "Root dependencies are not installed"
    fi
}

# Function to generate test report
generate_report() {
    print_header "Test Summary"
    
    local total_tests=$((TESTS_PASSED + TESTS_FAILED))
    
    echo "Total Tests: $total_tests" | tee -a "$TEST_LOG"
    echo "Passed: $TESTS_PASSED" | tee -a "$TEST_LOG"
    echo "Failed: $TESTS_FAILED" | tee -a "$TEST_LOG"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        print_status "All tests passed! Environment is properly configured."
    else
        print_error "Some tests failed. Please review the issues above."
        
        echo -e "\n${RED}Failed Tests:${NC}" | tee -a "$TEST_LOG"
        for test in "${FAILED_TESTS[@]}"; do
            echo "  - $test" | tee -a "$TEST_LOG"
        done
    fi
    
    echo -e "\nTest log saved to: $TEST_LOG"
    
    return $TESTS_FAILED
}

# Main function
main() {
    echo "Environment Configuration Test" > "$TEST_LOG"
    echo "Started at $(date)" >> "$TEST_LOG"
    echo "==============================" >> "$TEST_LOG"
    
    print_header "PHCityRent Environment Configuration Test"
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Run all tests
    test_dependencies
    test_project_structure
    test_package_installations
    test_package_json "Backend/package.json" "Backend"
    test_package_json "Frontend/package.json" "Frontend"
    test_package_json "package.json" "Root"
    test_environment_config
    test_docker_config
    test_scripts
    test_cicd_config
    
    # Generate report
    generate_report
}

# Run main function
main "$@"
