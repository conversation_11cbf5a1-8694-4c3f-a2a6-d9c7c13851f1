#!/bin/bash

# Simple Environment Verification Script
# Verifies that the environment setup is working correctly

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo "🔍 PHCityRent Environment Verification"
echo "======================================"

# Check Node.js
echo -n "Node.js version: "
if command -v node >/dev/null 2>&1; then
    node_version=$(node --version)
    echo -e "${GREEN}$node_version${NC}"
else
    echo -e "${RED}Not installed${NC}"
fi

# Check npm
echo -n "npm version: "
if command -v npm >/dev/null 2>&1; then
    npm_version=$(npm --version)
    echo -e "${GREEN}$npm_version${NC}"
else
    echo -e "${RED}Not installed${NC}"
fi

# Check Docker
echo -n "Docker: "
if command -v docker >/dev/null 2>&1; then
    if docker info >/dev/null 2>&1; then
        echo -e "${GREEN}Running${NC}"
    else
        echo -e "${YELLOW}Installed but not running${NC}"
    fi
else
    echo -e "${RED}Not installed${NC}"
fi

# Check project structure
echo ""
echo "📁 Project Structure:"

check_file() {
    if [ -f "$1" ]; then
        echo -e "  ✅ $1"
    else
        echo -e "  ❌ $1"
    fi
}

check_dir() {
    if [ -d "$1" ]; then
        echo -e "  ✅ $1/"
    else
        echo -e "  ❌ $1/"
    fi
}

# Check main directories
check_dir "Backend"
check_dir "Frontend"
check_dir "scripts"
check_dir "docs"

# Check key files
check_file "package.json"
check_file "docker-compose.yml"
check_file "Backend/package.json"
check_file "Frontend/package.json"

# Check environment files
echo ""
echo "🔧 Environment Files:"
check_file "Backend/.env.development"
check_file "Backend/.env.staging"
check_file "Backend/.env.production"
check_file "Frontend/.env.development"
check_file "Frontend/.env.staging"
check_file "Frontend/.env.production"

# Check scripts
echo ""
echo "📜 Scripts:"
check_file "scripts/env-setup.sh"
check_file "scripts/deploy.sh"
check_file "scripts/backup.sh"

# Check documentation
echo ""
echo "📚 Documentation:"
check_file "docs/SETUP.md"
check_file "docs/DEPLOYMENT.md"
check_file "docs/ERROR_HANDLING.md"

# Check dependencies
echo ""
echo "📦 Dependencies:"

if [ -d "node_modules" ]; then
    echo -e "  ✅ Root dependencies installed"
else
    echo -e "  ❌ Root dependencies not installed"
fi

if [ -d "Backend/node_modules" ]; then
    echo -e "  ✅ Backend dependencies installed"
else
    echo -e "  ❌ Backend dependencies not installed"
fi

if [ -d "Frontend/node_modules" ]; then
    echo -e "  ✅ Frontend dependencies installed"
else
    echo -e "  ❌ Frontend dependencies not installed"
fi

echo ""
echo "🎯 Quick Setup Commands:"
echo "  npm run setup              # Run full setup"
echo "  npm run env:dev            # Switch to development"
echo "  npm run dev                # Start development servers"
echo "  docker-compose up          # Start with Docker"

echo ""
echo "✨ Environment verification complete!"
