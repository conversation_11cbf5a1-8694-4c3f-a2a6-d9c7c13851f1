#!/usr/bin/env node

/**
 * GitHub Branch Protection Setup Script
 * Automatically configures branch protection rules for main and develop branches
 */

const { Octokit } = require('@octokit/rest');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function setupBranchProtection() {
  console.log('🛡️  GitHub Branch Protection Setup\n');

  // Get GitHub token
  const token = process.env.GITHUB_TOKEN || await askQuestion('Enter your GitHub Personal Access Token: ');
  
  if (!token) {
    console.error('❌ GitHub token is required');
    process.exit(1);
  }

  const octokit = new Octokit({ auth: token });

  // Repository details
  const owner = 'Woldreamz-Inc';
  const repo = 'ptownmoving';

  try {
    console.log('🔧 Setting up branch protection for main branch...');
    
    // Main branch protection
    await octokit.rest.repos.updateBranchProtection({
      owner,
      repo,
      branch: 'main',
      required_status_checks: {
        strict: true,
        contexts: [
          'lint-and-type-check',
          'security-scan', 
          'build-test',
          'run-tests'
        ]
      },
      enforce_admins: true,
      required_pull_request_reviews: {
        required_approving_review_count: 1,
        dismiss_stale_reviews: true,
        require_code_owner_reviews: true,
        require_last_push_approval: true
      },
      restrictions: null,
      required_linear_history: true,
      allow_force_pushes: false,
      allow_deletions: false,
      block_creations: false,
      required_conversation_resolution: true
    });

    console.log('✅ Main branch protection configured');

    console.log('🔧 Setting up branch protection for develop branch...');
    
    // Develop branch protection
    await octokit.rest.repos.updateBranchProtection({
      owner,
      repo,
      branch: 'develop',
      required_status_checks: {
        strict: true,
        contexts: [
          'lint-and-type-check',
          'build-test',
          'run-tests'
        ]
      },
      enforce_admins: true,
      required_pull_request_reviews: {
        required_approving_review_count: 1,
        dismiss_stale_reviews: false,
        require_code_owner_reviews: false
      },
      restrictions: null,
      required_linear_history: false,
      allow_force_pushes: false,
      allow_deletions: false,
      block_creations: false,
      required_conversation_resolution: true
    });

    console.log('✅ Develop branch protection configured');

    // Set up repository settings
    console.log('🔧 Configuring repository settings...');
    
    await octokit.rest.repos.update({
      owner,
      repo,
      allow_merge_commit: true,
      allow_squash_merge: true,
      allow_rebase_merge: false,
      delete_branch_on_merge: true,
      allow_auto_merge: true,
      use_squash_pr_title_as_default: true
    });

    console.log('✅ Repository settings configured');

    console.log('\n🎉 Branch protection setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   • Main branch: Full protection with required reviews and status checks');
    console.log('   • Develop branch: Standard protection with status checks');
    console.log('   • Repository: Configured for optimal workflow');
    console.log('\n💡 Next steps:');
    console.log('   1. Ensure all team members have appropriate repository access');
    console.log('   2. Test the CI/CD pipeline with a test PR');
    console.log('   3. Review and adjust protection rules as needed');

  } catch (error) {
    console.error('❌ Error setting up branch protection:', error.message);
    
    if (error.status === 404) {
      console.error('   Repository not found or insufficient permissions');
    } else if (error.status === 403) {
      console.error('   Insufficient permissions. Ensure your token has admin access to the repository');
    }
    
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the setup
if (require.main === module) {
  setupBranchProtection().catch(console.error);
}

module.exports = { setupBranchProtection };
