version: '3.8'

services:
  # Production-specific overrides
  backend:
    build:
      target: production
    environment:
      - NODE_ENV=production
      - DB_LOGGING=false
      - ENABLE_SWAGGER=false
      - CLUSTER_ENABLED=true
    restart: always
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      target: production
    environment:
      - NODE_ENV=production
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Production database with optimizations
  postgres:
    environment:
      POSTGRES_DB: ${PROD_DB_NAME}
      POSTGRES_USER: ${PROD_DB_USERNAME}
      POSTGRES_PASSWORD: ${PROD_DB_PASSWORD}
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    restart: always
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  # Production Redis with persistence
  redis:
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --save 900 1
      --save 300 10
      --save 60 10000
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --requirepass ${PROD_REDIS_PASSWORD}
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: phcityrent-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
      - ./nginx/logs:/var/log/nginx
    networks:
      - phcityrent-network
    depends_on:
      - backend
      - frontend
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

  # SSL Certificate Management
  certbot:
    image: certbot/certbot:latest
    container_name: phcityrent-certbot
    volumes:
      - ./ssl:/etc/letsencrypt
      - ./nginx/html:/var/www/html
    command: certonly --webroot --webroot-path=/var/www/html --email <EMAIL> --agree-tos --no-eff-email -d phcityrent.com -d www.phcityrent.com -d api.phcityrent.com
    profiles:
      - ssl

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: phcityrent-backup
    restart: "no"
    environment:
      PGPASSWORD: ${PROD_DB_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    command: /backup.sh
    networks:
      - phcityrent-network
    depends_on:
      - postgres
    profiles:
      - backup

  # Log Shipper
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: phcityrent-filebeat
    restart: always
    user: root
    volumes:
      - ./logging/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./Backend/logs:/app/logs:ro
    networks:
      - phcityrent-network
    profiles:
      - logging

  # Monitoring Agent
  node-exporter:
    image: prom/node-exporter:latest
    container_name: phcityrent-node-exporter
    restart: always
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - phcityrent-network
    profiles:
      - monitoring

  # Application Performance Monitoring
  apm-server:
    image: docker.elastic.co/apm/apm-server:8.8.0
    container_name: phcityrent-apm
    restart: always
    ports:
      - "8200:8200"
    volumes:
      - ./monitoring/apm-server.yml:/usr/share/apm-server/apm-server.yml:ro
    networks:
      - phcityrent-network
    profiles:
      - monitoring

volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  uploads_prod_data:
    driver: local
