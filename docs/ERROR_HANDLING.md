# Error Handling Guide

This document outlines the error handling strategies and best practices implemented in the PHCityRent application.

## 🎯 Error Handling Philosophy

Our error handling approach follows these principles:

1. **Fail Fast**: Detect errors early and handle them gracefully
2. **User-Friendly**: Provide meaningful error messages to users
3. **Developer-Friendly**: Include detailed error information for debugging
4. **Consistent**: Use standardized error formats across the application
5. **Secure**: Never expose sensitive information in error messages

## 🔧 Backend Error Handling

### Global Exception Filter

The backend uses a global exception filter to catch and handle all errors:

<augment_code_snippet path="Backend/src/common/filters/http-exception.filter.ts" mode="EXCERPT">
````typescript
@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    
    const status = exception instanceof HttpException 
      ? exception.getStatus() 
      : HttpStatus.INTERNAL_SERVER_ERROR;
    
    const errorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message: this.getErrorMessage(exception),
      ...(process.env.NODE_ENV === 'development' && { stack: exception.stack })
    };
    
    response.status(status).json(errorResponse);
  }
}
````
</augment_code_snippet>

### Custom Exception Classes

#### Business Logic Exceptions

```typescript
export class PropertyNotFoundException extends HttpException {
  constructor(propertyId: string) {
    super(`Property with ID ${propertyId} not found`, HttpStatus.NOT_FOUND);
  }
}

export class InsufficientFundsException extends HttpException {
  constructor(required: number, available: number) {
    super(
      `Insufficient funds. Required: ${required}, Available: ${available}`,
      HttpStatus.BAD_REQUEST
    );
  }
}
```

#### Validation Exceptions

```typescript
export class ValidationException extends HttpException {
  constructor(errors: ValidationError[]) {
    const message = errors.map(error => 
      Object.values(error.constraints || {}).join(', ')
    ).join('; ');
    
    super(message, HttpStatus.BAD_REQUEST);
  }
}
```

### Database Error Handling

```typescript
@Injectable()
export class DatabaseErrorHandler {
  handleDatabaseError(error: any): never {
    if (error.code === '23505') { // Unique constraint violation
      throw new ConflictException('Resource already exists');
    }
    
    if (error.code === '23503') { // Foreign key constraint violation
      throw new BadRequestException('Referenced resource does not exist');
    }
    
    if (error.code === '23502') { // Not null constraint violation
      throw new BadRequestException('Required field is missing');
    }
    
    throw new InternalServerErrorException('Database operation failed');
  }
}
```

### API Response Format

All API responses follow a consistent format:

#### Success Response
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation completed successfully",
  "timestamp": "2023-12-01T12:00:00.000Z"
}
```

#### Error Response
```json
{
  "success": false,
  "error": {
    "code": "PROPERTY_NOT_FOUND",
    "message": "Property with ID 123 not found",
    "details": { /* additional error details */ }
  },
  "timestamp": "2023-12-01T12:00:00.000Z",
  "path": "/api/v1/properties/123"
}
```

## 🎨 Frontend Error Handling

### Error Boundary Component

```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<PropsWithChildren, ErrorBoundaryState> {
  constructor(props: PropsWithChildren) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    // Send error to monitoring service
    this.reportError(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}
```

### API Error Handling

```typescript
export class ApiService {
  private async handleApiError(error: AxiosError): Promise<never> {
    const errorData = error.response?.data as ApiErrorResponse;
    
    switch (error.response?.status) {
      case 401:
        // Redirect to login
        window.location.href = '/login';
        break;
      case 403:
        throw new Error('You do not have permission to perform this action');
      case 404:
        throw new Error('The requested resource was not found');
      case 422:
        throw new ValidationError(errorData.error.details);
      case 500:
        throw new Error('An internal server error occurred. Please try again later.');
      default:
        throw new Error(errorData?.error?.message || 'An unexpected error occurred');
    }
  }
}
```

### Form Validation Errors

```typescript
export const useFormValidation = () => {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateField = (name: string, value: any, rules: ValidationRule[]) => {
    const fieldErrors: string[] = [];

    rules.forEach(rule => {
      if (!rule.validator(value)) {
        fieldErrors.push(rule.message);
      }
    });

    setErrors(prev => ({
      ...prev,
      [name]: fieldErrors.join(', ')
    }));

    return fieldErrors.length === 0;
  };

  return { errors, validateField, setErrors };
};
```

## 🚨 Error Monitoring and Logging

### Backend Logging

```typescript
@Injectable()
export class LoggerService {
  private logger = new Logger(LoggerService.name);

  logError(error: Error, context?: string, metadata?: any) {
    this.logger.error(
      `${error.message}${context ? ` [${context}]` : ''}`,
      error.stack,
      metadata
    );

    // Send to external monitoring service
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoring(error, context, metadata);
    }
  }

  private sendToMonitoring(error: Error, context?: string, metadata?: any) {
    // Integration with Sentry, DataDog, etc.
  }
}
```

### Frontend Error Reporting

```typescript
export const reportError = (error: Error, context?: string) => {
  console.error('Error reported:', error, context);

  // Send to monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Sentry.captureException(error, { extra: { context } });
  }
};
```

## 🔍 Common Error Scenarios

### Authentication Errors

| Error Code | HTTP Status | Description | User Action |
|------------|-------------|-------------|-------------|
| AUTH_TOKEN_EXPIRED | 401 | JWT token has expired | Redirect to login |
| AUTH_TOKEN_INVALID | 401 | Invalid or malformed token | Clear token, redirect to login |
| AUTH_INSUFFICIENT_PERMISSIONS | 403 | User lacks required permissions | Show permission denied message |

### Validation Errors

| Error Code | HTTP Status | Description | User Action |
|------------|-------------|-------------|-------------|
| VALIDATION_FAILED | 422 | Request data validation failed | Show field-specific errors |
| REQUIRED_FIELD_MISSING | 422 | Required field not provided | Highlight missing fields |
| INVALID_FORMAT | 422 | Field format is incorrect | Show format requirements |

### Business Logic Errors

| Error Code | HTTP Status | Description | User Action |
|------------|-------------|-------------|-------------|
| PROPERTY_NOT_FOUND | 404 | Property does not exist | Show "not found" message |
| INSUFFICIENT_FUNDS | 400 | Not enough funds for transaction | Show balance and required amount |
| BOOKING_CONFLICT | 409 | Property already booked for dates | Show alternative dates |

## 🛠️ Error Recovery Strategies

### Retry Mechanisms

```typescript
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries || !isRetryableError(error)) {
        throw error;
      }
      
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw new Error('Max retries exceeded');
};
```

### Circuit Breaker Pattern

```typescript
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
}
```

## 📊 Error Metrics and Monitoring

### Key Metrics to Track

1. **Error Rate**: Percentage of requests that result in errors
2. **Error Distribution**: Breakdown of errors by type and endpoint
3. **Response Time**: Impact of errors on application performance
4. **User Impact**: Number of users affected by errors

### Monitoring Setup

```typescript
export const errorMetrics = {
  incrementErrorCount: (errorType: string, endpoint: string) => {
    // Increment error counter in monitoring system
  },
  
  recordErrorDuration: (duration: number, errorType: string) => {
    // Record error handling duration
  },
  
  setErrorRate: (rate: number) => {
    // Update error rate metric
  }
};
```

## 🚀 Best Practices

### Do's

- ✅ Use specific error types for different scenarios
- ✅ Provide actionable error messages to users
- ✅ Log errors with sufficient context for debugging
- ✅ Implement proper error boundaries in React components
- ✅ Use consistent error response formats
- ✅ Sanitize error messages before showing to users
- ✅ Implement retry mechanisms for transient failures

### Don'ts

- ❌ Expose sensitive information in error messages
- ❌ Use generic error messages for all scenarios
- ❌ Ignore or swallow errors silently
- ❌ Return stack traces to end users in production
- ❌ Use error handling as control flow
- ❌ Log sensitive data in error messages

## 🔧 Testing Error Scenarios

### Backend Error Testing

```typescript
describe('PropertyController Error Handling', () => {
  it('should return 404 when property not found', async () => {
    const response = await request(app)
      .get('/api/v1/properties/non-existent-id')
      .expect(404);

    expect(response.body).toMatchObject({
      success: false,
      error: {
        code: 'PROPERTY_NOT_FOUND',
        message: expect.stringContaining('not found')
      }
    });
  });
});
```

### Frontend Error Testing

```typescript
describe('PropertyList Error Handling', () => {
  it('should display error message when API fails', async () => {
    server.use(
      rest.get('/api/v1/properties', (req, res, ctx) => {
        return res(ctx.status(500), ctx.json({ error: 'Server error' }));
      })
    );

    render(<PropertyList />);
    
    await waitFor(() => {
      expect(screen.getByText(/error occurred/i)).toBeInTheDocument();
    });
  });
});
```

---

For more information on specific error scenarios or implementation details, please refer to the individual component documentation or contact the development team.
