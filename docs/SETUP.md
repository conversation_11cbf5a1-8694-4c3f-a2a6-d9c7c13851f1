# PHCityRent Setup Guide

This comprehensive guide will help you set up the PHCityRent application for development, staging, and production environments.

## 📋 Prerequisites

### System Requirements

- **Node.js**: 18.0.0 or higher
- **npm**: 8.0.0 or higher
- **Docker**: 20.10.0 or higher
- **Docker Compose**: 2.0.0 or higher
- **Git**: 2.30.0 or higher

### Optional Tools

- **PostgreSQL**: 15+ (if not using Docker)
- **Redis**: 7+ (if not using Docker)
- **AWS CLI**: For production deployments
- **Terraform**: For infrastructure management

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/phcityrent/phcityrent.git
cd phcityrent
```

### 2. Automated Setup

Run the automated setup script for your desired environment:

```bash
# Development environment (default)
./scripts/env-setup.sh

# Or specify environment explicitly
./scripts/env-setup.sh development
./scripts/env-setup.sh staging
./scripts/env-setup.sh production
```

### 3. Manual Setup (Alternative)

If you prefer manual setup or the script fails:

```bash
# Install root dependencies
npm install

# Install backend dependencies
cd Backend && npm install && cd ..

# Install frontend dependencies
cd Frontend && npm install && cd ..

# Set up environment files
npm run env:dev

# Start the application
npm run dev
```

## 🔧 Environment Configuration

### Environment Files

The application uses different environment files for each environment:

- `Backend/.env.development` - Development backend configuration
- `Backend/.env.staging` - Staging backend configuration
- `Backend/.env.production` - Production backend configuration
- `Frontend/.env.development` - Development frontend configuration
- `Frontend/.env.staging` - Staging frontend configuration
- `Frontend/.env.production` - Production frontend configuration

### Switching Environments

```bash
# Switch to development
npm run env:dev

# Switch to staging
npm run env:staging

# Switch to production
npm run env:prod
```

### Required Environment Variables

#### Backend (.env)

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_NAME=phcityrent_dev

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Authentication
JWT_SECRET=your_jwt_secret_key
JWT_REFRESH_SECRET=your_refresh_secret_key

# Payment Gateways
PAYSTACK_SECRET_KEY=your_paystack_secret_key
FLUTTERWAVE_SECRET_KEY=your_flutterwave_secret_key

# External Services
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
CLOUDINARY_CLOUD_NAME=your_cloudinary_name
CLOUDINARY_API_KEY=your_cloudinary_key
CLOUDINARY_API_SECRET=your_cloudinary_secret
```

#### Frontend (.env)

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api/v1

# Payment Gateways
VITE_PAYSTACK_PUBLIC_KEY=your_paystack_public_key
VITE_FLUTTERWAVE_PUBLIC_KEY=your_flutterwave_public_key

# Google Services
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=true
```

## 🐳 Docker Setup

### Development with Docker

```bash
# Start all services
docker-compose -f docker-compose.yml -f docker-compose.development.yml up

# Start specific services
docker-compose up postgres redis

# View logs
docker-compose logs -f backend frontend
```

### Production with Docker

```bash
# Build and start production services
docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d

# Scale services
docker-compose up --scale backend=3 --scale frontend=2
```

### Docker Commands

```bash
# Build images
docker-compose build

# Stop all services
docker-compose down

# Remove volumes (careful!)
docker-compose down -v

# View service status
docker-compose ps

# Execute commands in containers
docker-compose exec backend npm run migration:run
docker-compose exec postgres psql -U postgres -d phcityrent_dev
```

## 💾 Database Setup

### Using Docker (Recommended)

The Docker setup automatically handles database creation and initialization.

### Manual Database Setup

1. **Install PostgreSQL**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install postgresql postgresql-contrib

   # macOS
   brew install postgresql
   ```

2. **Create Database**
   ```bash
   sudo -u postgres createdb phcityrent_dev
   sudo -u postgres createuser --interactive
   ```

3. **Run Migrations**
   ```bash
   cd Backend
   npm run migration:run
   ```

4. **Seed Database (Development)**
   ```bash
   npm run db:seed
   ```

### Database Commands

```bash
# Run migrations
npm run db:migrate

# Seed database
npm run db:seed

# Reset database
npm run db:reset

# Create new migration
npm run migration:create -- --name=CreateNewTable
```

## 🔧 Development Tools

### Code Quality Tools

```bash
# Linting
npm run lint              # Lint all code
npm run lint:fix          # Fix linting issues

# Formatting
npm run format            # Format all code

# Type checking
npm run type-check        # Check TypeScript types
```

### Testing

```bash
# Run all tests
npm run test

# Run backend tests
npm run test:backend

# Run frontend tests
npm run test:frontend

# Run E2E tests
npm run test:e2e

# Generate coverage report
npm run test:cov
```

### Development Servers

```bash
# Start both frontend and backend
npm run dev

# Start backend only
npm run dev:backend

# Start frontend only
npm run dev:frontend
```

## 🚀 Deployment

### Staging Deployment

```bash
# Deploy to staging
./scripts/deploy.sh staging

# Or using npm script
npm run deploy:staging
```

### Production Deployment

```bash
# Deploy to production
./scripts/deploy.sh production

# Or using npm script
npm run deploy:prod
```

### Deployment Options

```bash
# Skip tests during deployment
./scripts/deploy.sh production --skip-tests

# Skip backup creation
./scripts/deploy.sh production --skip-backup

# Dry run (show what would be deployed)
./scripts/deploy.sh production --dry-run

# Force deployment with uncommitted changes
./scripts/deploy.sh production --force
```

## 🔍 Monitoring and Debugging

### Health Checks

```bash
# Check application health
curl http://localhost:3001/api/v1/health

# Check database connection
npm run health:check
```

### Logs

```bash
# View application logs
docker-compose logs -f backend

# View database logs
docker-compose logs -f postgres

# View all logs
docker-compose logs -f
```

### Debugging

```bash
# Start backend in debug mode
cd Backend && npm run start:debug

# Start frontend with debug info
cd Frontend && npm run dev -- --debug
```

## 🔒 Security Setup

### SSL Certificates (Production)

```bash
# Generate SSL certificates with Let's Encrypt
docker-compose --profile ssl run certbot

# Renew certificates
docker-compose exec certbot certbot renew
```

### Security Scanning

```bash
# Audit dependencies
npm run security:audit

# Fix security issues
npm run security:fix
```

## 🧪 Testing Setup

### Unit Tests

```bash
# Backend tests
cd Backend && npm test

# Frontend tests
cd Frontend && npm test
```

### Integration Tests

```bash
# API integration tests
cd Backend && npm run test:e2e
```

### End-to-End Tests

```bash
# Install Playwright
cd Frontend && npx playwright install

# Run E2E tests
npm run test:e2e
```

## 📊 Performance Monitoring

### Application Performance

- **Backend**: http://localhost:3001/metrics
- **Frontend**: Built-in Web Vitals reporting
- **Database**: PostgreSQL performance insights

### Monitoring Stack

```bash
# Start monitoring services
docker-compose --profile monitoring up

# Access monitoring dashboards
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3000
```

## 🆘 Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the port
   lsof -i :3001
   
   # Kill the process
   kill -9 <PID>
   ```

2. **Database connection issues**
   ```bash
   # Check PostgreSQL status
   docker-compose ps postgres
   
   # Restart database
   docker-compose restart postgres
   ```

3. **Node modules issues**
   ```bash
   # Clean install
   npm run clean
   npm run install:all
   ```

4. **Docker issues**
   ```bash
   # Clean Docker
   docker system prune -a
   
   # Rebuild images
   docker-compose build --no-cache
   ```

### Getting Help

- Check the [FAQ](FAQ.md)
- Review [API Documentation](API.md)
- Create an issue on GitHub
- Contact support: <EMAIL>

## 📚 Next Steps

After successful setup:

1. Review the [API Documentation](API.md)
2. Explore the [Frontend Documentation](FRONTEND.md)
3. Read the [Deployment Guide](DEPLOYMENT.md)
4. Check out the [Contributing Guidelines](CONTRIBUTING.md)

## 🔄 Backup and Recovery

### Creating Backups

```bash
# Create full backup
./scripts/backup.sh

# Create backup with custom retention
./scripts/backup.sh --retention-days 7

# Upload backup to S3
./scripts/backup.sh --s3-bucket my-backup-bucket
```

### Restoring from Backup

```bash
# List available backups
ls -la backups/

# Restore database from backup
docker-compose exec postgres psql -U postgres -d phcityrent_dev < backups/20231201_120000/database/database.sql

# Restore uploads
tar -xzf backups/20231201_120000/uploads/uploads.tar.gz -C Backend/
```

## 🔧 Advanced Configuration

### Custom Environment Variables

You can override any environment variable by creating local override files:

```bash
# Backend override
echo "LOG_LEVEL=debug" >> Backend/.env.local

# Frontend override
echo "VITE_ENABLE_DEBUG_MODE=true" >> Frontend/.env.local
```

### Performance Tuning

#### Database Optimization

```sql
-- PostgreSQL configuration for production
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
```

#### Redis Configuration

```bash
# Redis memory optimization
redis-cli CONFIG SET maxmemory 512mb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

### Load Balancing

For production deployments with multiple instances:

```yaml
# nginx.conf
upstream backend {
    server backend1:3001;
    server backend2:3001;
    server backend3:3001;
}

upstream frontend {
    server frontend1:8081;
    server frontend2:8081;
}
```

---

**Need help?** Join our [Slack workspace](https://phcityrent.slack.com) or create an issue on GitHub.
