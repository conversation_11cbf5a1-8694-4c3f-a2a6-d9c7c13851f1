# 🚀 Final Production Deployment Guide

## **Pre-Deployment Checklist** ✅

Before executing the production deployment, ensure you have completed:

- [x] **Docker Desktop is running** ✅
- [x] **Deployment scripts are tested** ✅ (staging dry-run successful)
- [x] **Environment setup scripts are ready** ✅
- [ ] **Production environment variables are set** (Next step)
- [ ] **Production deployment executed** (Final step)

## **🔥 IMMEDIATE DEPLOYMENT STEPS**

### **Step 1: Set Up Production Environment Variables**

Choose one of these methods:

#### **Method A: Automated Setup (Recommended)**
```bash
# Run the interactive setup script
./scripts/setup-production-env.sh

# This will create a production.env file with all your variables
# Then source it:
source production.env
```

#### **Method B: Manual Setup**
```bash
# Set the 9 REQUIRED variables manually:
export PROD_DB_HOST="your-production-database-host"
export PROD_DB_USERNAME="your-database-username"  
export PROD_DB_PASSWORD="your-secure-database-password"
export PROD_DB_NAME="phcityrent_production"
export PROD_REDIS_HOST="your-redis-host"
export PROD_REDIS_PASSWORD="your-redis-password"
export PROD_JWT_SECRET="$(openssl rand -base64 64 | tr -d '=+/' | cut -c1-64)"
export PROD_JWT_REFRESH_SECRET="$(openssl rand -base64 64 | tr -d '=+/' | cut -c1-64)"
export PROD_SESSION_SECRET="$(openssl rand -base64 32 | tr -d '=+/' | cut -c1-32)"

# Optional but recommended for full functionality:
export PROD_PAYSTACK_SECRET_KEY="sk_live_your_paystack_secret"
export PROD_PAYSTACK_PUBLIC_KEY="pk_live_your_paystack_public"
export PROD_SENDGRID_API_KEY="SG.your_sendgrid_api_key"
```

### **Step 2: Validate Environment**
```bash
# Check if all required variables are set
./scripts/validate-env.sh

# Should show: "🎉 Ready for production deployment!"
```

### **Step 3: Final Deployment Test**
```bash
# Test the production deployment (dry run)
./scripts/deploy.sh production --dry-run

# Should show successful validation and deployment plan
```

### **Step 4: Execute Production Deployment**
```bash
# Deploy to production (LIVE DEPLOYMENT)
./scripts/deploy.sh production

# This will:
# 1. Run all tests
# 2. Build Docker images
# 3. Create database backup
# 4. Deploy new version
# 5. Run health checks
# 6. Confirm successful deployment
```

## **🎯 Expected Deployment Output**

When you run `./scripts/deploy.sh production`, you should see:

```
[DEPLOY] Checking prerequisites
[INFO] Prerequisites check passed
[DEPLOY] Validating environment: production
[INFO] Environment validation passed
[DEPLOY] Running tests
[INFO] Running backend tests...
[INFO] Running frontend tests...
[INFO] All tests passed
[DEPLOY] Creating backup
[INFO] Creating database backup...
[INFO] Creating uploads backup...
[INFO] Backup created at /path/to/backup
[DEPLOY] Building application
[INFO] Building Docker images...
[INFO] Build completed successfully
[DEPLOY] Deploying application to production
[INFO] Stopping existing services...
[INFO] Starting new services...
[INFO] Waiting for services to be ready...
[INFO] Backend health check passed
[INFO] Deployment completed successfully
[DEPLOY] Running post-deployment tasks
[INFO] Running database migrations...
[INFO] Clearing caches...
[INFO] Post-deployment tasks completed
[DEPLOY] Deployment Summary
[INFO] Environment: production
[INFO] Deployment completed at [timestamp]
[INFO] Production URL: https://phcityrent.com
[INFO] Admin URL: https://admin.phcityrent.com
```

## **🔧 Post-Deployment Verification**

After successful deployment, verify everything is working:

### **1. Health Check**
```bash
# Check backend health
curl https://api.phcityrent.com/api/v1/health

# Should return: {"status": "ok", "timestamp": "..."}
```

### **2. Frontend Access**
- Visit: `https://phcityrent.com`
- Should load the homepage successfully
- Test user registration/login

### **3. Admin Panel**
- Visit: `https://admin.phcityrent.com`
- Login with: `<EMAIL>` / `Admin123!@#`
- Verify dashboard loads correctly

### **4. Payment Testing**
- Test property payment flow
- Verify payment gateways are working
- Check webhook endpoints

## **🚨 Troubleshooting**

### **Common Issues & Solutions**

#### **Environment Variables Not Set**
```bash
# Error: "Required environment variable X is not set"
# Solution: Run validation and set missing variables
./scripts/validate-env.sh
```

#### **Database Connection Failed**
```bash
# Error: "Database connection failed"
# Solution: Check database credentials and connectivity
psql -h $PROD_DB_HOST -U $PROD_DB_USERNAME -d $PROD_DB_NAME
```

#### **Docker Build Failed**
```bash
# Error: "Docker build failed"
# Solution: Check Docker daemon and rebuild
docker system prune -f
./scripts/deploy.sh production --skip-tests
```

#### **Health Check Failed**
```bash
# Error: "Backend health check failed"
# Solution: Check application logs
docker-compose logs backend
```

### **Rollback Procedure**
If deployment fails or issues arise:

```bash
# Rollback to previous version
./scripts/deploy.sh production --rollback

# Check rollback status
./scripts/deploy.sh production --status
```

## **📊 Monitoring & Maintenance**

### **Application Monitoring**
```bash
# Check application status
./scripts/deploy.sh production --status

# View logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Monitor resource usage
docker stats
```

### **Database Maintenance**
```bash
# Manual backup
./scripts/backup.sh production

# Check database size
docker-compose exec postgres psql -U $PROD_DB_USERNAME -d $PROD_DB_NAME -c "SELECT pg_size_pretty(pg_database_size('$PROD_DB_NAME'));"
```

## **🎉 SUCCESS INDICATORS**

Your deployment is successful when:

- ✅ All health checks pass
- ✅ Frontend loads at your domain
- ✅ Admin panel is accessible
- ✅ User registration/login works
- ✅ Payment processing functions
- ✅ Database migrations completed
- ✅ No error logs in application

## **🔄 Continuous Deployment**

For future updates:

```bash
# Standard deployment process
git pull origin main
./scripts/deploy.sh production

# With specific options
./scripts/deploy.sh production --skip-backup  # Skip backup
./scripts/deploy.sh production --skip-tests   # Skip tests (not recommended)
```

## **📞 Support & Next Steps**

After successful deployment:

1. **Set up monitoring** (New Relic, Sentry)
2. **Configure SSL certificates** (Let's Encrypt)
3. **Set up automated backups**
4. **Configure domain DNS**
5. **Set up CDN** (CloudFlare)
6. **Enable monitoring alerts**

**🚀 You're now live in production!**

Your PHCityRent platform is ready to serve real users with:
- Full-stack real estate platform
- Multi-role user system
- Payment processing
- Real-time messaging
- Admin dashboard
- Mobile-responsive design
- Enterprise-grade security
