# Deployment Guide

This guide covers deployment strategies and procedures for the PHCityRent application across different environments.

## 🏗️ Deployment Architecture

### Environment Overview

- **Development**: Local development environment
- **Staging**: Pre-production testing environment
- **Production**: Live production environment

### Infrastructure Components

- **Application Servers**: Docker containers running on AWS ECS/Azure Container Instances
- **Database**: Managed PostgreSQL (AWS RDS/Azure Database)
- **Cache**: Managed Redis (AWS ElastiCache/Azure Cache)
- **Load Balancer**: AWS ALB/Azure Load Balancer
- **CDN**: AWS CloudFront/Azure CDN
- **Storage**: AWS S3/Azure Blob Storage

## 🚀 Deployment Methods

### 1. Automated Deployment (Recommended)

#### GitHub Actions CI/CD

The application uses GitHub Actions for automated deployment:

```bash
# Trigger staging deployment
git push origin main

# Trigger production deployment
git tag v1.0.0
git push origin v1.0.0
```

#### Manual Trigger

```bash
# Go to GitHub Actions
# Select "Continuous Deployment" workflow
# Click "Run workflow"
# Choose environment: staging/production
```

### 2. Script-based Deployment

#### Staging Deployment

```bash
# Deploy to staging
./scripts/deploy.sh staging

# Deploy with options
./scripts/deploy.sh staging --skip-tests --skip-backup
```

#### Production Deployment

```bash
# Deploy to production
./scripts/deploy.sh production

# Deploy with backup
./scripts/deploy.sh production --backup
```

### 3. Docker-based Deployment

#### Using Docker Compose

```bash
# Production deployment
docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d

# Staging deployment
docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
```

#### Using Docker Swarm

```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.production.yml phcityrent
```

## 🔧 Environment-Specific Configurations

### Staging Environment

#### Infrastructure

- **Compute**: 2 vCPUs, 4GB RAM
- **Database**: db.t3.micro (1 vCPU, 1GB RAM)
- **Cache**: cache.t3.micro (1 vCPU, 0.5GB RAM)

#### Configuration

```yaml
# docker-compose.staging.yml
services:
  backend:
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
  
  frontend:
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
```

### Production Environment

#### Infrastructure

- **Compute**: 4 vCPUs, 8GB RAM (Auto-scaling)
- **Database**: db.t3.large (2 vCPUs, 8GB RAM)
- **Cache**: cache.t3.medium (2 vCPUs, 3.22GB RAM)

#### Configuration

```yaml
# docker-compose.production.yml
services:
  backend:
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
  
  frontend:
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
```

## 📋 Pre-deployment Checklist

### Code Quality

- [ ] All tests pass
- [ ] Code review completed
- [ ] Security scan passed
- [ ] Performance benchmarks met
- [ ] Documentation updated

### Environment Preparation

- [ ] Environment variables configured
- [ ] Database migrations ready
- [ ] SSL certificates valid
- [ ] Monitoring alerts configured
- [ ] Backup strategy in place

### Infrastructure

- [ ] Server capacity adequate
- [ ] Load balancer configured
- [ ] CDN cache cleared
- [ ] DNS records updated
- [ ] Firewall rules configured

## 🔄 Deployment Process

### 1. Pre-deployment

```bash
# Run pre-deployment checks
./scripts/pre-deploy-check.sh production

# Create backup
./scripts/backup.sh --environment production

# Notify team
curl -X POST $SLACK_WEBHOOK_URL \
  -H 'Content-type: application/json' \
  --data '{"text":"🚀 Starting production deployment"}'
```

### 2. Deployment

```bash
# Deploy application
./scripts/deploy.sh production

# Verify deployment
./scripts/verify-deployment.sh production
```

### 3. Post-deployment

```bash
# Run smoke tests
./scripts/smoke-tests.sh production

# Monitor application
./scripts/monitor-deployment.sh production

# Notify completion
curl -X POST $SLACK_WEBHOOK_URL \
  -H 'Content-type: application/json' \
  --data '{"text":"✅ Production deployment completed successfully"}'
```

## 🔍 Monitoring and Verification

### Health Checks

```bash
# Backend health check
curl -f https://api.phcityrent.com/api/v1/health

# Frontend health check
curl -f https://phcityrent.com

# Database health check
docker-compose exec postgres pg_isready
```

### Performance Monitoring

```bash
# Check response times
curl -w "@curl-format.txt" -o /dev/null -s https://api.phcityrent.com/api/v1/health

# Monitor resource usage
docker stats

# Check logs
docker-compose logs -f --tail=100
```

### Application Metrics

- **Response Time**: < 200ms (95th percentile)
- **Error Rate**: < 0.1%
- **Uptime**: > 99.9%
- **CPU Usage**: < 70%
- **Memory Usage**: < 80%

## 🔙 Rollback Procedures

### Automatic Rollback

The deployment script includes automatic rollback on failure:

```bash
# Rollback is triggered automatically if:
# - Health checks fail
# - Smoke tests fail
# - Error rate exceeds threshold
```

### Manual Rollback

```bash
# Rollback to previous version
./scripts/deploy.sh production --rollback

# Rollback to specific version
./scripts/deploy.sh production --rollback --version=v1.2.3
```

### Database Rollback

```bash
# Rollback database migrations
cd Backend
npm run migration:revert

# Restore from backup
./scripts/restore-backup.sh 20231201_120000
```

## 🔒 Security Considerations

### SSL/TLS Configuration

```nginx
# nginx SSL configuration
server {
    listen 443 ssl http2;
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
}
```

### Environment Variables Security

```bash
# Use secrets management
export DB_PASSWORD=$(aws secretsmanager get-secret-value --secret-id prod/db/password --query SecretString --output text)

# Encrypt sensitive files
gpg --symmetric --cipher-algo AES256 .env.production
```

### Network Security

```bash
# Configure firewall rules
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 3001/tcp   # Block direct API access
```

## 📊 Deployment Metrics

### Key Performance Indicators

- **Deployment Frequency**: Daily (staging), Weekly (production)
- **Lead Time**: < 30 minutes
- **Mean Time to Recovery**: < 15 minutes
- **Change Failure Rate**: < 5%

### Monitoring Dashboard

```bash
# Access deployment metrics
# Grafana: https://monitoring.phcityrent.com
# Prometheus: https://metrics.phcityrent.com
```

## 🆘 Troubleshooting

### Common Deployment Issues

#### 1. Container Startup Failures

```bash
# Check container logs
docker-compose logs backend

# Check resource constraints
docker stats

# Verify environment variables
docker-compose exec backend env | grep -E "(DB_|REDIS_)"
```

#### 2. Database Connection Issues

```bash
# Test database connectivity
docker-compose exec backend npm run db:test

# Check database status
docker-compose exec postgres pg_isready

# Verify credentials
psql -h $DB_HOST -U $DB_USERNAME -d $DB_NAME
```

#### 3. Load Balancer Issues

```bash
# Check target health
aws elbv2 describe-target-health --target-group-arn $TARGET_GROUP_ARN

# Verify security groups
aws ec2 describe-security-groups --group-ids $SECURITY_GROUP_ID
```

### Emergency Procedures

#### 1. Complete Service Outage

```bash
# Immediate rollback
./scripts/deploy.sh production --rollback --emergency

# Activate maintenance mode
./scripts/maintenance-mode.sh enable

# Notify stakeholders
./scripts/notify-outage.sh
```

#### 2. Database Issues

```bash
# Switch to read-only mode
./scripts/readonly-mode.sh enable

# Restore from backup
./scripts/restore-backup.sh latest

# Verify data integrity
./scripts/verify-data.sh
```

## 📚 Additional Resources

- [Infrastructure as Code](INFRASTRUCTURE.md)
- [Monitoring and Alerting](MONITORING.md)
- [Security Best Practices](SECURITY.md)
- [Disaster Recovery](DISASTER_RECOVERY.md)

---

For deployment support, contact the DevOps <NAME_EMAIL>
