import React from 'react';
import { Routes, Route } from 'react-router-dom';

// Simple test component
const SimpleHome = () => (
  <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
    <h1 style={{ color: '#333', marginBottom: '20px' }}>PHCityRent - Port Harcourt Real Estate Platform</h1>
    <p style={{ color: '#666', fontSize: '16px', lineHeight: '1.6' }}>
      Welcome to PHCityRent! This is a simplified version to test the React application.
    </p>
    <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
      <h2 style={{ color: '#28a745', marginBottom: '10px' }}>✅ Application Status</h2>
      <ul style={{ color: '#666' }}>
        <li>✅ React is working</li>
        <li>✅ Routing is functional</li>
        <li>✅ UI components are loading</li>
        <li>✅ Development server is running</li>
      </ul>
    </div>
    <div style={{ marginTop: '20px' }}>
      <button
        style={{
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '16px'
        }}
        onClick={() => alert('PHCityRent is working!')}
      >
        Test Interaction
      </button>
    </div>
  </div>
);

function App() {
  return (
    <Routes>
      <Route path="/" element={<SimpleHome />} />
      <Route path="*" element={<SimpleHome />} />
    </Routes>
  );
}

export default App;
