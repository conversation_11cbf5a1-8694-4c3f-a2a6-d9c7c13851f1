import React from 'react';
import { useAuth } from '@/hooks/auth/useAuth';
import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard';

const AnalyticsDashboardPage: React.FC = () => {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">Please log in to access the Analytics Dashboard.</p>
        </div>
      </div>
    );
  }

  return <AnalyticsDashboard userId={user.id} />;
};

export default AnalyticsDashboardPage;
