import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertTriangle, Bug, Network, Server, Clock, Shield } from 'lucide-react';

import { errorHandler } from '@/services/errorHandler';
import { notifications } from '@/services/notificationService';
import { logger } from '@/services/logger';
import { useErrorRecovery, useRetryableOperation, useCircuitBreaker } from '@/hooks/useErrorRecovery';
import ComponentErrorBoundary from '@/components/error/ComponentErrorBoundary';
import { 
  ErrorFallback, 
  NetworkErrorFallback, 
  ServerErrorFallback, 
  TimeoutErrorFallback,
  OfflineFallback 
} from '@/components/error/FallbackComponents';

// Test component that throws errors
const ErrorThrowingComponent: React.FC<{ shouldThrow: boolean; errorType: string }> = ({ 
  shouldThrow, 
  errorType 
}) => {
  if (shouldThrow) {
    switch (errorType) {
      case 'render':
        throw new Error('Render error for testing');
      case 'async':
        throw new Error('Async error for testing');
      default:
        throw new Error('Generic error for testing');
    }
  }
  return <div className="p-4 bg-green-50 rounded">Component rendered successfully!</div>;
};

// Test async operation
const createTestAsyncOperation = (errorType: string, delay = 1000) => {
  return async (): Promise<string> => {
    await new Promise(resolve => setTimeout(resolve, delay));
    
    switch (errorType) {
      case 'network':
        throw { message: 'Network error', status: 0 };
      case 'server':
        throw { message: 'Internal server error', status: 500 };
      case 'timeout':
        throw { message: 'Request timeout', name: 'AbortError' };
      case 'auth':
        throw { message: 'Unauthorized', status: 401 };
      case 'forbidden':
        throw { message: 'Forbidden', status: 403 };
      case 'validation':
        throw { message: 'Validation failed', status: 422 };
      case 'success':
        return 'Operation completed successfully!';
      default:
        throw new Error('Unknown error type');
    }
  };
};

const ErrorTestPage: React.FC = () => {
  const [componentError, setComponentError] = useState<{ shouldThrow: boolean; errorType: string }>({
    shouldThrow: false,
    errorType: 'render',
  });

  // Test error recovery hook
  const [recoveryState, recoveryActions] = useErrorRecovery(
    createTestAsyncOperation('success'),
    { maxAttempts: 3 }
  );

  // Test retryable operation
  const [retryableState, retryableActions] = useRetryableOperation(
    createTestAsyncOperation('network'),
    [],
    { maxAttempts: 2 }
  );

  // Test circuit breaker
  const [circuitState, circuitActions] = useCircuitBreaker(
    createTestAsyncOperation('server'),
    { failureThreshold: 3 }
  );

  const testGlobalErrorHandler = (errorType: string) => {
    switch (errorType) {
      case 'javascript':
        // Simulate JavaScript error
        setTimeout(() => {
          throw new Error('Simulated JavaScript error');
        }, 100);
        break;
      
      case 'promise':
        // Simulate unhandled promise rejection
        Promise.reject(new Error('Simulated promise rejection'));
        break;
      
      case 'api':
        // Simulate API error
        errorHandler.handleApiError({
          response: { status: 500, data: { message: 'Server error' } },
          message: 'API request failed',
        });
        break;
      
      case 'network':
        errorHandler.reportNetworkError(new Error('Network connection failed'));
        break;
      
      case 'validation':
        errorHandler.reportValidationError('Invalid input provided');
        break;
      
      case 'critical':
        errorHandler.reportCriticalError(new Error('Critical system failure'));
        break;
    }
  };

  const testNotifications = (type: string) => {
    switch (type) {
      case 'success':
        notifications.success('Operation completed successfully!');
        break;
      case 'error':
        notifications.error('Something went wrong', {
          description: 'This is a test error notification',
        });
        break;
      case 'warning':
        notifications.warning('Warning message', {
          description: 'This is a test warning',
        });
        break;
      case 'info':
        notifications.info('Information', {
          description: 'This is a test info message',
        });
        break;
      case 'loading':
        const loadingId = notifications.loading('Processing...');
        setTimeout(() => {
          notifications.update(loadingId, 'Completed!', 'success');
        }, 2000);
        break;
      case 'retry':
        notifications.error('Operation failed', {
          retry: {
            maxRetries: 3,
            onRetry: async () => {
              await new Promise(resolve => setTimeout(resolve, 1000));
              notifications.success('Retry successful!');
            },
          },
        });
        break;
    }
  };

  const testLogger = (level: string) => {
    const context = { testData: 'sample', timestamp: Date.now() };
    
    switch (level) {
      case 'debug':
        logger.debug('Debug message', context, 'ErrorTestPage', 'Debug Test');
        break;
      case 'info':
        logger.info('Info message', context, 'ErrorTestPage', 'Info Test');
        break;
      case 'warn':
        logger.warn('Warning message', context, 'ErrorTestPage', 'Warn Test');
        break;
      case 'error':
        logger.error('Error message', new Error('Test error'), context, 'ErrorTestPage', 'Error Test');
        break;
      case 'critical':
        logger.critical('Critical message', new Error('Critical test error'), context, 'ErrorTestPage', 'Critical Test');
        break;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Error Handling Test Page</h1>
        <p className="text-gray-600">Test all error handling mechanisms and recovery systems</p>
      </div>

      {/* Error Boundaries Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Error Boundaries
          </CardTitle>
          <CardDescription>Test React error boundaries with different error types</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={() => setComponentError({ shouldThrow: true, errorType: 'render' })}
              variant="destructive"
              size="sm"
            >
              Trigger Render Error
            </Button>
            <Button
              onClick={() => setComponentError({ shouldThrow: false, errorType: 'render' })}
              variant="outline"
              size="sm"
            >
              Reset Component
            </Button>
          </div>
          
          <ComponentErrorBoundary componentName="Test Component">
            <ErrorThrowingComponent 
              shouldThrow={componentError.shouldThrow} 
              errorType={componentError.errorType} 
            />
          </ComponentErrorBoundary>
        </CardContent>
      </Card>

      {/* Global Error Handler Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="w-5 h-5" />
            Global Error Handler
          </CardTitle>
          <CardDescription>Test global error handling for different error types</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            <Button onClick={() => testGlobalErrorHandler('javascript')} variant="destructive" size="sm">
              JavaScript Error
            </Button>
            <Button onClick={() => testGlobalErrorHandler('promise')} variant="destructive" size="sm">
              Promise Rejection
            </Button>
            <Button onClick={() => testGlobalErrorHandler('api')} variant="destructive" size="sm">
              API Error
            </Button>
            <Button onClick={() => testGlobalErrorHandler('network')} variant="destructive" size="sm">
              Network Error
            </Button>
            <Button onClick={() => testGlobalErrorHandler('validation')} variant="destructive" size="sm">
              Validation Error
            </Button>
            <Button onClick={() => testGlobalErrorHandler('critical')} variant="destructive" size="sm">
              Critical Error
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Notifications Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            Toast Notifications
          </CardTitle>
          <CardDescription>Test different types of toast notifications</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            <Button onClick={() => testNotifications('success')} variant="default" size="sm">
              Success
            </Button>
            <Button onClick={() => testNotifications('error')} variant="destructive" size="sm">
              Error
            </Button>
            <Button onClick={() => testNotifications('warning')} variant="secondary" size="sm">
              Warning
            </Button>
            <Button onClick={() => testNotifications('info')} variant="outline" size="sm">
              Info
            </Button>
            <Button onClick={() => testNotifications('loading')} variant="outline" size="sm">
              Loading
            </Button>
            <Button onClick={() => testNotifications('retry')} variant="destructive" size="sm">
              With Retry
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Logger Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="w-5 h-5" />
            Logging System
          </CardTitle>
          <CardDescription>Test different log levels (check browser console)</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
            <Button onClick={() => testLogger('debug')} variant="outline" size="sm">
              Debug
            </Button>
            <Button onClick={() => testLogger('info')} variant="default" size="sm">
              Info
            </Button>
            <Button onClick={() => testLogger('warn')} variant="secondary" size="sm">
              Warn
            </Button>
            <Button onClick={() => testLogger('error')} variant="destructive" size="sm">
              Error
            </Button>
            <Button onClick={() => testLogger('critical')} variant="destructive" size="sm">
              Critical
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Recovery Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="w-5 h-5" />
            Error Recovery Mechanisms
          </CardTitle>
          <CardDescription>Test retry logic and fallback states</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Error Recovery Hook</h4>
            <div className="flex items-center gap-2 mb-2">
              <Badge variant={recoveryState.error ? 'destructive' : 'default'}>
                {recoveryState.isLoading ? 'Loading' : recoveryState.error ? 'Error' : 'Success'}
              </Badge>
              <span className="text-sm">Attempts: {recoveryState.attemptCount}</span>
            </div>
            <Button onClick={recoveryActions.retry} disabled={!recoveryState.canRetry} size="sm">
              Retry Operation
            </Button>
          </div>

          <Separator />

          <div>
            <h4 className="font-semibold mb-2">Circuit Breaker</h4>
            <div className="flex items-center gap-2 mb-2">
              <Badge variant={circuitState.circuitState === 'open' ? 'destructive' : 'default'}>
                Circuit: {circuitState.circuitState}
              </Badge>
              <span className="text-sm">Failures: {circuitState.failureCount}</span>
            </div>
            <Button onClick={circuitActions.retry} size="sm">
              Test Circuit Breaker
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Fallback Components Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Fallback Components
          </CardTitle>
          <CardDescription>Preview of different fallback UI components</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Network Error</h4>
            <NetworkErrorFallback retry={() => {}} />
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Server Error</h4>
            <ServerErrorFallback retry={() => {}} />
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Timeout Error</h4>
            <TimeoutErrorFallback retry={() => {}} />
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Offline State</h4>
            <OfflineFallback onRetry={() => {}} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ErrorTestPage;
