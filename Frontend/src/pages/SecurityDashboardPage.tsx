// =====================================================
// SECURITY DASHBOARD PAGE
// Main page for security monitoring and management
// =====================================================

import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import SecurityDashboard from '@/components/security/SecurityDashboard';

const SecurityDashboardPage: React.FC = () => {
  const { user } = useAuth();

  return <SecurityDashboard userId={user?.id} />;
};

export default SecurityDashboardPage;
