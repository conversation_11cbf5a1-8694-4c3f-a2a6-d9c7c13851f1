import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import CommunicationDashboard from '@/components/communication/CommunicationDashboard';

const CommunicationDashboardPage: React.FC = () => {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">Please log in to access the Communication Dashboard.</p>
        </div>
      </div>
    );
  }

  return <CommunicationDashboard userId={user.id} />;
};

export default CommunicationDashboardPage;
