// =====================================================
// PERFORMANCE DASHBOARD PAGE
// Main page for performance monitoring and optimization
// =====================================================

import React from 'react';
import { useAuth } from '@/hooks/auth/useAuth';
import PerformanceDashboard from '@/components/performance/PerformanceDashboard';

const PerformanceDashboardPage: React.FC = () => {
  const { user } = useAuth();

  return <PerformanceDashboard userId={user?.id} />;
};

export default PerformanceDashboardPage;
