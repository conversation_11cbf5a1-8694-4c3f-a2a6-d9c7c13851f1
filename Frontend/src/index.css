
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap');

@font-face {
  font-family: 'Brockmann';
  src: url('/brockmann-medium-webfont.ttf') format('truetype'),
       url('/brockmann-medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 24 95% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 24 95% 53%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 24 95% 53%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-white text-gray-900 font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar - more subtle */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f8f9fa;
  }

  ::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 6px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
}

@layer components {
  .text-mask-image {
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    background-size: cover;
    background-position: center;
  }

  /* Enhanced component classes */
  .glass-card {
    @apply bg-white/90 backdrop-blur-sm border border-white/20 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl;
  }

  .feature-card {
    @apply p-6 rounded-xl bg-white border border-gray-100 shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-1;
  }
  
  .button-primary {
    @apply bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 
           text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 
           shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]
           focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2;
  }

  .button-secondary {
    @apply bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 
           font-medium py-3 px-6 rounded-lg transition-all duration-200 
           shadow-sm hover:shadow-md hover:bg-gray-50;
  }
  
  .nav-link {
    @apply relative text-gray-700 hover:text-orange-600 py-2 font-medium transition-colors duration-200 
           after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-0 after:bg-orange-500 
           after:transition-all after:duration-200 hover:after:w-full;
  }

  /* Enhanced section styles */
  .section-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16;
  }

  .section-title {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight text-gray-900;
  }

  .section-subtitle {
    @apply text-lg md:text-xl text-gray-600 mt-4 max-w-3xl leading-relaxed;
  }
}

/* Enhanced animations - more subtle */
.image-scale-in {
  animation: subtleScaleIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes subtleScaleIn {
  0% {
    transform: scale(0.98);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.fadeIn {
  opacity: 0;
  transform: translateY(10px);
  animation: smoothFadeIn 0.6s ease forwards;
}

@keyframes smoothFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }

.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Remove excessive parallax effects for cleaner experience */
.parallax {
  transform: none;
  transition: none;
}

/* Enhanced mobile optimizations */
@media (max-width: 640px) {
  .section-container {
    @apply px-4 py-8;
  }
  
  .section-title {
    @apply text-2xl leading-tight;
  }
  
  .section-subtitle {
    @apply text-base;
  }

  .button-primary, .button-secondary {
    @apply w-full justify-center;
  }

  /* Better touch targets */
  .nav-link, button, a {
    @apply min-h-[44px] inline-flex items-center justify-center;
  }

  /* Mobile form improvements */
  input, textarea, select {
    @apply text-base; /* Prevents iOS zoom */
  }
}
