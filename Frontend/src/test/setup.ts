/**
 * Test Setup Configuration
 *
 * Global test setup following <PERSON>'s patterns for consistent testing environment.
 */

import '@testing-library/jest-dom';
import { vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import { afterEach, beforeAll, afterAll } from 'vitest';

// Cleanup after each test
afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

// Global test setup
beforeAll(() => {
  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(callback => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
    root: null,
    rootMargin: '',
    thresholds: [],
  }));

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(callback => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  // Mock scrollTo
  Object.defineProperty(window, 'scrollTo', {
    writable: true,
    value: vi.fn(),
  });

  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });

  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn(),
  };
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
  });

  // Mock URL.createObjectURL and revokeObjectURL
  Object.defineProperty(URL, 'createObjectURL', {
    writable: true,
    value: vi.fn(() => 'mocked-url'),
  });
  Object.defineProperty(URL, 'revokeObjectURL', {
    writable: true,
    value: vi.fn(),
  });

  // Mock performance API
  Object.defineProperty(window, 'performance', {
    writable: true,
    value: {
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByName: vi.fn(() => [{ duration: 100 }]),
      now: vi.fn(() => Date.now()),
      timing: {},
      navigation: {},
    },
  });

  // Mock crypto API for UUID generation
  Object.defineProperty(window, 'crypto', {
    writable: true,
    value: {
      randomUUID: vi.fn(() => 'mocked-uuid'),
      getRandomValues: vi.fn(arr => {
        for (let i = 0; i < arr.length; i++) {
          arr[i] = Math.floor(Math.random() * 256);
        }
        return arr;
      }),
    },
  });

  // Mock fetch globally
  global.fetch = vi.fn();

  // Mock console methods to reduce noise in tests
  vi.spyOn(console, 'warn').mockImplementation(() => {});
  vi.spyOn(console, 'error').mockImplementation(() => {});
  vi.spyOn(console, 'log').mockImplementation(() => {});

  // Set up fake timers
  vi.useFakeTimers();
});

afterAll(() => {
  vi.useRealTimers();
  vi.restoreAllMocks();
});

// Custom matchers for better assertions
expect.extend({
  toBeWithinRange(received: number, floor: number, ceiling: number) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () => `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },

  toHaveBeenCalledWithObjectContaining(received: any, expected: any) {
    const pass = received.mock.calls.some((call: any[]) =>
      call.some(arg => {
        if (typeof arg === 'object' && arg !== null) {
          return Object.keys(expected).every(key => arg[key] === expected[key]);
        }
        return false;
      })
    );

    if (pass) {
      return {
        message: () =>
          `expected function not to have been called with object containing ${JSON.stringify(expected)}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected function to have been called with object containing ${JSON.stringify(expected)}`,
        pass: false,
      };
    }
  },
});

// Global test utilities
export const createMockUser = (overrides = {}) => ({
  id: 'user-123',
  email: '<EMAIL>',
  full_name: 'Test User',
  role: 'admin',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

export const createMockApplication = (overrides = {}) => ({
  id: 'app-123',
  agent_id: 'agent-123',
  full_name: 'Test Agent',
  email: '<EMAIL>',
  whatsapp_number: '+1234567890',
  status: 'pending_review',
  created_at: new Date().toISOString(),
  operating_areas: ['Test Area'],
  residential_address: 'Test Address',
  is_registered_business: false,
  ...overrides,
});

export const createMockAgent = (overrides = {}) => ({
  id: 'agent-123',
  agent_id: 'AGT123',
  full_name: 'Test Agent',
  email: '<EMAIL>',
  whatsapp_number: '+1234567890',
  operating_areas: ['Test Area'],
  agent_status: 'active',
  verification_status: 'verified',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

export const waitForLoadingToFinish = () => {
  return new Promise(resolve => {
    setTimeout(resolve, 0);
  });
};

export const mockApiResponse = (data: any, delay = 0) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        ok: true,
        json: () => Promise.resolve(data),
      });
    }, delay);
  });
};

export const mockApiError = (status = 500, message = 'Server Error', delay = 0) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        ok: false,
        status,
        statusText: message,
        json: () => Promise.resolve({ message }),
      });
    }, delay);
  });
};

// Type declarations for custom matchers
declare global {
  interface CustomMatchers<R = unknown> {
    toBeWithinRange(floor: number, ceiling: number): R;
    toHaveBeenCalledWithObjectContaining(expected: Record<string, unknown>): R;
  }
}
