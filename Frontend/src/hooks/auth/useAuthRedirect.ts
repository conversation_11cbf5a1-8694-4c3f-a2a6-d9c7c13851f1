import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from './useAuth';

export const useAuthRedirect = () => {
  const navigate = useNavigate();
  const { user, loading, isAdmin } = useAuth();

  useEffect(() => {
    if (loading || !user) return;

    const checkUserRoleAndRedirect = () => {
      try {
        // Check if user has admin role (using the role from the backend API)
        if (
          isAdmin ||
          user.role === 'admin' ||
          user.role === 'ADMIN' ||
          user.role === 'super_admin' ||
          user.role === 'SUPER_ADMIN'
        ) {
          navigate('/admin');
          return;
        }

        // Check if user is an agent
        if (user.role === 'agent' || user.role === 'AGENT') {
          navigate('/enhanced-agent-dashboard');
          return;
        }

        // Check if user is a landlord
        if (user.role === 'landlord' || user.role === 'LANDLORD') {
          navigate('/landlord-portal');
          return;
        }

        // Default to tenant portal for tenants or any other role
        navigate('/tenant-portal');
      } catch (error) {
        console.error('Error checking user role:', error);
        // Default to tenant portal on error
        navigate('/tenant-portal');
      }
    };

    checkUserRoleAndRedirect();
  }, [user, loading, navigate, isAdmin]);
};
