import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { whatsappService } from '@/services/whatsappService';
import { emailService } from '@/services/emailService';
import { messagingService } from '@/services/messagingService';
import { smsService } from '@/services/smsService';
import {
  WhatsAppMessage,
  WhatsAppCampaign,
  EmailCampaign,
  EmailDelivery,
  Conversation,
  Message,
  SMSMessage,
  OTPVerification
} from '@/types/communication';

/**
 * Hook for WhatsApp messaging functionality
 */
export const useWhatsAppMessaging = (contactId?: string) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: messages,
    isLoading: messagesLoading
  } = useQuery({
    queryKey: ['whatsapp-messages', contactId],
    queryFn: () => contactId ? whatsappService.getContactMessages(contactId) : null,
    enabled: !!contactId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  });

  const {
    data: campaigns,
    isLoading: campaignsLoading
  } = useQuery({
    queryKey: ['whatsapp-campaigns'],
    queryFn: () => whatsappService.getCampaigns(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const sendMessageMutation = useMutation({
    mutationFn: ({ phoneNumber, message, messageType }: {
      phoneNumber: string;
      message: string;
      messageType?: 'text' | 'template';
    }) => {
      if (messageType === 'template') {
        return whatsappService.sendTemplateMessage(phoneNumber, message, []);
      }
      return whatsappService.sendTextMessage(phoneNumber, message, contactId);
    },
    onSuccess: (sentMessage) => {
      queryClient.invalidateQueries(['whatsapp-messages', contactId]);
      toast({
        title: "Message Sent",
        description: "WhatsApp message sent successfully.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Send Failed",
        description: error.message || "Failed to send WhatsApp message.",
        variant: "destructive",
      });
    }
  });

  const sendMediaMutation = useMutation({
    mutationFn: ({ phoneNumber, mediaUrl, mediaType, caption }: {
      phoneNumber: string;
      mediaUrl: string;
      mediaType: 'image' | 'document' | 'audio' | 'video';
      caption?: string;
    }) => whatsappService.sendMediaMessage(phoneNumber, mediaUrl, mediaType, caption, contactId),
    onSuccess: () => {
      queryClient.invalidateQueries(['whatsapp-messages', contactId]);
      toast({
        title: "Media Sent",
        description: "WhatsApp media message sent successfully.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Send Failed",
        description: error.message || "Failed to send WhatsApp media.",
        variant: "destructive",
      });
    }
  });

  const createCampaignMutation = useMutation({
    mutationFn: (campaignData: Omit<WhatsAppCampaign, 'id' | 'created_at' | 'metrics'>) =>
      whatsappService.createCampaign(campaignData),
    onSuccess: (newCampaign) => {
      queryClient.invalidateQueries(['whatsapp-campaigns']);
      toast({
        title: "Campaign Created",
        description: `WhatsApp campaign "${newCampaign.name}" created successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Creation Failed",
        description: error.message || "Failed to create WhatsApp campaign.",
        variant: "destructive",
      });
    }
  });

  const executeCampaignMutation = useMutation({
    mutationFn: (campaignId: string) => whatsappService.executeCampaign(campaignId),
    onSuccess: () => {
      queryClient.invalidateQueries(['whatsapp-campaigns']);
      toast({
        title: "Campaign Executed",
        description: "WhatsApp campaign is being sent to recipients.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Execution Failed",
        description: error.message || "Failed to execute WhatsApp campaign.",
        variant: "destructive",
      });
    }
  });

  const sendMessage = useCallback(
    (phoneNumber: string, message: string, messageType?: 'text' | 'template') => {
      sendMessageMutation.mutate({ phoneNumber, message, messageType });
    },
    [sendMessageMutation]
  );

  const sendMedia = useCallback(
    (phoneNumber: string, mediaUrl: string, mediaType: 'image' | 'document' | 'audio' | 'video', caption?: string) => {
      sendMediaMutation.mutate({ phoneNumber, mediaUrl, mediaType, caption });
    },
    [sendMediaMutation]
  );

  const createCampaign = useCallback(
    (campaignData: Omit<WhatsAppCampaign, 'id' | 'created_at' | 'metrics'>) => {
      createCampaignMutation.mutate(campaignData);
    },
    [createCampaignMutation]
  );

  const executeCampaign = useCallback(
    (campaignId: string) => {
      executeCampaignMutation.mutate(campaignId);
    },
    [executeCampaignMutation]
  );

  return {
    messages: messages || [],
    campaigns: campaigns || [],
    messagesLoading,
    campaignsLoading,
    sendMessage,
    sendMedia,
    createCampaign,
    executeCampaign,
    isSending: sendMessageMutation.isPending || sendMediaMutation.isPending,
    isCreatingCampaign: createCampaignMutation.isPending,
    isExecutingCampaign: executeCampaignMutation.isPending
  };
};

/**
 * Hook for email campaigns and notifications
 */
export const useEmailCampaigns = (campaignId?: string) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: campaigns,
    isLoading: campaignsLoading
  } = useQuery({
    queryKey: ['email-campaigns'],
    queryFn: () => emailService.getCampaigns(),
    staleTime: 5 * 60 * 1000,
  });

  const {
    data: campaignDetails,
    isLoading: detailsLoading
  } = useQuery({
    queryKey: ['email-campaign', campaignId],
    queryFn: () => campaignId ? emailService.getCampaign(campaignId) : null,
    enabled: !!campaignId,
    staleTime: 2 * 60 * 1000,
  });

  const {
    data: templates,
    isLoading: templatesLoading
  } = useQuery({
    queryKey: ['email-templates'],
    queryFn: () => emailService.getTemplates(),
    staleTime: 10 * 60 * 1000,
  });

  const sendTransactionalMutation = useMutation({
    mutationFn: ({ templateName, recipientEmail, recipientName, variables, options }: {
      templateName: string;
      recipientEmail: string;
      recipientName: string;
      variables?: Record<string, any>;
      options?: any;
    }) => emailService.sendTransactionalEmail(templateName, recipientEmail, recipientName, variables, options),
    onSuccess: () => {
      toast({
        title: "Email Sent",
        description: "Transactional email sent successfully.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Send Failed",
        description: error.message || "Failed to send email.",
        variant: "destructive",
      });
    }
  });

  const createCampaignMutation = useMutation({
    mutationFn: (campaignData: Omit<EmailCampaign, 'id' | 'created_at' | 'metrics'>) =>
      emailService.createCampaign(campaignData),
    onSuccess: (newCampaign) => {
      queryClient.invalidateQueries(['email-campaigns']);
      toast({
        title: "Campaign Created",
        description: `Email campaign "${newCampaign.name}" created successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Creation Failed",
        description: error.message || "Failed to create email campaign.",
        variant: "destructive",
      });
    }
  });

  const executeCampaignMutation = useMutation({
    mutationFn: (campaignId: string) => emailService.executeCampaign(campaignId),
    onSuccess: () => {
      queryClient.invalidateQueries(['email-campaigns']);
      toast({
        title: "Campaign Executed",
        description: "Email campaign is being sent to recipients.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Execution Failed",
        description: error.message || "Failed to execute email campaign.",
        variant: "destructive",
      });
    }
  });

  const sendTransactionalEmail = useCallback(
    (templateName: string, recipientEmail: string, recipientName: string, variables?: Record<string, any>, options?: any) => {
      sendTransactionalMutation.mutate({ templateName, recipientEmail, recipientName, variables, options });
    },
    [sendTransactionalMutation]
  );

  const createCampaign = useCallback(
    (campaignData: Omit<EmailCampaign, 'id' | 'created_at' | 'metrics'>) => {
      createCampaignMutation.mutate(campaignData);
    },
    [createCampaignMutation]
  );

  const executeCampaign = useCallback(
    (campaignId: string) => {
      executeCampaignMutation.mutate(campaignId);
    },
    [executeCampaignMutation]
  );

  return {
    campaigns: campaigns || [],
    campaignDetails,
    templates: templates || [],
    campaignsLoading,
    detailsLoading,
    templatesLoading,
    sendTransactionalEmail,
    createCampaign,
    executeCampaign,
    isSending: sendTransactionalMutation.isPending,
    isCreatingCampaign: createCampaignMutation.isPending,
    isExecutingCampaign: executeCampaignMutation.isPending
  };
};

/**
 * Hook for in-app chat functionality
 */
export const useInAppChat = (conversationId?: string) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);

  const {
    data: conversation,
    isLoading: conversationLoading
  } = useQuery({
    queryKey: ['conversation', conversationId],
    queryFn: () => conversationId ? messagingService.getConversation(conversationId) : null,
    enabled: !!conversationId,
    staleTime: 30 * 1000,
  });

  const {
    data: messages,
    isLoading: messagesLoading
  } = useQuery({
    queryKey: ['conversation-messages', conversationId],
    queryFn: () => conversationId ? messagingService.getConversationMessages(conversationId) : null,
    enabled: !!conversationId,
    staleTime: 10 * 1000,
    refetchInterval: 5 * 1000, // Refetch every 5 seconds for real-time feel
  });

  const sendMessageMutation = useMutation({
    mutationFn: ({ senderId, messageType, content, options }: {
      senderId: string;
      messageType: Message['message_type'];
      content: Message['content'];
      options?: any;
    }) => {
      if (!conversationId) throw new Error('No conversation selected');
      return messagingService.sendMessage(conversationId, senderId, messageType, content, options);
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['conversation-messages', conversationId]);
    },
    onError: (error: any) => {
      toast({
        title: "Send Failed",
        description: error.message || "Failed to send message.",
        variant: "destructive",
      });
    }
  });

  const uploadFileMutation = useMutation({
    mutationFn: ({ file, uploadedBy }: { file: File; uploadedBy: string }) => {
      if (!conversationId) throw new Error('No conversation selected');
      return messagingService.uploadFile(file, conversationId, uploadedBy);
    },
    onSuccess: (fileUpload) => {
      // Send file message
      sendMessageMutation.mutate({
        senderId: fileUpload.uploaded_by,
        messageType: fileUpload.file_type.startsWith('image/') ? 'image' : 'document',
        content: {
          file_url: fileUpload.file_url,
          file_name: fileUpload.file_name,
          file_size: fileUpload.file_size,
          file_type: fileUpload.file_type
        }
      });
    },
    onError: (error: any) => {
      toast({
        title: "Upload Failed",
        description: error.message || "Failed to upload file.",
        variant: "destructive",
      });
    }
  });

  // Subscribe to real-time updates
  useEffect(() => {
    if (conversationId) {
      messagingService.subscribeToConversation(conversationId);
      
      return () => {
        messagingService.unsubscribeFromConversation(conversationId);
      };
    }
  }, [conversationId]);

  const sendMessage = useCallback(
    (senderId: string, messageType: Message['message_type'], content: Message['content'], options?: any) => {
      sendMessageMutation.mutate({ senderId, messageType, content, options });
    },
    [sendMessageMutation]
  );

  const uploadFile = useCallback(
    (file: File, uploadedBy: string) => {
      uploadFileMutation.mutate({ file, uploadedBy });
    },
    [uploadFileMutation]
  );

  const sendTypingIndicator = useCallback(
    (userId: string, typing: boolean) => {
      if (conversationId) {
        messagingService.sendTypingIndicator(conversationId, userId, typing);
        setIsTyping(typing);
      }
    },
    [conversationId]
  );

  const markAsRead = useCallback(
    (messageId: string, userId: string) => {
      messagingService.markMessageAsRead(messageId, userId);
    },
    []
  );

  return {
    conversation,
    messages: messages || [],
    conversationLoading,
    messagesLoading,
    isTyping,
    typingUsers,
    sendMessage,
    uploadFile,
    sendTypingIndicator,
    markAsRead,
    isSending: sendMessageMutation.isPending,
    isUploading: uploadFileMutation.isPending
  };
};

/**
 * Hook for SMS alerts and notifications
 */
export const useSMSAlerts = (userId?: string) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: smsMessages,
    isLoading: messagesLoading
  } = useQuery({
    queryKey: ['sms-messages', userId],
    queryFn: () => userId ? smsService.getUserMessages(userId) : null,
    enabled: !!userId,
    staleTime: 2 * 60 * 1000,
  });

  const sendSMSMutation = useMutation({
    mutationFn: ({ phoneNumber, message, messageType, options }: {
      phoneNumber: string;
      message: string;
      messageType?: SMSMessage['message_type'];
      options?: any;
    }) => smsService.sendSMS(phoneNumber, message, messageType, options),
    onSuccess: () => {
      queryClient.invalidateQueries(['sms-messages', userId]);
      toast({
        title: "SMS Sent",
        description: "SMS message sent successfully.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Send Failed",
        description: error.message || "Failed to send SMS.",
        variant: "destructive",
      });
    }
  });

  const generateOTPMutation = useMutation({
    mutationFn: ({ phoneNumber, purpose, options }: {
      phoneNumber: string;
      purpose: OTPVerification['purpose'];
      options?: any;
    }) => smsService.generateOTP(phoneNumber, purpose, options),
    onSuccess: () => {
      toast({
        title: "OTP Sent",
        description: "Verification code sent to your phone.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "OTP Failed",
        description: error.message || "Failed to send verification code.",
        variant: "destructive",
      });
    }
  });

  const verifyOTPMutation = useMutation({
    mutationFn: ({ phoneNumber, code, purpose }: {
      phoneNumber: string;
      code: string;
      purpose: OTPVerification['purpose'];
    }) => smsService.verifyOTP(phoneNumber, code, purpose),
    onSuccess: (result) => {
      if (result.success) {
        toast({
          title: "Verification Successful",
          description: result.message,
        });
      } else {
        toast({
          title: "Verification Failed",
          description: result.message,
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Verification Error",
        description: error.message || "Failed to verify code.",
        variant: "destructive",
      });
    }
  });

  const sendSMS = useCallback(
    (phoneNumber: string, message: string, messageType?: SMSMessage['message_type'], options?: any) => {
      sendSMSMutation.mutate({ phoneNumber, message, messageType, options });
    },
    [sendSMSMutation]
  );

  const generateOTP = useCallback(
    (phoneNumber: string, purpose: OTPVerification['purpose'], options?: any) => {
      generateOTPMutation.mutate({ phoneNumber, purpose, options });
    },
    [generateOTPMutation]
  );

  const verifyOTP = useCallback(
    (phoneNumber: string, code: string, purpose: OTPVerification['purpose']) => {
      verifyOTPMutation.mutate({ phoneNumber, code, purpose });
    },
    [verifyOTPMutation]
  );

  return {
    smsMessages: smsMessages || [],
    messagesLoading,
    sendSMS,
    generateOTP,
    verifyOTP,
    otpData: generateOTPMutation.data,
    verificationResult: verifyOTPMutation.data,
    isSending: sendSMSMutation.isPending,
    isGeneratingOTP: generateOTPMutation.isPending,
    isVerifying: verifyOTPMutation.isPending
  };
};
