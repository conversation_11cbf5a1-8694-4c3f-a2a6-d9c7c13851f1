import { useQuery } from '@tanstack/react-query';
import { ApiService, ApiProperty } from '@/services/apiService';

export const useProperty = (id: string) => {
  return useQuery({
    queryKey: ['property', id],
    queryFn: async (): Promise<ApiProperty> => {
      return await ApiService.getProperty(id);
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export const useProperties = (
  filters?: {
    page?: number;
    limit?: number;
    search?: string;
    propertyType?: string;
    minPrice?: number;
    maxPrice?: number;
    bedrooms?: number;
    bathrooms?: number;
    city?: string;
    state?: string;
    isVerified?: boolean;
    isFeatured?: boolean;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  } = {}
) => {
  return useQuery({
    queryKey: ['properties', filters],
    queryFn: async () => {
      const result = await ApiService.getProperties(filters);
      return result.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
};
