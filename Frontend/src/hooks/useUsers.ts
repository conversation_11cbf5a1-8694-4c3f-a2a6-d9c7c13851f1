import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ApiService, ApiUser } from '@/services/apiService';
import { useToast } from '@/hooks/use-toast';

// Hook for fetching users with filters
export const useUsers = (
  filters?: {
    page?: number;
    limit?: number;
    role?: string;
    status?: string;
    search?: string;
  } = {}
) => {
  return useQuery({
    queryKey: ['users', filters],
    queryFn: async () => {
      const result = await ApiService.getUsers(filters);
      return result;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
};

// Hook for fetching a single user
export const useUser = (id: string) => {
  return useQuery({
    queryKey: ['user', id],
    queryFn: async (): Promise<ApiUser> => {
      return await ApiService.getUser(id);
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Hook for updating user
export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<ApiUser> }) => {
      return await ApiService.updateUser(id, updates);
    },
    onSuccess: updatedUser => {
      // Invalidate and refetch users
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user', updatedUser.id] });

      toast({
        title: 'Success',
        description: 'User updated successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update user',
        variant: 'destructive',
      });
    },
  });
};

// Hook for user statistics
export const useUserStats = () => {
  return useQuery({
    queryKey: ['user-stats'],
    queryFn: async () => {
      const stats = await ApiService.getStats();

      // Get users by role for additional breakdown
      const [landlords, agents, tenants] = await Promise.all([
        ApiService.getUsers({ role: 'landlord', limit: 1 }),
        ApiService.getUsers({ role: 'agent', limit: 1 }),
        ApiService.getUsers({ role: 'tenant', limit: 1 }),
      ]);

      return {
        total: stats.totalUsers,
        landlords: landlords.meta.total,
        agents: agents.meta.total,
        tenants: tenants.meta.total,
        // Calculate growth metrics (mock for now)
        monthlyGrowth: 8.2,
        weeklyGrowth: 2.1,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Hook for user analytics
export const useUserAnalytics = () => {
  return useQuery({
    queryKey: ['user-analytics'],
    queryFn: async () => {
      const stats = await ApiService.getStats();

      // Get role distribution
      const [landlords, agents, tenants, admins] = await Promise.all([
        ApiService.getUsers({ role: 'landlord', limit: 1 }),
        ApiService.getUsers({ role: 'agent', limit: 1 }),
        ApiService.getUsers({ role: 'tenant', limit: 1 }),
        ApiService.getUsers({ role: 'admin', limit: 1 }),
      ]);

      const roleDistribution = [
        { name: 'Tenants', value: tenants.meta.total, color: '#3B82F6' },
        { name: 'Landlords', value: landlords.meta.total, color: '#10B981' },
        { name: 'Agents', value: agents.meta.total, color: '#F59E0B' },
        { name: 'Admins', value: admins.meta.total, color: '#8B5CF6' },
      ];

      // Generate monthly growth data (mock based on total)
      const monthlyGrowth = Array.from({ length: 12 }, (_, i) => {
        const month = new Date();
        month.setMonth(month.getMonth() - (11 - i));
        const monthName = month.toLocaleDateString('en-US', { month: 'short' });

        // Simulate growth pattern
        const baseGrowth = Math.floor(stats.totalUsers / 12);
        const variation = Math.floor(baseGrowth * (0.8 + Math.random() * 0.4));

        return {
          month: monthName,
          users: variation,
          landlords: Math.floor(variation * 0.2),
          agents: Math.floor(variation * 0.15),
          tenants: Math.floor(variation * 0.65),
        };
      });

      return {
        totalUsers: stats.totalUsers,
        roleDistribution,
        monthlyGrowth,
        activeUsers: Math.floor(stats.totalUsers * 0.75), // Estimate 75% active
        verifiedUsers: Math.floor(stats.totalUsers * 0.85), // Estimate 85% verified
      };
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};
