import { useState, useCallback } from 'react';

interface UseImageOptimizationReturn {
  optimizeImage: (file: File, options?: OptimizationOptions) => Promise<File>;
  isOptimizing: boolean;
  error: string | null;
}

interface OptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
}

export const useImageOptimization = (): UseImageOptimizationReturn => {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const optimizeImage = useCallback(
    async (file: File, options: OptimizationOptions = {}): Promise<File> => {
      setIsOptimizing(true);
      setError(null);

      try {
        const { maxWidth = 1920, maxHeight = 1080, quality = 0.8, format = 'jpeg' } = options;

        return new Promise((resolve, reject) => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const img = new Image();

          img.onload = () => {
            // Calculate new dimensions
            let { width, height } = img;

            if (width > maxWidth) {
              height = (height * maxWidth) / width;
              width = maxWidth;
            }

            if (height > maxHeight) {
              width = (width * maxHeight) / height;
              height = maxHeight;
            }

            canvas.width = width;
            canvas.height = height;

            // Draw and compress
            ctx?.drawImage(img, 0, 0, width, height);

            canvas.toBlob(
              blob => {
                if (blob) {
                  const optimizedFile = new File([blob], file.name, {
                    type: `image/${format}`,
                    lastModified: Date.now(),
                  });
                  resolve(optimizedFile);
                } else {
                  reject(new Error('Failed to optimize image'));
                }
              },
              `image/${format}`,
              quality
            );
          };

          img.onerror = () => reject(new Error('Failed to load image'));
          img.src = URL.createObjectURL(file);
        });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        throw err;
      } finally {
        setIsOptimizing(false);
      }
    },
    []
  );

  return {
    optimizeImage,
    isOptimizing,
    error,
  };
};
