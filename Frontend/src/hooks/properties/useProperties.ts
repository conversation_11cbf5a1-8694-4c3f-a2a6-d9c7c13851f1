
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { propertyService } from '@/services/properties';
import {
  Property, SearchFilters, CreatePropertyRequest,
  UpdatePropertyRequest, PropertySearchResult,
  PropertyAnalytics
} from '@/types/property';
import { PROPERTY_ENDPOINTS } from '@/config/endpoints';
import { ApiResponse } from '@/services/apiClient';
import { HTTP_STATUS } from '@/config/api';
import { PropertyStatusEnum } from '@/types/property';
import apiClient from '@/services/apiClient';
// Helper function to extract actual error message
const getErrorMessage = (error: any): string => {
  if (error instanceof Error) return error.message;
  if (
    error && typeof error === 'object' && 'message' in error) {
      return (error as any).message;
    }
  return 'An unexpected error occurred';
};

export const useProperties = (
  filters?: SearchFilters,
  page: number = 1,
  pageSize: number = 20
) => {
  return useQuery<PropertySearchResult, Error>({
    queryKey: ['properties', filters, page, pageSize],
    queryFn: () => propertyService.getProperties(filters, page, pageSize),
    // TODO: will consider adding more options e.g staleTime, cacheTime etc
  });
};

export const useMyProperties = (
  filters?: SearchFilters,
  page: number = 1,
  pageSize: number = 20
) => {
  return useQuery<PropertySearchResult, Error>({
    queryKey: ['my-properties', filters, page, pageSize],
    queryFn: () => propertyService.getMyProperties(filters, page, pageSize),
  });
};

export const usePropertyById = (propertyId: string) => {
  return useQuery<Property, Error>({
    queryKey: ['property', propertyId],
    queryFn: () => propertyService.getProperty(propertyId),
    enabled: !!propertyId, // Only run the query if propertyId is available
  });
};

export const useCreateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation<Property, Error, CreatePropertyRequest>({
    mutationFn: (
      propertyData: CreatePropertyRequest
    ) => propertyService.createProperty(propertyData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['my-properties'] });
    },
    onError: (error) => {
      console.error('Failed to create property:', getErrorMessage(error));
      // TODO: add toast notifications or other error handling here
    },
  });
};

export const useUpdateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation<Property, Error, { id: string, updates: UpdatePropertyRequest }>({
    mutationFn: ({ id, updates }) => propertyService.updateProperty(id, updates),
    onSuccess: (updatedProperty) => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['my-properties'] });
      queryClient.invalidateQueries({ queryKey: ['property', updatedProperty] });
    },
    onError: (error) => {
      console.error('Failed to update property:', getErrorMessage(error));
    },
  });
};

export const useDeleteProperty = () => {
  const queryClient = useQueryClient();

  return useMutation<void, Error, string>({
    mutationFn: async (id: string) => propertyService.deleteProperty(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['my-properties'] });
    },
    onError: (error) => {
      console.error('Failed to delete property:', getErrorMessage(error));
    },
  });
};

// Add hooks for other property operations, following the same pattern:
// useUploadPropertyImages, useGetPropertyImages, useDeletePropertyImage, useReorderPropertyImages
// useGetPropertyAnalytics, useGetPropertyMarketData, useGetPropertyRecommendations
// useSaveSearch, useGetSavedSearches, useUpdateSavedSearch, useDeleteSavedSearch, useToggleSavedSearchActive

export const usePropertyStats = () => {
  return useQuery<any, Error>({
    queryKey: ['property-stats'],
    queryFn: () => propertyService.getPropertyStats(),
  });
};

export const useTogglePropertyFeature = () => {
  
};

export const useTogglePropertyVerification = () => {
  
};
