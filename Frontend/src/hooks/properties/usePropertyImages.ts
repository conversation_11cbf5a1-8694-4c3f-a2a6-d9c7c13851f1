import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { propertyImagesService } from '@/services/properties';
import { PropertyImage, ImageUploadRequest, ReorderImagesRequest } from '@/types/property';

// Helper to extract error message
const getErrorMessage = (error: any): string => {
  if (error instanceof Error) return error.message;
  if (error && typeof error === 'object' && 'message' in error) return (error as any).message;
  return 'An unexpected error occurred';
};

/**
 * Hook for fetching all images for a specific property.
 * @param propertyId The ID of the property.
 */
export const usePropertyImages = (propertyId: string) => {
  return useQuery<PropertyImage[], Error>({
    queryKey: ['propertyImages', propertyId],
    queryFn: () => propertyImagesService.getPropertyImages(propertyId),
    enabled: !!propertyId, // Only run if propertyId is available
  });
};

/**
 * Hook for uploading images to a property.
 */
export const useUploadPropertyImages = () => {
  const queryClient = useQueryClient();

  return useMutation<PropertyImage[], Error, { propertyId: string; request: ImageUploadRequest }>({
    mutationFn: ({ propertyId, request }) => propertyImagesService.uploadPropertyImages(propertyId, request),
    onSuccess: (_, variables) => {
      // Invalidate queries to refetch images after successful upload
      queryClient.invalidateQueries({ queryKey: ['propertyImages', variables.propertyId] });
      queryClient.invalidateQueries({ queryKey: ['property', variables.propertyId] }); // If property entity has an images array
    },
    onError: (error) => {
      console.error('Failed to upload images:', getErrorMessage(error));
    },
  });
};

/**
 * Hook for deleting a specific property image.
 */
export const useDeletePropertyImage = () => {
  const queryClient = useQueryClient();

  return useMutation<void, Error, { propertyId: string; imageId: string }>({
    mutationFn: ({ propertyId, imageId }) => propertyImagesService.deleteImage(propertyId, imageId),
    onSuccess: (_, variables) => {
      // Invalidate queries to refetch images after successful deletion
      queryClient.invalidateQueries({ queryKey: ['propertyImages', variables.propertyId] });
      queryClient.invalidateQueries({ queryKey: ['property', variables.propertyId] });
    },
    onError: (error) => {
      console.error('Failed to delete image:', getErrorMessage(error));
    },
  });
};

/**
 * Hook for reordering property images.
 */
export const useReorderPropertyImages = () => {
  const queryClient = useQueryClient();

  return useMutation<PropertyImage[], Error, { propertyId: string; request: ReorderImagesRequest }>({
    mutationFn: ({ propertyId, request }) => propertyImagesService.reorderImages(propertyId, request),
    onSuccess: (_, variables) => {
      // Invalidate queries to refetch images after successful reorder
      queryClient.invalidateQueries({ queryKey: ['propertyImages', variables.propertyId] });
      queryClient.invalidateQueries({ queryKey: ['property', variables.propertyId] });
    },
    onError: (error) => {
      console.error('Failed to reorder images:', getErrorMessage(error));
    },
  });
};