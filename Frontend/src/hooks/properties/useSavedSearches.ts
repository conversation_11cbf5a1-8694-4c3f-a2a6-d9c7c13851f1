import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { savedSearchesService } from '@/services/properties/savedSearchesService';
import { SavedSearch, CreateSavedSearchRequest, UpdateSavedSearchRequest } from '@/types/property'; // Assuming these types are defined

// Helper to extract error message
const getErrorMessage = (error: any): string => {
  if (error instanceof Error) return error.message;
  if (error && typeof error === 'object' && 'message' in error) return (error as any).message;
  return 'An unexpected error occurred';
};

/**
 * Hook for fetching all saved searches for a specific user.
 * @param userId The ID of the user.
 */
export const useSavedSearches = (userId: string) => {
  return useQuery<SavedSearch[], Error>({
    queryKey: ['savedSearches', userId],
    queryFn: () => savedSearchesService.getSavedSearches(userId),
    enabled: !!userId,
  });
};

/**
 * Hook for fetching a single saved search by ID.
 * @param id The ID of the saved search.
 */
export const useSavedSearchById = (id: string) => {
  return useQuery<SavedSearch, Error>({
    queryKey: ['savedSearch', id],
    queryFn: () => savedSearchesService.getSavedSearchById(id),
    enabled: !!id,
  });
};

/**
 * Hook for creating a new saved search.
 */
export const useCreateSavedSearch = () => {
  const queryClient = useQueryClient();

  return useMutation<SavedSearch, Error, CreateSavedSearchRequest>({
    mutationFn: (request) => savedSearchesService.createSavedSearch(request),
    onSuccess: (newSavedSearch) => {
      // Invalidate the list of saved searches for the user who created it
      queryClient.invalidateQueries({ queryKey: ['savedSearches', newSavedSearch.userId] });
    },
    onError: (error) => {
      console.error('Failed to create saved search:', getErrorMessage(error));
    },
  });
};

/**
 * Hook for updating an existing saved search.
 */
export const useUpdateSavedSearch = () => {
  const queryClient = useQueryClient();

  return useMutation<SavedSearch, Error, { id: string; request: UpdateSavedSearchRequest }>({
    mutationFn: ({ id, request }) => savedSearchesService.updateSavedSearch(id, request),
    onSuccess: (updatedSavedSearch) => {
      // Invalidate the specific saved search and the list
      queryClient.invalidateQueries({ queryKey: ['savedSearch', updatedSavedSearch.id] });
      queryClient.invalidateQueries({ queryKey: ['savedSearches', updatedSavedSearch.userId] });
    },
    onError: (error) => {
      console.error('Failed to update saved search:', getErrorMessage(error));
    },
  });
};

/**
 * Hook for deleting a saved search.
 */
export const useDeleteSavedSearch = () => {
  const queryClient = useQueryClient();

  return useMutation<void, Error, { id: string; userId: string }>({
    mutationFn: ({ id }) => savedSearchesService.deleteSavedSearch(id),
    onSuccess: (_, variables) => {
      // Invalidate the list of saved searches for the user
      queryClient.invalidateQueries({ queryKey: ['savedSearches', variables.userId] });
    },
    onError: (error) => {
      console.error('Failed to delete saved search:', getErrorMessage(error));
    },
  });
};

/**
 * Hook for toggling the active status of a saved search.
 */
export const useToggleSavedSearchActive = () => {
  const queryClient = useQueryClient();

  return useMutation<SavedSearch, Error, { id: string; isActive: boolean }>({
    mutationFn: ({ id, isActive }) => savedSearchesService.toggleSavedSearchActive(id, isActive),
    onSuccess: (updatedSavedSearch) => {
      // Invalidate the specific saved search and the list
      queryClient.invalidateQueries({ queryKey: ['savedSearch', updatedSavedSearch.id] });
      queryClient.invalidateQueries({ queryKey: ['savedSearches', updatedSavedSearch.userId] });
    },
    onError: (error) => {
      console.error('Failed to toggle saved search active status:', getErrorMessage(error));
    },
  });
};
