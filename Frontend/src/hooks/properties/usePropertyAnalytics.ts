import { useQuery } from '@tanstack/react-query';
import { propertyAnalyticsService } from '@/services/properties';
import { PropertyAnalytics, PropertyMarketData, PropertyRecommendation } from '@/types/property';

// Helper to extract error message
const getErrorMessage = (error: any): string => {
  if (error instanceof Error) return error.message;
  if (error && typeof error === 'object' && 'message' in error) return (error as any).message;
  return 'An unexpected error occurred';
};

/**
 * Hook for fetching detailed analytics for a specific property.
 * @param propertyId The ID of the property.
 */
export const usePropertyAnalytics = (propertyId: string) => {
  return useQuery<PropertyAnalytics, Error>({
    queryKey: ['propertyAnalytics', propertyId],
    queryFn: () => propertyAnalyticsService.getPropertyAnalytics(propertyId),
    enabled: !!propertyId,
  });
};

/**
 * Hook for fetching market data for a given location and optional property type.
 * @param location The location for which to fetch market data.
 * @param propertyType Optional property type to filter market data.
 */
export const usePropertyMarketData = (location: string, propertyType?: string) => {
  return useQuery<PropertyMarketData, Error>({
    queryKey: ['propertyMarketData', location, propertyType],
    queryFn: () => propertyAnalyticsService.getPropertyMarketData(location, propertyType),
    enabled: !!location, // Ensure location is provided
  });
};

/**
 * Hook for fetching property recommendations for a user.
 * @param userId The ID of the user for whom to get recommendations.
 * @param limit The maximum number of recommendations to return.
 */
export const usePropertyRecommendations = (userId: string, limit: number = 10) => {
  return useQuery<PropertyRecommendation[], Error>({
    queryKey: ['propertyRecommendations', userId, limit],
    queryFn: () => propertyAnalyticsService.getPropertyRecommendations(userId, limit),
    enabled: !!userId, // Ensure userId is provided
  });
};