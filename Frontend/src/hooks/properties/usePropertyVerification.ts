import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { propertyVerificationService } from '@/services/properties';
import {
  VerificationStep,
  SubmitVerificationStepsRequest,
  UpdateVerificationStatusRequest,
  VerificationProgress,
  ScheduleInspectionRequest,
  CompleteInspectionRequest
} from '@/types/property';

/**
 * Custom hook to fetch all verification steps for a property.
 * @param propertyId The ID of the property.
 */
export const usePropertyVerificationSteps = (propertyId: string) => {
  return useQuery<VerificationStep[]>({
    queryKey: ['verificationSteps', propertyId],
    queryFn: () => propertyVerificationService.getVerificationSteps(propertyId),
    enabled: !!propertyId, // Only run the query if propertyId exists
  });
};

/**
 * Custom hook for submitting a new verification step.
 */
export const useSubmitVerificationStep = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ propertyId, data }: { propertyId: string; data: SubmitVerificationStepsRequest }) =>
      propertyVerificationService.submitVerificationStep(propertyId, data),
    onSuccess: (newStep, { propertyId }) => {
      // Invalidate the query for this property's steps to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ['verificationSteps', propertyId] });
      // You might also want to invalidate the main property query if the verification status changes
      queryClient.invalidateQueries({ queryKey: ['property', propertyId] });
      console.log(`Verification step ${newStep.stepType} submitted successfully for property ${propertyId}`);
    },
    onError: (error) => {
      console.error('Failed to submit verification step:', error);
    },
  });
};

/**
 * Custom hook for scheduling a site inspection (Admin use).
 */
export const useScheduleInspection = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ propertyId, data }: { propertyId: string; data: ScheduleInspectionRequest }) =>
      propertyVerificationService.scheduleInspection(propertyId, data),
    onSuccess: (newStep, { propertyId }) => {
      queryClient.invalidateQueries({ queryKey: ['verificationSteps', propertyId] });
      queryClient.invalidateQueries({ queryKey: ['verificationProgress', propertyId] });
      console.log(`Inspection scheduled successfully for property ${propertyId}`);
    },
    onError: (error) => {
      console.error('Failed to schedule inspection:', error);
    },
  });
};

/**
 * Custom hook for completing a scheduled inspection (Admin use).
 */
export const useCompleteInspection = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ propertyId, stepId, data }: { propertyId: string; stepId: string; data: CompleteInspectionRequest }) =>
      propertyVerificationService.completeInspection(propertyId, stepId, data),
    onSuccess: (updatedStep, { propertyId }) => {
      queryClient.invalidateQueries({ queryKey: ['verificationSteps', propertyId] });
      queryClient.invalidateQueries({ queryKey: ['verificationProgress', propertyId] });
      queryClient.invalidateQueries({ queryKey: ['property', propertyId] });
      console.log(`Inspection for step ${updatedStep.id} completed successfully`);
    },
    onError: (error) => {
      console.error('Failed to complete inspection:', error);
    },
  });
};

/**
 * Custom hook to fetch overall verification progress for a property.
 * @param propertyId The ID of the property.
 */
export const useVerificationProgress = (propertyId: string) => {
  return useQuery<VerificationProgress>({
    queryKey: ['verificationProgress', propertyId],
    queryFn: () => propertyVerificationService.getVerificationProgress(propertyId),
    enabled: !!propertyId,
  });
};


/**
 * Custom hook for updating the status of a verification step (Admin use).
 */
export const useUpdateVerificationStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (
        {
          propertyId, stepId, data}: { propertyId: string; stepId: string; data: UpdateVerificationStatusRequest }
    ) =>
      propertyVerificationService.updateVerificationStatus(propertyId, stepId, data),
    onSuccess: (updatedStep, { propertyId }) => {
      // Invalidate the query for this property's steps to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ['verificationSteps', propertyId] });
      // Invalidate the main property query if the overall verification status changes
      queryClient.invalidateQueries({ queryKey: ['property', propertyId] });
      console.log(`Verification step status for step ${updatedStep.id} updated successfully`);
    },
    onError: (error) => {
      console.error('Failed to update verification status:', error);
    },
  });
};
