import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { sampleProperties } from '@/components/admin/seed/SampleData';
import { API_CONFIG } from '@/config/api';
import { useProperty } from '@/hooks/useProperty';

// Define Property types based on our backend API
interface Property {
  id: string;
  title: string;
  description: string;
  location: string;
  price: number;
  property_type: string;
  bedrooms: number;
  bathrooms: number;
  area_sqft: number;
  is_available: boolean;
  is_verified: boolean;
  is_featured: boolean;
  images: string[];
  created_at: string;
  updated_at: string;
}

interface PropertyInsert {
  title: string;
  description: string;
  location: string;
  price: number;
  property_type: string;
  bedrooms: number;
  bathrooms: number;
  area_sqft: number;
  is_available?: boolean;
  is_verified?: boolean;
  is_featured?: boolean;
  images?: string[];
}

export const useProperties = (filters?: {
  search?: string;
  location?: string;
  propertyType?: string;
  priceRange?: string;
  minPrice?: number;
  maxPrice?: number;
  bedrooms?: string;
  bathrooms?: string;
  isVerified?: boolean;
  isFeatured?: boolean;
}) => {
  return useQuery({
    queryKey: ['properties', filters],
    queryFn: async () => {
      try {
        // Try to fetch from real API first
        const apiFilters: any = {};

        if (filters?.search) {
          apiFilters.search = filters.search;
        }

        if (filters?.location && filters.location !== 'all') {
          apiFilters.city = filters.location;
        }

        if (filters?.propertyType && filters.propertyType !== 'all') {
          apiFilters.propertyType = filters.propertyType;
        }

        if (filters?.bedrooms && filters.bedrooms !== 'all') {
          apiFilters.bedrooms = parseInt(filters.bedrooms);
        }

        if (filters?.bathrooms && filters.bathrooms !== 'all') {
          apiFilters.bathrooms = parseInt(filters.bathrooms);
        }

        if (filters?.minPrice) {
          apiFilters.minPrice = filters.minPrice;
        }

        if (filters?.maxPrice) {
          apiFilters.maxPrice = filters.maxPrice;
        }

        if (filters?.isVerified !== undefined) {
          apiFilters.isVerified = filters.isVerified;
        }

        if (filters?.isFeatured !== undefined) {
          apiFilters.isFeatured = filters.isFeatured;
        }

        const result = await ApiService.getProperties(apiFilters);
        return result.data;
      } catch (error) {
        // Fallback to sample data in development mode
        const isDevelopment = import.meta.env.DEV;

        if (isDevelopment) {
          console.warn('API failed, falling back to sample data:', error);
          await new Promise(resolve => setTimeout(resolve, 500));
          return sampleProperties.filter(property => property.is_available);
        }

        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1, // Only retry once before falling back
  });
};

// Alias for useProperty to maintain compatibility
export const usePropertyById = (id: string) => {
  return useProperty(id);
};

// Hook for getting user's properties (landlord/agent)
export const useMyProperties = (userId?: string) => {
  return useProperties({
    // This would need to be implemented in the API
    // For now, return all properties
  });
};

// Hook for property statistics
export const usePropertyStats = (propertyId: string) => {
  return useQuery({
    queryKey: ['property-stats', propertyId],
    queryFn: async () => {
      // This would use ApiService.getPropertyAnalytics when implemented
      return {
        views: 0,
        inquiries: 0,
        applications: 0,
        conversionRate: 0,
      };
    },
    enabled: !!propertyId,
  });
};

// Property status enum
export enum PropertyStatus {
  AVAILABLE = 'available',
  RENTED = 'rented',
  MAINTENANCE = 'maintenance',
  DRAFT = 'draft',
}

// Hook for deleting property
export const useDeleteProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (propertyId: string) => {
      // This would use ApiService.deleteProperty when implemented
      console.log('Delete property:', propertyId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
  });
};

export const useCreateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (property: PropertyInsert) => {
      const response = await fetch(`${API_CONFIG.BASE_URL}/properties`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify(property),
      });

      if (!response.ok) {
        throw new Error(`Failed to create property: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
  });
};

export const useUpdateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Property> }) => {
      const response = await fetch(`${API_CONFIG.BASE_URL}/properties/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error(`Failed to update property: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
  });
};

export const usePropertyInquiry = () => {
  return useMutation({
    mutationFn: async (inquiry: {
      property_id: string;
      inquirer_name: string;
      inquirer_email: string;
      inquirer_phone?: string;
      message?: string;
      inquiry_type?: string;
    }) => {
      const response = await fetch(`${API_CONFIG.BASE_URL}/property-inquiries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify(inquiry),
      });

      if (!response.ok) {
        throw new Error(`Failed to create inquiry: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    },
  });
};
