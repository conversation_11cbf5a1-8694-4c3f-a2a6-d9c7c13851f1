import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ApiService, ApiApplication } from '@/services/apiService';
import { useToast } from '@/hooks/use-toast';

// Hook for fetching applications with filters
export const useApplications = (
  filters?: {
    page?: number;
    limit?: number;
    status?: string;
    propertyId?: string;
    tenantId?: string;
  } = {}
) => {
  return useQuery({
    queryKey: ['applications', filters],
    queryFn: async () => {
      const result = await ApiService.getApplications(filters);
      return result;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
};

// Hook for fetching a single application
export const useApplication = (id: string) => {
  return useQuery({
    queryKey: ['application', id],
    queryFn: async (): Promise<ApiApplication> => {
      return await ApiService.getApplication(id);
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Hook for updating application status
export const useUpdateApplicationStatus = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      id,
      status,
      adminNotes,
    }: {
      id: string;
      status: string;
      adminNotes?: string;
    }) => {
      return await ApiService.updateApplicationStatus(id, status, adminNotes);
    },
    onSuccess: updatedApplication => {
      // Invalidate and refetch applications
      queryClient.invalidateQueries({ queryKey: ['applications'] });
      queryClient.invalidateQueries({ queryKey: ['application', updatedApplication.id] });

      toast({
        title: 'Success',
        description: 'Application status updated successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update application status',
        variant: 'destructive',
      });
    },
  });
};

// Hook for application statistics
export const useApplicationStats = () => {
  return useQuery({
    queryKey: ['application-stats'],
    queryFn: async () => {
      const stats = await ApiService.getStats();
      return {
        total: stats.totalApplications,
        pending: stats.pendingApplications,
        approved: stats.approvedApplications,
        rejected: stats.rejectedApplications,
        // Calculate additional metrics
        approvalRate:
          stats.totalApplications > 0
            ? (stats.approvedApplications / stats.totalApplications) * 100
            : 0,
        rejectionRate:
          stats.totalApplications > 0
            ? (stats.rejectedApplications / stats.totalApplications) * 100
            : 0,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Hook for application analytics data
export const useApplicationAnalytics = () => {
  return useQuery({
    queryKey: ['application-analytics'],
    queryFn: async () => {
      const stats = await ApiService.getStats();

      // Generate mock analytics data based on real stats
      const monthlyData = Array.from({ length: 12 }, (_, i) => {
        const month = new Date();
        month.setMonth(month.getMonth() - (11 - i));
        const monthName = month.toLocaleDateString('en-US', { month: 'short' });

        // Simulate monthly distribution
        const factor = 0.7 + Math.random() * 0.6; // Random factor between 0.7 and 1.3
        const monthlyTotal = Math.floor((stats.totalApplications / 12) * factor);

        return {
          month: monthName,
          applications: monthlyTotal,
          approved: Math.floor(monthlyTotal * 0.6),
          rejected: Math.floor(monthlyTotal * 0.25),
          pending: Math.floor(monthlyTotal * 0.15),
        };
      });

      const statusDistribution = [
        { name: 'Approved', value: stats.approvedApplications, color: '#10B981' },
        { name: 'Pending', value: stats.pendingApplications, color: '#F59E0B' },
        { name: 'Rejected', value: stats.rejectedApplications, color: '#EF4444' },
      ];

      return {
        monthlyData,
        statusDistribution,
        totalApplications: stats.totalApplications,
        conversionRate:
          stats.totalApplications > 0
            ? (stats.approvedApplications / stats.totalApplications) * 100
            : 0,
      };
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};
