// =====================================================
// DATA SYNC HOOK
// React hook for backup and synchronization
// =====================================================

import { useState, useCallback, useEffect } from 'react';
import { backupService, BackupConfig, BackupJob, SyncConfig, SyncJob, OfflineData, DataRecoveryPoint } from '@/services/backupService';

export interface UseDataSyncProps {
  syncConfig?: SyncConfig;
  autoSync?: boolean;
  offlineSupport?: boolean;
}

export interface UseDataSyncReturn {
  // State
  backupConfigs: BackupConfig[];
  syncConfigs: SyncConfig[];
  backupJobs: BackupJob[];
  syncJobs: SyncJob[];
  isOnline: boolean;
  isSyncing: boolean;
  isBackingUp: boolean;
  offlineQueueStatus: { total: number; pending: number; synced: number };
  error: string | null;

  // Backup Actions
  createBackup: (configId: string, immediate?: boolean) => Promise<string | null>;
  restoreFromBackup: (backupId: string, tables?: string[]) => Promise<boolean>;
  getBackupJobs: (limit?: number) => Promise<BackupJob[]>;
  createRecoveryPoint: (name: string, description: string, tables: string[]) => Promise<string | null>;
  
  // Sync Actions
  syncData: (configId?: string) => Promise<boolean>;
  addOfflineOperation: (table: string, operation: 'insert' | 'update' | 'delete', data: Record<string, any>) => Promise<void>;
  forceSyncOfflineData: () => Promise<boolean>;
  
  // Configuration
  getBackupConfigs: () => BackupConfig[];
  getSyncConfigs: () => SyncConfig[];
  
  // Utilities
  checkOnlineStatus: () => boolean;
  getStorageUsage: () => Promise<{ used: number; available: number; total: number }>;
  validateBackupIntegrity: (backupId: string) => Promise<boolean>;
  scheduleBackup: (configId: string, schedule: any) => Promise<boolean>;
  cancelBackup: (jobId: string) => Promise<boolean>;
}

export const useDataSync = (props: UseDataSyncProps = {}): UseDataSyncReturn => {
  const { syncConfig, autoSync = true, offlineSupport = true } = props;

  // State
  const [backupConfigs, setBackupConfigs] = useState<BackupConfig[]>([]);
  const [syncConfigs, setSyncConfigs] = useState<SyncConfig[]>([]);
  const [backupJobs, setBackupJobs] = useState<BackupJob[]>([]);
  const [syncJobs, setSyncJobs] = useState<SyncJob[]>([]);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isSyncing, setIsSyncing] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [offlineQueueStatus, setOfflineQueueStatus] = useState({ total: 0, pending: 0, synced: 0 });
  const [error, setError] = useState<string | null>(null);

  // Create backup
  const createBackup = useCallback(async (configId: string, immediate: boolean = false): Promise<string | null> => {
    try {
      setIsBackingUp(true);
      setError(null);

      const jobId = await backupService.createBackup(configId, immediate);
      
      if (jobId) {
        // Refresh backup jobs
        const jobs = await backupService.getBackupJobs(50);
        setBackupJobs(jobs);
      }

      return jobId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Backup creation failed';
      setError(errorMessage);
      return null;
    } finally {
      setIsBackingUp(false);
    }
  }, []);

  // Restore from backup
  const restoreFromBackup = useCallback(async (backupId: string, tables?: string[]): Promise<boolean> => {
    try {
      setError(null);
      return await backupService.restoreFromBackup(backupId, tables);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Restore failed';
      setError(errorMessage);
      return false;
    }
  }, []);

  // Get backup jobs
  const getBackupJobs = useCallback(async (limit: number = 50): Promise<BackupJob[]> => {
    try {
      setError(null);
      const jobs = await backupService.getBackupJobs(limit);
      setBackupJobs(jobs);
      return jobs;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get backup jobs';
      setError(errorMessage);
      return [];
    }
  }, []);

  // Create recovery point
  const createRecoveryPoint = useCallback(async (
    name: string, 
    description: string, 
    tables: string[]
  ): Promise<string | null> => {
    try {
      setError(null);
      return await backupService.createRecoveryPoint(name, description, tables);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create recovery point';
      setError(errorMessage);
      return null;
    }
  }, []);

  // Sync data
  const syncData = useCallback(async (configId?: string): Promise<boolean> => {
    try {
      setIsSyncing(true);
      setError(null);

      // If no configId provided, sync all active configs
      const configsToSync = configId 
        ? syncConfigs.filter(config => config.id === configId)
        : syncConfigs.filter(config => config.isActive);

      let allSuccessful = true;

      for (const config of configsToSync) {
        try {
          // Implementation would call actual sync service
          // For now, we'll simulate sync
          await new Promise(resolve => setTimeout(resolve, 1000));
          console.log(`Syncing config: ${config.name}`);
        } catch (err) {
          console.error(`Sync failed for config ${config.name}:`, err);
          allSuccessful = false;
        }
      }

      return allSuccessful;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Sync failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsSyncing(false);
    }
  }, [syncConfigs]);

  // Add offline operation
  const addOfflineOperation = useCallback(async (
    table: string, 
    operation: 'insert' | 'update' | 'delete', 
    data: Record<string, any>
  ): Promise<void> => {
    try {
      setError(null);
      
      // Get current user ID (would come from auth context)
      const userId = 'current-user-id'; // This would be from useAuth hook
      
      await backupService.addOfflineOperation(table, operation, data, userId);
      
      // Update offline queue status
      const status = backupService.getOfflineQueueStatus();
      setOfflineQueueStatus(status);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add offline operation';
      setError(errorMessage);
    }
  }, []);

  // Force sync offline data
  const forceSyncOfflineData = useCallback(async (): Promise<boolean> => {
    try {
      setIsSyncing(true);
      setError(null);

      // This would trigger the sync of offline data
      // Implementation would be in the backup service
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate sync

      // Update offline queue status
      const status = backupService.getOfflineQueueStatus();
      setOfflineQueueStatus(status);

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Offline sync failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsSyncing(false);
    }
  }, []);

  // Get backup configurations
  const getBackupConfigs = useCallback((): BackupConfig[] => {
    return backupService.getBackupConfigs();
  }, []);

  // Get sync configurations
  const getSyncConfigs = useCallback((): SyncConfig[] => {
    return backupService.getSyncConfigs();
  }, []);

  // Check online status
  const checkOnlineStatus = useCallback((): boolean => {
    const online = navigator.onLine;
    setIsOnline(online);
    return online;
  }, []);

  // Get storage usage
  const getStorageUsage = useCallback(async (): Promise<{ used: number; available: number; total: number }> => {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        const used = estimate.usage || 0;
        const total = estimate.quota || 0;
        const available = total - used;

        return { used, available, total };
      }

      // Fallback for browsers that don't support storage estimation
      return { used: 0, available: 0, total: 0 };
    } catch (err) {
      console.error('Failed to get storage usage:', err);
      return { used: 0, available: 0, total: 0 };
    }
  }, []);

  // Validate backup integrity
  const validateBackupIntegrity = useCallback(async (backupId: string): Promise<boolean> => {
    try {
      setError(null);
      
      // Implementation would validate backup checksums and data integrity
      // For now, simulate validation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Backup validation failed';
      setError(errorMessage);
      return false;
    }
  }, []);

  // Schedule backup
  const scheduleBackup = useCallback(async (configId: string, schedule: any): Promise<boolean> => {
    try {
      setError(null);
      
      // Implementation would schedule backup using cron or similar
      console.log(`Scheduling backup for config ${configId}:`, schedule);
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to schedule backup';
      setError(errorMessage);
      return false;
    }
  }, []);

  // Cancel backup
  const cancelBackup = useCallback(async (jobId: string): Promise<boolean> => {
    try {
      setError(null);
      
      // Implementation would cancel running backup job
      console.log(`Cancelling backup job: ${jobId}`);
      
      // Update backup jobs list
      const jobs = await backupService.getBackupJobs(50);
      setBackupJobs(jobs);
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel backup';
      setError(errorMessage);
      return false;
    }
  }, []);

  // Initialize configurations
  useEffect(() => {
    const loadConfigs = async () => {
      try {
        const backupConfigs = backupService.getBackupConfigs();
        const syncConfigs = backupService.getSyncConfigs();
        
        setBackupConfigs(backupConfigs);
        setSyncConfigs(syncConfigs);

        // Load recent backup jobs
        const jobs = await backupService.getBackupJobs(20);
        setBackupJobs(jobs);

        // Get offline queue status
        const status = backupService.getOfflineQueueStatus();
        setOfflineQueueStatus(status);
      } catch (err) {
        console.error('Failed to load configurations:', err);
      }
    };

    loadConfigs();
  }, []);

  // Set up online/offline event listeners
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (autoSync) {
        forceSyncOfflineData();
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [autoSync, forceSyncOfflineData]);

  // Auto-sync on mount if online
  useEffect(() => {
    if (autoSync && isOnline && syncConfigs.length > 0) {
      syncData();
    }
  }, [autoSync, isOnline, syncConfigs, syncData]);

  // Periodic sync
  useEffect(() => {
    if (!autoSync) return;

    const interval = setInterval(() => {
      if (isOnline && !isSyncing) {
        syncData();
      }
    }, 5 * 60 * 1000); // Sync every 5 minutes

    return () => clearInterval(interval);
  }, [autoSync, isOnline, isSyncing, syncData]);

  // Update offline queue status periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const status = backupService.getOfflineQueueStatus();
      setOfflineQueueStatus(status);
    }, 30 * 1000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return {
    // State
    backupConfigs,
    syncConfigs,
    backupJobs,
    syncJobs,
    isOnline,
    isSyncing,
    isBackingUp,
    offlineQueueStatus,
    error,

    // Backup Actions
    createBackup,
    restoreFromBackup,
    getBackupJobs,
    createRecoveryPoint,

    // Sync Actions
    syncData,
    addOfflineOperation,
    forceSyncOfflineData,

    // Configuration
    getBackupConfigs,
    getSyncConfigs,

    // Utilities
    checkOnlineStatus,
    getStorageUsage,
    validateBackupIntegrity,
    scheduleBackup,
    cancelBackup
  };
};

export default useDataSync;
