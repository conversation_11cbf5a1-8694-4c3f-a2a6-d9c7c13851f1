// =====================================================
// GOOGLE MAPS HOOK
// React hook for Google Maps integration
// =====================================================

import { useState, useEffect, useRef, useCallback } from 'react';
import { googleMapsService, Location, PropertyLocation, Amenity, RouteInfo } from '@/services/mapsService';

export interface UseGoogleMapsProps {
  propertyLocation?: Location;
  properties?: PropertyLocation[];
  autoInitialize?: boolean;
}

export interface UseGoogleMapsReturn {
  // Map state
  map: google.maps.Map | null;
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;

  // Location data
  currentLocation: Location | null;
  nearbyAmenities: Amenity[];
  routeInfo: RouteInfo | null;

  // Actions
  initializeMap: (container: HTMLElement, properties?: PropertyLocation[]) => Promise<boolean>;
  geocodeAddress: (address: string) => Promise<Location | null>;
  reverseGeocode: (lat: number, lng: number) => Promise<string | null>;
  findNearbyAmenities: (location: Location, types?: string[], radius?: number) => Promise<Amenity[]>;
  calculateRoute: (origin: Location, destination: Location) => Promise<RouteInfo | null>;
  getCurrentLocation: () => Promise<Location | null>;
  addPropertyMarker: (property: PropertyLocation) => void;
  removePropertyMarker: (propertyId: string) => void;
  fitMapToProperties: (properties: PropertyLocation[]) => void;
  
  // Utilities
  calculateDistance: (point1: Location, point2: Location) => number;
  isLocationInPortHarcourt: (location: Location) => boolean;
}

export const useGoogleMaps = (props: UseGoogleMapsProps = {}): UseGoogleMapsReturn => {
  const { propertyLocation, properties = [], autoInitialize = true } = props;

  // State
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentLocation, setCurrentLocation] = useState<Location | null>(propertyLocation || null);
  const [nearbyAmenities, setNearbyAmenities] = useState<Amenity[]>([]);
  const [routeInfo, setRouteInfo] = useState<RouteInfo | null>(null);

  // Refs
  const markersRef = useRef<Map<string, google.maps.Marker>>(new Map());
  const infoWindowRef = useRef<google.maps.InfoWindow | null>(null);

  // Initialize Google Maps
  const initializeMap = useCallback(async (container: HTMLElement, mapProperties?: PropertyLocation[]): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const success = await googleMapsService.initialize();
      if (!success) {
        throw new Error('Failed to initialize Google Maps');
      }

      const propertiesToShow = mapProperties || properties;
      const mapInstance = await googleMapsService.createPropertyMap(container, propertiesToShow);
      
      if (mapInstance) {
        setMap(mapInstance);
        setIsLoaded(true);
        
        // Add click listener for map
        mapInstance.addListener('click', (event: google.maps.MapMouseEvent) => {
          if (event.latLng) {
            const location: Location = {
              lat: event.latLng.lat(),
              lng: event.latLng.lng()
            };
            setCurrentLocation(location);
          }
        });

        return true;
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [properties]);

  // Geocode address
  const geocodeAddress = useCallback(async (address: string): Promise<Location | null> => {
    try {
      setError(null);
      const location = await googleMapsService.geocodeAddress(address);
      
      if (location) {
        setCurrentLocation(location);
      }
      
      return location;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Geocoding failed';
      setError(errorMessage);
      return null;
    }
  }, []);

  // Reverse geocode
  const reverseGeocode = useCallback(async (lat: number, lng: number): Promise<string | null> => {
    try {
      setError(null);
      return await googleMapsService.reverseGeocode(lat, lng);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Reverse geocoding failed';
      setError(errorMessage);
      return null;
    }
  }, []);

  // Find nearby amenities
  const findNearbyAmenities = useCallback(async (
    location: Location, 
    types: string[] = ['school', 'hospital', 'shopping_mall', 'restaurant', 'bank'], 
    radius: number = 5000
  ): Promise<Amenity[]> => {
    try {
      setError(null);
      const amenities = await googleMapsService.findNearbyAmenities(location, types, radius);
      setNearbyAmenities(amenities);
      return amenities;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to find amenities';
      setError(errorMessage);
      return [];
    }
  }, []);

  // Calculate route
  const calculateRoute = useCallback(async (origin: Location, destination: Location): Promise<RouteInfo | null> => {
    try {
      setError(null);
      const route = await googleMapsService.getRoute(origin, destination);
      setRouteInfo(route);
      return route;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Route calculation failed';
      setError(errorMessage);
      return null;
    }
  }, []);

  // Get current location
  const getCurrentLocation = useCallback((): Promise<Location | null> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location: Location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          setCurrentLocation(location);
          resolve(location);
        },
        (error) => {
          let errorMessage = 'Failed to get current location';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Location access denied by user';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information unavailable';
              break;
            case error.TIMEOUT:
              errorMessage = 'Location request timed out';
              break;
          }
          setError(errorMessage);
          reject(new Error(errorMessage));
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }, []);

  // Add property marker
  const addPropertyMarker = useCallback((property: PropertyLocation) => {
    if (!map) return;

    const marker = new google.maps.Marker({
      position: { lat: property.lat, lng: property.lng },
      map: map,
      title: property.title,
      animation: google.maps.Animation.DROP
    });

    // Add click listener
    marker.addListener('click', () => {
      if (!infoWindowRef.current) {
        infoWindowRef.current = new google.maps.InfoWindow();
      }

      const content = `
        <div style="max-width: 250px;">
          <h3 style="margin: 0 0 8px 0;">${property.title}</h3>
          <p style="margin: 0 0 4px 0; color: #059669; font-weight: 600;">₦${property.price.toLocaleString()}</p>
          <p style="margin: 0 0 8px 0; color: #6b7280;">${property.bedrooms || 0} bed • ${property.bathrooms || 0} bath</p>
          <button onclick="window.open('/properties/${property.propertyId}', '_blank')" 
                  style="background: #f97316; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">
            View Details
          </button>
        </div>
      `;

      infoWindowRef.current.setContent(content);
      infoWindowRef.current.open(map, marker);
    });

    markersRef.current.set(property.propertyId, marker);
  }, [map]);

  // Remove property marker
  const removePropertyMarker = useCallback((propertyId: string) => {
    const marker = markersRef.current.get(propertyId);
    if (marker) {
      marker.setMap(null);
      markersRef.current.delete(propertyId);
    }
  }, []);

  // Fit map to properties
  const fitMapToProperties = useCallback((propertiesToFit: PropertyLocation[]) => {
    if (!map || propertiesToFit.length === 0) return;

    const bounds = new google.maps.LatLngBounds();
    
    propertiesToFit.forEach(property => {
      bounds.extend(new google.maps.LatLng(property.lat, property.lng));
    });

    map.fitBounds(bounds);

    // Ensure minimum zoom level
    const listener = google.maps.event.addListener(map, 'idle', () => {
      if (map.getZoom()! > 15) {
        map.setZoom(15);
      }
      google.maps.event.removeListener(listener);
    });
  }, [map]);

  // Calculate distance between two points
  const calculateDistance = useCallback((point1: Location, point2: Location): number => {
    return googleMapsService.calculateDistance(point1, point2);
  }, []);

  // Check if location is in Port Harcourt
  const isLocationInPortHarcourt = useCallback((location: Location): boolean => {
    // Port Harcourt approximate bounds
    const phBounds = {
      north: 4.9,
      south: 4.7,
      east: 7.2,
      west: 6.9
    };

    return (
      location.lat >= phBounds.south &&
      location.lat <= phBounds.north &&
      location.lng >= phBounds.west &&
      location.lng <= phBounds.east
    );
  }, []);

  // Auto-initialize if enabled
  useEffect(() => {
    if (autoInitialize && !isLoaded && !isLoading) {
      googleMapsService.initialize().then((success) => {
        setIsLoaded(success);
        if (!success) {
          setError('Failed to auto-initialize Google Maps');
        }
      });
    }
  }, [autoInitialize, isLoaded, isLoading]);

  // Load nearby amenities when current location changes
  useEffect(() => {
    if (currentLocation && isLoaded) {
      findNearbyAmenities(currentLocation);
    }
  }, [currentLocation, isLoaded, findNearbyAmenities]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear markers
      markersRef.current.forEach(marker => marker.setMap(null));
      markersRef.current.clear();
      
      // Clear info window
      if (infoWindowRef.current) {
        infoWindowRef.current.close();
      }
    };
  }, []);

  return {
    // State
    map,
    isLoaded,
    isLoading,
    error,
    currentLocation,
    nearbyAmenities,
    routeInfo,

    // Actions
    initializeMap,
    geocodeAddress,
    reverseGeocode,
    findNearbyAmenities,
    calculateRoute,
    getCurrentLocation,
    addPropertyMarker,
    removePropertyMarker,
    fitMapToProperties,

    // Utilities
    calculateDistance,
    isLocationInPortHarcourt
  };
};

export default useGoogleMaps;
