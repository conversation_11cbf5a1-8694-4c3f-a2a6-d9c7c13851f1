// =====================================================
// DOCUMENT GENERATION HOOK
// React hook for document generation and management
// =====================================================

import { useState, useCallback, useEffect } from 'react';
import { documentService, DocumentTemplate, GeneratedDocument, DocumentData, DocumentSignature, ComplianceCheck } from '@/services/documentService';

export interface UseDocumentGenerationProps {
  templateId?: string;
  autoLoadTemplates?: boolean;
}

export interface UseDocumentGenerationReturn {
  // State
  templates: DocumentTemplate[];
  currentTemplate: DocumentTemplate | null;
  generatedDocument: GeneratedDocument | null;
  isGenerating: boolean;
  isLoading: boolean;
  error: string | null;
  validationErrors: string[];
  complianceResults: ComplianceCheck[];

  // Actions
  loadTemplates: (category?: string) => Promise<DocumentTemplate[]>;
  selectTemplate: (templateId: string) => DocumentTemplate | null;
  generateDocument: (documentData: DocumentData) => Promise<GeneratedDocument | null>;
  validateDocumentData: (templateId: string, variables: Record<string, any>) => string[];
  downloadDocument: (documentId: string, format: 'pdf' | 'html') => Promise<boolean>;
  createSignatureRequest: (documentId: string, signers: Omit<DocumentSignature, 'id' | 'documentId' | 'status'>[]) => Promise<boolean>;
  updateDocumentStatus: (documentId: string, status: GeneratedDocument['status']) => Promise<boolean>;
  getDocument: (documentId: string) => Promise<GeneratedDocument | null>;
  createDocumentVersion: (documentId: string, content: string, changes: string[]) => Promise<boolean>;
  
  // Utilities
  previewDocument: (templateId: string, variables: Record<string, any>) => string;
  getTemplateVariables: (templateId: string) => any[];
  checkCompliance: (templateId: string, documentData: DocumentData) => ComplianceCheck[];
  exportDocumentData: (documentId: string) => Promise<any>;
}

export const useDocumentGeneration = (props: UseDocumentGenerationProps = {}): UseDocumentGenerationReturn => {
  const { templateId, autoLoadTemplates = true } = props;

  // State
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [currentTemplate, setCurrentTemplate] = useState<DocumentTemplate | null>(null);
  const [generatedDocument, setGeneratedDocument] = useState<GeneratedDocument | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [complianceResults, setComplianceResults] = useState<ComplianceCheck[]>([]);

  // Load templates
  const loadTemplates = useCallback(async (category?: string): Promise<DocumentTemplate[]> => {
    try {
      setIsLoading(true);
      setError(null);

      const loadedTemplates = await documentService.getTemplates(category);
      setTemplates(loadedTemplates);
      
      return loadedTemplates;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load templates';
      setError(errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Select template
  const selectTemplate = useCallback((templateId: string): DocumentTemplate | null => {
    try {
      setError(null);
      const template = documentService.getTemplate(templateId);
      setCurrentTemplate(template);
      return template;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to select template';
      setError(errorMessage);
      return null;
    }
  }, []);

  // Generate document
  const generateDocument = useCallback(async (documentData: DocumentData): Promise<GeneratedDocument | null> => {
    try {
      setIsGenerating(true);
      setError(null);
      setValidationErrors([]);

      // Validate data first
      const template = documentService.getTemplate(documentData.templateId);
      if (!template) {
        throw new Error('Template not found');
      }

      // Run validation
      const errors = validateDocumentData(documentData.templateId, documentData.variables);
      if (errors.length > 0) {
        setValidationErrors(errors);
        throw new Error(`Validation failed: ${errors.join(', ')}`);
      }

      // Generate document
      const document = await documentService.generateDocument(documentData);
      if (!document) {
        throw new Error('Document generation failed');
      }

      setGeneratedDocument(document);
      
      // Extract compliance results from metadata
      if (document.metadata?.complianceResults) {
        setComplianceResults(document.metadata.complianceResults);
      }

      return document;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Document generation failed';
      setError(errorMessage);
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  // Validate document data
  const validateDocumentData = useCallback((templateId: string, variables: Record<string, any>): string[] => {
    try {
      const template = documentService.getTemplate(templateId);
      if (!template) {
        return ['Template not found'];
      }

      const errors: string[] = [];

      template.variables.forEach(variable => {
        const value = variables[variable.name];

        // Check required fields
        if (variable.required && (value === null || value === undefined || value === '')) {
          errors.push(`${variable.label} is required`);
          return;
        }

        // Skip validation if value is empty and not required
        if (!value && !variable.required) return;

        // Type validation
        switch (variable.type) {
          case 'number':
            if (isNaN(Number(value))) {
              errors.push(`${variable.label} must be a number`);
            }
            break;
          case 'date':
            if (!(value instanceof Date) && isNaN(Date.parse(value))) {
              errors.push(`${variable.label} must be a valid date`);
            }
            break;
          case 'select':
            if (variable.options && !variable.options.includes(value)) {
              errors.push(`${variable.label} must be one of: ${variable.options.join(', ')}`);
            }
            break;
        }

        // Custom validation
        if (variable.validation) {
          const validation = variable.validation;
          
          if (validation.min !== undefined && Number(value) < validation.min) {
            errors.push(`${variable.label} must be at least ${validation.min}`);
          }
          
          if (validation.max !== undefined && Number(value) > validation.max) {
            errors.push(`${variable.label} must be at most ${validation.max}`);
          }
          
          if (validation.pattern && !new RegExp(validation.pattern).test(String(value))) {
            errors.push(validation.message || `${variable.label} format is invalid`);
          }
        }
      });

      setValidationErrors(errors);
      return errors;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Validation failed';
      setError(errorMessage);
      return [errorMessage];
    }
  }, []);

  // Download document
  const downloadDocument = useCallback(async (documentId: string, format: 'pdf' | 'html' = 'pdf'): Promise<boolean> => {
    try {
      setError(null);

      const document = await documentService.getDocument(documentId);
      if (!document) {
        throw new Error('Document not found');
      }

      if (format === 'pdf' && document.pdfUrl) {
        // Download PDF
        const link = document.createElement('a');
        link.href = document.pdfUrl;
        link.download = `${document.title}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        return true;
      } else if (format === 'html') {
        // Download HTML
        const blob = new Blob([document.content], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${document.title}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        return true;
      }

      throw new Error('Invalid format or PDF not available');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Download failed';
      setError(errorMessage);
      return false;
    }
  }, []);

  // Create signature request
  const createSignatureRequest = useCallback(async (
    documentId: string, 
    signers: Omit<DocumentSignature, 'id' | 'documentId' | 'status'>[]
  ): Promise<boolean> => {
    try {
      setError(null);
      return await documentService.createSignatureRequest(documentId, signers);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create signature request';
      setError(errorMessage);
      return false;
    }
  }, []);

  // Update document status
  const updateDocumentStatus = useCallback(async (
    documentId: string, 
    status: GeneratedDocument['status']
  ): Promise<boolean> => {
    try {
      setError(null);
      const success = await documentService.updateDocumentStatus(documentId, status);
      
      if (success && generatedDocument?.id === documentId) {
        setGeneratedDocument(prev => prev ? { ...prev, status } : null);
      }
      
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update document status';
      setError(errorMessage);
      return false;
    }
  }, [generatedDocument]);

  // Get document
  const getDocument = useCallback(async (documentId: string): Promise<GeneratedDocument | null> => {
    try {
      setError(null);
      const document = await documentService.getDocument(documentId);
      
      if (document) {
        setGeneratedDocument(document);
      }
      
      return document;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get document';
      setError(errorMessage);
      return null;
    }
  }, []);

  // Create document version
  const createDocumentVersion = useCallback(async (
    documentId: string, 
    content: string, 
    changes: string[]
  ): Promise<boolean> => {
    try {
      setError(null);
      // Note: createdBy would come from auth context in real implementation
      return await documentService.createDocumentVersion(documentId, content, changes, 'current-user-id');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create document version';
      setError(errorMessage);
      return false;
    }
  }, []);

  // Preview document
  const previewDocument = useCallback((templateId: string, variables: Record<string, any>): string => {
    try {
      const template = documentService.getTemplate(templateId);
      if (!template) {
        return 'Template not found';
      }

      // Simple template processing for preview
      let preview = template.template;
      
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        preview = preview.replace(regex, String(value || `[${key}]`));
      });

      // Replace date placeholders
      preview = preview.replace(/{{current_date}}/g, new Date().toLocaleDateString('en-NG'));
      preview = preview.replace(/{{current_year}}/g, new Date().getFullYear().toString());

      return preview;
    } catch (err) {
      return 'Preview generation failed';
    }
  }, []);

  // Get template variables
  const getTemplateVariables = useCallback((templateId: string): any[] => {
    try {
      const template = documentService.getTemplate(templateId);
      return template?.variables || [];
    } catch (err) {
      setError('Failed to get template variables');
      return [];
    }
  }, []);

  // Check compliance
  const checkCompliance = useCallback((templateId: string, documentData: DocumentData): ComplianceCheck[] => {
    try {
      // This would integrate with the compliance checking system
      // For now, return empty array
      return [];
    } catch (err) {
      setError('Compliance check failed');
      return [];
    }
  }, []);

  // Export document data
  const exportDocumentData = useCallback(async (documentId: string): Promise<any> => {
    try {
      setError(null);
      const document = await documentService.getDocument(documentId);
      
      if (!document) {
        throw new Error('Document not found');
      }

      const exportData = {
        document: {
          id: document.id,
          title: document.title,
          status: document.status,
          version: document.version,
          createdAt: document.createdAt,
          updatedAt: document.updatedAt
        },
        template: currentTemplate,
        content: document.content,
        metadata: document.metadata,
        signatures: document.signatures
      };

      // Download as JSON
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${document.title}_export.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      return exportData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Export failed';
      setError(errorMessage);
      return null;
    }
  }, [currentTemplate]);

  // Auto-load templates on mount
  useEffect(() => {
    if (autoLoadTemplates) {
      loadTemplates();
    }
  }, [autoLoadTemplates, loadTemplates]);

  // Auto-select template if provided
  useEffect(() => {
    if (templateId && templates.length > 0) {
      selectTemplate(templateId);
    }
  }, [templateId, templates, selectTemplate]);

  return {
    // State
    templates,
    currentTemplate,
    generatedDocument,
    isGenerating,
    isLoading,
    error,
    validationErrors,
    complianceResults,

    // Actions
    loadTemplates,
    selectTemplate,
    generateDocument,
    validateDocumentData,
    downloadDocument,
    createSignatureRequest,
    updateDocumentStatus,
    getDocument,
    createDocumentVersion,

    // Utilities
    previewDocument,
    getTemplateVariables,
    checkCompliance,
    exportDocumentData
  };
};

export default useDocumentGeneration;
