import { useQuery } from '@tanstack/react-query';
import { ApiService, ApiProperty } from '@/services/apiService';
import { sampleProperties } from '@/components/admin/seed/SampleData';

type Property = ApiProperty;

export const useProperty = (id: string) => {
  return useQuery({
    queryKey: ['property', id],
    queryFn: async () => {
      try {
        // Try to fetch from real API first
        const property = await ApiService.getProperty(id);
        return property;
      } catch (error) {
        // Fallback to sample data in development mode
        const isDevelopment = import.meta.env.DEV;

        if (isDevelopment) {
          console.warn('API failed, falling back to sample data:', error);
          await new Promise(resolve => setTimeout(resolve, 300)); // Simulate loading
          const property = sampleProperties.find(p => p.id === id);

          if (!property) {
            throw new Error('Property not found in sample data');
          }

          // Transform sample data to match API format
          return {
            ...property,
            propertyType: property.type || 'apartment',
            pricePerYear: (property.price * 12).toString(),
            pricePerMonth: property.price.toString(),
            securityDeposit: property.price.toString(),
            sizeInSqm: property.area?.toString() || '0',
            furnishingStatus: 'furnished',
            amenities: property.amenities || [],
            latitude: '4.8156',
            longitude: '7.0498',
            isFeatured: property.featured || false,
            isVerified: property.is_verified || false,
            viewsCount: 0,
            inquiriesCount: 0,
            landlordId: property.landlord_id,
            createdAt: property.created_at,
            updatedAt: property.updated_at,
          } as ApiProperty;
        }

        throw error;
      }
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1, // Only retry once before falling back
  });
};
