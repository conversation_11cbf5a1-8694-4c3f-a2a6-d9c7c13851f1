import { useQuery } from '@tanstack/react-query';
import { ApiService } from '@/services/apiService';
import { useAuth } from '@/hooks/useAuth';

interface DashboardStats {
  user_id: string;
  saved_properties_count: number;
  pending_applications_count: number;
  total_applications_count: number;
  unread_messages_count: number;
  total_messages_count: number;
  active_searches_count: number;
}

export const useDashboardStats = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['dashboard-stats', user?.id],
    queryFn: async (): Promise<DashboardStats | null> => {
      if (!user) return null;

      try {
        // Try to get user-specific stats from API
        if (user.role === 'tenant') {
          const applications = await ApiService.getApplications({
            tenantId: user.id,
            limit: 1000,
          });

          return {
            user_id: user.id,
            saved_properties_count: 0, // Would need saved properties endpoint
            pending_applications_count: applications.data.filter(a => a.status === 'pending')
              .length,
            total_applications_count: applications.meta.total,
            unread_messages_count: 0, // Would need messages endpoint
            total_messages_count: 0, // Would need messages endpoint
            active_searches_count: 0, // Would need saved searches endpoint
          };
        }

        // For other roles, return basic stats
        return {
          user_id: user.id,
          saved_properties_count: 0,
          pending_applications_count: 0,
          total_applications_count: 0,
          unread_messages_count: 0,
          total_messages_count: 0,
          active_searches_count: 0,
        };
      } catch (error) {
        console.warn('Failed to fetch dashboard stats from API, using fallback:', error);
        return {
          user_id: user.id,
          saved_properties_count: 0,
          pending_applications_count: 0,
          total_applications_count: 0,
          unread_messages_count: 0,
          total_messages_count: 0,
          active_searches_count: 0,
        };
      }
    },
    enabled: !!user,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchInterval: 1000 * 60 * 5, // Refetch every 5 minutes
  });
};
