import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { dashboardService } from '@/services/dashboardService';
import { performanceMetricsService } from '@/services/performanceMetricsService';
import { marketTrendAnalysisService } from '@/services/marketTrendAnalysisService';
import { reportGenerationService } from '@/services/reportGenerationService';
import {
  DashboardMetrics,
  CustomDashboard,
  AlertRule,
  AgentPerformanceMetrics,
  PropertyPerformanceMetrics,
  MarketPerformanceMetrics,
  PriceTrendAnalysis,
  DemandPatternAnalysis,
  SeasonalTrendAnalysis,
  ReportConfig,
  GeneratedReport,
  ExportOptions
} from '@/types/analytics';

/**
 * Hook for dashboard metrics and real-time analytics
 */
export const useDashboardMetrics = (
  timeRange: { start: string; end: string },
  filters?: {
    locations?: string[];
    property_types?: string[];
    agents?: string[];
  }
) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);

  const {
    data: metrics,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['dashboard-metrics', timeRange, filters],
    queryFn: () => dashboardService.getDashboardMetrics(timeRange, filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: realTimeEnabled ? 30000 : false, // 30 seconds when real-time enabled
    retry: 2
  });

  const {
    data: customDashboards,
    isLoading: dashboardsLoading
  } = useQuery({
    queryKey: ['custom-dashboards'],
    queryFn: () => dashboardService.getUserDashboards('current-user-id'), // Replace with actual user ID
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const createDashboardMutation = useMutation({
    mutationFn: (dashboardData: Omit<CustomDashboard, 'id' | 'created_at' | 'updated_at'>) =>
      dashboardService.createCustomDashboard(dashboardData),
    onSuccess: (newDashboard) => {
      queryClient.invalidateQueries(['custom-dashboards']);
      toast({
        title: "Dashboard Created",
        description: `Custom dashboard "${newDashboard.name}" has been created successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Creation Failed",
        description: error.message || "Failed to create custom dashboard.",
        variant: "destructive",
      });
    }
  });

  const updateDashboardMutation = useMutation({
    mutationFn: ({ dashboardId, updates }: {
      dashboardId: string;
      updates: Partial<CustomDashboard>;
    }) => dashboardService.updateCustomDashboard(dashboardId, updates),
    onSuccess: (updatedDashboard) => {
      queryClient.invalidateQueries(['custom-dashboards']);
      toast({
        title: "Dashboard Updated",
        description: `Dashboard "${updatedDashboard.name}" has been updated successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update dashboard.",
        variant: "destructive",
      });
    }
  });

  const createAlertMutation = useMutation({
    mutationFn: (alertData: Omit<AlertRule, 'id' | 'created_at' | 'last_triggered'>) =>
      dashboardService.createAlertRule(alertData),
    onSuccess: (newAlert) => {
      toast({
        title: "Alert Created",
        description: `Alert rule "${newAlert.name}" has been created successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Alert Creation Failed",
        description: error.message || "Failed to create alert rule.",
        variant: "destructive",
      });
    }
  });

  const toggleRealTime = useCallback(() => {
    setRealTimeEnabled(prev => !prev);
  }, []);

  const refreshMetrics = useCallback(() => {
    refetch();
  }, [refetch]);

  const createCustomDashboard = useCallback(
    (dashboardData: Omit<CustomDashboard, 'id' | 'created_at' | 'updated_at'>) => {
      createDashboardMutation.mutate(dashboardData);
    },
    [createDashboardMutation]
  );

  const updateCustomDashboard = useCallback(
    (dashboardId: string, updates: Partial<CustomDashboard>) => {
      updateDashboardMutation.mutate({ dashboardId, updates });
    },
    [updateDashboardMutation]
  );

  const createAlert = useCallback(
    (alertData: Omit<AlertRule, 'id' | 'created_at' | 'last_triggered'>) => {
      createAlertMutation.mutate(alertData);
    },
    [createAlertMutation]
  );

  // Start real-time monitoring when enabled
  useEffect(() => {
    if (realTimeEnabled) {
      dashboardService.startRealTimeMonitoring();
    } else {
      dashboardService.stopRealTimeMonitoring();
    }

    return () => {
      dashboardService.stopRealTimeMonitoring();
    };
  }, [realTimeEnabled]);

  return {
    metrics,
    customDashboards: customDashboards || [],
    isLoading,
    dashboardsLoading,
    error,
    realTimeEnabled,
    toggleRealTime,
    refreshMetrics,
    createCustomDashboard,
    updateCustomDashboard,
    createAlert,
    isCreatingDashboard: createDashboardMutation.isPending,
    isUpdatingDashboard: updateDashboardMutation.isPending,
    isCreatingAlert: createAlertMutation.isPending
  };
};

/**
 * Hook for performance analytics
 */
export const usePerformanceAnalytics = (
  entityId: string,
  entityType: 'agent' | 'property' | 'market',
  period: { start: string; end: string }
) => {
  const { toast } = useToast();

  const {
    data: performanceData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['performance-analytics', entityId, entityType, period],
    queryFn: async () => {
      switch (entityType) {
        case 'agent':
          return await performanceMetricsService.calculateAgentPerformance(entityId, period);
        case 'property':
          return await performanceMetricsService.calculatePropertyPerformance(entityId, period);
        case 'market':
          return await performanceMetricsService.calculateMarketPerformance(entityId, undefined, period);
        default:
          throw new Error('Invalid entity type');
      }
    },
    enabled: !!entityId && !!entityType,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2
  });

  const refreshAnalytics = useCallback(() => {
    refetch();
  }, [refetch]);

  return {
    performanceData,
    isLoading,
    error,
    refreshAnalytics
  };
};

/**
 * Hook for market trends analysis
 */
export const useMarketTrends = (
  location: string,
  propertyType?: string,
  period?: { start: string; end: string }
) => {
  const { toast } = useToast();
  const [analysisType, setAnalysisType] = useState<'price' | 'demand' | 'seasonal' | 'cycle'>('price');

  const defaultPeriod = {
    start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
    end: new Date().toISOString()
  };

  const {
    data: priceTrends,
    isLoading: priceLoading
  } = useQuery({
    queryKey: ['price-trends', location, propertyType, period],
    queryFn: () => marketTrendAnalysisService.analyzePriceTrends(
      location, 
      propertyType || 'all', 
      period || defaultPeriod
    ),
    enabled: !!location && analysisType === 'price',
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  const {
    data: demandPatterns,
    isLoading: demandLoading
  } = useQuery({
    queryKey: ['demand-patterns', location, propertyType, period],
    queryFn: () => marketTrendAnalysisService.analyzeDemandPatterns(
      location, 
      propertyType || 'all', 
      period || defaultPeriod
    ),
    enabled: !!location && analysisType === 'demand',
    staleTime: 15 * 60 * 1000,
  });

  const {
    data: seasonalTrends,
    isLoading: seasonalLoading
  } = useQuery({
    queryKey: ['seasonal-trends', location, propertyType],
    queryFn: () => marketTrendAnalysisService.analyzeSeasonalTrends(location, propertyType || 'all', 3),
    enabled: !!location && analysisType === 'seasonal',
    staleTime: 30 * 60 * 1000, // 30 minutes
  });

  const {
    data: marketCycle,
    isLoading: cycleLoading
  } = useQuery({
    queryKey: ['market-cycle', location],
    queryFn: () => marketTrendAnalysisService.predictMarketCycle(location),
    enabled: !!location && analysisType === 'cycle',
    staleTime: 60 * 60 * 1000, // 1 hour
  });

  const {
    data: competitiveAnalysis,
    isLoading: competitiveLoading
  } = useQuery({
    queryKey: ['competitive-analysis', location, propertyType],
    queryFn: () => marketTrendAnalysisService.performCompetitiveAnalysis(location, propertyType || 'all'),
    enabled: !!location,
    staleTime: 30 * 60 * 1000,
  });

  const switchAnalysisType = useCallback((type: 'price' | 'demand' | 'seasonal' | 'cycle') => {
    setAnalysisType(type);
  }, []);

  const getCurrentAnalysisData = useCallback(() => {
    switch (analysisType) {
      case 'price':
        return priceTrends;
      case 'demand':
        return demandPatterns;
      case 'seasonal':
        return seasonalTrends;
      case 'cycle':
        return marketCycle;
      default:
        return null;
    }
  }, [analysisType, priceTrends, demandPatterns, seasonalTrends, marketCycle]);

  const isLoading = priceLoading || demandLoading || seasonalLoading || cycleLoading;

  return {
    priceTrends,
    demandPatterns,
    seasonalTrends,
    marketCycle,
    competitiveAnalysis,
    currentAnalysis: getCurrentAnalysisData(),
    analysisType,
    isLoading,
    competitiveLoading,
    switchAnalysisType
  };
};

/**
 * Hook for custom reports
 */
export const useCustomReports = (userId: string) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: reportConfigs,
    isLoading: configsLoading
  } = useQuery({
    queryKey: ['report-configs', userId],
    queryFn: () => reportGenerationService.getUserReportConfigs(userId),
    enabled: !!userId,
    staleTime: 10 * 60 * 1000,
  });

  const createReportMutation = useMutation({
    mutationFn: (config: Omit<ReportConfig, 'id' | 'created_at' | 'updated_at'>) =>
      reportGenerationService.createReportConfig(config),
    onSuccess: (newConfig) => {
      queryClient.invalidateQueries(['report-configs', userId]);
      toast({
        title: "Report Created",
        description: `Report configuration "${newConfig.name}" has been created.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Creation Failed",
        description: error.message || "Failed to create report configuration.",
        variant: "destructive",
      });
    }
  });

  const generateReportMutation = useMutation({
    mutationFn: ({ configId, options }: {
      configId: string;
      options?: ExportOptions;
    }) => reportGenerationService.generateReport(configId, options),
    onSuccess: (generatedReport) => {
      toast({
        title: "Report Generated",
        description: `Report "${generatedReport.name}" has been generated successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate report.",
        variant: "destructive",
      });
    }
  });

  const createReport = useCallback(
    (config: Omit<ReportConfig, 'id' | 'created_at' | 'updated_at'>) => {
      createReportMutation.mutate(config);
    },
    [createReportMutation]
  );

  const generateReport = useCallback(
    (configId: string, options?: ExportOptions) => {
      generateReportMutation.mutate({ configId, options });
    },
    [generateReportMutation]
  );

  return {
    reportConfigs: reportConfigs || [],
    generatedReport: generateReportMutation.data,
    configsLoading,
    isCreating: createReportMutation.isPending,
    isGenerating: generateReportMutation.isPending,
    createReport,
    generateReport
  };
};
