import { useState, useCallback, useRef, useEffect } from 'react';
import { logger } from '@/services/logger';
import { notifications } from '@/services/notificationService';

export interface RetryConfig {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  retryCondition?: (error: any, attempt: number) => boolean;
}

export interface FallbackConfig<T> {
  fallbackData?: T;
  fallbackComponent?: React.ComponentType<{ error: Error; retry: () => void }>;
  showFallback?: boolean;
}

export interface ErrorRecoveryState<T> {
  data: T | null;
  error: Error | null;
  isLoading: boolean;
  isRetrying: boolean;
  attemptCount: number;
  canRetry: boolean;
}

export interface ErrorRecoveryActions {
  retry: () => Promise<void>;
  reset: () => void;
  setError: (error: Error) => void;
}

const defaultRetryConfig: Required<RetryConfig> = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: (error: any, attempt: number) => {
    // Don't retry on client errors (4xx) except 408, 429
    if (error.status >= 400 && error.status < 500) {
      return error.status === 408 || error.status === 429;
    }
    // Retry on network errors and server errors
    return true;
  },
};

export function useErrorRecovery<T>(
  asyncOperation: () => Promise<T>,
  retryConfig: RetryConfig = {},
  fallbackConfig: FallbackConfig<T> = {}
): [ErrorRecoveryState<T>, ErrorRecoveryActions] {
  const config = { ...defaultRetryConfig, ...retryConfig };
  const [state, setState] = useState<ErrorRecoveryState<T>>({
    data: fallbackConfig.fallbackData || null,
    error: null,
    isLoading: false,
    isRetrying: false,
    attemptCount: 0,
    canRetry: false,
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const calculateDelay = useCallback((attempt: number): number => {
    const delay = Math.min(
      config.baseDelay * Math.pow(config.backoffFactor, attempt - 1),
      config.maxDelay
    );
    // Add jitter to prevent thundering herd
    return delay + Math.random() * 1000;
  }, [config]);

  const executeOperation = useCallback(async (isRetry = false): Promise<void> => {
    // Cancel any pending operation
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    abortControllerRef.current = new AbortController();

    setState(prev => ({
      ...prev,
      isLoading: !isRetry,
      isRetrying: isRetry,
      error: null,
    }));

    try {
      const result = await asyncOperation();
      
      setState(prev => ({
        ...prev,
        data: result,
        error: null,
        isLoading: false,
        isRetrying: false,
        attemptCount: isRetry ? prev.attemptCount + 1 : 1,
        canRetry: false,
      }));

      logger.info('Operation completed successfully', {
        attemptCount: state.attemptCount + 1,
        isRetry,
      }, 'ErrorRecovery', 'Success');

    } catch (error: any) {
      if (abortControllerRef.current?.signal.aborted) {
        return; // Operation was cancelled
      }

      const newAttemptCount = isRetry ? state.attemptCount + 1 : 1;
      const canRetry = newAttemptCount < config.maxAttempts && 
                      config.retryCondition(error, newAttemptCount);

      setState(prev => ({
        ...prev,
        data: fallbackConfig.fallbackData || prev.data,
        error,
        isLoading: false,
        isRetrying: false,
        attemptCount: newAttemptCount,
        canRetry,
      }));

      logger.error('Operation failed', error, {
        attemptCount: newAttemptCount,
        canRetry,
        maxAttempts: config.maxAttempts,
      }, 'ErrorRecovery', 'Failure');

      // Show error notification with retry option
      if (canRetry) {
        notifications.error(`Operation failed (attempt ${newAttemptCount}/${config.maxAttempts})`, {
          description: error.message,
          retry: {
            maxRetries: config.maxAttempts - newAttemptCount,
            onRetry: async () => {
              const delay = calculateDelay(newAttemptCount);
              await new Promise(resolve => setTimeout(resolve, delay));
              await executeOperation(true);
            },
            retryMessage: `Retry (${config.maxAttempts - newAttemptCount} left)`,
          },
        });
      } else {
        notifications.error('Operation failed', {
          description: error.message,
        });
      }
    }
  }, [asyncOperation, config, fallbackConfig, calculateDelay, state.attemptCount]);

  const retry = useCallback(async (): Promise<void> => {
    if (!state.canRetry) {
      logger.warn('Retry attempted but not allowed', {
        attemptCount: state.attemptCount,
        maxAttempts: config.maxAttempts,
      }, 'ErrorRecovery', 'RetryBlocked');
      return;
    }

    const delay = calculateDelay(state.attemptCount);
    
    logger.info('Retrying operation', {
      attemptCount: state.attemptCount + 1,
      delay,
    }, 'ErrorRecovery', 'Retry');

    timeoutRef.current = setTimeout(() => {
      executeOperation(true);
    }, delay);
  }, [state.canRetry, state.attemptCount, config.maxAttempts, calculateDelay, executeOperation]);

  const reset = useCallback((): void => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setState({
      data: fallbackConfig.fallbackData || null,
      error: null,
      isLoading: false,
      isRetrying: false,
      attemptCount: 0,
      canRetry: false,
    });

    logger.info('Error recovery state reset', undefined, 'ErrorRecovery', 'Reset');
  }, [fallbackConfig.fallbackData]);

  const setError = useCallback((error: Error): void => {
    setState(prev => ({
      ...prev,
      error,
      isLoading: false,
      isRetrying: false,
      canRetry: prev.attemptCount < config.maxAttempts,
    }));
  }, [config.maxAttempts]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Auto-execute on mount
  useEffect(() => {
    executeOperation();
  }, []);

  return [
    state,
    {
      retry,
      reset,
      setError,
    },
  ];
}

// Hook for automatic retry with exponential backoff
export function useRetryableOperation<T>(
  operation: () => Promise<T>,
  dependencies: any[] = [],
  config: RetryConfig = {}
) {
  const [state, actions] = useErrorRecovery(operation, config);

  // Re-execute when dependencies change
  useEffect(() => {
    actions.reset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies);

  return [state, actions] as const;
}

// Hook for operations with circuit breaker pattern
export function useCircuitBreaker<T>(
  operation: () => Promise<T>,
  config: {
    failureThreshold?: number;
    resetTimeout?: number;
    monitoringPeriod?: number;
  } = {}
) {
  const {
    failureThreshold = 5,
    resetTimeout = 60000,
    monitoringPeriod = 300000,
  } = config;

  const [circuitState, setCircuitState] = useState<'closed' | 'open' | 'half-open'>('closed');
  const [failureCount, setFailureCount] = useState(0);
  const [lastFailureTime, setLastFailureTime] = useState<number | null>(null);

  const wrappedOperation = useCallback(async (): Promise<T> => {
    const now = Date.now();

    // Reset failure count if monitoring period has passed
    if (lastFailureTime && now - lastFailureTime > monitoringPeriod) {
      setFailureCount(0);
      setLastFailureTime(null);
    }

    // Check circuit state
    if (circuitState === 'open') {
      if (lastFailureTime && now - lastFailureTime > resetTimeout) {
        setCircuitState('half-open');
        logger.info('Circuit breaker transitioning to half-open', undefined, 'CircuitBreaker', 'StateChange');
      } else {
        throw new Error('Circuit breaker is open - operation blocked');
      }
    }

    try {
      const result = await operation();
      
      // Success - reset circuit
      if (circuitState === 'half-open') {
        setCircuitState('closed');
        setFailureCount(0);
        setLastFailureTime(null);
        logger.info('Circuit breaker closed after successful operation', undefined, 'CircuitBreaker', 'StateChange');
      }
      
      return result;
    } catch (error) {
      const newFailureCount = failureCount + 1;
      setFailureCount(newFailureCount);
      setLastFailureTime(now);

      if (newFailureCount >= failureThreshold) {
        setCircuitState('open');
        logger.warn('Circuit breaker opened due to failures', {
          failureCount: newFailureCount,
          threshold: failureThreshold,
        }, 'CircuitBreaker', 'StateChange');
      }

      throw error;
    }
  }, [operation, circuitState, failureCount, lastFailureTime, failureThreshold, resetTimeout, monitoringPeriod]);

  const [state, actions] = useErrorRecovery(wrappedOperation, {
    retryCondition: (error, attempt) => {
      // Don't retry if circuit is open
      return circuitState !== 'open' && attempt < 3;
    },
  });

  return [
    { ...state, circuitState, failureCount },
    actions,
  ] as const;
}
