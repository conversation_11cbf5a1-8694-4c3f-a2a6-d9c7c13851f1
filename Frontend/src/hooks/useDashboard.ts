import { useQuery } from '@tanstack/react-query';
import { ApiService } from '@/services/apiService';
import { useAuth } from '@/hooks/useAuth';

// Dashboard statistics interface
export interface DashboardStats {
  totalUsers: number;
  totalProperties: number;
  totalApplications: number;
  pendingApplications: number;
  approvedApplications: number;
  rejectedApplications: number;
  totalRevenue: number;
  activeListings: number;
  verifiedProperties: number;
  featuredProperties: number;
  monthlyGrowth: number;
  userGrowth: number;
  revenueGrowth: number;
  propertyGrowth: number;
}

// Recent activity interface
export interface RecentActivity {
  id: string;
  type: 'user_registration' | 'property_listing' | 'application' | 'payment' | 'property_view';
  description: string;
  timestamp: Date;
  status: 'success' | 'pending' | 'failed';
  user?: {
    name: string;
    avatar?: string;
  };
  metadata?: any;
}

// System health interface
export interface SystemHealth {
  api: { status: 'online' | 'offline' | 'degraded'; uptime: number };
  database: { status: 'healthy' | 'warning' | 'error'; performance: number };
  storage: { status: 'optimal' | 'warning' | 'critical'; usage: number };
  cdn: { status: 'optimal' | 'degraded' | 'offline'; performance: number };
}

/**
 * Hook for fetching comprehensive dashboard statistics
 */
export const useDashboardStats = () => {
  return useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async (): Promise<DashboardStats> => {
      try {
        const stats = await ApiService.getStats();

        // Calculate growth metrics (mock for now - would come from API)
        const monthlyGrowth = 12.5;
        const userGrowth = 8.2;
        const revenueGrowth = 15.7;
        const propertyGrowth = 5.3;

        return {
          ...stats,
          monthlyGrowth,
          userGrowth,
          revenueGrowth,
          propertyGrowth,
        };
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error);
        // Return fallback data
        return {
          totalUsers: 0,
          totalProperties: 0,
          totalApplications: 0,
          pendingApplications: 0,
          approvedApplications: 0,
          rejectedApplications: 0,
          totalRevenue: 0,
          activeListings: 0,
          verifiedProperties: 0,
          featuredProperties: 0,
          monthlyGrowth: 0,
          userGrowth: 0,
          revenueGrowth: 0,
          propertyGrowth: 0,
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

/**
 * Hook for fetching recent activity
 */
export const useRecentActivity = () => {
  return useQuery({
    queryKey: ['recent-activity'],
    queryFn: async (): Promise<RecentActivity[]> => {
      try {
        // For now, generate mock activity based on real data
        const [properties, applications, users] = await Promise.all([
          ApiService.getProperties({ limit: 5, sortBy: 'createdAt', sortOrder: 'DESC' }),
          ApiService.getApplications({ limit: 5, sortBy: 'createdAt', sortOrder: 'DESC' }),
          ApiService.getUsers({ limit: 5, sortBy: 'createdAt', sortOrder: 'DESC' }),
        ]);

        const activities: RecentActivity[] = [];

        // Add recent property listings
        properties.data.forEach((property, index) => {
          activities.push({
            id: `property-${property.id}`,
            type: 'property_listing',
            description: `New property listed: ${property.title}`,
            timestamp: new Date(property.createdAt),
            status: 'success',
            user: {
              name:
                property.landlord?.firstName && property.landlord?.lastName
                  ? `${property.landlord.firstName} ${property.landlord.lastName}`
                  : 'Property Owner',
            },
            metadata: { propertyId: property.id, location: property.location },
          });
        });

        // Add recent user registrations
        users.data.forEach((user, index) => {
          activities.push({
            id: `user-${user.id}`,
            type: 'user_registration',
            description: `New ${user.role} registered: ${user.firstName} ${user.lastName}`,
            timestamp: new Date(user.createdAt),
            status: 'success',
            user: {
              name: `${user.firstName} ${user.lastName}`,
              avatar: user.avatar,
            },
            metadata: { userId: user.id, role: user.role },
          });
        });

        // Add recent applications
        applications.data.forEach((application, index) => {
          activities.push({
            id: `application-${application.id}`,
            type: 'application',
            description: `Rental application ${application.status}`,
            timestamp: new Date(application.createdAt),
            status:
              application.status === 'approved'
                ? 'success'
                : application.status === 'rejected'
                  ? 'failed'
                  : 'pending',
            user: {
              name:
                application.tenant?.firstName && application.tenant?.lastName
                  ? `${application.tenant.firstName} ${application.tenant.lastName}`
                  : 'Applicant',
            },
            metadata: { applicationId: application.id, propertyId: application.propertyId },
          });
        });

        // Sort by timestamp and return latest 10
        return activities
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
          .slice(0, 10);
      } catch (error) {
        console.error('Failed to fetch recent activity:', error);
        return [];
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
};

/**
 * Hook for system health monitoring
 */
export const useSystemHealth = () => {
  return useQuery({
    queryKey: ['system-health'],
    queryFn: async (): Promise<SystemHealth> => {
      try {
        const health = await ApiService.healthCheck();

        return {
          api: {
            status: 'online',
            uptime: health.uptime || 99.9,
          },
          database: {
            status: 'healthy',
            performance: 95,
          },
          storage: {
            status: 'warning',
            usage: 78,
          },
          cdn: {
            status: 'optimal',
            performance: 99,
          },
        };
      } catch (error) {
        console.error('Failed to fetch system health:', error);
        return {
          api: { status: 'offline', uptime: 0 },
          database: { status: 'error', performance: 0 },
          storage: { status: 'critical', usage: 0 },
          cdn: { status: 'offline', performance: 0 },
        };
      }
    },
    staleTime: 1 * 60 * 1000, // 1 minute
    retry: 2,
  });
};

/**
 * Hook for user-specific dashboard data
 */
export const useUserDashboard = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user-dashboard', user?.id],
    queryFn: async () => {
      if (!user) return null;

      try {
        // Fetch user-specific data based on role
        if (user.role === 'landlord') {
          const properties = await ApiService.getProperties({
            landlordId: user.id,
            limit: 100,
          });

          return {
            totalProperties: properties.meta.total,
            activeListings: properties.data.filter(p => p.status === 'available').length,
            totalViews: properties.data.reduce((sum, p) => sum + p.viewsCount, 0),
            totalInquiries: properties.data.reduce((sum, p) => sum + p.inquiriesCount, 0),
            monthlyRevenue: properties.data.reduce(
              (sum, p) => sum + parseFloat(p.pricePerMonth || '0'),
              0
            ),
          };
        }

        if (user.role === 'agent') {
          const properties = await ApiService.getProperties({
            agentId: user.id,
            limit: 100,
          });

          return {
            managedProperties: properties.meta.total,
            activeListings: properties.data.filter(p => p.status === 'available').length,
            totalCommissions: 0, // Would calculate from actual data
            clientCount: 0, // Would fetch from clients table
          };
        }

        if (user.role === 'tenant') {
          const applications = await ApiService.getApplications({
            tenantId: user.id,
            limit: 100,
          });

          return {
            totalApplications: applications.meta.total,
            pendingApplications: applications.data.filter(a => a.status === 'pending').length,
            approvedApplications: applications.data.filter(a => a.status === 'approved').length,
            savedProperties: 0, // Would fetch from saved properties
          };
        }

        return null;
      } catch (error) {
        console.error('Failed to fetch user dashboard data:', error);
        return null;
      }
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};
