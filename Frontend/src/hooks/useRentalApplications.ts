import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { API_CONFIG } from '@/config/api';

interface RentalApplication {
  id: string;
  property_id: string;
  user_id: string;
  status: string;
  application_data: any;
  created_at: string;
  updated_at: string;
}

interface RentalApplicationInsert {
  property_id: string;
  application_data: any;
}

export const useRentalApplications = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['rental-applications', user?.id],
    queryFn: async () => {
      if (!user) return [];

      const response = await fetch(`${API_CONFIG.BASE_URL}/rental-applications`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch rental applications: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data || [];
    },
    enabled: !!user,
  });
};

export const useCreateRentalApplication = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (data: {
      propertyId: string;
      applicationData: any;
    }) => {
      if (!user) throw new Error('User not authenticated');

      const applicationData: RentalApplicationInsert = {
        property_id: data.propertyId,
        application_data: data.applicationData,
      };

      const response = await fetch(`${API_CONFIG.BASE_URL}/rental-applications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify(applicationData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create rental application: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rental-applications'] });
      toast({
        title: "Application Submitted",
        description: "Your rental application has been submitted successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your application. Please try again.",
        variant: "destructive"
      });
    },
  });
};

export const useUpdateRentalApplication = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<RentalApplication> }) => {
      const { data, error } = await supabase
        .from('rental_applications')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rental-applications'] });
      toast({
        title: "Application Updated",
        description: "Your application has been updated successfully.",
      });
    },
  });
};

export const useRentalApplicationStats = () => {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['rental-application-stats', user?.id],
    queryFn: async () => {
      if (!user) return { total: 0, pending: 0, approved: 0, rejected: 0 };
      
      const { data, error } = await supabase
        .from('rental_applications')
        .select('status')
        .eq('user_id', user.id);

      if (error) throw error;
      
      const stats = {
        total: data.length,
        pending: data.filter(app => app.status === 'submitted' || app.status === 'under_review').length,
        approved: data.filter(app => app.status === 'approved').length,
        rejected: data.filter(app => app.status === 'rejected').length,
      };
      
      return stats;
    },
    enabled: !!user,
  });
};
