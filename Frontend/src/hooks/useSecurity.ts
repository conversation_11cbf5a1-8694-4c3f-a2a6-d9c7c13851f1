// =====================================================
// SECURITY HOOKS
// React hooks for security validation and monitoring
// =====================================================

import { useState, useEffect, useCallback, useRef } from 'react';
import { validationService } from '@/services/mockServices';

// Mock types for security services
interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
}

interface CSRFValidationResult {
  valid: boolean;
  token: string;
}

interface EncryptedData {
  data: string;
  iv: string;
}

interface SecurityEvent {
  id: string;
  type: string;
  severity: string;
  timestamp: string;
  details: any;
}

interface SecurityAlert {
  id: string;
  message: string;
  severity: string;
  timestamp: string;
}

// Mock services
const rateLimitingService = {
  checkRateLimit: async (key: string): Promise<RateLimitResult> => ({
    allowed: true,
    remaining: 100,
    resetTime: Date.now() + 3600000
  })
};

const csrfProtectionService = {
  validateToken: async (token: string): Promise<CSRFValidationResult> => ({
    valid: true,
    token: token || 'mock-csrf-token'
  }),
  generateToken: (): string => 'mock-csrf-token'
};

const encryptionService = {
  encrypt: async (data: string): Promise<EncryptedData> => ({
    data: btoa(data),
    iv: 'mock-iv'
  }),
  decrypt: async (encryptedData: EncryptedData): Promise<string> => {
    return atob(encryptedData.data);
  }
};

const securityMonitoringService = {
  logEvent: async (event: SecurityEvent): Promise<void> => {
    console.log('Security event logged:', event);
  },
  getAlerts: async (): Promise<SecurityAlert[]> => ([]),
  createAlert: async (alert: Omit<SecurityAlert, 'id' | 'timestamp'>): Promise<SecurityAlert> => ({
    ...alert,
    id: Date.now().toString(),
    timestamp: new Date().toISOString()
  })
};

// Input Validation Hook
export const useInputValidation = () => {
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({});
  const [isValidating, setIsValidating] = useState(false);

  const validateProperty = useCallback(async (data: any): Promise<ValidationResult> => {
    setIsValidating(true);
    try {
      const result = await validationService.validateProperty(data);
      
      if (!result.isValid) {
        setValidationErrors(prev => ({
          ...prev,
          property: result.errors
        }));
      } else {
        setValidationErrors(prev => {
          const { property, ...rest } = prev;
          return rest;
        });
      }
      
      return result;
    } catch (error) {
      console.error('Property validation error:', error);
      return {
        isValid: false,
        errors: ['Validation failed'],
        securityFlags: []
      };
    } finally {
      setIsValidating(false);
    }
  }, []);

  const validateUser = useCallback(async (data: any): Promise<ValidationResult> => {
    setIsValidating(true);
    try {
      const result = await validationService.validateUser(data);
      
      if (!result.isValid) {
        setValidationErrors(prev => ({
          ...prev,
          user: result.errors
        }));
      } else {
        setValidationErrors(prev => {
          const { user, ...rest } = prev;
          return rest;
        });
      }
      
      return result;
    } catch (error) {
      console.error('User validation error:', error);
      return {
        isValid: false,
        errors: ['Validation failed'],
        securityFlags: []
      };
    } finally {
      setIsValidating(false);
    }
  }, []);

  const validateMessage = useCallback(async (data: any): Promise<ValidationResult> => {
    setIsValidating(true);
    try {
      const result = await validationService.validateMessage(data);
      
      if (!result.isValid) {
        setValidationErrors(prev => ({
          ...prev,
          message: result.errors
        }));
      } else {
        setValidationErrors(prev => {
          const { message, ...rest } = prev;
          return rest;
        });
      }
      
      return result;
    } catch (error) {
      console.error('Message validation error:', error);
      return {
        isValid: false,
        errors: ['Validation failed'],
        securityFlags: []
      };
    } finally {
      setIsValidating(false);
    }
  }, []);

  const validateFile = useCallback(async (file: File): Promise<ValidationResult> => {
    setIsValidating(true);
    try {
      const config = {
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
        maxSize: 10 * 1024 * 1024, // 10MB
        allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'pdf'],
        scanForMalware: true,
        checkMagicBytes: true
      };
      
      const result = await validationService.validateFile(file, config);
      
      if (!result.isValid) {
        setValidationErrors(prev => ({
          ...prev,
          file: result.errors
        }));
      } else {
        setValidationErrors(prev => {
          const { file, ...rest } = prev;
          return rest;
        });
      }
      
      return result;
    } catch (error) {
      console.error('File validation error:', error);
      return {
        isValid: false,
        errors: ['File validation failed'],
        securityFlags: []
      };
    } finally {
      setIsValidating(false);
    }
  }, []);

  const sanitizeHtml = useCallback((html: string): string => {
    return validationService.sanitizeHtml(html);
  }, []);

  const clearErrors = useCallback((field?: string) => {
    if (field) {
      setValidationErrors(prev => {
        const { [field]: removed, ...rest } = prev;
        return rest;
      });
    } else {
      setValidationErrors({});
    }
  }, []);

  return {
    validationErrors,
    isValidating,
    validateProperty,
    validateUser,
    validateMessage,
    validateFile,
    sanitizeHtml,
    clearErrors
  };
};

// Rate Limiting Hook
export const useRateLimit = () => {
  const [rateLimitStatus, setRateLimitStatus] = useState<RateLimitResult | null>(null);
  const [isBlocked, setIsBlocked] = useState(false);

  const checkRateLimit = useCallback(async (req: any): Promise<RateLimitResult> => {
    try {
      const result = await rateLimitingService.checkRateLimit(req);
      setRateLimitStatus(result);
      setIsBlocked(!result.allowed);
      return result;
    } catch (error) {
      console.error('Rate limit check error:', error);
      return {
        allowed: true,
        limit: 0,
        remaining: 0,
        resetTime: Date.now()
      };
    }
  }, []);

  const getRateLimitStats = useCallback(async () => {
    try {
      return await rateLimitingService.getRateLimitStats();
    } catch (error) {
      console.error('Failed to get rate limit stats:', error);
      return null;
    }
  }, []);

  return {
    rateLimitStatus,
    isBlocked,
    checkRateLimit,
    getRateLimitStats
  };
};

// CSRF Protection Hook
export const useCSRFProtection = () => {
  const [csrfToken, setCSRFToken] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const generateToken = useCallback(async (req: any, res: any): Promise<string> => {
    try {
      const token = await csrfProtectionService.setupCSRFProtection(req, res);
      setCSRFToken(token);
      return token;
    } catch (error) {
      console.error('CSRF token generation error:', error);
      throw error;
    }
  }, []);

  const validateToken = useCallback(async (req: any): Promise<CSRFValidationResult> => {
    setIsValidating(true);
    try {
      const result = await csrfProtectionService.validateToken(req);
      return result;
    } catch (error) {
      console.error('CSRF validation error:', error);
      return {
        valid: false,
        reason: 'Validation failed',
        securityFlags: ['validation_error']
      };
    } finally {
      setIsValidating(false);
    }
  }, []);

  const getCSRFStats = useCallback(async () => {
    try {
      return await csrfProtectionService.getCSRFStats();
    } catch (error) {
      console.error('Failed to get CSRF stats:', error);
      return null;
    }
  }, []);

  return {
    csrfToken,
    isValidating,
    generateToken,
    validateToken,
    getCSRFStats
  };
};

// Encryption Hook
export const useEncryption = () => {
  const [isEncrypting, setIsEncrypting] = useState(false);
  const [isDecrypting, setIsDecrypting] = useState(false);

  const encryptData = useCallback(async (data: string, purpose: 'data' | 'file' | 'communication' | 'backup' = 'data'): Promise<EncryptedData | null> => {
    setIsEncrypting(true);
    try {
      const result = await encryptionService.encryptData(data, purpose);
      return result;
    } catch (error) {
      console.error('Encryption error:', error);
      return null;
    } finally {
      setIsEncrypting(false);
    }
  }, []);

  const decryptData = useCallback(async (encryptedData: EncryptedData): Promise<string | null> => {
    setIsDecrypting(true);
    try {
      const result = await encryptionService.decryptData(encryptedData);
      return result;
    } catch (error) {
      console.error('Decryption error:', error);
      return null;
    } finally {
      setIsDecrypting(false);
    }
  }, []);

  const encryptFile = useCallback(async (fileBuffer: Buffer): Promise<EncryptedData | null> => {
    setIsEncrypting(true);
    try {
      const result = await encryptionService.encryptFile(fileBuffer);
      return result;
    } catch (error) {
      console.error('File encryption error:', error);
      return null;
    } finally {
      setIsEncrypting(false);
    }
  }, []);

  const decryptFile = useCallback(async (encryptedData: EncryptedData): Promise<Buffer | null> => {
    setIsDecrypting(true);
    try {
      const result = await encryptionService.decryptFile(encryptedData);
      return result;
    } catch (error) {
      console.error('File decryption error:', error);
      return null;
    } finally {
      setIsDecrypting(false);
    }
  }, []);

  const generateSearchableHash = useCallback((value: string, salt?: string): string => {
    return encryptionService.generateSearchableHash(value, salt);
  }, []);

  const verifySearchableHash = useCallback((value: string, hash: string): boolean => {
    return encryptionService.verifySearchableHash(value, hash);
  }, []);

  const getEncryptionStats = useCallback(async () => {
    try {
      return await encryptionService.getEncryptionStats();
    } catch (error) {
      console.error('Failed to get encryption stats:', error);
      return null;
    }
  }, []);

  return {
    isEncrypting,
    isDecrypting,
    encryptData,
    decryptData,
    encryptFile,
    decryptFile,
    generateSearchableHash,
    verifySearchableHash,
    getEncryptionStats
  };
};

// Security Monitoring Hook
export const useSecurityMonitoring = () => {
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const logSecurityEvent = useCallback(async (event: Omit<SecurityEvent, 'id' | 'timestamp' | 'resolved'>): Promise<string | null> => {
    try {
      const eventId = await securityMonitoringService.logSecurityEvent(event);
      
      // Add to local state
      const newEvent: SecurityEvent = {
        id: eventId,
        timestamp: Date.now(),
        resolved: false,
        ...event
      };
      
      setSecurityEvents(prev => [newEvent, ...prev.slice(0, 99)]); // Keep last 100 events
      
      return eventId;
    } catch (error) {
      console.error('Failed to log security event:', error);
      return null;
    }
  }, []);

  const getSecurityMetrics = useCallback(async (timeRange: 'hour' | 'day' | 'week' = 'day') => {
    setIsLoading(true);
    try {
      const metrics = await securityMonitoringService.getSecurityMetrics(timeRange);
      return metrics;
    } catch (error) {
      console.error('Failed to get security metrics:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const resolveEvent = useCallback(async (eventId: string, resolvedBy: string): Promise<void> => {
    try {
      await securityMonitoringService.resolveEvent(eventId, resolvedBy);
      
      // Update local state
      setSecurityEvents(prev => 
        prev.map(event => 
          event.id === eventId 
            ? { ...event, resolved: true, resolvedAt: Date.now(), resolvedBy }
            : event
        )
      );
    } catch (error) {
      console.error('Failed to resolve security event:', error);
    }
  }, []);

  const updateAlertStatus = useCallback(async (alertId: string, status: SecurityAlert['status'], assignedTo?: string): Promise<void> => {
    try {
      await securityMonitoringService.updateAlertStatus(alertId, status, assignedTo);
      
      // Update local state
      setSecurityAlerts(prev => 
        prev.map(alert => 
          alert.id === alertId 
            ? { ...alert, status, assignedTo, updatedAt: Date.now() }
            : alert
        )
      );
    } catch (error) {
      console.error('Failed to update alert status:', error);
    }
  }, []);

  const checkThreatIntelligence = useCallback((ipAddress: string) => {
    return securityMonitoringService.checkThreatIntelligence(ipAddress);
  }, []);

  return {
    securityEvents,
    securityAlerts,
    isLoading,
    logSecurityEvent,
    getSecurityMetrics,
    resolveEvent,
    updateAlertStatus,
    checkThreatIntelligence
  };
};

// Security Dashboard Hook
export const useSecurityDashboard = () => {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  const { getSecurityMetrics } = useSecurityMonitoring();
  const { getRateLimitStats } = useRateLimit();
  const { getCSRFStats } = useCSRFProtection();
  const { getEncryptionStats } = useEncryption();

  const loadDashboardData = useCallback(async () => {
    setIsLoading(true);
    try {
      const [securityMetrics, rateLimitStats, csrfStats, encryptionStats] = await Promise.all([
        getSecurityMetrics(),
        getRateLimitStats(),
        getCSRFStats(),
        getEncryptionStats()
      ]);

      setDashboardData({
        securityMetrics,
        rateLimitStats,
        csrfStats,
        encryptionStats,
        lastUpdated: Date.now()
      });
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [getSecurityMetrics, getRateLimitStats, getCSRFStats, getEncryptionStats]);

  const startAutoRefresh = useCallback((intervalMs: number = 30000) => {
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }

    const interval = setInterval(loadDashboardData, intervalMs);
    setRefreshInterval(interval);
  }, [loadDashboardData, refreshInterval]);

  const stopAutoRefresh = useCallback(() => {
    if (refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }
  }, [refreshInterval]);

  useEffect(() => {
    loadDashboardData();
    startAutoRefresh();

    return () => {
      stopAutoRefresh();
    };
  }, [loadDashboardData, startAutoRefresh, stopAutoRefresh]);

  return {
    dashboardData,
    isLoading,
    loadDashboardData,
    startAutoRefresh,
    stopAutoRefresh
  };
};

// Security Context Hook
export const useSecurityContext = () => {
  const validation = useInputValidation();
  const rateLimit = useRateLimit();
  const csrf = useCSRFProtection();
  const encryption = useEncryption();
  const monitoring = useSecurityMonitoring();
  const dashboard = useSecurityDashboard();

  return {
    validation,
    rateLimit,
    csrf,
    encryption,
    monitoring,
    dashboard
  };
};

export default {
  useInputValidation,
  useRateLimit,
  useCSRFProtection,
  useEncryption,
  useSecurityMonitoring,
  useSecurityDashboard,
  useSecurityContext
};
