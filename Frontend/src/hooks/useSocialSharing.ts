// =====================================================
// SOCIAL SHARING HOOK
// React hook for social media integration
// =====================================================

import { useState, useCallback } from 'react';
import { socialMediaService, ShareContent, SocialPlatform, SocialMetrics, UserGeneratedContent } from '@/services/socialMediaService';

export interface UseSocialSharingProps {
  contentData?: ShareContent;
  autoGenerateContent?: boolean;
}

export interface UseSocialSharingReturn {
  // State
  platforms: SocialPlatform[];
  isSharing: boolean;
  shareResults: Record<string, any>;
  error: string | null;
  metrics: SocialMetrics[];
  userContent: UserGeneratedContent[];

  // Actions
  shareContent: (content: ShareContent, platforms: string[]) => Promise<boolean>;
  shareProperty: (propertyData: any, platforms: string[]) => Promise<boolean>;
  generateShareContent: (propertyData: any) => ShareContent;
  getSharePreview: (content: ShareContent, platformId: string) => any;
  initializeSocialLogin: (provider: string) => Promise<boolean>;
  submitUserContent: (content: Omit<UserGeneratedContent, 'id' | 'createdAt' | 'likes' | 'shares' | 'comments' | 'isVerified'>) => Promise<boolean>;
  getUserContent: (propertyId: string, contentType?: string) => Promise<UserGeneratedContent[]>;
  getSocialMetrics: (propertyId?: string, dateRange?: { start: string; end: string }) => Promise<SocialMetrics[]>;
  
  // Utilities
  isPlatformConfigured: (platformId: string) => boolean;
  formatContentForPlatform: (content: ShareContent, platformId: string) => string;
  copyToClipboard: (text: string) => Promise<boolean>;
}

export const useSocialSharing = (props: UseSocialSharingProps = {}): UseSocialSharingReturn => {
  const { contentData, autoGenerateContent = true } = props;

  // State
  const [platforms] = useState<SocialPlatform[]>(socialMediaService.getPlatforms());
  const [isSharing, setIsSharing] = useState(false);
  const [shareResults, setShareResults] = useState<Record<string, any>>({});
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<SocialMetrics[]>([]);
  const [userContent, setUserContent] = useState<UserGeneratedContent[]>([]);

  // Share content across platforms
  const shareContent = useCallback(async (content: ShareContent, selectedPlatforms: string[]): Promise<boolean> => {
    try {
      setIsSharing(true);
      setError(null);

      const result = await socialMediaService.shareProperty(content, selectedPlatforms);
      setShareResults(result.results);

      if (!result.success) {
        throw new Error('Sharing failed');
      }

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Sharing failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsSharing(false);
    }
  }, []);

  // Share property with auto-generated content
  const shareProperty = useCallback(async (propertyData: any, selectedPlatforms: string[]): Promise<boolean> => {
    try {
      const content = generateShareContent(propertyData);
      return await shareContent(content, selectedPlatforms);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Property sharing failed';
      setError(errorMessage);
      return false;
    }
  }, [shareContent]);

  // Generate share content from property data
  const generateShareContent = useCallback((propertyData: any): ShareContent => {
    const {
      id,
      title,
      description,
      price,
      location,
      property_type,
      bedrooms,
      bathrooms,
      images
    } = propertyData;

    // Generate compelling title
    const shareTitle = title || `${bedrooms || 'Beautiful'} Bedroom ${property_type || 'Property'} in ${location || 'Port Harcourt'}`;

    // Generate description
    let shareDescription = description || `Discover this amazing ${property_type || 'property'} in ${location || 'Port Harcourt'}.`;
    
    if (bedrooms || bathrooms) {
      shareDescription += ` Features ${bedrooms || 0} bedrooms and ${bathrooms || 0} bathrooms.`;
    }

    if (price) {
      shareDescription += ` Available for ₦${price.toLocaleString()} per month.`;
    }

    shareDescription += ' Contact us for viewing arrangements!';

    // Generate hashtags
    const hashtags = [
      'PHCityRent',
      'PortHarcourt',
      'RealEstate',
      'PropertyRental',
      property_type?.replace(/\s+/g, '') || 'Property',
      location?.replace(/\s+/g, '') || 'PH'
    ];

    return {
      title: shareTitle,
      description: shareDescription,
      url: `${window.location.origin}/properties/${id}`,
      imageUrl: images?.[0] || undefined,
      hashtags,
      price,
      location,
      propertyType: property_type
    };
  }, []);

  // Get share preview for platform
  const getSharePreview = useCallback((content: ShareContent, platformId: string) => {
    return socialMediaService.generateSocialPreview(content, platformId);
  }, []);

  // Initialize social login
  const initializeSocialLogin = useCallback(async (provider: string): Promise<boolean> => {
    try {
      setError(null);

      const providerConfig = {
        provider: provider as any,
        clientId: process.env[`NEXT_PUBLIC_${provider.toUpperCase()}_CLIENT_ID`] || '',
        redirectUri: `${window.location.origin}/auth/callback`,
        scope: ['email', 'profile']
      };

      return await socialMediaService.initializeSocialLogin(providerConfig);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Social login initialization failed';
      setError(errorMessage);
      return false;
    }
  }, []);

  // Submit user-generated content
  const submitUserContent = useCallback(async (content: Omit<UserGeneratedContent, 'id' | 'createdAt' | 'likes' | 'shares' | 'comments' | 'isVerified'>): Promise<boolean> => {
    try {
      setError(null);
      return await socialMediaService.submitUserContent(content);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit content';
      setError(errorMessage);
      return false;
    }
  }, []);

  // Get user-generated content
  const getUserContent = useCallback(async (propertyId: string, contentType?: string): Promise<UserGeneratedContent[]> => {
    try {
      setError(null);
      const content = await socialMediaService.getPropertyUserContent(propertyId, contentType);
      setUserContent(content);
      return content;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get user content';
      setError(errorMessage);
      return [];
    }
  }, []);

  // Get social metrics
  const getSocialMetrics = useCallback(async (propertyId?: string, dateRange?: { start: string; end: string }): Promise<SocialMetrics[]> => {
    try {
      setError(null);
      const metricsData = await socialMediaService.getSocialMetrics(propertyId, dateRange);
      setMetrics(metricsData);
      return metricsData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get social metrics';
      setError(errorMessage);
      return [];
    }
  }, []);

  // Check if platform is configured
  const isPlatformConfigured = useCallback((platformId: string): boolean => {
    return socialMediaService.isPlatformConfigured(platformId);
  }, []);

  // Format content for specific platform
  const formatContentForPlatform = useCallback((content: ShareContent, platformId: string): string => {
    const platform = platforms.find(p => p.id === platformId);
    if (!platform) return '';

    switch (platformId) {
      case 'twitter':
        let tweetText = `${content.title}\n\n${content.description}`;
        if (content.price) {
          tweetText += `\n💰 ₦${content.price.toLocaleString()}`;
        }
        if (content.location) {
          tweetText += `\n📍 ${content.location}`;
        }
        if (content.hashtags) {
          const hashtagText = content.hashtags.map(tag => `#${tag}`).join(' ');
          // Ensure tweet doesn't exceed 280 characters
          const availableSpace = 280 - tweetText.length - content.url.length - 3; // 3 for spaces
          if (hashtagText.length <= availableSpace) {
            tweetText += `\n\n${hashtagText}`;
          }
        }
        return tweetText;

      case 'facebook':
      case 'linkedin':
        return `${content.title}\n\n${content.description}`;

      case 'whatsapp':
        let whatsappText = `*${content.title}*\n\n${content.description}`;
        if (content.price) {
          whatsappText += `\n\n💰 *Price:* ₦${content.price.toLocaleString()}`;
        }
        if (content.location) {
          whatsappText += `\n📍 *Location:* ${content.location}`;
        }
        return whatsappText;

      case 'instagram':
        let instagramText = `${content.title}\n\n${content.description}`;
        if (content.hashtags) {
          instagramText += `\n\n${content.hashtags.map(tag => `#${tag}`).join(' ')}`;
        }
        return instagramText;

      default:
        return `${content.title}\n\n${content.description}`;
    }
  }, [platforms]);

  // Copy text to clipboard
  const copyToClipboard = useCallback(async (text: string): Promise<boolean> => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        return true;
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const success = document.execCommand('copy');
        document.body.removeChild(textArea);
        return success;
      }
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
      return false;
    }
  }, []);

  return {
    // State
    platforms,
    isSharing,
    shareResults,
    error,
    metrics,
    userContent,

    // Actions
    shareContent,
    shareProperty,
    generateShareContent,
    getSharePreview,
    initializeSocialLogin,
    submitUserContent,
    getUserContent,
    getSocialMetrics,

    // Utilities
    isPlatformConfigured,
    formatContentForPlatform,
    copyToClipboard
  };
};

export default useSocialSharing;
