import { useState } from "react";
import { useDocumentUpload } from "@/hooks/useDocumentUpload";

interface ApplicationData {
  fullName: string;
  whatsappNumber: string;
  email?: string;
  residentialAddress: string;
  operatingAreas: string[];
  isRegisteredBusiness: boolean;
  refereeFullName: string;
  refereeWhatsappNumber: string;
  refereeRole: string;
}

interface DocumentFiles {
  idDocument?: File;
  selfieWithId?: File;
  cacDocument?: File;
}

export const useAgentApplication = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { uploadDocument } = useDocumentUpload();

  const submitApplication = async (
    applicationData: ApplicationData,
    documents: DocumentFiles
  ) => {
    setIsSubmitting(true);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Generate mock agent ID
      const agentId = `AGT${Date.now().toString().slice(-6)}`;

      // Create mock application
      const application = {
        id: `app_${Date.now()}`,
        agent_id: agentId,
        full_name: applicationData.fullName,
        whatsapp_number: applicationData.whatsappNumber,
        email: applicationData.email,
        residential_address: applicationData.residentialAddress,
        operating_areas: applicationData.operatingAreas,
        is_registered_business: applicationData.isRegisteredBusiness,
        status: 'pending',
        created_at: new Date().toISOString()
      };

      // Mock referee verification record
      const refereeVerification = {
        id: `ref_${Date.now()}`,
        application_id: application.id,
        referee_full_name: applicationData.refereeFullName,
        referee_whatsapp_number: applicationData.refereeWhatsappNumber,
        referee_role: applicationData.refereeRole,
        status: 'pending'
      };

      // Mock document uploads
      const uploadPromises = [];

      if (documents.idDocument) {
        uploadPromises.push(
          uploadDocument(documents.idDocument, 'id_document', application.id)
        );
      }

      if (documents.selfieWithId) {
        uploadPromises.push(
          uploadDocument(documents.selfieWithId, 'selfie_with_id', application.id)
        );
      }

      if (documents.cacDocument) {
        uploadPromises.push(
          uploadDocument(documents.cacDocument, 'cac_document', application.id)
        );
      }

      if (uploadPromises.length > 0) {
        await Promise.all(uploadPromises);
      }

      // Log the mock submission
      console.log('Mock agent application submitted:', {
        application,
        refereeVerification,
        documents: Object.keys(documents)
      });

      return { success: true, agentId };
    } catch (error) {
      console.error('Application submission error:', error);
      return { success: false, error: error.message || 'Submission failed' };
    } finally {
      setIsSubmitting(false);
    }
  };

  return { submitApplication, isSubmitting };
};
