// =====================================================
// PERFORMANCE OPTIMIZATION HOOKS
// React hooks for performance monitoring and optimization
// =====================================================

import { useState, useEffect, useCallback, useRef } from 'react';
import { performanceMonitoringService } from '@/services/mockServices';

// Mock types and interfaces
interface OptimizedImage {
  url: string;
  width: number;
  height: number;
  format: string;
  size: number;
}

interface PerformanceMetrics {
  responseTime: number;
  throughput: number;
  errorRate: number;
  uptime: number;
}

interface PerformanceAlert {
  id: string;
  type: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  timestamp: string;
}

// Mock services
const imageOptimizationService = {
  optimizeImage: async (file: File): Promise<OptimizedImage> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      url: URL.createObjectURL(file),
      width: 800,
      height: 600,
      format: 'webp',
      size: file.size * 0.7
    };
  },
  generateResponsiveImages: async (file: File): Promise<OptimizedImage[]> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    const baseUrl = URL.createObjectURL(file);
    return [
      { url: baseUrl, width: 400, height: 300, format: 'webp', size: file.size * 0.3 },
      { url: baseUrl, width: 800, height: 600, format: 'webp', size: file.size * 0.7 },
      { url: baseUrl, width: 1200, height: 900, format: 'webp', size: file.size * 0.9 }
    ];
  }
};

const cacheService = {
  get: async (key: string): Promise<any> => {
    const cached = localStorage.getItem(`cache_${key}`);
    return cached ? JSON.parse(cached) : null;
  },
  set: async (key: string, value: any, ttl: number = 3600): Promise<void> => {
    localStorage.setItem(`cache_${key}`, JSON.stringify({
      value,
      expires: Date.now() + (ttl * 1000)
    }));
  },
  delete: async (key: string): Promise<void> => {
    localStorage.removeItem(`cache_${key}`);
  },
  clear: async (): Promise<void> => {
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('cache_')) {
        localStorage.removeItem(key);
      }
    });
  }
};

const bundleOptimizationService = {
  analyzeBundleSize: async (): Promise<any> => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return {
      totalSize: 2.5,
      chunks: [
        { name: 'vendor', size: 1.2, gzipped: 0.4 },
        { name: 'main', size: 0.8, gzipped: 0.3 },
        { name: 'components', size: 0.5, gzipped: 0.2 }
      ]
    };
  },
  optimizeBundle: async (): Promise<any> => {
    await new Promise(resolve => setTimeout(resolve, 2000));
    return {
      originalSize: 2.5,
      optimizedSize: 1.8,
      savings: 0.7,
      techniques: ['tree-shaking', 'code-splitting', 'compression']
    };
  }
};

// Performance Monitoring Hook
export const usePerformanceMonitoring = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    // Set up alert callback
    performanceMonitoringService.onAlert = (alert: PerformanceAlert) => {
      setAlerts(prev => [...prev, alert]);
    };

    // Start monitoring
    performanceMonitoringService.startMonitoring();
    setIsMonitoring(true);

    // Update metrics periodically
    const interval = setInterval(() => {
      const currentMetrics = performanceMonitoringService.getCurrentMetrics();
      if (currentMetrics) {
        setMetrics(currentMetrics);
      }
    }, 1000);

    return () => {
      clearInterval(interval);
      performanceMonitoringService.stopMonitoring();
      setIsMonitoring(false);
    };
  }, []);

  const acknowledgeAlert = useCallback((alertId: string) => {
    performanceMonitoringService.acknowledgeAlert(alertId);
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  }, []);

  const clearAlerts = useCallback(() => {
    performanceMonitoringService.clearAcknowledgedAlerts();
    setAlerts(prev => prev.filter(alert => !alert.acknowledged));
  }, []);

  return {
    metrics,
    alerts,
    isMonitoring,
    acknowledgeAlert,
    clearAlerts
  };
};

// Image Optimization Hook
export const useImageOptimization = () => {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizedImages, setOptimizedImages] = useState<Map<string, OptimizedImage>>(new Map());
  const [stats, setStats] = useState<any>(null);

  const optimizeImage = useCallback(async (imageUrl: string, config?: any) => {
    setIsOptimizing(true);
    try {
      const optimized = await imageOptimizationService.optimizeImage(imageUrl, config);
      if (optimized) {
        setOptimizedImages(prev => new Map(prev).set(imageUrl, optimized));
        return optimized;
      }
      return null;
    } catch (error) {
      console.error('Image optimization failed:', error);
      return null;
    } finally {
      setIsOptimizing(false);
    }
  }, []);

  const batchOptimize = useCallback(async (imageUrls: string[], config?: any) => {
    setIsOptimizing(true);
    try {
      const results = await imageOptimizationService.batchOptimize(
        imageUrls, 
        config,
        (completed, total) => {
          console.log(`Optimized ${completed}/${total} images`);
        }
      );
      
      results.forEach(result => {
        setOptimizedImages(prev => new Map(prev).set(result.originalUrl, result));
      });
      
      return results;
    } catch (error) {
      console.error('Batch optimization failed:', error);
      return [];
    } finally {
      setIsOptimizing(false);
    }
  }, []);

  const getOptimizationStats = useCallback(async () => {
    try {
      const stats = await imageOptimizationService.getOptimizationStats();
      setStats(stats);
      return stats;
    } catch (error) {
      console.error('Failed to get optimization stats:', error);
      return null;
    }
  }, []);

  return {
    isOptimizing,
    optimizedImages,
    stats,
    optimizeImage,
    batchOptimize,
    getOptimizationStats
  };
};

// Caching Hook
export const useCaching = () => {
  const [cacheStats, setCacheStats] = useState(cacheService.getStats());

  useEffect(() => {
    const interval = setInterval(() => {
      setCacheStats(cacheService.getStats());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const get = useCallback(async <T>(key: string, config?: any): Promise<T | null> => {
    return cacheService.get<T>(key, config);
  }, []);

  const set = useCallback(async <T>(key: string, value: T, config?: any): Promise<void> => {
    return cacheService.set(key, value, config);
  }, []);

  const memoize = useCallback(async <T>(
    key: string,
    fn: () => Promise<T>,
    config?: any
  ): Promise<T> => {
    return cacheService.memoize(key, fn, config);
  }, []);

  const invalidate = useCallback(async (pattern: string): Promise<void> => {
    return cacheService.invalidate(pattern);
  }, []);

  const clear = useCallback(async (): Promise<void> => {
    return cacheService.clear();
  }, []);

  return {
    cacheStats,
    get,
    set,
    memoize,
    invalidate,
    clear
  };
};

// Lazy Loading Hook
export const useLazyLoading = (config?: any) => {
  const [observer, setObserver] = useState<IntersectionObserver | null>(null);
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());

  useEffect(() => {
    const lazyObserver = imageOptimizationService.createLazyLoader(config);
    setObserver(lazyObserver);

    return () => {
      lazyObserver.disconnect();
    };
  }, [config]);

  const observeImage = useCallback((img: HTMLImageElement) => {
    if (observer && img.dataset.src) {
      observer.observe(img);
    }
  }, [observer]);

  const markAsLoaded = useCallback((src: string) => {
    setLoadedImages(prev => new Set(prev).add(src));
  }, []);

  return {
    observeImage,
    markAsLoaded,
    loadedImages
  };
};

// Bundle Optimization Hook
export const useBundleOptimization = () => {
  const [analysis, setAnalysis] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [budgetStatus, setBudgetStatus] = useState<any>(null);

  const analyzeBundleComposition = useCallback(async () => {
    setIsAnalyzing(true);
    try {
      const result = await bundleOptimizationService.analyzeBundleComposition();
      setAnalysis(result);
      return result;
    } catch (error) {
      console.error('Bundle analysis failed:', error);
      return null;
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  const monitorBundlePerformance = useCallback(async () => {
    try {
      const status = await bundleOptimizationService.monitorBundlePerformance();
      setBudgetStatus(status);
      return status;
    } catch (error) {
      console.error('Bundle monitoring failed:', error);
      return null;
    }
  }, []);

  const getBundleSizeTrends = useCallback(async (days: number = 30) => {
    try {
      return await bundleOptimizationService.getBundleSizeTrends(days);
    } catch (error) {
      console.error('Failed to get bundle trends:', error);
      return [];
    }
  }, []);

  return {
    analysis,
    isAnalyzing,
    budgetStatus,
    analyzeBundleComposition,
    monitorBundlePerformance,
    getBundleSizeTrends
  };
};

// Resource Preloading Hook
export const useResourcePreloading = () => {
  const preloadedResources = useRef<Set<string>>(new Set());

  const preloadImage = useCallback((src: string): Promise<void> => {
    if (preloadedResources.current.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        preloadedResources.current.add(src);
        resolve();
      };
      img.onerror = reject;
      img.src = src;
    });
  }, []);

  const preloadScript = useCallback((src: string): Promise<void> => {
    if (preloadedResources.current.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.onload = () => {
        preloadedResources.current.add(src);
        resolve();
      };
      script.onerror = reject;
      script.src = src;
      document.head.appendChild(script);
    });
  }, []);

  const preloadStylesheet = useCallback((href: string): Promise<void> => {
    if (preloadedResources.current.has(href)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.onload = () => {
        preloadedResources.current.add(href);
        resolve();
      };
      link.onerror = reject;
      link.href = href;
      document.head.appendChild(link);
    });
  }, []);

  const preloadCriticalImages = useCallback(async (imageUrls: string[]): Promise<void> => {
    await imageOptimizationService.preloadCriticalImages(imageUrls);
    imageUrls.forEach(url => preloadedResources.current.add(url));
  }, []);

  return {
    preloadImage,
    preloadScript,
    preloadStylesheet,
    preloadCriticalImages,
    preloadedResources: preloadedResources.current
  };
};

// Performance Budget Hook
export const usePerformanceBudget = () => {
  const [budgets, setBudgets] = useState<any[]>([]);
  const [violations, setViolations] = useState<any[]>([]);

  const checkBudgets = useCallback(async () => {
    try {
      const report = await performanceMonitoringService.getPerformanceReport();
      const budgetViolations = report.budgetStatus.filter(
        status => status.status === 'exceeded'
      );
      setViolations(budgetViolations);
      return budgetViolations;
    } catch (error) {
      console.error('Budget check failed:', error);
      return [];
    }
  }, []);

  const updateBudgets = useCallback((newBudgets: any[]) => {
    setBudgets(newBudgets);
    performanceMonitoringService.updateBudgets(newBudgets);
  }, []);

  return {
    budgets,
    violations,
    checkBudgets,
    updateBudgets
  };
};

// Web Vitals Hook
export const useWebVitals = () => {
  const [vitals, setVitals] = useState<{
    lcp: number | null;
    fid: number | null;
    cls: number | null;
    fcp: number | null;
    ttfb: number | null;
  }>({
    lcp: null,
    fid: null,
    cls: null,
    fcp: null,
    ttfb: null
  });

  const [scores, setScores] = useState<{
    lcp: number;
    fid: number;
    cls: number;
    overall: number;
  }>({
    lcp: 0,
    fid: 0,
    cls: 0,
    overall: 0
  });

  useEffect(() => {
    const updateVitals = () => {
      const metrics = performanceMonitoringService.getCurrentMetrics();
      if (metrics) {
        setVitals({
          lcp: metrics.lcp,
          fid: metrics.fid,
          cls: metrics.cls,
          fcp: metrics.fcp,
          ttfb: metrics.ttfb
        });

        // Calculate scores
        const lcpScore = metrics.lcp <= 2500 ? 100 : metrics.lcp <= 4000 ? 50 : 0;
        const fidScore = metrics.fid <= 100 ? 100 : metrics.fid <= 300 ? 50 : 0;
        const clsScore = metrics.cls <= 0.1 ? 100 : metrics.cls <= 0.25 ? 50 : 0;
        const overall = Math.round((lcpScore + fidScore + clsScore) / 3);

        setScores({
          lcp: lcpScore,
          fid: fidScore,
          cls: clsScore,
          overall
        });
      }
    };

    const interval = setInterval(updateVitals, 1000);
    updateVitals(); // Initial call

    return () => clearInterval(interval);
  }, []);

  return {
    vitals,
    scores
  };
};

// Performance Optimization Recommendations Hook
export const usePerformanceRecommendations = () => {
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const getRecommendations = useCallback(async () => {
    setIsLoading(true);
    try {
      const [bundleAnalysis, performanceReport] = await Promise.all([
        bundleOptimizationService.analyzeBundleComposition(),
        performanceMonitoringService.getPerformanceReport()
      ]);

      const allRecommendations = [
        ...bundleAnalysis.recommendations,
        ...performanceReport.recommendations.map(rec => ({
          type: 'performance',
          priority: 'medium',
          description: rec,
          estimatedSavings: 0,
          implementation: 'Follow Core Web Vitals best practices'
        }))
      ];

      setRecommendations(allRecommendations);
      return allRecommendations;
    } catch (error) {
      console.error('Failed to get recommendations:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    getRecommendations();
  }, [getRecommendations]);

  return {
    recommendations,
    isLoading,
    refreshRecommendations: getRecommendations
  };
};

export default {
  usePerformanceMonitoring,
  useImageOptimization,
  useCaching,
  useLazyLoading,
  useBundleOptimization,
  useResourcePreloading,
  usePerformanceBudget,
  useWebVitals,
  usePerformanceRecommendations
};
