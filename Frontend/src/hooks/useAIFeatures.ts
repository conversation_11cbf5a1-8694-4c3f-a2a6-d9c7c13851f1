import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { aiRecommendationService } from '@/services/aiRecommendationService';
import { pricePredictionService } from '@/services/pricePredictionService';
import { marketAnalyticsService } from '@/services/marketAnalyticsService';
import { intelligentMatchingService } from '@/services/intelligentMatchingService';
import {
  PropertyRecommendation,
  UserPreferences,
  PricePrediction,
  PropertyFeatures,
  MarketInsight,
  ComparativeMarketAnalysis,
  InvestmentOpportunity,
  PropertyMatch,
  MatchingCriteria,
  LeadScore,
  ConversionPrediction
} from '@/types/ai';

/**
 * Hook for AI-powered property recommendations
 */
export const usePropertyRecommendations = (userId: string | null) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);

  const {
    data: recommendations,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['property-recommendations', userId, preferences],
    queryFn: () => userId ? aiRecommendationService.getPersonalizedRecommendations(userId, 10, true) : null,
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2
  });

  const updatePreferencesMutation = useMutation({
    mutationFn: (newPreferences: UserPreferences) =>
      aiRecommendationService.updateUserPreferences(userId!, newPreferences),
    onSuccess: (updatedPreferences) => {
      setPreferences(updatedPreferences);
      queryClient.invalidateQueries(['property-recommendations', userId]);
      toast({
        title: "Preferences Updated",
        description: "Your property preferences have been updated. Getting new recommendations...",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update preferences.",
        variant: "destructive",
      });
    }
  });

  const trackInteractionMutation = useMutation({
    mutationFn: ({ propertyId, interactionType, metadata }: {
      propertyId: string;
      interactionType: 'view' | 'save' | 'inquiry' | 'contact';
      metadata?: any;
    }) => aiRecommendationService.trackUserInteraction(userId!, propertyId, interactionType, metadata),
    onSuccess: () => {
      // Silently update recommendations in background
      queryClient.invalidateQueries(['property-recommendations', userId]);
    }
  });

  const updatePreferences = useCallback(
    (newPreferences: UserPreferences) => {
      updatePreferencesMutation.mutate(newPreferences);
    },
    [updatePreferencesMutation]
  );

  const trackInteraction = useCallback(
    (propertyId: string, interactionType: 'view' | 'save' | 'inquiry' | 'contact', metadata?: any) => {
      if (!userId) return;
      trackInteractionMutation.mutate({ propertyId, interactionType, metadata });
    },
    [userId, trackInteractionMutation]
  );

  const refreshRecommendations = useCallback(() => {
    refetch();
  }, [refetch]);

  return {
    recommendations: recommendations || [],
    preferences,
    isLoading,
    error,
    updatePreferences,
    trackInteraction,
    refreshRecommendations,
    isUpdatingPreferences: updatePreferencesMutation.isPending,
    isTrackingInteraction: trackInteractionMutation.isPending
  };
};

/**
 * Hook for AI-powered price predictions
 */
export const usePricePredictions = (propertyData: PropertyFeatures | null) => {
  const { toast } = useToast();
  const [predictionHistory, setPredictionHistory] = useState<PricePrediction[]>([]);

  const {
    data: prediction,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['price-prediction', propertyData],
    queryFn: () => propertyData ? pricePredictionService.predictPrice(propertyData) : null,
    enabled: !!propertyData,
    staleTime: 30 * 60 * 1000, // 30 minutes
    retry: 2
  });

  const {
    data: historicalTrends,
    isLoading: trendsLoading
  } = useQuery({
    queryKey: ['price-trends', propertyData?.location, propertyData?.property_type],
    queryFn: () => propertyData ? 
      pricePredictionService.getHistoricalTrends(propertyData.location, propertyData.property_type, 12) : null,
    enabled: !!propertyData,
    staleTime: 60 * 60 * 1000, // 1 hour
  });

  const analyzePriceFactorsMutation = useMutation({
    mutationFn: (propertyId: string) => pricePredictionService.analyzePriceFactors(propertyId),
    onSuccess: (analysis) => {
      toast({
        title: "Price Analysis Complete",
        description: `Property is ${analysis.market_position} market price.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Analysis Failed",
        description: error.message || "Failed to analyze price factors.",
        variant: "destructive",
      });
    }
  });

  const analyzePriceFactors = useCallback(
    (propertyId: string) => {
      analyzePriceFactorsMutation.mutate(propertyId);
    },
    [analyzePriceFactorsMutation]
  );

  const savePrediction = useCallback(
    (prediction: PricePrediction) => {
      setPredictionHistory(prev => [prediction, ...prev.slice(0, 9)]); // Keep last 10
    },
    []
  );

  return {
    prediction,
    historicalTrends: historicalTrends || [],
    predictionHistory,
    priceAnalysis: analyzePriceFactorsMutation.data,
    isLoading,
    trendsLoading,
    error,
    refetch,
    analyzePriceFactors,
    savePrediction,
    isAnalyzing: analyzePriceFactorsMutation.isPending
  };
};

/**
 * Hook for market analysis and insights
 */
export const useMarketAnalysis = (location: string | null, propertyType?: string) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: marketInsights,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['market-insights', location, propertyType],
    queryFn: () => location ? marketAnalyticsService.getEnhancedMarketInsights(location, propertyType) : null,
    enabled: !!location,
    staleTime: 30 * 60 * 1000, // 30 minutes
    retry: 2
  });

  const {
    data: investmentOpportunities,
    isLoading: opportunitiesLoading
  } = useQuery({
    queryKey: ['investment-opportunities', location],
    queryFn: () => location ? marketAnalyticsService.identifyInvestmentOpportunities({
      locations: [location],
      min_roi: 0.08,
      risk_tolerance: 'medium'
    }) : null,
    enabled: !!location,
    staleTime: 60 * 60 * 1000, // 1 hour
  });

  const performCMAMutation = useMutation({
    mutationFn: (propertyId: string) => marketAnalyticsService.performCMA(propertyId),
    onSuccess: (cma) => {
      toast({
        title: "CMA Complete",
        description: `Property valued at ₦${cma.subject_property.estimated_value.toLocaleString()}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "CMA Failed",
        description: error.message || "Failed to perform comparative market analysis.",
        variant: "destructive",
      });
    }
  });

  const generateDemandForecastMutation = useMutation({
    mutationFn: ({ location, propertyType, months }: {
      location: string;
      propertyType: string;
      months: number;
    }) => marketAnalyticsService.generateDemandForecast(location, propertyType, months),
    onSuccess: (forecast) => {
      toast({
        title: "Forecast Generated",
        description: `Demand trend: ${forecast.demand_trend} (${forecast.demand_change_percentage}%)`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Forecast Failed",
        description: error.message || "Failed to generate demand forecast.",
        variant: "destructive",
      });
    }
  });

  const performCMA = useCallback(
    (propertyId: string) => {
      performCMAMutation.mutate(propertyId);
    },
    [performCMAMutation]
  );

  const generateDemandForecast = useCallback(
    (propertyType: string, months: number = 12) => {
      if (!location) return;
      generateDemandForecastMutation.mutate({ location, propertyType, months });
    },
    [location, generateDemandForecastMutation]
  );

  return {
    marketInsights,
    investmentOpportunities: investmentOpportunities || [],
    cmaResult: performCMAMutation.data,
    demandForecast: generateDemandForecastMutation.data,
    isLoading,
    opportunitiesLoading,
    error,
    refetch,
    performCMA,
    generateDemandForecast,
    isPerformingCMA: performCMAMutation.isPending,
    isGeneratingForecast: generateDemandForecastMutation.isPending
  };
};

/**
 * Hook for intelligent matching system
 */
export const useIntelligentMatching = (criteria: MatchingCriteria | null) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: propertyMatches,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['property-matches', criteria],
    queryFn: () => criteria ? intelligentMatchingService.findPropertyMatches(criteria, 20) : null,
    enabled: !!criteria,
    staleTime: 15 * 60 * 1000, // 15 minutes
    retry: 2
  });

  const calculateLeadScoreMutation = useMutation({
    mutationFn: (leadData: any) => intelligentMatchingService.calculateLeadScore(leadData),
    onSuccess: (leadScore) => {
      toast({
        title: "Lead Scored",
        description: `Lead quality: ${leadScore.quality_grade} (${leadScore.score}/100)`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Scoring Failed",
        description: error.message || "Failed to calculate lead score.",
        variant: "destructive",
      });
    }
  });

  const findAgentMatchesMutation = useMutation({
    mutationFn: ({ clientId, requirements }: {
      clientId: string;
      requirements: any;
    }) => intelligentMatchingService.findAgentMatches(clientId, requirements, 5),
    onSuccess: (matches) => {
      toast({
        title: "Agent Matches Found",
        description: `Found ${matches.length} compatible agents.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Matching Failed",
        description: error.message || "Failed to find agent matches.",
        variant: "destructive",
      });
    }
  });

  const calculateLeadScore = useCallback(
    (leadData: any) => {
      calculateLeadScoreMutation.mutate(leadData);
    },
    [calculateLeadScoreMutation]
  );

  const findAgentMatches = useCallback(
    (clientId: string, requirements: any) => {
      findAgentMatchesMutation.mutate({ clientId, requirements });
    },
    [findAgentMatchesMutation]
  );

  const updateCriteria = useCallback(
    (newCriteria: Partial<MatchingCriteria>) => {
      if (!criteria) return;
      const updatedCriteria = { ...criteria, ...newCriteria };
      queryClient.setQueryData(['property-matches', updatedCriteria], null);
      queryClient.invalidateQueries(['property-matches', updatedCriteria]);
    },
    [criteria, queryClient]
  );

  return {
    propertyMatches: propertyMatches || [],
    leadScore: calculateLeadScoreMutation.data,
    agentMatches: findAgentMatchesMutation.data || [],
    isLoading,
    error,
    refetch,
    calculateLeadScore,
    findAgentMatches,
    updateCriteria,
    isCalculatingScore: calculateLeadScoreMutation.isPending,
    isFindingAgents: findAgentMatchesMutation.isPending
  };
};
