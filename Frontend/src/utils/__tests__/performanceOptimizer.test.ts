/**
 * Test Suite for Performance Optimization Utilities
 *
 * Tests for caching, pagination, debouncing, and other performance utilities
 * following <PERSON>'s testing patterns.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import {
  CacheManager,
  createPaginationResult,
  useDebounce,
  useThrottle,
  useVirtualScrolling,
  useOptimizedFetch,
  useIntersectionObserver,
  performanceMonitor,
} from '../performanceOptimizer';

describe('CacheManager', () => {
  let cache: CacheManager;

  beforeEach(() => {
    cache = CacheManager.getInstance();
    cache.clear();
  });

  afterEach(() => {
    cache.clear();
  });

  describe('Singleton Pattern', () => {
    it('returns the same instance', () => {
      const instance1 = CacheManager.getInstance();
      const instance2 = CacheManager.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Basic Operations', () => {
    it('stores and retrieves data', () => {
      const testData = { id: 1, name: 'test' };
      cache.set('test-key', testData);

      const retrieved = cache.get('test-key');
      expect(retrieved).toEqual(testData);
    });

    it('returns null for non-existent keys', () => {
      const result = cache.get('non-existent');
      expect(result).toBeNull();
    });

    it('respects TTL and expires data', async () => {
      const testData = { id: 1, name: 'test' };
      cache.set('test-key', testData, 100); // 100ms TTL

      expect(cache.get('test-key')).toEqual(testData);

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150));

      expect(cache.get('test-key')).toBeNull();
    });

    it('invalidates specific keys', () => {
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');

      cache.invalidate('key1');

      expect(cache.get('key1')).toBeNull();
      expect(cache.get('key2')).toBe('value2');
    });

    it('invalidates keys by pattern', () => {
      cache.set('user-1', 'user1');
      cache.set('user-2', 'user2');
      cache.set('post-1', 'post1');

      cache.invalidatePattern('^user-');

      expect(cache.get('user-1')).toBeNull();
      expect(cache.get('user-2')).toBeNull();
      expect(cache.get('post-1')).toBe('post1');
    });

    it('clears all data', () => {
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');

      cache.clear();

      expect(cache.get('key1')).toBeNull();
      expect(cache.get('key2')).toBeNull();
    });

    it('provides cache statistics', () => {
      cache.set('key1', 'value1');
      cache.set('key2', 'value2');

      const stats = cache.getStats();

      expect(stats.size).toBe(2);
      expect(stats.keys).toContain('key1');
      expect(stats.keys).toContain('key2');
    });
  });
});

describe('Pagination Utilities', () => {
  describe('createPaginationResult', () => {
    it('creates correct pagination result', () => {
      const data = [1, 2, 3, 4, 5];
      const result = createPaginationResult(data, 2, 3, 10);

      expect(result.data).toEqual(data);
      expect(result.pagination).toEqual({
        page: 2,
        limit: 3,
        total: 10,
      });
      expect(result.hasNextPage).toBe(true);
      expect(result.hasPreviousPage).toBe(true);
      expect(result.totalPages).toBe(4);
    });

    it('handles first page correctly', () => {
      const data = [1, 2, 3];
      const result = createPaginationResult(data, 1, 3, 10);

      expect(result.hasNextPage).toBe(true);
      expect(result.hasPreviousPage).toBe(false);
    });

    it('handles last page correctly', () => {
      const data = [10];
      const result = createPaginationResult(data, 4, 3, 10);

      expect(result.hasNextPage).toBe(false);
      expect(result.hasPreviousPage).toBe(true);
    });

    it('handles single page correctly', () => {
      const data = [1, 2];
      const result = createPaginationResult(data, 1, 5, 2);

      expect(result.hasNextPage).toBe(false);
      expect(result.hasPreviousPage).toBe(false);
      expect(result.totalPages).toBe(1);
    });
  });
});

describe('React Hooks', () => {
  describe('useDebounce', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('debounces value changes', () => {
      const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
        initialProps: { value: 'initial', delay: 500 },
      });

      expect(result.current).toBe('initial');

      // Change value multiple times quickly
      rerender({ value: 'change1', delay: 500 });
      rerender({ value: 'change2', delay: 500 });
      rerender({ value: 'final', delay: 500 });

      // Value should still be initial
      expect(result.current).toBe('initial');

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(500);
      });

      // Now value should be updated to the final value
      expect(result.current).toBe('final');
    });

    it('cancels previous timeout on new changes', () => {
      const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
        initialProps: { value: 'initial', delay: 500 },
      });

      rerender({ value: 'change1', delay: 500 });

      // Advance time partially
      act(() => {
        vi.advanceTimersByTime(300);
      });

      rerender({ value: 'change2', delay: 500 });

      // Advance remaining time from first change
      act(() => {
        vi.advanceTimersByTime(200);
      });

      // Should still be initial because timeout was cancelled
      expect(result.current).toBe('initial');

      // Advance full delay for second change
      act(() => {
        vi.advanceTimersByTime(300);
      });

      expect(result.current).toBe('change2');
    });
  });

  describe('useThrottle', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('throttles function calls', () => {
      const mockFn = vi.fn();
      const { result } = renderHook(() => useThrottle(mockFn, 1000));

      const throttledFn = result.current;

      // Call multiple times quickly
      throttledFn('call1');
      throttledFn('call2');
      throttledFn('call3');

      // Only first call should execute
      expect(mockFn).toHaveBeenCalledTimes(1);
      expect(mockFn).toHaveBeenCalledWith('call1');

      // Advance time
      act(() => {
        vi.advanceTimersByTime(1000);
      });

      // Now next call should work
      throttledFn('call4');
      expect(mockFn).toHaveBeenCalledTimes(2);
      expect(mockFn).toHaveBeenCalledWith('call4');
    });
  });

  describe('useVirtualScrolling', () => {
    it('calculates visible items correctly', () => {
      const items = Array.from({ length: 100 }, (_, i) => `item-${i}`);
      const { result } = renderHook(() => useVirtualScrolling(items, 50, 300));

      expect(result.current.startIndex).toBe(0);
      expect(result.current.endIndex).toBe(7); // Math.ceil(300/50) + 1
      expect(result.current.items).toHaveLength(7);
      expect(result.current.totalHeight).toBe(5000); // 100 * 50
      expect(result.current.offsetY).toBe(0);
    });

    it('updates visible items on scroll', () => {
      const items = Array.from({ length: 100 }, (_, i) => `item-${i}`);
      const { result } = renderHook(() => useVirtualScrolling(items, 50, 300));

      // Simulate scroll
      const mockEvent = {
        currentTarget: { scrollTop: 250 },
      } as React.UIEvent<HTMLDivElement>;

      act(() => {
        result.current.handleScroll(mockEvent);
      });

      expect(result.current.startIndex).toBe(5); // Math.floor(250/50)
      expect(result.current.offsetY).toBe(250); // 5 * 50
    });
  });

  describe('useOptimizedFetch', () => {
    let cache: CacheManager;

    beforeEach(() => {
      cache = CacheManager.getInstance();
      cache.clear();
      vi.useFakeTimers();
    });

    afterEach(() => {
      cache.clear();
      vi.useRealTimers();
    });

    it('fetches data and caches result', async () => {
      const mockFetch = vi.fn().mockResolvedValue('test-data');

      const { result } = renderHook(() =>
        useOptimizedFetch(mockFetch, 'test-key', [], { ttl: 1000 })
      );

      expect(result.current.loading).toBe(true);
      expect(result.current.data).toBeNull();

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.loading).toBe(false);
      expect(result.current.data).toBe('test-data');
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // Verify data is cached
      expect(cache.get('test-key')).toBe('test-data');
    });

    it('uses cached data on subsequent renders', async () => {
      const mockFetch = vi.fn().mockResolvedValue('test-data');

      // Pre-populate cache
      cache.set('test-key', 'cached-data');

      const { result } = renderHook(() =>
        useOptimizedFetch(mockFetch, 'test-key', [], { ttl: 1000 })
      );

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.data).toBe('cached-data');
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('retries on failure', async () => {
      const mockFetch = vi
        .fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValue('success');

      const { result } = renderHook(() =>
        useOptimizedFetch(mockFetch, 'test-key', [], {
          retryAttempts: 3,
          retryDelay: 100,
        })
      );

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.data).toBe('success');
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('handles fetch errors', async () => {
      const mockFetch = vi.fn().mockRejectedValue(new Error('Fetch failed'));

      const { result } = renderHook(() =>
        useOptimizedFetch(mockFetch, 'test-key', [], { retryAttempts: 1 })
      );

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.loading).toBe(false);
      expect(result.current.data).toBeNull();
      expect(result.current.error).toBeInstanceOf(Error);
    });

    it('can be disabled', async () => {
      const mockFetch = vi.fn().mockResolvedValue('test-data');

      const { result } = renderHook(() =>
        useOptimizedFetch(mockFetch, 'test-key', [], { enabled: false })
      );

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.data).toBeNull();
      expect(mockFetch).not.toHaveBeenCalled();
    });
  });

  describe('useIntersectionObserver', () => {
    let mockObserver: any;

    beforeEach(() => {
      mockObserver = {
        observe: vi.fn(),
        disconnect: vi.fn(),
      };

      global.IntersectionObserver = vi.fn().mockImplementation(callback => {
        mockObserver.callback = callback;
        return mockObserver;
      });
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    it('creates intersection observer', () => {
      const { result } = renderHook(() => useIntersectionObserver());

      expect(result.current.elementRef).toBeDefined();
      expect(result.current.isIntersecting).toBe(false);
      expect(result.current.entry).toBeNull();
    });

    it('observes element when ref is set', () => {
      const { result } = renderHook(() => useIntersectionObserver());

      const mockElement = document.createElement('div');

      act(() => {
        result.current.elementRef.current = mockElement;
      });

      expect(global.IntersectionObserver).toHaveBeenCalled();
      expect(mockObserver.observe).toHaveBeenCalledWith(mockElement);
    });

    it('updates intersection state', () => {
      const { result } = renderHook(() => useIntersectionObserver());

      const mockElement = document.createElement('div');

      act(() => {
        result.current.elementRef.current = mockElement;
      });

      const mockEntry = {
        isIntersecting: true,
        target: mockElement,
      };

      act(() => {
        mockObserver.callback([mockEntry]);
      });

      expect(result.current.isIntersecting).toBe(true);
      expect(result.current.entry).toEqual(mockEntry);
    });
  });
});

describe('Performance Monitor', () => {
  beforeEach(() => {
    vi.spyOn(performance, 'mark').mockImplementation(() => {});
    vi.spyOn(performance, 'measure').mockImplementation(() => {});
    vi.spyOn(performance, 'getEntriesByName').mockReturnValue([
      { duration: 123.45 } as PerformanceEntry,
    ]);
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Timing Functions', () => {
    it('starts and ends timing correctly', () => {
      performanceMonitor.startTiming('test-operation');
      expect(performance.mark).toHaveBeenCalledWith('test-operation-start');

      const duration = performanceMonitor.endTiming('test-operation');
      expect(performance.mark).toHaveBeenCalledWith('test-operation-end');
      expect(performance.measure).toHaveBeenCalledWith(
        'test-operation',
        'test-operation-start',
        'test-operation-end'
      );
      expect(duration).toBe(123.45);
    });

    it('measures component lifecycle', () => {
      const monitor = performanceMonitor.measureComponent('TestComponent');

      monitor.onMount();
      expect(performance.mark).toHaveBeenCalledWith('TestComponent-mount-start');

      monitor.onUnmount();
      expect(performance.mark).toHaveBeenCalledWith('TestComponent-mount-end');
      expect(console.log).toHaveBeenCalledWith('TestComponent mount time: 123.45ms');
    });

    it('measures function execution', () => {
      const testFn = vi.fn().mockReturnValue('result');
      const measuredFn = performanceMonitor.measureFunction(testFn, 'test-function');

      const result = measuredFn('arg1', 'arg2');

      expect(result).toBe('result');
      expect(testFn).toHaveBeenCalledWith('arg1', 'arg2');
      expect(console.log).toHaveBeenCalledWith('test-function execution time: 123.45ms');
    });

    it('measures async function execution', async () => {
      const testFn = vi.fn().mockResolvedValue('async-result');
      const measuredFn = performanceMonitor.measureFunction(testFn, 'async-function');

      const result = await measuredFn('arg1');

      expect(result).toBe('async-result');
      expect(testFn).toHaveBeenCalledWith('arg1');
      expect(console.log).toHaveBeenCalledWith('async-function execution time: 123.45ms');
    });
  });
});
