import React from 'react';

const TestComponent = () => {
  return (
    <div style={{ padding: '20px', backgroundColor: '#f0f0f0', margin: '20px' }}>
      <h1 style={{ color: '#333', fontSize: '24px' }}>PHCityRent Test Component</h1>
      <p style={{ color: '#666', fontSize: '16px' }}>
        If you can see this, React is working correctly!
      </p>
      <div style={{ marginTop: '20px' }}>
        <button 
          style={{ 
            padding: '10px 20px', 
            backgroundColor: '#007bff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
          onClick={() => alert('Button clicked! React is working!')}
        >
          Test Button
        </button>
      </div>
    </div>
  );
};

export default TestComponent;
