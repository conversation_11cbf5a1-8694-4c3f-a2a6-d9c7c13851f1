import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import AuthModal from '@/components/auth/AuthModal';

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('AuthModal Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('renders sign in form by default', () => {
    render(
      <TestWrapper>
        <AuthModal isOpen={true} onClose={jest.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText(/sign in/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
  });

  it('switches to sign up form when toggle is clicked', async () => {
    render(
      <TestWrapper>
        <AuthModal isOpen={true} onClose={jest.fn()} />
      </TestWrapper>
    );

    const signUpToggle = screen.getByText(/don't have an account/i);
    fireEvent.click(signUpToggle);

    await waitFor(() => {
      expect(screen.getByText(/create account/i)).toBeInTheDocument();
    });
  });

  it('validates email format', async () => {
    render(
      <TestWrapper>
        <AuthModal isOpen={true} onClose={jest.fn()} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText(/email/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
    });
  });

  it('validates password requirements', async () => {
    render(
      <TestWrapper>
        <AuthModal isOpen={true} onClose={jest.fn()} />
      </TestWrapper>
    );

    // Switch to sign up
    const signUpToggle = screen.getByText(/don't have an account/i);
    fireEvent.click(signUpToggle);

    await waitFor(() => {
      const passwordInput = screen.getByLabelText(/^password$/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      fireEvent.change(passwordInput, { target: { value: '123' } });
      fireEvent.click(submitButton);
    });

    await waitFor(() => {
      expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
    });
  });

  it('calls onClose when close button is clicked', () => {
    const mockOnClose = jest.fn();
    
    render(
      <TestWrapper>
        <AuthModal isOpen={true} onClose={mockOnClose} />
      </TestWrapper>
    );

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('does not render when isOpen is false', () => {
    render(
      <TestWrapper>
        <AuthModal isOpen={false} onClose={jest.fn()} />
      </TestWrapper>
    );

    expect(screen.queryByText(/sign in/i)).not.toBeInTheDocument();
  });

  it('handles form submission with valid data', async () => {
    render(
      <TestWrapper>
        <AuthModal isOpen={true} onClose={jest.fn()} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    // Since we're using mock services, we expect the form to handle submission
    await waitFor(() => {
      // Check for loading state or success message
      expect(submitButton).toBeInTheDocument();
    });
  });
});
