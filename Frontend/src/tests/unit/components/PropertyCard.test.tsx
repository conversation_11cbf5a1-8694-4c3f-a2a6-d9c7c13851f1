// =====================================================
// PROPERTY CARD COMPONENT UNIT TESTS
// Comprehensive tests for PropertyCard component
// =====================================================

import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { customRender, mockProperty, mockUser } from '@tests/setup/test-utils';
import PropertyCard from '@/components/properties/PropertyCard';

// Mock the router
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

// Mock the image optimization hook
jest.mock('@/hooks/useImageOptimization', () => ({
  useImageOptimization: () => ({
    optimizeImage: jest.fn(),
    isOptimizing: false,
    optimizedImages: new Map()
  })
}));

describe('PropertyCard Component', () => {
  const defaultProps = {
    property: mockProperty,
    onFavorite: jest.fn(),
    onContact: jest.fn(),
    isFavorited: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render property information correctly', () => {
      customRender(<PropertyCard {...defaultProps} />);

      expect(screen.getByText(mockProperty.title)).toBeInTheDocument();
      expect(screen.getByText(mockProperty.description)).toBeInTheDocument();
      expect(screen.getByText(`₦${mockProperty.price.toLocaleString()}`)).toBeInTheDocument();
      expect(screen.getByText(mockProperty.location)).toBeInTheDocument();
      expect(screen.getByText(`${mockProperty.bedrooms} bed`)).toBeInTheDocument();
      expect(screen.getByText(`${mockProperty.bathrooms} bath`)).toBeInTheDocument();
    });

    it('should render property type badge', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const typeBadge = screen.getByText(mockProperty.property_type);
      expect(typeBadge).toBeInTheDocument();
      expect(typeBadge).toHaveClass('badge');
    });

    it('should render status badge', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const statusBadge = screen.getByText(mockProperty.status);
      expect(statusBadge).toBeInTheDocument();
    });

    it('should render property images', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const images = screen.getAllByRole('img');
      expect(images).toHaveLength(mockProperty.images.length);
      
      images.forEach((img, index) => {
        expect(img).toHaveAttribute('alt', `${mockProperty.title} - Image ${index + 1}`);
      });
    });

    it('should render placeholder when no images available', () => {
      const propertyWithoutImages = { ...mockProperty, images: [] };
      customRender(<PropertyCard {...defaultProps} property={propertyWithoutImages} />);

      const placeholder = screen.getByTestId('image-placeholder');
      expect(placeholder).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    it('should navigate to property details when card is clicked', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const card = screen.getByTestId('property-card');
      fireEvent.click(card);

      expect(mockNavigate).toHaveBeenCalledWith(`/properties/${mockProperty.id}`);
    });

    it('should call onFavorite when favorite button is clicked', () => {
      const onFavorite = jest.fn();
      customRender(<PropertyCard {...defaultProps} onFavorite={onFavorite} />);

      const favoriteButton = screen.getByTestId('favorite-button');
      fireEvent.click(favoriteButton);

      expect(onFavorite).toHaveBeenCalledWith(mockProperty.id);
    });

    it('should call onContact when contact button is clicked', () => {
      const onContact = jest.fn();
      customRender(<PropertyCard {...defaultProps} onContact={onContact} />);

      const contactButton = screen.getByTestId('contact-button');
      fireEvent.click(contactButton);

      expect(onContact).toHaveBeenCalledWith(mockProperty);
    });

    it('should prevent navigation when favorite button is clicked', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const favoriteButton = screen.getByTestId('favorite-button');
      fireEvent.click(favoriteButton);

      expect(mockNavigate).not.toHaveBeenCalled();
    });

    it('should prevent navigation when contact button is clicked', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const contactButton = screen.getByTestId('contact-button');
      fireEvent.click(contactButton);

      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  describe('Favorite State', () => {
    it('should show filled heart when property is favorited', () => {
      customRender(<PropertyCard {...defaultProps} isFavorited={true} />);

      const favoriteButton = screen.getByTestId('favorite-button');
      expect(favoriteButton).toHaveClass('favorited');
    });

    it('should show empty heart when property is not favorited', () => {
      customRender(<PropertyCard {...defaultProps} isFavorited={false} />);

      const favoriteButton = screen.getByTestId('favorite-button');
      expect(favoriteButton).not.toHaveClass('favorited');
    });
  });

  describe('Responsive Design', () => {
    it('should apply mobile styles on small screens', () => {
      // Mock window.innerWidth
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      customRender(<PropertyCard {...defaultProps} />);

      const card = screen.getByTestId('property-card');
      expect(card).toHaveClass('mobile-card');
    });

    it('should apply desktop styles on large screens', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      });

      customRender(<PropertyCard {...defaultProps} />);

      const card = screen.getByTestId('property-card');
      expect(card).toHaveClass('desktop-card');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const card = screen.getByRole('article');
      expect(card).toHaveAttribute('aria-label', `Property: ${mockProperty.title}`);

      const favoriteButton = screen.getByTestId('favorite-button');
      expect(favoriteButton).toHaveAttribute('aria-label', 'Add to favorites');

      const contactButton = screen.getByTestId('contact-button');
      expect(contactButton).toHaveAttribute('aria-label', 'Contact landlord');
    });

    it('should be keyboard navigable', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const card = screen.getByTestId('property-card');
      const favoriteButton = screen.getByTestId('favorite-button');
      const contactButton = screen.getByTestId('contact-button');

      expect(card).toHaveAttribute('tabIndex', '0');
      expect(favoriteButton).toHaveAttribute('tabIndex', '0');
      expect(contactButton).toHaveAttribute('tabIndex', '0');
    });

    it('should handle keyboard events', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const card = screen.getByTestId('property-card');
      fireEvent.keyDown(card, { key: 'Enter' });

      expect(mockNavigate).toHaveBeenCalledWith(`/properties/${mockProperty.id}`);
    });
  });

  describe('Loading States', () => {
    it('should show loading skeleton when property is undefined', () => {
      customRender(<PropertyCard {...defaultProps} property={undefined} />);

      const skeleton = screen.getByTestId('property-card-skeleton');
      expect(skeleton).toBeInTheDocument();
    });

    it('should show image loading state', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const images = screen.getAllByRole('img');
      images.forEach(img => {
        fireEvent.load(img);
      });

      // Should not show loading indicators after images load
      expect(screen.queryByTestId('image-loading')).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing property data gracefully', () => {
      const incompleteProperty = {
        id: 'test-id',
        title: 'Test Property'
        // Missing other required fields
      };

      customRender(<PropertyCard {...defaultProps} property={incompleteProperty as any} />);

      expect(screen.getByText('Test Property')).toBeInTheDocument();
      // Should not crash and should render what's available
    });

    it('should handle image loading errors', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const images = screen.getAllByRole('img');
      fireEvent.error(images[0]);

      // Should show fallback image or placeholder
      expect(screen.getByTestId('image-error-fallback')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('should lazy load images', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const images = screen.getAllByRole('img');
      images.forEach(img => {
        expect(img).toHaveAttribute('loading', 'lazy');
      });
    });

    it('should optimize image sizes', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const images = screen.getAllByRole('img');
      images.forEach(img => {
        expect(img).toHaveAttribute('sizes');
        expect(img).toHaveAttribute('srcset');
      });
    });
  });

  describe('Animation', () => {
    it('should apply hover animations', async () => {
      customRender(<PropertyCard {...defaultProps} />);

      const card = screen.getByTestId('property-card');
      fireEvent.mouseEnter(card);

      await waitFor(() => {
        expect(card).toHaveClass('hover-animated');
      });

      fireEvent.mouseLeave(card);

      await waitFor(() => {
        expect(card).not.toHaveClass('hover-animated');
      });
    });

    it('should apply click animations', async () => {
      customRender(<PropertyCard {...defaultProps} />);

      const favoriteButton = screen.getByTestId('favorite-button');
      fireEvent.click(favoriteButton);

      await waitFor(() => {
        expect(favoriteButton).toHaveClass('click-animated');
      });
    });
  });

  describe('Context Menu', () => {
    it('should show context menu on right click', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const card = screen.getByTestId('property-card');
      fireEvent.contextMenu(card);

      expect(screen.getByTestId('property-context-menu')).toBeInTheDocument();
    });

    it('should hide context menu when clicking outside', () => {
      customRender(<PropertyCard {...defaultProps} />);

      const card = screen.getByTestId('property-card');
      fireEvent.contextMenu(card);

      expect(screen.getByTestId('property-context-menu')).toBeInTheDocument();

      fireEvent.click(document.body);

      expect(screen.queryByTestId('property-context-menu')).not.toBeInTheDocument();
    });
  });

  describe('Share Functionality', () => {
    it('should show share button when Web Share API is available', () => {
      // Mock Web Share API
      Object.assign(navigator, {
        share: jest.fn()
      });

      customRender(<PropertyCard {...defaultProps} />);

      expect(screen.getByTestId('share-button')).toBeInTheDocument();
    });

    it('should call Web Share API when share button is clicked', () => {
      const mockShare = jest.fn();
      Object.assign(navigator, {
        share: mockShare
      });

      customRender(<PropertyCard {...defaultProps} />);

      const shareButton = screen.getByTestId('share-button');
      fireEvent.click(shareButton);

      expect(mockShare).toHaveBeenCalledWith({
        title: mockProperty.title,
        text: mockProperty.description,
        url: `${window.location.origin}/properties/${mockProperty.id}`
      });
    });
  });
});
