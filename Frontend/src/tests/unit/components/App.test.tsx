import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import App from '../../../App';

// Mock all the page components
vi.mock('../../../pages/Index', () => ({
  default: () => <div data-testid="index-page">Index Page</div>,
}));

vi.mock('../../../pages/Properties', () => ({
  default: () => <div data-testid="properties-page">Properties Page</div>,
}));

vi.mock('../../../pages/PropertyDetail', () => ({
  default: () => <div data-testid="property-detail-page">Property Detail Page</div>,
}));

vi.mock('../../../pages/Auth', () => ({
  default: () => <div data-testid="auth-page">Auth Page</div>,
}));

vi.mock('../../../pages/AdminDashboard', () => ({
  default: () => <div data-testid="admin-dashboard-page">Admin Dashboard Page</div>,
}));

vi.mock('../../../pages/NotFound', () => ({
  default: () => <div data-testid="not-found-page">Not Found Page</div>,
}));

// Mock auth context
vi.mock('../../../hooks/useAuth', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-provider">{children}</div>
  ),
}));

// Mock other providers
vi.mock('../../../components/localization/LanguageManager', () => ({
  TranslationProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="translation-provider">{children}</div>
  ),
}));

// Mock UI components
vi.mock('../../../components/ui/toaster', () => ({
  Toaster: () => <div data-testid="toaster" />,
}));

vi.mock('../../../components/ui/sonner', () => ({
  Toaster: () => <div data-testid="sonner" />,
}));

vi.mock('../../../components/ui/tooltip', () => ({
  TooltipProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tooltip-provider">{children}</div>
  ),
}));

// Mock error boundaries
vi.mock('../../../components/error/ErrorBoundary', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="error-boundary">{children}</div>
  ),
}));

vi.mock('../../../components/error/AsyncErrorBoundary', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="async-error-boundary">{children}</div>
  ),
}));

vi.mock('../../../components/error/PageErrorBoundary', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="page-error-boundary">{children}</div>
  ),
}));

// Mock protected route
vi.mock('../../../components/auth/ProtectedRoute', () => ({
  AdminRoute: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="admin-route">{children}</div>
  ),
}));

const renderAppWithRouter = (initialEntries: string[] = ['/']) => {
  return render(
    <MemoryRouter initialEntries={initialEntries}>
      <App />
    </MemoryRouter>
  );
};

describe('App Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Routing', () => {
    it('renders the index page on root route', async () => {
      renderAppWithRouter(['/']);

      await waitFor(() => {
        expect(screen.getByTestId('index-page')).toBeInTheDocument();
      });
    });

    it('renders the properties page on /properties route', async () => {
      renderAppWithRouter(['/properties']);

      await waitFor(() => {
        expect(screen.getByTestId('properties-page')).toBeInTheDocument();
      });
    });

    it('renders the property detail page on /properties/:id route', async () => {
      renderAppWithRouter(['/properties/123']);

      await waitFor(() => {
        expect(screen.getByTestId('property-detail-page')).toBeInTheDocument();
      });
    });

    it('renders the auth page on /auth route', async () => {
      renderAppWithRouter(['/auth']);

      await waitFor(() => {
        expect(screen.getByTestId('auth-page')).toBeInTheDocument();
      });
    });

    it('renders the admin dashboard with protection on /admin route', async () => {
      renderAppWithRouter(['/admin']);

      await waitFor(() => {
        expect(screen.getByTestId('admin-route')).toBeInTheDocument();
        expect(screen.getByTestId('page-error-boundary')).toBeInTheDocument();
        expect(screen.getByTestId('admin-dashboard-page')).toBeInTheDocument();
      });
    });

    it('renders the not found page for invalid routes', async () => {
      renderAppWithRouter(['/invalid-route']);

      await waitFor(() => {
        expect(screen.getByTestId('not-found-page')).toBeInTheDocument();
      });
    });
  });

  describe('Providers and Context', () => {
    it('renders all required providers', async () => {
      renderAppWithRouter();

      await waitFor(() => {
        expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
        expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
        expect(screen.getByTestId('async-error-boundary')).toBeInTheDocument();
        expect(screen.getByTestId('translation-provider')).toBeInTheDocument();
        expect(screen.getByTestId('tooltip-provider')).toBeInTheDocument();
      });
    });

    it('renders toast components', async () => {
      renderAppWithRouter();

      await waitFor(() => {
        expect(screen.getByTestId('toaster')).toBeInTheDocument();
        expect(screen.getByTestId('sonner')).toBeInTheDocument();
      });
    });
  });

  describe('Error Boundaries', () => {
    it('wraps the app with error boundary', async () => {
      renderAppWithRouter();

      await waitFor(() => {
        expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      });
    });

    it('wraps admin routes with page error boundary', async () => {
      renderAppWithRouter(['/admin']);

      await waitFor(() => {
        expect(screen.getByTestId('page-error-boundary')).toBeInTheDocument();
      });
    });
  });

  describe('Protected Routes', () => {
    it('wraps admin routes with AdminRoute protection', async () => {
      renderAppWithRouter(['/admin']);

      await waitFor(() => {
        expect(screen.getByTestId('admin-route')).toBeInTheDocument();
      });
    });

    it('wraps admin seed data route with AdminRoute protection', async () => {
      renderAppWithRouter(['/admin/seed-data']);

      await waitFor(() => {
        expect(screen.getByTestId('admin-route')).toBeInTheDocument();
      });
    });
  });

  describe('Route Coverage', () => {
    const routes = [
      { path: '/', testId: 'index-page' },
      { path: '/properties', testId: 'properties-page' },
      { path: '/auth', testId: 'auth-page' },
      { path: '/invalid', testId: 'not-found-page' },
    ];

    routes.forEach(({ path, testId }) => {
      it(`renders correct component for ${path}`, async () => {
        renderAppWithRouter([path]);

        await waitFor(() => {
          expect(screen.getByTestId(testId)).toBeInTheDocument();
        });
      });
    });
  });
});
