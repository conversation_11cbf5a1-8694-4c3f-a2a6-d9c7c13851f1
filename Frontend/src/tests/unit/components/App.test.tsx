import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import App from '../../../App';

// Mock all the page components
jest.mock('../../../pages/Index', () => {
  const MockIndex = () => <div data-testid="index-page">Index Page</div>;
  return { default: MockIndex };
});

jest.mock('../../../pages/Properties', () => {
  const MockProperties = () => <div data-testid="properties-page">Properties Page</div>;
  return { default: MockProperties };
});

jest.mock('../../../pages/PropertyDetail', () => {
  const MockPropertyDetail = () => (
    <div data-testid="property-detail-page">Property Detail Page</div>
  );
  return { default: MockPropertyDetail };
});

jest.mock('../../../pages/Auth', () => {
  const MockAuth = () => <div data-testid="auth-page">Auth Page</div>;
  return { default: MockAuth };
});

jest.mock('../../../pages/AdminDashboard', () => {
  const MockAdminDashboard = () => (
    <div data-testid="admin-dashboard-page">Admin Dashboard Page</div>
  );
  return { default: MockAdminDashboard };
});

jest.mock('../../../pages/NotFound', () => {
  const MockNotFound = () => <div data-testid="not-found-page">Not Found Page</div>;
  return { default: MockNotFound };
});

// Mock auth context
jest.mock('../../../hooks/useAuth', () => {
  const MockAuthProvider = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-provider">{children}</div>
  );
  return { AuthProvider: MockAuthProvider };
});

// Mock other providers
jest.mock('../../../components/localization/LanguageManager', () => {
  const MockTranslationProvider = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="translation-provider">{children}</div>
  );
  return { TranslationProvider: MockTranslationProvider };
});

// Mock UI components
jest.mock('../../../components/ui/toaster', () => {
  const MockToaster = () => <div data-testid="toaster" />;
  return { Toaster: MockToaster };
});

jest.mock('../../../components/ui/sonner', () => {
  const MockSonner = () => <div data-testid="sonner" />;
  return { Toaster: MockSonner };
});

jest.mock('../../../components/ui/tooltip', () => {
  const MockTooltipProvider = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tooltip-provider">{children}</div>
  );
  return { TooltipProvider: MockTooltipProvider };
});

// Mock error boundaries
jest.mock('../../../components/error/ErrorBoundary', () => {
  const MockErrorBoundary = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="error-boundary">{children}</div>
  );
  return { default: MockErrorBoundary };
});

jest.mock('../../../components/error/AsyncErrorBoundary', () => {
  const MockAsyncErrorBoundary = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="async-error-boundary">{children}</div>
  );
  return { default: MockAsyncErrorBoundary };
});

jest.mock('../../../components/error/PageErrorBoundary', () => {
  const MockPageErrorBoundary = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="page-error-boundary">{children}</div>
  );
  return { default: MockPageErrorBoundary };
});

// Mock protected route
jest.mock('../../../components/auth/ProtectedRoute', () => {
  const MockAdminRoute = ({ children }: { children: React.ReactNode }) => (
    <div data-testid="admin-route">{children}</div>
  );
  return { AdminRoute: MockAdminRoute };
});

const renderAppWithRouter = (initialEntries: string[] = ['/']) => {
  return render(
    <MemoryRouter initialEntries={initialEntries}>
      <App />
    </MemoryRouter>
  );
};

describe('App Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Routing', () => {
    it('renders the index page on root route', async () => {
      renderAppWithRouter(['/']);

      await waitFor(() => {
        expect(screen.getByTestId('index-page')).toBeInTheDocument();
      });
    });

    it('renders the properties page on /properties route', async () => {
      renderAppWithRouter(['/properties']);

      await waitFor(() => {
        expect(screen.getByTestId('properties-page')).toBeInTheDocument();
      });
    });

    it('renders the property detail page on /properties/:id route', async () => {
      renderAppWithRouter(['/properties/123']);

      await waitFor(() => {
        expect(screen.getByTestId('property-detail-page')).toBeInTheDocument();
      });
    });

    it('renders the auth page on /auth route', async () => {
      renderAppWithRouter(['/auth']);

      await waitFor(() => {
        expect(screen.getByTestId('auth-page')).toBeInTheDocument();
      });
    });

    it('renders the admin dashboard with protection on /admin route', async () => {
      renderAppWithRouter(['/admin']);

      await waitFor(() => {
        expect(screen.getByTestId('admin-route')).toBeInTheDocument();
        expect(screen.getByTestId('page-error-boundary')).toBeInTheDocument();
        expect(screen.getByTestId('admin-dashboard-page')).toBeInTheDocument();
      });
    });

    it('renders the not found page for invalid routes', async () => {
      renderAppWithRouter(['/invalid-route']);

      await waitFor(() => {
        expect(screen.getByTestId('not-found-page')).toBeInTheDocument();
      });
    });
  });

  describe('Providers and Context', () => {
    it('renders all required providers', async () => {
      renderAppWithRouter();

      await waitFor(() => {
        expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
        expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
        expect(screen.getByTestId('async-error-boundary')).toBeInTheDocument();
        expect(screen.getByTestId('translation-provider')).toBeInTheDocument();
        expect(screen.getByTestId('tooltip-provider')).toBeInTheDocument();
      });
    });

    it('renders toast components', async () => {
      renderAppWithRouter();

      await waitFor(() => {
        expect(screen.getByTestId('toaster')).toBeInTheDocument();
        expect(screen.getByTestId('sonner')).toBeInTheDocument();
      });
    });
  });

  describe('Error Boundaries', () => {
    it('wraps the app with error boundary', async () => {
      renderAppWithRouter();

      await waitFor(() => {
        expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      });
    });

    it('wraps admin routes with page error boundary', async () => {
      renderAppWithRouter(['/admin']);

      await waitFor(() => {
        expect(screen.getByTestId('page-error-boundary')).toBeInTheDocument();
      });
    });
  });

  describe('Protected Routes', () => {
    it('wraps admin routes with AdminRoute protection', async () => {
      renderAppWithRouter(['/admin']);

      await waitFor(() => {
        expect(screen.getByTestId('admin-route')).toBeInTheDocument();
      });
    });

    it('wraps admin seed data route with AdminRoute protection', async () => {
      renderAppWithRouter(['/admin/seed-data']);

      await waitFor(() => {
        expect(screen.getByTestId('admin-route')).toBeInTheDocument();
      });
    });
  });

  describe('Route Coverage', () => {
    const routes = [
      { path: '/', testId: 'index-page' },
      { path: '/properties', testId: 'properties-page' },
      { path: '/auth', testId: 'auth-page' },
      { path: '/invalid', testId: 'not-found-page' },
    ];

    routes.forEach(({ path, testId }) => {
      it(`renders correct component for ${path}`, async () => {
        renderAppWithRouter([path]);

        await waitFor(() => {
          expect(screen.getByTestId(testId)).toBeInTheDocument();
        });
      });
    });
  });
});
