// =====================================================
// VALIDATION SERVICE UNIT TESTS
// Comprehensive tests for input validation and security
// =====================================================

import { validationService } from '@/services/mockServices';
import { createMockFile } from '@tests/setup/test-utils';

describe('ValidationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Property Validation', () => {
    it('should validate valid property data', async () => {
      const validProperty = {
        title: 'Beautiful 3-Bedroom Apartment',
        description: 'A lovely apartment in the heart of Port Harcourt with modern amenities',
        price: 500000,
        location: 'GRA Phase 2, Port Harcourt',
        property_type: 'apartment',
        bedrooms: 3,
        bathrooms: 2,
        contact_phone: '+2348012345678',
        contact_email: '<EMAIL>'
      };

      const result = await validationService.validateProperty(validProperty);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitizedData).toBeDefined();
      expect(result.securityFlags).toHaveLength(0);
    });

    it('should reject property with invalid title', async () => {
      const invalidProperty = {
        title: 'Bad', // Too short
        description: 'A lovely apartment in the heart of Port Harcourt',
        price: 500000,
        location: 'GRA Phase 2, Port Harcourt',
        property_type: 'apartment',
        bedrooms: 3,
        bathrooms: 2,
        contact_phone: '+2348012345678',
        contact_email: '<EMAIL>'
      };

      const result = await validationService.validateProperty(invalidProperty);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('title: Title must be at least 5 characters');
    });

    it('should reject property with invalid price range', async () => {
      const invalidProperty = {
        title: 'Beautiful Apartment',
        description: 'A lovely apartment in the heart of Port Harcourt',
        price: 500, // Too low
        location: 'GRA Phase 2, Port Harcourt',
        property_type: 'apartment',
        bedrooms: 3,
        bathrooms: 2,
        contact_phone: '+2348012345678',
        contact_email: '<EMAIL>'
      };

      const result = await validationService.validateProperty(invalidProperty);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('price: Price must be at least ₦1,000');
    });

    it('should reject property with invalid phone number', async () => {
      const invalidProperty = {
        title: 'Beautiful Apartment',
        description: 'A lovely apartment in the heart of Port Harcourt',
        price: 500000,
        location: 'GRA Phase 2, Port Harcourt',
        property_type: 'apartment',
        bedrooms: 3,
        bathrooms: 2,
        contact_phone: '123456789', // Invalid format
        contact_email: '<EMAIL>'
      };

      const result = await validationService.validateProperty(invalidProperty);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('contact_phone: Invalid Nigerian phone number format');
    });
  });

  describe('User Validation', () => {
    it('should validate valid user data', async () => {
      const validUser = {
        email: '<EMAIL>',
        full_name: 'John Doe',
        phone: '+2348012345678',
        bio: 'A professional looking for a nice apartment',
        location: 'Port Harcourt'
      };

      const result = await validationService.validateUser(validUser);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitizedData).toBeDefined();
    });

    it('should reject user with invalid email', async () => {
      const invalidUser = {
        email: 'invalid-email',
        full_name: 'John Doe',
        phone: '+2348012345678'
      };

      const result = await validationService.validateUser(invalidUser);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('email: Invalid email format');
    });

    it('should reject user with invalid name characters', async () => {
      const invalidUser = {
        email: '<EMAIL>',
        full_name: 'John123 Doe!', // Invalid characters
        phone: '+2348012345678'
      };

      const result = await validationService.validateUser(invalidUser);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('full_name: Name contains invalid characters');
    });
  });

  describe('Message Validation', () => {
    it('should validate valid message data', async () => {
      const validMessage = {
        content: 'Hello, I am interested in this property',
        recipient_id: '123e4567-e89b-12d3-a456-************',
        conversation_id: '123e4567-e89b-12d3-a456-426614174001'
      };

      const result = await validationService.validateMessage(validMessage);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty message', async () => {
      const invalidMessage = {
        content: '',
        recipient_id: '123e4567-e89b-12d3-a456-************'
      };

      const result = await validationService.validateMessage(invalidMessage);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('content: Message cannot be empty');
    });

    it('should reject message with invalid UUID', async () => {
      const invalidMessage = {
        content: 'Hello there',
        recipient_id: 'invalid-uuid'
      };

      const result = await validationService.validateMessage(invalidMessage);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('recipient_id: Invalid recipient ID');
    });
  });

  describe('File Validation', () => {
    it('should validate valid image file', async () => {
      const validFile = createMockFile('test.jpg', 1024 * 1024, 'image/jpeg');
      const config = {
        allowedTypes: ['image/jpeg', 'image/png'],
        maxSize: 10 * 1024 * 1024,
        allowedExtensions: ['jpg', 'jpeg', 'png'],
        scanForMalware: false,
        checkMagicBytes: false
      };

      const result = await validationService.validateFile(validFile, config);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject file with invalid type', async () => {
      const invalidFile = createMockFile('test.exe', 1024, 'application/x-executable');
      const config = {
        allowedTypes: ['image/jpeg', 'image/png'],
        maxSize: 10 * 1024 * 1024,
        allowedExtensions: ['jpg', 'jpeg', 'png'],
        scanForMalware: false,
        checkMagicBytes: false
      };

      const result = await validationService.validateFile(invalidFile, config);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File type application/x-executable is not allowed');
      expect(result.securityFlags).toContainEqual(
        expect.objectContaining({
          type: 'malicious_file',
          severity: 'medium'
        })
      );
    });

    it('should reject file exceeding size limit', async () => {
      const largeFile = createMockFile('large.jpg', 20 * 1024 * 1024, 'image/jpeg');
      const config = {
        allowedTypes: ['image/jpeg'],
        maxSize: 10 * 1024 * 1024,
        allowedExtensions: ['jpg'],
        scanForMalware: false,
        checkMagicBytes: false
      };

      const result = await validationService.validateFile(largeFile, config);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File size exceeds maximum allowed size of 10485760 bytes');
    });
  });

  describe('Security Pattern Detection', () => {
    it('should detect XSS attempts', async () => {
      const xssProperty = {
        title: 'Beautiful Apartment',
        description: '<script>alert("xss")</script>Nice apartment',
        price: 500000,
        location: 'Port Harcourt',
        property_type: 'apartment',
        bedrooms: 3,
        bathrooms: 2,
        contact_phone: '+2348012345678',
        contact_email: '<EMAIL>'
      };

      const result = await validationService.validateProperty(xssProperty);

      expect(result.securityFlags).toContainEqual(
        expect.objectContaining({
          type: 'xss',
          severity: 'high',
          field: 'description'
        })
      );
    });

    it('should detect SQL injection attempts', async () => {
      const sqlInjectionProperty = {
        title: 'Beautiful Apartment',
        description: "Nice apartment'; DROP TABLE properties; --",
        price: 500000,
        location: 'Port Harcourt',
        property_type: 'apartment',
        bedrooms: 3,
        bathrooms: 2,
        contact_phone: '+2348012345678',
        contact_email: '<EMAIL>'
      };

      const result = await validationService.validateProperty(sqlInjectionProperty);

      expect(result.securityFlags).toContainEqual(
        expect.objectContaining({
          type: 'sql_injection',
          severity: 'critical',
          field: 'description'
        })
      );
    });

    it('should detect path traversal attempts', async () => {
      const pathTraversalProperty = {
        title: 'Beautiful Apartment',
        description: 'Nice apartment',
        price: 500000,
        location: '../../../etc/passwd',
        property_type: 'apartment',
        bedrooms: 3,
        bathrooms: 2,
        contact_phone: '+2348012345678',
        contact_email: '<EMAIL>'
      };

      const result = await validationService.validateProperty(pathTraversalProperty);

      expect(result.securityFlags).toContainEqual(
        expect.objectContaining({
          type: 'path_traversal',
          severity: 'high',
          field: 'location'
        })
      );
    });
  });

  describe('HTML Sanitization', () => {
    it('should sanitize HTML content', () => {
      const dirtyHtml = '<script>alert("xss")</script><p>Safe content</p><img src="x" onerror="alert(1)">';
      const cleanHtml = validationService.sanitizeHtml(dirtyHtml);

      expect(cleanHtml).not.toContain('<script>');
      expect(cleanHtml).not.toContain('onerror');
      expect(cleanHtml).toContain('<p>Safe content</p>');
    });

    it('should preserve allowed HTML tags', () => {
      const html = '<p>This is <strong>bold</strong> and <em>italic</em> text</p>';
      const cleanHtml = validationService.sanitizeHtml(html);

      expect(cleanHtml).toContain('<p>');
      expect(cleanHtml).toContain('<strong>');
      expect(cleanHtml).toContain('<em>');
    });

    it('should remove disallowed HTML tags', () => {
      const html = '<div><script>alert("xss")</script><p>Safe content</p></div>';
      const cleanHtml = validationService.sanitizeHtml(html);

      expect(cleanHtml).not.toContain('<div>');
      expect(cleanHtml).not.toContain('<script>');
      expect(cleanHtml).toContain('Safe content');
    });
  });

  describe('Configuration Management', () => {
    it('should update configuration', () => {
      const newConfig = {
        maxStringLength: 5000,
        enableXSSProtection: false
      };

      validationService.updateConfig(newConfig);
      const config = validationService.getConfig();

      expect(config.maxStringLength).toBe(5000);
      expect(config.enableXSSProtection).toBe(false);
    });

    it('should return current configuration', () => {
      const config = validationService.getConfig();

      expect(config).toHaveProperty('enableXSSProtection');
      expect(config).toHaveProperty('enableSQLInjectionProtection');
      expect(config).toHaveProperty('maxStringLength');
      expect(config).toHaveProperty('allowedHtmlTags');
    });
  });

  describe('API Parameter Validation', () => {
    it('should validate API parameters with schema', () => {
      const schema = validationService.validateApiParams;
      // This would test the schema validation functionality
      // Implementation depends on the specific schema structure
    });
  });

  describe('Error Handling', () => {
    it('should handle validation errors gracefully', async () => {
      // Test with malformed data that might cause exceptions
      const malformedData = {
        title: null,
        description: undefined,
        price: 'not-a-number'
      };

      const result = await validationService.validateProperty(malformedData as any);

      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(Array.isArray(result.errors)).toBe(true);
    });

    it('should handle file validation errors', async () => {
      const invalidFile = null as any;
      const config = {
        allowedTypes: ['image/jpeg'],
        maxSize: 1024,
        allowedExtensions: ['jpg'],
        scanForMalware: false,
        checkMagicBytes: false
      };

      const result = await validationService.validateFile(invalidFile, config);

      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
    });
  });
});
