import { MockDataService } from '../../../services/mockDataService';

describe('MockDataService', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  describe('Authentication', () => {
    it('should sign in with valid credentials', async () => {
      const result = await MockDataService.signIn('<EMAIL>', 'Admin123!@#');

      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(result.user?.email).toBe('<EMAIL>');
      expect(result.token).toBeDefined();
    });

    it('should fail sign in with invalid credentials', async () => {
      const result = await MockDataService.signIn('<EMAIL>', 'wrongpassword');

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should sign up new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'New User',
        role: 'tenant' as const,
      };

      const result = await MockDataService.signUp(userData);

      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(result.user?.email).toBe(userData.email);
      expect(result.user?.fullName).toBe(userData.fullName);
    });

    it('should sign out user', async () => {
      const result = await MockDataService.signOut();

      expect(result.success).toBe(true);
    });

    it('should reset password', async () => {
      const result = await MockDataService.resetPassword('<EMAIL>');

      expect(result.success).toBe(true);
    });
  });

  describe('Properties', () => {
    it('should get properties list', async () => {
      const properties = await MockDataService.getProperties();

      expect(Array.isArray(properties)).toBe(true);
      expect(properties.length).toBeGreaterThan(0);
      expect(properties[0]).toHaveProperty('id');
      expect(properties[0]).toHaveProperty('title');
      expect(properties[0]).toHaveProperty('price');
    });

    it('should get single property by id', async () => {
      const properties = await MockDataService.getProperties();
      const firstProperty = properties[0];

      const property = await MockDataService.getProperty(firstProperty.id);

      expect(property).toBeDefined();
      expect(property?.id).toBe(firstProperty.id);
    });

    it('should return null for non-existent property', async () => {
      const property = await MockDataService.getProperty('non-existent-id');

      expect(property).toBeNull();
    });

    it('should create new property', async () => {
      const propertyData = {
        title: 'Test Property',
        description: 'A test property',
        price: 150000,
        location: 'Test Location',
        bedrooms: 2,
        bathrooms: 1,
        type: 'apartment' as const,
      };

      const property = await MockDataService.createProperty(propertyData);

      expect(property).toBeDefined();
      expect(property.title).toBe(propertyData.title);
      expect(property.price).toBe(propertyData.price);
      expect(property.id).toBeDefined();
    });
  });

  describe('Statistics', () => {
    it('should get dashboard stats', async () => {
      const stats = await MockDataService.getStats();

      expect(stats).toBeDefined();
      expect(stats).toHaveProperty('totalProperties');
      expect(stats).toHaveProperty('totalUsers');
      expect(stats).toHaveProperty('totalApplications');
      expect(stats).toHaveProperty('totalRevenue');
      expect(typeof stats.totalProperties).toBe('number');
    });
  });

  describe('Users', () => {
    it('should get users list', async () => {
      const users = await MockDataService.getUsers();

      expect(Array.isArray(users)).toBe(true);
      expect(users.length).toBeGreaterThan(0);
      expect(users[0]).toHaveProperty('id');
      expect(users[0]).toHaveProperty('email');
      expect(users[0]).toHaveProperty('role');
    });

    it('should get user by id', async () => {
      const users = await MockDataService.getUsers();
      const firstUser = users[0];

      const user = await MockDataService.getUser(firstUser.id);

      expect(user).toBeDefined();
      expect(user?.id).toBe(firstUser.id);
    });
  });

  describe('Applications', () => {
    it('should get applications list', async () => {
      const applications = await MockDataService.getApplications();

      expect(Array.isArray(applications)).toBe(true);
      expect(applications.length).toBeGreaterThan(0);
      expect(applications[0]).toHaveProperty('id');
      expect(applications[0]).toHaveProperty('status');
    });

    it('should create new application', async () => {
      const applicationData = {
        propertyId: 'prop-1',
        applicantName: 'Test Applicant',
        email: '<EMAIL>',
        phone: '+1234567890',
      };

      const application = await MockDataService.createApplication(applicationData);

      expect(application).toBeDefined();
      expect(application.applicantName).toBe(applicationData.applicantName);
      expect(application.email).toBe(applicationData.email);
      expect(application.id).toBeDefined();
    });
  });

  describe('Notifications', () => {
    it('should get notifications for user', async () => {
      const notifications = await MockDataService.getNotifications('user-1');

      expect(Array.isArray(notifications)).toBe(true);
      expect(notifications.length).toBeGreaterThan(0);
      expect(notifications[0]).toHaveProperty('id');
      expect(notifications[0]).toHaveProperty('message');
      expect(notifications[0]).toHaveProperty('type');
    });

    it('should mark notification as read', async () => {
      const result = await MockDataService.markNotificationAsRead('notification-1');

      expect(result.success).toBe(true);
    });
  });

  describe('File Upload', () => {
    it('should simulate file upload', async () => {
      const mockFile = new File(['test content'], 'test.txt', { type: 'text/plain' });

      const result = await MockDataService.uploadFile(mockFile, 'documents');

      expect(result.success).toBe(true);
      expect(result.url).toBeDefined();
      expect(result.fileName).toBe('test.txt');
    });
  });
});
