// =====================================================
// E2E GLOBAL SETUP
// Global setup for Playwright tests
// =====================================================

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test setup...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Wait for development server to be ready
    console.log('⏳ Waiting for development server...');
    const baseURL = config.projects[0].use.baseURL || 'http://localhost:5173';
    
    let retries = 30;
    while (retries > 0) {
      try {
        const response = await page.goto(baseURL, { timeout: 5000 });
        if (response && response.ok()) {
          console.log('✅ Development server is ready');
          break;
        }
      } catch (error) {
        retries--;
        if (retries === 0) {
          throw new Error(`Development server not ready after 30 attempts: ${error}`);
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Setup test data
    console.log('📝 Setting up test data...');
    await setupTestData(page);

    // Create test users
    console.log('👥 Creating test users...');
    await createTestUsers(page);

    // Setup authentication state
    console.log('🔐 Setting up authentication states...');
    await setupAuthStates(page);

    console.log('✅ E2E test setup completed successfully');

  } catch (error) {
    console.error('❌ E2E test setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

async function setupTestData(page: any) {
  // Setup mock data for tests
  await page.evaluate(() => {
    // Clear any existing data
    localStorage.clear();
    sessionStorage.clear();
    
    // Set test environment flag
    localStorage.setItem('test_environment', 'true');
    
    // Setup mock properties
    const mockProperties = [
      {
        id: 'test-property-1',
        title: 'Test Apartment 1',
        description: 'A beautiful test apartment',
        price: 500000,
        location: 'GRA Phase 2, Port Harcourt',
        property_type: 'apartment',
        bedrooms: 3,
        bathrooms: 2,
        status: 'available',
        images: ['test-image-1.jpg', 'test-image-2.jpg']
      },
      {
        id: 'test-property-2',
        title: 'Test House 1',
        description: 'A spacious test house',
        price: 800000,
        location: 'Old GRA, Port Harcourt',
        property_type: 'house',
        bedrooms: 4,
        bathrooms: 3,
        status: 'available',
        images: ['test-image-3.jpg', 'test-image-4.jpg']
      }
    ];
    
    localStorage.setItem('test_properties', JSON.stringify(mockProperties));
  });
}

async function createTestUsers(page: any) {
  const testUsers = [
    {
      id: 'test-tenant-1',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      full_name: 'Test Tenant',
      role: 'tenant',
      phone: '+2348012345678'
    },
    {
      id: 'test-landlord-1',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      full_name: 'Test Landlord',
      role: 'landlord',
      phone: '+2348012345679'
    },
    {
      id: 'test-admin-1',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      full_name: 'Test Admin',
      role: 'admin',
      phone: '+2348012345680'
    }
  ];

  await page.evaluate((users) => {
    localStorage.setItem('test_users', JSON.stringify(users));
  }, testUsers);
}

async function setupAuthStates(page: any) {
  // Create authenticated states for different user types
  const authStates = {
    tenant: {
      user: {
        id: 'test-tenant-1',
        email: '<EMAIL>',
        full_name: 'Test Tenant',
        role: 'tenant'
      },
      token: 'test-tenant-token'
    },
    landlord: {
      user: {
        id: 'test-landlord-1',
        email: '<EMAIL>',
        full_name: 'Test Landlord',
        role: 'landlord'
      },
      token: 'test-landlord-token'
    },
    admin: {
      user: {
        id: 'test-admin-1',
        email: '<EMAIL>',
        full_name: 'Test Admin',
        role: 'admin'
      },
      token: 'test-admin-token'
    }
  };

  // Save auth states for use in tests
  await page.evaluate((states) => {
    localStorage.setItem('test_auth_states', JSON.stringify(states));
  }, authStates);

  // Create storage states for authenticated users
  const storageStates = ['tenant', 'landlord', 'admin'];
  
  for (const userType of storageStates) {
    const authState = authStates[userType as keyof typeof authStates];
    
    // Set authentication data
    await page.evaluate((auth) => {
      localStorage.setItem('auth_token', auth.token);
      localStorage.setItem('user_data', JSON.stringify(auth.user));
    }, authState);

    // Save storage state
    await page.context().storageState({ 
      path: `src/tests/e2e/auth-states/${userType}.json` 
    });
  }
}

export default globalSetup;
