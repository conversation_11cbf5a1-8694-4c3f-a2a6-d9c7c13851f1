// =====================================================
// USER JOURNEY E2E TESTS
// Complete user workflow testing with Playwright
// =====================================================

import { test, expect, Page } from '@playwright/test';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  fullName: 'Test User',
  phone: '+2348012345678'
};

const testLandlord = {
  email: '<EMAIL>',
  password: 'LandlordPassword123!',
  fullName: 'Test Landlord',
  phone: '+2348012345679'
};

const testProperty = {
  title: 'Beautiful 3-Bedroom Apartment',
  description: 'A lovely apartment in the heart of Port Harcourt with modern amenities and excellent location.',
  price: '500000',
  location: 'GRA Phase 2, Port Harcourt',
  bedrooms: '3',
  bathrooms: '2',
  phone: '+2348012345679',
  email: '<EMAIL>'
};

// Helper functions
async function loginUser(page: Page, email: string, password: string) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', email);
  await page.fill('[data-testid="password-input"]', password);
  await page.click('[data-testid="login-button"]');
  await page.waitForURL('/dashboard');
}

async function registerUser(page: Page, userData: typeof testUser) {
  await page.goto('/register');
  await page.fill('[data-testid="email-input"]', userData.email);
  await page.fill('[data-testid="password-input"]', userData.password);
  await page.fill('[data-testid="full-name-input"]', userData.fullName);
  await page.fill('[data-testid="phone-input"]', userData.phone);
  await page.selectOption('[data-testid="role-select"]', 'tenant');
  await page.click('[data-testid="register-button"]');
  await page.waitForURL('/dashboard');
}

test.describe('Complete User Journey', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('/');
  });

  test('Tenant Registration and Property Search Journey', async ({ page }) => {
    // Step 1: Visit homepage
    await expect(page).toHaveTitle(/PHCityRent/);
    await expect(page.locator('[data-testid="hero-section"]')).toBeVisible();

    // Step 2: Navigate to registration
    await page.click('[data-testid="register-link"]');
    await expect(page).toHaveURL(/.*register/);

    // Step 3: Register as tenant
    await registerUser(page, testUser);
    
    // Step 4: Verify successful registration and dashboard access
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('Welcome');
    await expect(page.locator('[data-testid="user-name"]')).toContainText(testUser.fullName);

    // Step 5: Navigate to property search
    await page.click('[data-testid="search-properties-link"]');
    await expect(page).toHaveURL(/.*properties/);

    // Step 6: Search for properties
    await page.fill('[data-testid="search-input"]', 'apartment');
    await page.selectOption('[data-testid="location-filter"]', 'Port Harcourt');
    await page.fill('[data-testid="min-price-input"]', '300000');
    await page.fill('[data-testid="max-price-input"]', '700000');
    await page.click('[data-testid="search-button"]');

    // Step 7: Verify search results
    await expect(page.locator('[data-testid="property-card"]').first()).toBeVisible();
    await expect(page.locator('[data-testid="search-results-count"]')).toContainText(/\d+ properties found/);

    // Step 8: View property details
    await page.click('[data-testid="property-card"]').first();
    await expect(page).toHaveURL(/.*properties\/.*$/);
    await expect(page.locator('[data-testid="property-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="property-price"]')).toBeVisible();
    await expect(page.locator('[data-testid="property-description"]')).toBeVisible();

    // Step 9: Contact landlord
    await page.click('[data-testid="contact-landlord-button"]');
    await expect(page.locator('[data-testid="contact-modal"]')).toBeVisible();
    
    await page.fill('[data-testid="message-input"]', 'Hi, I am interested in this property. Can we schedule a viewing?');
    await page.click('[data-testid="send-message-button"]');
    
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Message sent successfully');

    // Step 10: Add to favorites
    await page.click('[data-testid="favorite-button"]');
    await expect(page.locator('[data-testid="favorite-button"]')).toHaveClass(/favorited/);

    // Step 11: View favorites
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="favorites-link"]');
    await expect(page).toHaveURL(/.*favorites/);
    await expect(page.locator('[data-testid="favorite-property"]').first()).toBeVisible();

    // Step 12: View messages
    await page.click('[data-testid="messages-link"]');
    await expect(page).toHaveURL(/.*messages/);
    await expect(page.locator('[data-testid="conversation"]').first()).toBeVisible();

    // Step 13: Logout
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');
    await expect(page).toHaveURL('/');
    await expect(page.locator('[data-testid="login-link"]')).toBeVisible();
  });

  test('Landlord Property Management Journey', async ({ page }) => {
    // Step 1: Register as landlord
    await page.goto('/register');
    await page.fill('[data-testid="email-input"]', testLandlord.email);
    await page.fill('[data-testid="password-input"]', testLandlord.password);
    await page.fill('[data-testid="full-name-input"]', testLandlord.fullName);
    await page.fill('[data-testid="phone-input"]', testLandlord.phone);
    await page.selectOption('[data-testid="role-select"]', 'landlord');
    await page.click('[data-testid="register-button"]');

    // Step 2: Navigate to property management
    await page.click('[data-testid="manage-properties-link"]');
    await expect(page).toHaveURL(/.*properties\/manage/);

    // Step 3: Add new property
    await page.click('[data-testid="add-property-button"]');
    await expect(page.locator('[data-testid="property-form"]')).toBeVisible();

    // Fill property form
    await page.fill('[data-testid="title-input"]', testProperty.title);
    await page.fill('[data-testid="description-textarea"]', testProperty.description);
    await page.fill('[data-testid="price-input"]', testProperty.price);
    await page.fill('[data-testid="location-input"]', testProperty.location);
    await page.selectOption('[data-testid="property-type-select"]', 'apartment');
    await page.fill('[data-testid="bedrooms-input"]', testProperty.bedrooms);
    await page.fill('[data-testid="bathrooms-input"]', testProperty.bathrooms);
    await page.fill('[data-testid="contact-phone-input"]', testProperty.phone);
    await page.fill('[data-testid="contact-email-input"]', testProperty.email);

    // Upload images
    const fileInput = page.locator('[data-testid="image-upload-input"]');
    await fileInput.setInputFiles([
      'src/tests/fixtures/test-image-1.jpg',
      'src/tests/fixtures/test-image-2.jpg'
    ]);

    // Submit form
    await page.click('[data-testid="submit-property-button"]');
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Property added successfully');

    // Step 4: Verify property in list
    await expect(page.locator('[data-testid="property-list-item"]').first()).toBeVisible();
    await expect(page.locator('[data-testid="property-title"]').first()).toContainText(testProperty.title);

    // Step 5: Edit property
    await page.click('[data-testid="edit-property-button"]').first();
    await page.fill('[data-testid="price-input"]', '550000');
    await page.click('[data-testid="update-property-button"]');
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Property updated successfully');

    // Step 6: View property analytics
    await page.click('[data-testid="analytics-link"]');
    await expect(page).toHaveURL(/.*analytics/);
    await expect(page.locator('[data-testid="analytics-dashboard"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-properties-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-views-metric"]')).toBeVisible();

    // Step 7: View messages from tenants
    await page.click('[data-testid="messages-link"]');
    await expect(page).toHaveURL(/.*messages/);
    
    // Step 8: Respond to tenant inquiry
    if (await page.locator('[data-testid="conversation"]').first().isVisible()) {
      await page.click('[data-testid="conversation"]').first();
      await page.fill('[data-testid="reply-input"]', 'Thank you for your interest! I would be happy to schedule a viewing. When would be convenient for you?');
      await page.click('[data-testid="send-reply-button"]');
      await expect(page.locator('[data-testid="message"]').last()).toContainText('Thank you for your interest');
    }
  });

  test('Property Search and Filter Journey', async ({ page }) => {
    // Step 1: Go to properties page
    await page.goto('/properties');

    // Step 2: Test basic search
    await page.fill('[data-testid="search-input"]', 'apartment');
    await page.click('[data-testid="search-button"]');
    await page.waitForSelector('[data-testid="property-card"]');

    // Step 3: Test location filter
    await page.selectOption('[data-testid="location-filter"]', 'GRA');
    await page.waitForSelector('[data-testid="property-card"]');

    // Step 4: Test price range filter
    await page.fill('[data-testid="min-price-input"]', '400000');
    await page.fill('[data-testid="max-price-input"]', '600000');
    await page.click('[data-testid="apply-filters-button"]');

    // Step 5: Test property type filter
    await page.check('[data-testid="apartment-checkbox"]');
    await page.check('[data-testid="house-checkbox"]');

    // Step 6: Test sorting
    await page.selectOption('[data-testid="sort-select"]', 'price-asc');
    await page.waitForSelector('[data-testid="property-card"]');

    // Verify sorting worked
    const prices = await page.locator('[data-testid="property-price"]').allTextContents();
    const numericPrices = prices.map(price => parseInt(price.replace(/[^\d]/g, '')));
    
    for (let i = 1; i < numericPrices.length; i++) {
      expect(numericPrices[i]).toBeGreaterThanOrEqual(numericPrices[i - 1]);
    }

    // Step 7: Test pagination
    if (await page.locator('[data-testid="next-page-button"]').isVisible()) {
      await page.click('[data-testid="next-page-button"]');
      await page.waitForSelector('[data-testid="property-card"]');
      await expect(page.locator('[data-testid="current-page"]')).toContainText('2');
    }

    // Step 8: Clear filters
    await page.click('[data-testid="clear-filters-button"]');
    await expect(page.locator('[data-testid="search-input"]')).toHaveValue('');
    await expect(page.locator('[data-testid="min-price-input"]')).toHaveValue('');
  });

  test('Mobile Responsive Journey', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Step 1: Test mobile navigation
    await page.goto('/');
    await page.click('[data-testid="mobile-menu-button"]');
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();

    // Step 2: Test mobile property search
    await page.click('[data-testid="mobile-search-link"]');
    await expect(page).toHaveURL(/.*properties/);

    // Step 3: Test mobile filters
    await page.click('[data-testid="mobile-filters-button"]');
    await expect(page.locator('[data-testid="mobile-filters-modal"]')).toBeVisible();

    await page.fill('[data-testid="mobile-search-input"]', 'apartment');
    await page.selectOption('[data-testid="mobile-location-select"]', 'Port Harcourt');
    await page.click('[data-testid="apply-mobile-filters-button"]');

    // Step 4: Test mobile property card
    await expect(page.locator('[data-testid="mobile-property-card"]').first()).toBeVisible();
    await page.click('[data-testid="mobile-property-card"]').first();

    // Step 5: Test mobile property details
    await expect(page.locator('[data-testid="mobile-property-details"]')).toBeVisible();
    await expect(page.locator('[data-testid="mobile-contact-button"]')).toBeVisible();

    // Step 6: Test mobile image gallery
    await page.click('[data-testid="mobile-image-gallery"]');
    await expect(page.locator('[data-testid="mobile-image-modal"]')).toBeVisible();
    
    // Swipe through images
    await page.touchscreen.tap(200, 300);
    await page.touchscreen.tap(300, 300);
  });

  test('Error Handling and Edge Cases', async ({ page }) => {
    // Step 1: Test 404 page
    await page.goto('/non-existent-page');
    await expect(page.locator('[data-testid="404-page"]')).toBeVisible();
    await expect(page.locator('[data-testid="back-home-button"]')).toBeVisible();

    // Step 2: Test network error handling
    await page.route('**/api/**', route => route.abort());
    await page.goto('/properties');
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();

    // Step 3: Test form validation
    await page.goto('/register');
    await page.click('[data-testid="register-button"]');
    await expect(page.locator('[data-testid="email-error"]')).toContainText('Email is required');
    await expect(page.locator('[data-testid="password-error"]')).toContainText('Password is required');

    // Step 4: Test invalid login
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');
    await expect(page.locator('[data-testid="login-error"]')).toContainText('Invalid credentials');

    // Step 5: Test session timeout
    // Simulate expired session
    await page.evaluate(() => {
      localStorage.removeItem('auth_token');
    });
    await page.goto('/dashboard');
    await expect(page).toHaveURL(/.*login/);
  });
});
