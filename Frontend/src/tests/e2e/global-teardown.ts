// =====================================================
// E2E GLOBAL TEARDOWN
// Global teardown for Playwright tests
// =====================================================

import { FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test teardown...');

  try {
    // Clean up test data
    console.log('🗑️  Cleaning up test data...');
    await cleanupTestData();

    // Clean up auth states
    console.log('🔐 Cleaning up auth states...');
    await cleanupAuthStates();

    // Generate test report
    console.log('📊 Generating test report...');
    await generateTestReport();

    // Clean up temporary files
    console.log('🧽 Cleaning up temporary files...');
    await cleanupTempFiles();

    console.log('✅ E2E test teardown completed successfully');

  } catch (error) {
    console.error('❌ E2E test teardown failed:', error);
    // Don't throw error to avoid failing the test run
  }
}

async function cleanupTestData() {
  // Clean up any test data that might have been created
  // This would typically involve database cleanup in a real scenario
  
  const testDataPaths = [
    'test-results/test-data',
    'test-results/uploads',
    'test-results/temp'
  ];

  for (const dataPath of testDataPaths) {
    const fullPath = path.join(process.cwd(), dataPath);
    if (fs.existsSync(fullPath)) {
      fs.rmSync(fullPath, { recursive: true, force: true });
    }
  }
}

async function cleanupAuthStates() {
  const authStatesDir = path.join(process.cwd(), 'src/tests/e2e/auth-states');
  
  if (fs.existsSync(authStatesDir)) {
    const files = fs.readdirSync(authStatesDir);
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        const filePath = path.join(authStatesDir, file);
        fs.unlinkSync(filePath);
      }
    }
  }
}

async function generateTestReport() {
  const resultsDir = path.join(process.cwd(), 'test-results');
  
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir, { recursive: true });
  }

  // Read Playwright results if available
  const playwrightResultsPath = path.join(resultsDir, 'playwright-results.json');
  let playwrightResults = null;
  
  if (fs.existsSync(playwrightResultsPath)) {
    try {
      const resultsContent = fs.readFileSync(playwrightResultsPath, 'utf8');
      playwrightResults = JSON.parse(resultsContent);
    } catch (error) {
      console.warn('Could not parse Playwright results:', error);
    }
  }

  // Generate comprehensive test report
  const report = {
    timestamp: new Date().toISOString(),
    environment: {
      node: process.version,
      platform: process.platform,
      arch: process.arch,
      ci: !!process.env.CI
    },
    summary: playwrightResults ? {
      total: playwrightResults.suites?.reduce((total: number, suite: any) => 
        total + suite.specs?.length || 0, 0) || 0,
      passed: playwrightResults.suites?.reduce((total: number, suite: any) => 
        total + (suite.specs?.filter((spec: any) => spec.ok).length || 0), 0) || 0,
      failed: playwrightResults.suites?.reduce((total: number, suite: any) => 
        total + (suite.specs?.filter((spec: any) => !spec.ok).length || 0), 0) || 0,
      duration: playwrightResults.stats?.duration || 0
    } : null,
    performance: {
      averageTestDuration: 0,
      slowestTests: [],
      fastestTests: []
    },
    coverage: {
      // E2E coverage would be calculated differently
      // This is a placeholder for future implementation
      statements: 0,
      branches: 0,
      functions: 0,
      lines: 0
    },
    artifacts: {
      screenshots: countFiles(path.join(resultsDir, 'playwright-artifacts'), '.png'),
      videos: countFiles(path.join(resultsDir, 'playwright-artifacts'), '.webm'),
      traces: countFiles(path.join(resultsDir, 'playwright-artifacts'), '.zip')
    }
  };

  // Calculate performance metrics if results are available
  if (playwrightResults && playwrightResults.suites) {
    const allTests: any[] = [];
    
    playwrightResults.suites.forEach((suite: any) => {
      if (suite.specs) {
        suite.specs.forEach((spec: any) => {
          if (spec.tests) {
            spec.tests.forEach((test: any) => {
              allTests.push({
                title: test.title,
                duration: test.results?.[0]?.duration || 0,
                status: test.results?.[0]?.status || 'unknown'
              });
            });
          }
        });
      }
    });

    if (allTests.length > 0) {
      const durations = allTests.map(test => test.duration);
      report.performance.averageTestDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
      
      const sortedTests = allTests.sort((a, b) => b.duration - a.duration);
      report.performance.slowestTests = sortedTests.slice(0, 10);
      report.performance.fastestTests = sortedTests.slice(-10).reverse();
    }
  }

  // Write comprehensive report
  const reportPath = path.join(resultsDir, 'e2e-test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  // Generate markdown report
  const markdownReport = generateMarkdownReport(report);
  const markdownPath = path.join(resultsDir, 'e2e-test-report.md');
  fs.writeFileSync(markdownPath, markdownReport);

  console.log(`📄 Test report generated: ${reportPath}`);
  console.log(`📄 Markdown report generated: ${markdownPath}`);
}

function generateMarkdownReport(report: any): string {
  let markdown = '# E2E Test Report\n\n';
  
  markdown += `**Generated:** ${report.timestamp}\n\n`;
  
  markdown += '## Environment\n\n';
  markdown += `- **Node.js:** ${report.environment.node}\n`;
  markdown += `- **Platform:** ${report.environment.platform}\n`;
  markdown += `- **Architecture:** ${report.environment.arch}\n`;
  markdown += `- **CI:** ${report.environment.ci ? 'Yes' : 'No'}\n\n`;
  
  if (report.summary) {
    markdown += '## Test Summary\n\n';
    markdown += `- **Total Tests:** ${report.summary.total}\n`;
    markdown += `- **Passed:** ${report.summary.passed} ✅\n`;
    markdown += `- **Failed:** ${report.summary.failed} ❌\n`;
    markdown += `- **Duration:** ${Math.round(report.summary.duration / 1000)}s\n\n`;
    
    const passRate = report.summary.total > 0 ? 
      Math.round((report.summary.passed / report.summary.total) * 100) : 0;
    markdown += `**Pass Rate:** ${passRate}%\n\n`;
  }
  
  if (report.performance.slowestTests.length > 0) {
    markdown += '## Performance\n\n';
    markdown += `**Average Test Duration:** ${Math.round(report.performance.averageTestDuration)}ms\n\n`;
    
    markdown += '### Slowest Tests\n\n';
    markdown += '| Test | Duration |\n';
    markdown += '|------|----------|\n';
    
    report.performance.slowestTests.forEach((test: any) => {
      markdown += `| ${test.title} | ${test.duration}ms |\n`;
    });
    
    markdown += '\n';
  }
  
  markdown += '## Artifacts\n\n';
  markdown += `- **Screenshots:** ${report.artifacts.screenshots}\n`;
  markdown += `- **Videos:** ${report.artifacts.videos}\n`;
  markdown += `- **Traces:** ${report.artifacts.traces}\n\n`;
  
  markdown += '## Recommendations\n\n';
  
  if (report.summary && report.summary.failed > 0) {
    markdown += '- ❌ Some tests failed. Review the detailed results and fix failing tests.\n';
  }
  
  if (report.performance.averageTestDuration > 5000) {
    markdown += '- ⚠️ Average test duration is high. Consider optimizing slow tests.\n';
  }
  
  if (report.artifacts.screenshots > 10) {
    markdown += '- 📸 Many screenshots were captured. This might indicate test failures or slow performance.\n';
  }
  
  markdown += '- 📊 Review the detailed HTML report for more insights.\n';
  markdown += '- 🔍 Check traces for failed tests to understand the root cause.\n';
  
  return markdown;
}

function countFiles(directory: string, extension: string): number {
  if (!fs.existsSync(directory)) {
    return 0;
  }
  
  try {
    const files = fs.readdirSync(directory, { recursive: true });
    return files.filter(file => 
      typeof file === 'string' && file.endsWith(extension)
    ).length;
  } catch (error) {
    return 0;
  }
}

async function cleanupTempFiles() {
  const tempPaths = [
    'test-results/temp',
    '.nyc_output',
    'coverage/tmp'
  ];

  for (const tempPath of tempPaths) {
    const fullPath = path.join(process.cwd(), tempPath);
    if (fs.existsSync(fullPath)) {
      fs.rmSync(fullPath, { recursive: true, force: true });
    }
  }
}

export default globalTeardown;
