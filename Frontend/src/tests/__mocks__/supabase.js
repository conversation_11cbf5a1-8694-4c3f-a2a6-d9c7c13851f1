// Mock Supabase client for testing
const mockUnsubscribe = jest.fn();

const mockSupabaseClient = {
  auth: {
    getSession: jest.fn().mockResolvedValue({ 
      data: { session: null }, 
      error: null 
    }),
    onAuthStateChange: jest.fn().mockImplementation((callback) => {
      callback('INITIAL_SESSION', null);
      return { 
        data: { subscription: { unsubscribe: mockUnsubscribe } },
        unsubscribe: mockUnsubscribe 
      };
    }),
    signInWithPassword: jest.fn().mockResolvedValue({ 
      data: { 
        session: {
          user: {
            id: 'test-user-id',
            email: '<EMAIL>',
            user_metadata: { full_name: 'Test User' }
          },
          access_token: 'mock-access-token'
        } 
      }, 
      error: null 
    }),
    signUp: jest.fn().mockResolvedValue({ 
      data: { 
        session: null,
        user: {
          id: 'test-user-id',
          email: '<EMAIL>'
        }
      }, 
      error: null 
    }),
    signOut: jest.fn().mockResolvedValue({ error: null }),
    resetPasswordForEmail: jest.fn().mockResolvedValue({ error: null }),
    updateUser: jest.fn().mockResolvedValue({ 
      data: { user: null }, 
      error: null 
    })
  },
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ 
      data: null, 
      error: null 
    }),
    then: jest.fn().mockResolvedValue({ 
      data: [], 
      error: null 
    })
  })),
  storage: {
    from: jest.fn(() => ({
      upload: jest.fn().mockResolvedValue({ 
        data: { path: 'mock-path' }, 
        error: null 
      }),
      download: jest.fn().mockResolvedValue({ 
        data: new Blob(), 
        error: null 
      }),
      remove: jest.fn().mockResolvedValue({ 
        data: null, 
        error: null 
      }),
      getPublicUrl: jest.fn().mockReturnValue({ 
        data: { publicUrl: 'https://mock-url.com' } 
      })
    }))
  },
  realtime: {
    channel: jest.fn(() => ({
      on: jest.fn().mockReturnThis(),
      subscribe: jest.fn().mockReturnThis(),
      unsubscribe: jest.fn()
    }))
  }
};

// Export both named and default exports
module.exports = {
  supabase: mockSupabaseClient,
  default: mockSupabaseClient
};
