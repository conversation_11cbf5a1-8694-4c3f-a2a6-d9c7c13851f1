// Mock for import.meta in Jest environment
module.exports = {
  env: {
    VITE_API_BASE_URL: 'http://localhost:3001/api/v1',
    VITE_SUPABASE_URL: 'https://test.supabase.co',
    VITE_SUPABASE_ANON_KEY: 'test-key',
    VITE_ENVIRONMENT: 'test',
    MODE: 'test',
    DEV: false,
    PROD: false,
    SSR: false
  },
  url: 'http://localhost:3000',
  hot: {
    accept: () => {},
    dispose: () => {},
    decline: () => {},
    invalidate: () => {}
  }
};
