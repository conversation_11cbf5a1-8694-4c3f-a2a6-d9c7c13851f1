// Mock hooks for testing
const React = require('react');

// Mock useAuth hook
const mockUseAuth = () => ({
  user: null,
  session: null,
  loading: false,
  signIn: jest.fn().mockResolvedValue({ success: true }),
  signUp: jest.fn().mockResolvedValue({ success: true }),
  signOut: jest.fn().mockResolvedValue({ success: true }),
  resetPassword: jest.fn().mockResolvedValue({ success: true }),
  updateProfile: jest.fn().mockResolvedValue({ success: true }),
  isAuthenticated: false,
  isLoading: false,
  error: null
});

// Mock AuthProvider
const MockAuthProvider = ({ children }) => {
  return React.createElement('div', { 'data-testid': 'auth-provider' }, children);
};

// Mock useImageOptimization hook
const mockUseImageOptimization = () => ({
  optimizeImage: jest.fn().mockResolvedValue('optimized-image-url'),
  isOptimizing: false,
  error: null
});

// Export mocks
module.exports = {
  useAuth: mockUseAuth,
  AuthProvider: MockAuthProvider,
  useImageOptimization: mockUseImageOptimization,
  default: {
    useAuth: mockUseAuth,
    AuthProvider: MockAuthProvider,
    useImageOptimization: mockUseImageOptimization
  }
};
