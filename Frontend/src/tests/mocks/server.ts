// =====================================================
// MOCK SERVER SETUP
// MSW (Mock Service Worker) configuration for API mocking
// =====================================================

import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { 
  mockUser, 
  mockLandlord, 
  mockProperty, 
  mockConversation, 
  mockMessage
} from '../setup/test-utils';

// Types
type Property = typeof mockProperty;
type User = typeof mockUser;
type Conversation = typeof mockConversation;
type Message = typeof mockMessage;

// Mock data stores
let properties: Property[] = [mockProperty];
let users: User[] = [mockUser, mockLandlord];
let conversations: Conversation[] = [mockConversation];
let messages: Message[] = [mockMessage];

// API Base URL
const API_BASE = process.env.VITE_SUPABASE_URL || 'http://localhost:54321';

// Request handlers
export const handlers = [
  // Authentication endpoints
  http.post(`${API_BASE}/auth/v1/token`, () => {
    return HttpResponse.json({
      access_token: 'mock-access-token',
      token_type: 'bearer',
      expires_in: 3600,
      refresh_token: 'mock-refresh-token',
      user: mockUser
    });
  }),

  http.post(`${API_BASE}/auth/v1/signup`, () => {
    return HttpResponse.json({
      user: mockUser,
      session: {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token'
      }
    });
  }),

  http.post(`${API_BASE}/auth/v1/logout`, () => {
    return new HttpResponse(null, { status: 204 });
  }),

  // Properties endpoints
  http.get(`${API_BASE}/rest/v1/properties`, () => {
    return HttpResponse.json(properties);
  }),

  http.get(`${API_BASE}/rest/v1/properties/:id`, ({ params }) => {
    const { id } = params;
    const property = properties.find(p => p.id === id);
    
    if (!property) {
      return HttpResponse.json({ error: 'Property not found' }, { status: 404 });
    }

    return HttpResponse.json(property);
  }),

  http.post(`${API_BASE}/rest/v1/properties`, async ({ request }) => {
    const body = await request.json() as Partial<Property>;
    const newProperty: Property = {
      ...mockProperty,
      ...body,
      id: `property-${Date.now()}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    properties.push(newProperty);
    return HttpResponse.json(newProperty, { status: 201 });
  }),

  http.patch(`${API_BASE}/rest/v1/properties/:id`, async ({ params, request }) => {
    const { id } = params;
    const propertyIndex = properties.findIndex(p => p.id === id);
    
    if (propertyIndex === -1) {
      return HttpResponse.json({ error: 'Property not found' }, { status: 404 });
    }

    const body = await request.json() as Partial<Property>;
    properties[propertyIndex] = {
      ...properties[propertyIndex],
      ...body,
      updated_at: new Date().toISOString()
    };

    return HttpResponse.json(properties[propertyIndex]);
  }),

  http.delete(`${API_BASE}/rest/v1/properties/:id`, ({ params }) => {
    const { id } = params;
    const propertyIndex = properties.findIndex(p => p.id === id);
    
    if (propertyIndex === -1) {
      return HttpResponse.json({ error: 'Property not found' }, { status: 404 });
    }

    properties.splice(propertyIndex, 1);
    return new HttpResponse(null, { status: 204 });
  })
];

// Create server
export const server = setupServer(...handlers);

// Reset mock data
export const resetMockData = () => {
  properties = [mockProperty];
  users = [mockUser, mockLandlord];
  conversations = [mockConversation];
  messages = [mockMessage];
};

// Helper functions for tests
export const addMockProperty = (property: Property) => {
  properties.push(property);
};

export const addMockUser = (user: User) => {
  users.push(user);
};

export const getMockProperties = () => [...properties];
export const getMockUsers = () => [...users];
export const getMockConversations = () => [...conversations];
export const getMockMessages = () => [...messages];
