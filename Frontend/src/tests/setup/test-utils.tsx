// =====================================================
// TEST UTILITIES
// Custom render functions and test helpers
// =====================================================

import React, { ReactElement, ReactNode } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/hooks/useAuth';
import { ThemeProvider } from '@/contexts/ThemeContext';

// Mock Supabase client
jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      neq: jest.fn().mockReturnThis(),
      gt: jest.fn().mockReturnThis(),
      gte: jest.fn().mockReturnThis(),
      lt: jest.fn().mockReturnThis(),
      lte: jest.fn().mockReturnThis(),
      like: jest.fn().mockReturnThis(),
      ilike: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      contains: jest.fn().mockReturnThis(),
      containedBy: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis()
    })),
    auth: {
      getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signOut: jest.fn(),
      onAuthStateChange: jest.fn()
    },
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn(),
        download: jest.fn(),
        remove: jest.fn(),
        getPublicUrl: jest.fn()
      }))
    },
    rpc: jest.fn()
  }
}));

// Types
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  user?: any;
  queryClient?: QueryClient;
}

interface AllTheProvidersProps {
  children: ReactNode;
  initialEntries?: string[];
  user?: any;
  queryClient?: QueryClient;
}

// Mock user data
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  full_name: 'Test User',
  role: 'tenant',
  phone: '+2348012345678',
  location: 'Port Harcourt',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

export const mockLandlord = {
  id: 'test-landlord-id',
  email: '<EMAIL>',
  full_name: 'Test Landlord',
  role: 'landlord',
  phone: '+2348012345679',
  location: 'Port Harcourt',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

export const mockAdmin = {
  id: 'test-admin-id',
  email: '<EMAIL>',
  full_name: 'Test Admin',
  role: 'admin',
  phone: '+2348012345680',
  location: 'Port Harcourt',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

// Mock property data
export const mockProperty = {
  id: 'test-property-id',
  title: 'Beautiful 3-Bedroom Apartment',
  description: 'A lovely apartment in the heart of Port Harcourt',
  price: 500000,
  location: 'GRA Phase 2, Port Harcourt',
  property_type: 'apartment',
  bedrooms: 3,
  bathrooms: 2,
  status: 'available',
  landlord_id: 'test-landlord-id',
  contact_phone: '+2348012345679',
  contact_email: '<EMAIL>',
  images: ['image1.jpg', 'image2.jpg'],
  amenities: ['parking', 'security', 'generator'],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

// Mock conversation data
export const mockConversation = {
  id: 'test-conversation-id',
  property_id: 'test-property-id',
  tenant_id: 'test-user-id',
  landlord_id: 'test-landlord-id',
  status: 'active',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

// Mock message data
export const mockMessage = {
  id: 'test-message-id',
  conversation_id: 'test-conversation-id',
  sender_id: 'test-user-id',
  content: 'Hello, I am interested in this property',
  message_type: 'text',
  created_at: '2024-01-01T00:00:00Z'
};

// Create test query client
export const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
        staleTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

// All providers wrapper
const AllTheProviders: React.FC<AllTheProvidersProps> = ({ 
  children, 
  initialEntries = ['/'], 
  user = null,
  queryClient = createTestQueryClient()
}) => {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          <ThemeProvider>
            {children}
          </ThemeProvider>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

// Custom render function
export const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult => {
  const { initialEntries, user, queryClient, ...renderOptions } = options;

  const Wrapper: React.FC<{ children: ReactNode }> = ({ children }) => (
    <AllTheProviders 
      initialEntries={initialEntries} 
      user={user}
      queryClient={queryClient}
    >
      {children}
    </AllTheProviders>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Render with authenticated user
export const renderWithAuth = (
  ui: ReactElement,
  user: any = mockUser,
  options: CustomRenderOptions = {}
): RenderResult => {
  // Mock the auth session before rendering
  const mockSession = {
    user: {
      id: user.id,
      email: user.email,
      user_metadata: {
        full_name: user.full_name,
        role: user.role
      }
    }
  };
  
  jest.spyOn(require('@/integrations/supabase/client').supabase.auth, 'getSession')
    .mockResolvedValue({ data: { session: mockSession }, error: null });

  return customRender(ui, { ...options, user });
};

// Render with landlord user
export const renderWithLandlord = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult => {
  return renderWithAuth(ui, mockLandlord, options);
};

// Render with admin user
export const renderWithAdmin = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult => {
  return renderWithAuth(ui, mockAdmin, options);
};

// Wait for loading to complete
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

// Mock file for file upload tests
export const createMockFile = (
  name: string = 'test.jpg',
  size: number = 1024,
  type: string = 'image/jpeg'
): File => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

// Mock image for image tests
export const createMockImage = (
  width: number = 100,
  height: number = 100
): HTMLImageElement => {
  const img = new Image();
  img.width = width;
  img.height = height;
  img.src = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=';
  return img;
};

// Mock form data
export const createMockFormData = (data: Record<string, any>): FormData => {
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    if (value instanceof File) {
      formData.append(key, value);
    } else if (Array.isArray(value)) {
      value.forEach(item => formData.append(key, item));
    } else {
      formData.append(key, String(value));
    }
  });
  return formData;
};

// Mock API response
export const createMockApiResponse = <T>(
  data: T,
  status: number = 200,
  statusText: string = 'OK'
): Response => {
  return new Response(JSON.stringify(data), {
    status,
    statusText,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// Mock error response
export const createMockErrorResponse = (
  message: string = 'An error occurred',
  status: number = 500
): Response => {
  return new Response(JSON.stringify({ error: message }), {
    status,
    statusText: 'Internal Server Error',
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// Test data generators
export const generateMockProperties = (count: number = 5) => {
  return Array.from({ length: count }, (_, index) => ({
    ...mockProperty,
    id: `property-${index + 1}`,
    title: `Property ${index + 1}`,
    price: 500000 + (index * 100000),
    bedrooms: 2 + (index % 3),
    bathrooms: 1 + (index % 2)
  }));
};

export const generateMockUsers = (count: number = 5) => {
  return Array.from({ length: count }, (_, index) => ({
    ...mockUser,
    id: `user-${index + 1}`,
    email: `user${index + 1}@example.com`,
    full_name: `User ${index + 1}`,
    phone: `+23480123456${70 + index}`
  }));
};

export const generateMockMessages = (count: number = 5) => {
  return Array.from({ length: count }, (_, index) => ({
    ...mockMessage,
    id: `message-${index + 1}`,
    content: `Message ${index + 1}`,
    created_at: new Date(Date.now() - (index * 60000)).toISOString()
  }));
};

// Async test helpers
export const flushPromises = () => {
  return new Promise(resolve => setImmediate(resolve));
};

export const waitFor = (condition: () => boolean, timeout: number = 5000) => {
  return new Promise<void>((resolve, reject) => {
    const startTime = Date.now();
    
    const check = () => {
      if (condition()) {
        resolve();
      } else if (Date.now() - startTime > timeout) {
        reject(new Error('Timeout waiting for condition'));
      } else {
        setTimeout(check, 10);
      }
    };
    
    check();
  });
};

// Performance testing helpers
export const measurePerformance = async (fn: () => Promise<void> | void) => {
  const start = performance.now();
  await fn();
  const end = performance.now();
  return end - start;
};

export const measureMemoryUsage = () => {
  if ('memory' in performance) {
    return (performance as any).memory;
  }
  return null;
};

// Accessibility testing helpers
export const getByRole = (container: HTMLElement, role: string, options?: any) => {
  return container.querySelector(`[role="${role}"]`);
};

export const getAllByRole = (container: HTMLElement, role: string) => {
  return Array.from(container.querySelectorAll(`[role="${role}"]`));
};

// Custom assertions
export const expectToBeInDocument = (element: HTMLElement | null) => {
  expect(element).toBeInTheDocument();
};

export const expectToHaveClass = (element: HTMLElement | null, className: string) => {
  expect(element).toHaveClass(className);
};

export const expectToBeVisible = (element: HTMLElement | null) => {
  expect(element).toBeVisible();
};

export const expectToBeDisabled = (element: HTMLElement | null) => {
  expect(element).toBeDisabled();
};

// Re-export everything from React Testing Library
export * from '@testing-library/react';
export { customRender as render };
