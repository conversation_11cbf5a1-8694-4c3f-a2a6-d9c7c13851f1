// =====================================================
// TEST RESULTS PROCESSOR
// Processes and formats test results for reporting
// =====================================================

const processResults = (results) => {
  if (!results || !results.testResults) {
    return {
      ...results,
      testResults: [],
      success: false,
      numTotalTests: 0,
      numPassedTests: 0,
      numFailedTests: 0,
      numPendingTests: 0
    };
  }

  // Process test results
  const processedResults = results.testResults.map(testResult => {
    if (!testResult || !testResult.testResults) {
      return null;
    }

    return {
      ...testResult,
      testResults: testResult.testResults.map(test => ({
        ancestorTitles: test.ancestorTitles || [],
        duration: test.duration || 0,
        failureMessages: test.failureMessages || [],
        fullName: test.fullName || '',
        location: test.location || null,
        numPassingAsserts: test.numPassingAsserts || 0,
        status: test.status || 'unknown',
        title: test.title || ''
      }))
    };
  }).filter(Boolean);

  return {
    ...results,
    testResults: processedResults,
    success: processedResults.every(result => result.status === 'passed')
  };
};

module.exports = processResults; 