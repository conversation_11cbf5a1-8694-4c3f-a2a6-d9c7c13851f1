// =====================================================
// PROPERTIES API INTEGRATION TESTS
// End-to-end API testing for property operations
// =====================================================

import { supabase } from '@/lib/supabase';
import { mockProperty, mockUser, mockLandlord } from '@tests/setup/test-utils';
import { server, resetMockData } from '@tests/mocks/server';

// Mock Supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      ilike: jest.fn().mockReturnThis(),
      gte: jest.fn().mockReturnThis(),
      lte: jest.fn().mockReturnThis(),
      or: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis()
    })),
    auth: {
      getSession: jest.fn()
    }
  }
}));

describe('Properties API Integration', () => {
  beforeEach(() => {
    resetMockData();
    jest.clearAllMocks();
    
    // Reset Supabase mock implementation for each test
    const mockSupabase = supabase as jest.Mocked<typeof supabase>;
    mockSupabase.from().select.mockImplementation(() => Promise.resolve({
      data: [mockProperty],
      error: null
    }));
  });

  describe('Property Listing', () => {
    it('should fetch properties with pagination', async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .range(0, 9);

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeLessThanOrEqual(10);
    });

    it('should filter properties by location', async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .ilike('location', '%Port Harcourt%');

      expect(error).toBeNull();
      expect(data).toBeDefined();
      data?.forEach(property => {
        expect(property.location.toLowerCase()).toContain('port harcourt');
      });
    });

    it('should filter properties by price range', async () => {
      const minPrice = 300000;
      const maxPrice = 700000;

      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .gte('price', minPrice)
        .lte('price', maxPrice);

      expect(error).toBeNull();
      expect(data).toBeDefined();
      data?.forEach(property => {
        expect(property.price).toBeGreaterThanOrEqual(minPrice);
        expect(property.price).toBeLessThanOrEqual(maxPrice);
      });
    });

    it('should filter properties by type', async () => {
      const propertyType = 'apartment';

      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .eq('property_type', propertyType);

      expect(error).toBeNull();
      expect(data).toBeDefined();
      data?.forEach(property => {
        expect(property.property_type).toBe(propertyType);
      });
    });

    it('should search properties by title and description', async () => {
      const searchTerm = 'beautiful';

      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);

      expect(error).toBeNull();
      expect(data).toBeDefined();
      data?.forEach(property => {
        const matchesTitle = property.title.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesDescription = property.description.toLowerCase().includes(searchTerm.toLowerCase());
        expect(matchesTitle || matchesDescription).toBe(true);
      });
    });

    it('should sort properties by price', async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .order('price', { ascending: true });

      expect(error).toBeNull();
      expect(data).toBeDefined();
      
      if (data && data.length > 1) {
        for (let i = 1; i < data.length; i++) {
          expect(data[i].price).toBeGreaterThanOrEqual(data[i - 1].price);
        }
      }
    });

    it('should sort properties by creation date', async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .order('created_at', { ascending: false });

      expect(error).toBeNull();
      expect(data).toBeDefined();
      
      if (data && data.length > 1) {
        for (let i = 1; i < data.length; i++) {
          const currentDate = new Date(data[i].created_at);
          const previousDate = new Date(data[i - 1].created_at);
          expect(currentDate.getTime()).toBeLessThanOrEqual(previousDate.getTime());
        }
      }
    });
  });

  describe('Property Details', () => {
    it('should fetch single property by ID', async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .eq('id', mockProperty.id)
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data?.id).toBe(mockProperty.id);
      expect(data?.title).toBe(mockProperty.title);
    });

    it('should return 404 for non-existent property', async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('*')
        .eq('id', 'non-existent-id')
        .single();

      expect(error).toBeDefined();
      expect(data).toBeNull();
    });

    it('should include landlord information', async () => {
      const { data, error } = await supabase
        .from('properties')
        .select(`
          *,
          landlord:users!landlord_id (
            id,
            full_name,
            email,
            phone
          )
        `)
        .eq('id', mockProperty.id)
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data?.landlord).toBeDefined();
      expect(data?.landlord.id).toBe(mockProperty.landlord_id);
    });
  });

  describe('Property Creation', () => {
    it('should create property with valid data', async () => {
      // Mock authentication
      const mockSession = {
        access_token: 'mock-token',
        user: mockLandlord
      };
      
      // Set auth header
      supabase.auth.getSession = jest.fn().mockResolvedValue({
        data: { session: mockSession },
        error: null
      });

      const newProperty = {
        title: 'New Test Property',
        description: 'A beautiful new property for testing',
        price: 600000,
        location: 'Test Location, Port Harcourt',
        property_type: 'house',
        bedrooms: 4,
        bathrooms: 3,
        contact_phone: '+2348012345678',
        contact_email: '<EMAIL>',
        landlord_id: mockLandlord.id
      };

      const { data, error } = await supabase
        .from('properties')
        .insert(newProperty)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data?.title).toBe(newProperty.title);
      expect(data?.price).toBe(newProperty.price);
      expect(data?.landlord_id).toBe(newProperty.landlord_id);
    });

    it('should reject property creation without authentication', async () => {
      // Clear auth session
      supabase.auth.getSession = jest.fn().mockResolvedValue({
        data: { session: null },
        error: null
      });

      const newProperty = {
        title: 'Unauthorized Property',
        description: 'This should fail',
        price: 500000,
        location: 'Test Location',
        property_type: 'apartment',
        bedrooms: 2,
        bathrooms: 1
      };

      const { data, error } = await supabase
        .from('properties')
        .insert(newProperty);

      expect(error).toBeDefined();
      expect(data).toBeNull();
    });

    it('should validate required fields', async () => {
      const mockSession = {
        access_token: 'mock-token',
        user: mockLandlord
      };
      
      supabase.auth.getSession = jest.fn().mockResolvedValue({
        data: { session: mockSession },
        error: null
      });

      const incompleteProperty = {
        title: 'Incomplete Property'
        // Missing required fields
      };

      const { data, error } = await supabase
        .from('properties')
        .insert(incompleteProperty);

      expect(error).toBeDefined();
      expect(data).toBeNull();
    });
  });

  describe('Property Updates', () => {
    it('should update property with valid data', async () => {
      const mockSession = {
        access_token: 'mock-token',
        user: mockLandlord
      };
      
      supabase.auth.getSession = jest.fn().mockResolvedValue({
        data: { session: mockSession },
        error: null
      });

      const updates = {
        title: 'Updated Property Title',
        price: 550000,
        status: 'rented'
      };

      const { data, error } = await supabase
        .from('properties')
        .update(updates)
        .eq('id', mockProperty.id)
        .select()
        .single();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data?.title).toBe(updates.title);
      expect(data?.price).toBe(updates.price);
      expect(data?.status).toBe(updates.status);
    });

    it('should reject unauthorized updates', async () => {
      supabase.auth.getSession = jest.fn().mockResolvedValue({
        data: { session: null },
        error: null
      });

      const updates = {
        title: 'Unauthorized Update'
      };

      const { data, error } = await supabase
        .from('properties')
        .update(updates)
        .eq('id', mockProperty.id);

      expect(error).toBeDefined();
      expect(data).toBeNull();
    });

    it('should prevent updates by non-owners', async () => {
      const mockSession = {
        access_token: 'mock-token',
        user: mockUser // Different user, not the landlord
      };
      
      supabase.auth.getSession = jest.fn().mockResolvedValue({
        data: { session: mockSession },
        error: null
      });

      const updates = {
        title: 'Unauthorized Update by Non-Owner'
      };

      const { data, error } = await supabase
        .from('properties')
        .update(updates)
        .eq('id', mockProperty.id);

      expect(error).toBeDefined();
      expect(data).toBeNull();
    });
  });

  describe('Property Deletion', () => {
    it('should delete property by owner', async () => {
      const mockSession = {
        access_token: 'mock-token',
        user: mockLandlord
      };
      
      supabase.auth.getSession = jest.fn().mockResolvedValue({
        data: { session: mockSession },
        error: null
      });

      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', mockProperty.id);

      expect(error).toBeNull();

      // Verify property is deleted
      const { data, error: fetchError } = await supabase
        .from('properties')
        .select('*')
        .eq('id', mockProperty.id)
        .single();

      expect(fetchError).toBeDefined();
      expect(data).toBeNull();
    });

    it('should reject unauthorized deletion', async () => {
      supabase.auth.getSession = jest.fn().mockResolvedValue({
        data: { session: null },
        error: null
      });

      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', mockProperty.id);

      expect(error).toBeDefined();
    });
  });

  describe('Property Analytics', () => {
    it('should fetch property statistics', async () => {
      const { data, error } = await supabase
        .rpc('get_property_stats');

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data).toHaveProperty('total_properties');
      expect(data).toHaveProperty('available_properties');
      expect(data).toHaveProperty('average_price');
    });

    it('should fetch properties by location stats', async () => {
      const { data, error } = await supabase
        .rpc('get_properties_by_location');

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(Array.isArray(data)).toBe(true);
    });

    it('should fetch price trends', async () => {
      const { data, error } = await supabase
        .rpc('get_price_trends', {
          days: 30
        });

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(Array.isArray(data)).toBe(true);
    });
  });

  describe('Property Images', () => {
    it('should upload property images', async () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const fileName = `property-${mockProperty.id}-${Date.now()}.jpg`;

      const { data, error } = await supabase.storage
        .from('property-images')
        .upload(fileName, mockFile);

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data?.path).toBe(fileName);
    });

    it('should get public URL for uploaded images', async () => {
      const fileName = 'test-image.jpg';

      const { data } = supabase.storage
        .from('property-images')
        .getPublicUrl(fileName);

      expect(data.publicUrl).toBeDefined();
      expect(data.publicUrl).toContain(fileName);
    });

    it('should delete property images', async () => {
      const fileName = 'test-image-to-delete.jpg';

      const { error } = await supabase.storage
        .from('property-images')
        .remove([fileName]);

      expect(error).toBeNull();
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      // Simulate network error
      server.use(
        rest.get('*/rest/v1/properties', (req, res, ctx) => {
          return res.networkError('Network connection failed');
        })
      );

      const { data, error } = await supabase
        .from('properties')
        .select('*');

      expect(error).toBeDefined();
      expect(data).toBeNull();
    });

    it('should handle server errors gracefully', async () => {
      server.use(
        rest.get('*/rest/v1/properties', (req, res, ctx) => {
          return res(
            ctx.status(500),
            ctx.json({ error: 'Internal server error' })
          );
        })
      );

      const { data, error } = await supabase
        .from('properties')
        .select('*');

      expect(error).toBeDefined();
      expect(data).toBeNull();
    });

    it('should handle timeout errors', async () => {
      server.use(
        rest.get('*/rest/v1/properties', (req, res, ctx) => {
          return res(
            ctx.delay(10000),
            ctx.status(408),
            ctx.json({ error: 'Request timeout' })
          );
        })
      );

      const { data, error } = await supabase
        .from('properties')
        .select('*');

      expect(error).toBeDefined();
      expect(data).toBeNull();
    });
  });
});
