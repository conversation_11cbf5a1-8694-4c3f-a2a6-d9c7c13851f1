import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import AuthModal from '@/components/auth/AuthModal';
import { MockDataService } from '@/services/mockDataService';

// Mock the MockDataService
jest.mock('@/services/mockDataService', () => ({
  MockDataService: {
    signIn: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
    getCurrentUser: jest.fn(),
    resetPassword: jest.fn(),
  },
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>{children}</BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Authentication Flow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  describe('Sign In Flow', () => {
    it('should complete successful sign in flow', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'tenant',
        isActive: true,
      };

      (MockDataService.signIn as jest.Mock).mockResolvedValue({
        success: true,
        user: mockUser,
        token: 'mock-token',
      });

      const mockOnClose = jest.fn();

      render(
        <TestWrapper>
          <AuthModal isOpen={true} onClose={mockOnClose} />
        </TestWrapper>
      );

      // Fill in sign in form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      // Wait for API call
      await waitFor(() => {
        expect(MockDataService.signIn).toHaveBeenCalledWith('<EMAIL>', 'password123');
      });
    });

    it('should handle sign in errors gracefully', async () => {
      (MockDataService.signIn as jest.Mock).mockResolvedValue({
        success: false,
        error: 'Invalid credentials',
      });

      const mockOnClose = jest.fn();

      render(
        <TestWrapper>
          <AuthModal isOpen={true} onClose={mockOnClose} />
        </TestWrapper>
      );

      // Fill in sign in form with invalid credentials
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
      fireEvent.click(submitButton);

      // Wait for error message
      await waitFor(() => {
        expect(MockDataService.signIn).toHaveBeenCalledWith('<EMAIL>', 'wrongpassword');
      });
    });
  });

  describe('Sign Up Flow', () => {
    it('should complete successful sign up flow', async () => {
      const mockUser = {
        id: '2',
        email: '<EMAIL>',
        role: 'tenant',
        isActive: true,
      };

      (MockDataService.signUp as jest.Mock).mockResolvedValue({
        success: true,
        user: mockUser,
        token: 'mock-token',
      });

      const mockOnClose = jest.fn();

      render(
        <TestWrapper>
          <AuthModal isOpen={true} onClose={mockOnClose} />
        </TestWrapper>
      );

      // Switch to sign up mode
      const signUpToggle = screen.getByText(/don't have an account/i);
      fireEvent.click(signUpToggle);

      await waitFor(() => {
        expect(screen.getByText(/create account/i)).toBeInTheDocument();
      });

      // Fill in sign up form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const fullNameInput = screen.getByLabelText(/full name/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.change(fullNameInput, { target: { value: 'New User' } });
      fireEvent.click(submitButton);

      // Wait for API call
      await waitFor(() => {
        expect(MockDataService.signUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
          fullName: 'New User',
          role: 'tenant',
        });
      });
    });
  });

  describe('Form Validation Integration', () => {
    it('should validate email format across sign in and sign up', async () => {
      const mockOnClose = jest.fn();

      render(
        <TestWrapper>
          <AuthModal isOpen={true} onClose={mockOnClose} />
        </TestWrapper>
      );

      // Test sign in validation
      const emailInput = screen.getByLabelText(/email/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
      });

      // Switch to sign up and test validation there too
      const signUpToggle = screen.getByText(/don't have an account/i);
      fireEvent.click(signUpToggle);

      await waitFor(() => {
        const signUpEmailInput = screen.getByLabelText(/email/i);
        const signUpSubmitButton = screen.getByRole('button', { name: /create account/i });

        fireEvent.change(signUpEmailInput, { target: { value: 'another-invalid-email' } });
        fireEvent.click(signUpSubmitButton);
      });

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
      });
    });

    it('should validate password requirements in sign up', async () => {
      const mockOnClose = jest.fn();

      render(
        <TestWrapper>
          <AuthModal isOpen={true} onClose={mockOnClose} />
        </TestWrapper>
      );

      // Switch to sign up
      const signUpToggle = screen.getByText(/don't have an account/i);
      fireEvent.click(signUpToggle);

      await waitFor(() => {
        const passwordInput = screen.getByLabelText(/^password$/i);
        const submitButton = screen.getByRole('button', { name: /create account/i });

        // Test weak password
        fireEvent.change(passwordInput, { target: { value: '123' } });
        fireEvent.click(submitButton);
      });

      await waitFor(() => {
        expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
      });
    });
  });

  describe('Modal State Management', () => {
    it('should handle modal open/close state correctly', () => {
      const mockOnClose = jest.fn();

      const { rerender } = render(
        <TestWrapper>
          <AuthModal isOpen={false} onClose={mockOnClose} />
        </TestWrapper>
      );

      // Modal should not be visible when closed
      expect(screen.queryByText(/sign in/i)).not.toBeInTheDocument();

      // Rerender with modal open
      rerender(
        <TestWrapper>
          <AuthModal isOpen={true} onClose={mockOnClose} />
        </TestWrapper>
      );

      // Modal should be visible when open
      expect(screen.getByText(/sign in/i)).toBeInTheDocument();

      // Close button should call onClose
      const closeButton = screen.getByRole('button', { name: /close/i });
      fireEvent.click(closeButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });
});
