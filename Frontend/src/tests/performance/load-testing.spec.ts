// =====================================================
// PERFORMANCE LOAD TESTING
// Comprehensive performance and load testing suite
// =====================================================

import { test, expect, Page } from '@playwright/test';

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  pageLoadTime: 3000, // 3 seconds
  firstContentfulPaint: 1500, // 1.5 seconds
  largestContentfulPaint: 2500, // 2.5 seconds
  firstInputDelay: 100, // 100ms
  cumulativeLayoutShift: 0.1,
  timeToInteractive: 3000, // 3 seconds
  apiResponseTime: 1000, // 1 second
  imageLoadTime: 2000, // 2 seconds
  bundleSize: 500 * 1024, // 500KB
  memoryUsage: 50 * 1024 * 1024 // 50MB
};

// Helper functions
async function measurePageLoadTime(page: Page, url: string): Promise<number> {
  const startTime = Date.now();
  await page.goto(url, { waitUntil: 'networkidle' });
  const endTime = Date.now();
  return endTime - startTime;
}

async function measureWebVitals(page: Page) {
  return await page.evaluate(() => {
    return new Promise((resolve) => {
      const vitals: any = {};
      
      // Measure FCP (First Contentful Paint)
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            vitals.fcp = entry.startTime;
          }
        });
      }).observe({ entryTypes: ['paint'] });

      // Measure LCP (Largest Contentful Paint)
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        vitals.lcp = lastEntry.startTime;
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // Measure FID (First Input Delay)
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          vitals.fid = entry.processingStart - entry.startTime;
        });
      }).observe({ entryTypes: ['first-input'] });

      // Measure CLS (Cumulative Layout Shift)
      let clsValue = 0;
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        vitals.cls = clsValue;
      }).observe({ entryTypes: ['layout-shift'] });

      // Measure TTI (Time to Interactive)
      if ('getEntriesByType' in performance) {
        const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        vitals.tti = navigationEntry.domInteractive - navigationEntry.navigationStart;
      }

      setTimeout(() => resolve(vitals), 3000);
    });
  });
}

async function measureMemoryUsage(page: Page): Promise<number> {
  return await page.evaluate(() => {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  });
}

async function measureBundleSize(page: Page): Promise<number> {
  const resources = await page.evaluate(() => {
    const entries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    return entries
      .filter(entry => entry.name.includes('.js') || entry.name.includes('.css'))
      .reduce((total, entry) => total + (entry.transferSize || 0), 0);
  });
  return resources;
}

test.describe('Performance Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Clear cache and storage
    await page.context().clearCookies();
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  });

  test('Homepage Performance', async ({ page }) => {
    // Measure page load time
    const loadTime = await measurePageLoadTime(page, '/');
    console.log(`Homepage load time: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.pageLoadTime);

    // Measure Web Vitals
    const vitals = await measureWebVitals(page);
    console.log('Web Vitals:', vitals);

    if (vitals.fcp) {
      expect(vitals.fcp).toBeLessThan(PERFORMANCE_THRESHOLDS.firstContentfulPaint);
    }
    if (vitals.lcp) {
      expect(vitals.lcp).toBeLessThan(PERFORMANCE_THRESHOLDS.largestContentfulPaint);
    }
    if (vitals.fid) {
      expect(vitals.fid).toBeLessThan(PERFORMANCE_THRESHOLDS.firstInputDelay);
    }
    if (vitals.cls) {
      expect(vitals.cls).toBeLessThan(PERFORMANCE_THRESHOLDS.cumulativeLayoutShift);
    }
    if (vitals.tti) {
      expect(vitals.tti).toBeLessThan(PERFORMANCE_THRESHOLDS.timeToInteractive);
    }

    // Measure bundle size
    const bundleSize = await measureBundleSize(page);
    console.log(`Bundle size: ${bundleSize} bytes`);
    expect(bundleSize).toBeLessThan(PERFORMANCE_THRESHOLDS.bundleSize);

    // Measure memory usage
    const memoryUsage = await measureMemoryUsage(page);
    console.log(`Memory usage: ${memoryUsage} bytes`);
    expect(memoryUsage).toBeLessThan(PERFORMANCE_THRESHOLDS.memoryUsage);
  });

  test('Properties Page Performance', async ({ page }) => {
    const loadTime = await measurePageLoadTime(page, '/properties');
    console.log(`Properties page load time: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.pageLoadTime);

    // Test search performance
    const searchStartTime = Date.now();
    await page.fill('[data-testid="search-input"]', 'apartment');
    await page.click('[data-testid="search-button"]');
    await page.waitForSelector('[data-testid="property-card"]');
    const searchTime = Date.now() - searchStartTime;
    
    console.log(`Search time: ${searchTime}ms`);
    expect(searchTime).toBeLessThan(2000); // 2 seconds for search

    // Test infinite scroll performance
    const initialPropertyCount = await page.locator('[data-testid="property-card"]').count();
    
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    
    await page.waitForTimeout(1000);
    const newPropertyCount = await page.locator('[data-testid="property-card"]').count();
    
    expect(newPropertyCount).toBeGreaterThan(initialPropertyCount);
  });

  test('Image Loading Performance', async ({ page }) => {
    await page.goto('/properties');
    
    // Measure image loading time
    const imageLoadPromises = page.locator('[data-testid="property-image"]').all();
    const images = await imageLoadPromises;
    
    for (const image of images.slice(0, 5)) { // Test first 5 images
      const startTime = Date.now();
      await image.waitFor({ state: 'visible' });
      const loadTime = Date.now() - startTime;
      
      console.log(`Image load time: ${loadTime}ms`);
      expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.imageLoadTime);
    }

    // Test lazy loading
    const belowFoldImages = page.locator('[data-testid="property-image"]').nth(10);
    if (await belowFoldImages.isVisible()) {
      // Should not be loaded initially
      const src = await belowFoldImages.getAttribute('src');
      expect(src).toContain('placeholder');
    }
  });

  test('API Response Performance', async ({ page }) => {
    // Intercept API calls and measure response times
    const apiTimes: number[] = [];
    
    page.on('response', response => {
      if (response.url().includes('/api/') || response.url().includes('/rest/v1/')) {
        const timing = response.timing();
        if (timing) {
          const responseTime = timing.responseEnd - timing.requestStart;
          apiTimes.push(responseTime);
          console.log(`API ${response.url()} response time: ${responseTime}ms`);
        }
      }
    });

    await page.goto('/properties');
    await page.waitForLoadState('networkidle');

    // Check API response times
    apiTimes.forEach(time => {
      expect(time).toBeLessThan(PERFORMANCE_THRESHOLDS.apiResponseTime);
    });

    const averageApiTime = apiTimes.reduce((sum, time) => sum + time, 0) / apiTimes.length;
    console.log(`Average API response time: ${averageApiTime}ms`);
    expect(averageApiTime).toBeLessThan(PERFORMANCE_THRESHOLDS.apiResponseTime);
  });

  test('Memory Leak Detection', async ({ page }) => {
    await page.goto('/');
    
    const initialMemory = await measureMemoryUsage(page);
    console.log(`Initial memory: ${initialMemory} bytes`);

    // Navigate through multiple pages
    const pages = ['/properties', '/dashboard', '/messages', '/favorites'];
    
    for (const pagePath of pages) {
      await page.goto(pagePath);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000);
    }

    // Force garbage collection if available
    await page.evaluate(() => {
      if ('gc' in window) {
        (window as any).gc();
      }
    });

    const finalMemory = await measureMemoryUsage(page);
    console.log(`Final memory: ${finalMemory} bytes`);
    
    const memoryIncrease = finalMemory - initialMemory;
    console.log(`Memory increase: ${memoryIncrease} bytes`);
    
    // Memory should not increase by more than 20MB
    expect(memoryIncrease).toBeLessThan(20 * 1024 * 1024);
  });

  test('Concurrent User Simulation', async ({ browser }) => {
    const concurrentUsers = 5;
    const userPromises: Promise<void>[] = [];

    for (let i = 0; i < concurrentUsers; i++) {
      const userPromise = (async () => {
        const context = await browser.newContext();
        const page = await context.newPage();
        
        try {
          const startTime = Date.now();
          await page.goto('/properties');
          await page.waitForSelector('[data-testid="property-card"]');
          
          // Simulate user interactions
          await page.fill('[data-testid="search-input"]', 'apartment');
          await page.click('[data-testid="search-button"]');
          await page.waitForSelector('[data-testid="property-card"]');
          
          const endTime = Date.now();
          const totalTime = endTime - startTime;
          
          console.log(`User ${i + 1} completed in: ${totalTime}ms`);
          expect(totalTime).toBeLessThan(10000); // 10 seconds max
          
        } finally {
          await context.close();
        }
      })();
      
      userPromises.push(userPromise);
    }

    await Promise.all(userPromises);
  });

  test('Mobile Performance', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Simulate slow 3G network
    await page.context().route('**/*', async route => {
      await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay
      await route.continue();
    });

    const loadTime = await measurePageLoadTime(page, '/');
    console.log(`Mobile load time: ${loadTime}ms`);
    
    // Mobile should load within 5 seconds on slow network
    expect(loadTime).toBeLessThan(5000);

    const vitals = await measureWebVitals(page);
    console.log('Mobile Web Vitals:', vitals);

    // Mobile-specific thresholds (more lenient)
    if (vitals.fcp) {
      expect(vitals.fcp).toBeLessThan(2500);
    }
    if (vitals.lcp) {
      expect(vitals.lcp).toBeLessThan(4000);
    }
  });

  test('Database Query Performance', async ({ page }) => {
    // Test large dataset performance
    await page.goto('/properties');
    
    // Load many properties
    for (let i = 0; i < 5; i++) {
      await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });
      await page.waitForTimeout(500);
    }

    const propertyCount = await page.locator('[data-testid="property-card"]').count();
    console.log(`Loaded ${propertyCount} properties`);
    
    // Should be able to load at least 50 properties efficiently
    expect(propertyCount).toBeGreaterThan(50);

    // Test complex search performance
    const complexSearchStart = Date.now();
    await page.fill('[data-testid="search-input"]', 'luxury apartment');
    await page.selectOption('[data-testid="location-filter"]', 'GRA');
    await page.fill('[data-testid="min-price-input"]', '500000');
    await page.fill('[data-testid="max-price-input"]', '1000000');
    await page.check('[data-testid="apartment-checkbox"]');
    await page.click('[data-testid="search-button"]');
    await page.waitForSelector('[data-testid="property-card"]');
    
    const complexSearchTime = Date.now() - complexSearchStart;
    console.log(`Complex search time: ${complexSearchTime}ms`);
    expect(complexSearchTime).toBeLessThan(3000);
  });

  test('Resource Optimization', async ({ page }) => {
    await page.goto('/');

    // Check for unused CSS
    const unusedCSS = await page.evaluate(() => {
      const stylesheets = Array.from(document.styleSheets);
      let totalRules = 0;
      let usedRules = 0;

      stylesheets.forEach(sheet => {
        try {
          const rules = Array.from(sheet.cssRules || []);
          totalRules += rules.length;
          
          rules.forEach(rule => {
            if (rule.type === CSSRule.STYLE_RULE) {
              const styleRule = rule as CSSStyleRule;
              if (document.querySelector(styleRule.selectorText)) {
                usedRules++;
              }
            }
          });
        } catch (e) {
          // Cross-origin stylesheets
        }
      });

      return {
        total: totalRules,
        used: usedRules,
        unused: totalRules - usedRules,
        efficiency: usedRules / totalRules
      };
    });

    console.log('CSS Usage:', unusedCSS);
    expect(unusedCSS.efficiency).toBeGreaterThan(0.7); // 70% CSS should be used

    // Check for duplicate resources
    const resources = await page.evaluate(() => {
      const entries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const resourceMap = new Map();
      
      entries.forEach(entry => {
        const url = entry.name;
        if (resourceMap.has(url)) {
          resourceMap.set(url, resourceMap.get(url) + 1);
        } else {
          resourceMap.set(url, 1);
        }
      });

      const duplicates = Array.from(resourceMap.entries())
        .filter(([url, count]) => count > 1);
      
      return duplicates;
    });

    console.log('Duplicate resources:', resources);
    expect(resources.length).toBe(0); // No duplicate resources
  });
});
