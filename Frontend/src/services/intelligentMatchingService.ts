import { MockDataService } from '@/services/mockDataService';

export interface MatchingCriteria {
  user_id: string;
  budget_min: number;
  budget_max: number;
  property_types: string[];
  locations: string[];
  bedrooms: number[];
  bathrooms: number[];
  amenities: string[];
  lifestyle_preferences: string[];
  commute_requirements: Array<{
    location: string;
    max_distance_km: number;
    importance: 'high' | 'medium' | 'low';
  }>;
  move_in_date: string;
  lease_duration_months: number;
  pet_friendly: boolean;
  furnished: boolean;
}

export interface PropertyMatch {
  property_id: string;
  match_score: number;
  confidence_level: number;
  match_factors: {
    budget_match: number;
    location_match: number;
    size_match: number;
    amenity_match: number;
    lifestyle_match: number;
    commute_match: number;
    availability_match: number;
  };
  reasons: string[];
  potential_issues: string[];
  recommendation_strength: 'strong' | 'moderate' | 'weak';
  property_details: any;
}

export interface AgentClientMatch {
  agent_id: string;
  client_id: string;
  match_score: number;
  compatibility_factors: {
    expertise_match: number;
    location_expertise: number;
    communication_style: number;
    availability_match: number;
    success_rate: number;
    client_preference_match: number;
  };
  predicted_success_rate: number;
  recommended_approach: string[];
  potential_challenges: string[];
}

export interface LeadScore {
  lead_id: string;
  score: number;
  quality_grade: 'A' | 'B' | 'C' | 'D';
  conversion_probability: number;
  priority_level: 'high' | 'medium' | 'low';
  scoring_factors: {
    budget_qualification: number;
    timeline_urgency: number;
    engagement_level: number;
    information_completeness: number;
    behavioral_indicators: number;
    source_quality: number;
  };
  recommended_actions: string[];
  follow_up_schedule: Array<{
    action: string;
    timing: string;
    priority: number;
  }>;
}

export interface SmartNotification {
  user_id: string;
  notification_type: 'property_match' | 'price_alert' | 'market_update' | 'agent_recommendation';
  priority: 'high' | 'medium' | 'low';
  title: string;
  message: string;
  action_url?: string;
  metadata: any;
  delivery_channels: ('email' | 'sms' | 'push' | 'whatsapp')[];
  optimal_send_time: string;
  personalization_data: any;
}

export interface ConversionPrediction {
  user_id: string;
  property_id?: string;
  agent_id?: string;
  conversion_probability: number;
  predicted_timeline_days: number;
  confidence_level: number;
  key_factors: Array<{
    factor: string;
    impact: number;
    description: string;
  }>;
  recommended_interventions: Array<{
    action: string;
    expected_impact: number;
    timing: string;
  }>;
  risk_factors: string[];
}

/**
 * Intelligent Matching Service
 * Uses AI algorithms to match tenants with properties, clients with agents,
 * and provide intelligent lead scoring and conversion predictions
 */
export class IntelligentMatchingService {
  private static instance: IntelligentMatchingService;
  private matchingCache: Map<string, any> = new Map();
  private userBehaviorCache: Map<string, any> = new Map();
  private cacheExpiry: number = 15 * 60 * 1000; // 15 minutes

  public static getInstance(): IntelligentMatchingService {
    if (!IntelligentMatchingService.instance) {
      IntelligentMatchingService.instance = new IntelligentMatchingService();
    }
    return IntelligentMatchingService.instance;
  }

  // =====================================================
  // PROPERTY-TENANT MATCHING
  // =====================================================

  /**
   * Find best property matches for a user using AI algorithms
   */
  async findPropertyMatches(
    criteria: MatchingCriteria,
    limit: number = 20
  ): Promise<PropertyMatch[]> {
    try {
      // Get available properties that meet basic criteria
      const candidateProperties = await this.getCandidateProperties(criteria);
      
      if (!candidateProperties.length) return [];

      // Calculate match scores for each property
      const matches = await Promise.all(
        candidateProperties.map(async (property) => {
          const matchScore = await this.calculatePropertyMatchScore(property, criteria);
          return {
            property_id: property.id,
            match_score: matchScore.total_score,
            confidence_level: matchScore.confidence,
            match_factors: matchScore.factors,
            reasons: matchScore.reasons,
            potential_issues: matchScore.issues,
            recommendation_strength: this.determineRecommendationStrength(matchScore.total_score),
            property_details: property
          };
        })
      );

      // Sort by match score and apply diversity filter
      const sortedMatches = matches.sort((a, b) => b.match_score - a.match_score);
      const diverseMatches = this.applyDiversityFilter(sortedMatches, limit);

      // Log matching event for learning
      await this.logMatchingEvent(criteria.user_id, diverseMatches);

      return diverseMatches;
    } catch (error) {
      console.error('Error finding property matches:', error);
      throw new Error('Failed to find property matches');
    }
  }

  /**
   * Calculate comprehensive property match score
   */
  private async calculatePropertyMatchScore(
    property: any,
    criteria: MatchingCriteria
  ): Promise<{
    total_score: number;
    confidence: number;
    factors: any;
    reasons: string[];
    issues: string[];
  }> {
    const factors = {
      budget_match: this.calculateBudgetMatch(property.price_per_year, criteria),
      location_match: this.calculateLocationMatch(property.location, criteria.locations),
      size_match: this.calculateSizeMatch(property, criteria),
      amenity_match: this.calculateAmenityMatch(property.amenities, criteria.amenities),
      lifestyle_match: await this.calculateLifestyleMatch(property, criteria.lifestyle_preferences),
      commute_match: await this.calculateCommuteMatch(property, criteria.commute_requirements),
      availability_match: this.calculateAvailabilityMatch(property, criteria)
    };

    // Weighted scoring
    const weights = {
      budget_match: 0.25,
      location_match: 0.20,
      size_match: 0.15,
      amenity_match: 0.15,
      lifestyle_match: 0.10,
      commute_match: 0.10,
      availability_match: 0.05
    };

    const totalScore = Object.entries(factors).reduce(
      (sum, [key, value]) => sum + (value * weights[key as keyof typeof weights]),
      0
    );

    // Calculate confidence based on data completeness
    const confidence = this.calculateMatchConfidence(factors, property, criteria);

    // Generate reasons and issues
    const reasons = this.generateMatchReasons(factors, property, criteria);
    const issues = this.identifyPotentialIssues(factors, property, criteria);

    return {
      total_score: Math.round(totalScore * 100) / 100,
      confidence: Math.round(confidence * 100) / 100,
      factors,
      reasons,
      issues
    };
  }

  // =====================================================
  // AGENT-CLIENT MATCHING
  // =====================================================

  /**
   * Find best agent matches for a client
   */
  async findAgentMatches(
    clientId: string,
    clientRequirements: any,
    limit: number = 5
  ): Promise<AgentClientMatch[]> {
    try {
      // Get available agents
      const { data: agents, error } = await supabase
        .from('agent_profiles')
        .select('*')
        .eq('is_active', true)
        .eq('is_verified', true);

      if (error) throw error;

      // Calculate match scores
      const matches = await Promise.all(
        agents.map(async (agent) => {
          const matchScore = await this.calculateAgentClientMatchScore(agent, clientId, clientRequirements);
          return {
            agent_id: agent.agent_id,
            client_id: clientId,
            match_score: matchScore.total_score,
            compatibility_factors: matchScore.factors,
            predicted_success_rate: matchScore.success_rate,
            recommended_approach: matchScore.approach,
            potential_challenges: matchScore.challenges
          };
        })
      );

      return matches
        .sort((a, b) => b.match_score - a.match_score)
        .slice(0, limit);

    } catch (error) {
      console.error('Error finding agent matches:', error);
      throw new Error('Failed to find agent matches');
    }
  }

  // =====================================================
  // LEAD SCORING
  // =====================================================

  /**
   * Calculate intelligent lead score
   */
  async calculateLeadScore(leadData: any): Promise<LeadScore> {
    try {
      const factors = {
        budget_qualification: this.scoreBudgetQualification(leadData),
        timeline_urgency: this.scoreTimelineUrgency(leadData),
        engagement_level: await this.scoreEngagementLevel(leadData),
        information_completeness: this.scoreInformationCompleteness(leadData),
        behavioral_indicators: await this.scoreBehavioralIndicators(leadData),
        source_quality: this.scoreSourceQuality(leadData)
      };

      // Weighted scoring
      const weights = {
        budget_qualification: 0.25,
        timeline_urgency: 0.20,
        engagement_level: 0.20,
        information_completeness: 0.15,
        behavioral_indicators: 0.15,
        source_quality: 0.05
      };

      const totalScore = Object.entries(factors).reduce(
        (sum, [key, value]) => sum + (value * weights[key as keyof typeof weights]),
        0
      );

      const qualityGrade = this.determineQualityGrade(totalScore);
      const conversionProbability = this.calculateConversionProbability(totalScore, factors);
      const priorityLevel = this.determinePriorityLevel(totalScore, factors);

      return {
        lead_id: leadData.id,
        score: Math.round(totalScore * 100),
        quality_grade: qualityGrade,
        conversion_probability: Math.round(conversionProbability * 100) / 100,
        priority_level: priorityLevel,
        scoring_factors: factors,
        recommended_actions: this.generateRecommendedActions(factors, totalScore),
        follow_up_schedule: this.generateFollowUpSchedule(factors, priorityLevel)
      };

    } catch (error) {
      console.error('Error calculating lead score:', error);
      throw new Error('Failed to calculate lead score');
    }
  }
}

// Export singleton instance
export const intelligentMatchingService = IntelligentMatchingService.getInstance();
