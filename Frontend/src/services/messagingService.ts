import { MockDataService } from '@/services/mockDataService';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface Conversation {
  id: string;
  participants: string[];
  conversation_type: 'direct' | 'group' | 'support';
  title?: string;
  description?: string;
  property_id?: string;
  last_message?: Message;
  last_activity: string;
  is_archived: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  message_type: 'text' | 'image' | 'document' | 'audio' | 'video' | 'location' | 'system';
  content: {
    text?: string;
    file_url?: string;
    file_name?: string;
    file_size?: number;
    file_type?: string;
    location?: { latitude: number; longitude: number; address?: string };
    system_message?: string;
  };
  reply_to?: string;
  thread_id?: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  is_edited: boolean;
  is_deleted: boolean;
  reactions: Array<{
    user_id: string;
    emoji: string;
    created_at: string;
  }>;
  mentions: string[];
  created_at: string;
  updated_at: string;
  delivered_at?: string;
  read_at?: string;
}

export interface MessageThread {
  id: string;
  parent_message_id: string;
  conversation_id: string;
  messages: Message[];
  participant_count: number;
  last_activity: string;
  created_at: string;
}

export interface TypingIndicator {
  conversation_id: string;
  user_id: string;
  is_typing: boolean;
  timestamp: string;
}

export interface MessageDeliveryStatus {
  message_id: string;
  user_id: string;
  status: 'sent' | 'delivered' | 'read';
  timestamp: string;
}

export interface FileUpload {
  id: string;
  file_name: string;
  file_size: number;
  file_type: string;
  file_url: string;
  uploaded_by: string;
  conversation_id: string;
  message_id?: string;
  created_at: string;
}

export interface MessageEncryption {
  message_id: string;
  encrypted_content: string;
  encryption_key_id: string;
  algorithm: string;
  created_at: string;
}

/**
 * In-App Messaging Service
 * Provides real-time chat capabilities with message threading,
 * file sharing, encryption, and delivery tracking
 */
export class MessagingService {
  private static instance: MessagingService;
  private realtimeChannels: Map<string, RealtimeChannel> = new Map();
  private typingTimers: Map<string, NodeJS.Timeout> = new Map();
  private messageCache: Map<string, Message[]> = new Map();
  private encryptionEnabled: boolean = true;

  public static getInstance(): MessagingService {
    if (!MessagingService.instance) {
      MessagingService.instance = new MessagingService();
    }
    return MessagingService.instance;
  }

  // =====================================================
  // CONVERSATION MANAGEMENT
  // =====================================================

  /**
   * Create a new conversation
   */
  async createConversation(
    participants: string[],
    conversationType: 'direct' | 'group' | 'support',
    options: {
      title?: string;
      description?: string;
      property_id?: string;
    } = {}
  ): Promise<Conversation> {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .insert({
          participants,
          conversation_type: conversationType,
          title: options.title,
          description: options.description,
          property_id: options.property_id,
          last_activity: new Date().toISOString(),
          is_archived: false,
          created_by: participants[0], // Assuming first participant is creator
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Subscribe to real-time updates for this conversation
      await this.subscribeToConversation(data.id);

      return data;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw new Error('Failed to create conversation');
    }
  }

  /**
   * Get user's conversations
   */
  async getUserConversations(userId: string): Promise<Conversation[]> {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .select(`
          *,
          last_message:messages(*)
        `)
        .contains('participants', [userId])
        .eq('is_archived', false)
        .order('last_activity', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting user conversations:', error);
      return [];
    }
  }

  /**
   * Get conversation by ID
   */
  async getConversation(conversationId: string): Promise<Conversation | null> {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .select('*')
        .eq('id', conversationId)
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error getting conversation:', error);
      return null;
    }
  }

  // =====================================================
  // MESSAGE HANDLING
  // =====================================================

  /**
   * Send a message
   */
  async sendMessage(
    conversationId: string,
    senderId: string,
    messageType: Message['message_type'],
    content: Message['content'],
    options: {
      reply_to?: string;
      thread_id?: string;
      mentions?: string[];
    } = {}
  ): Promise<Message> {
    try {
      // Encrypt content if encryption is enabled
      const processedContent = this.encryptionEnabled 
        ? await this.encryptMessageContent(content)
        : content;

      const messageData = {
        conversation_id: conversationId,
        sender_id: senderId,
        message_type: messageType,
        content: processedContent,
        reply_to: options.reply_to,
        thread_id: options.thread_id,
        status: 'sent',
        is_edited: false,
        is_deleted: false,
        reactions: [],
        mentions: options.mentions || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('messages')
        .insert(messageData)
        .select()
        .single();

      if (error) throw error;

      // Update conversation last activity
      await this.updateConversationActivity(conversationId);

      // Send real-time notification
      await this.broadcastMessage(conversationId, data);

      // Update message cache
      this.updateMessageCache(conversationId, data);

      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to send message');
    }
  }

  /**
   * Get conversation messages
   */
  async getConversationMessages(
    conversationId: string,
    options: {
      limit?: number;
      offset?: number;
      before_timestamp?: string;
      thread_id?: string;
    } = {}
  ): Promise<Message[]> {
    try {
      // Check cache first
      const cacheKey = `${conversationId}-${JSON.stringify(options)}`;
      if (this.messageCache.has(cacheKey)) {
        return this.messageCache.get(cacheKey)!;
      }

      let query = supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });

      if (options.thread_id) {
        query = query.eq('thread_id', options.thread_id);
      }

      if (options.before_timestamp) {
        query = query.lt('created_at', options.before_timestamp);
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1);
      }

      const { data, error } = await query;

      if (error) throw error;

      const messages = data || [];

      // Decrypt messages if encryption is enabled
      const decryptedMessages = this.encryptionEnabled 
        ? await Promise.all(messages.map(msg => this.decryptMessage(msg)))
        : messages;

      // Cache the results
      this.messageCache.set(cacheKey, decryptedMessages);

      return decryptedMessages;
    } catch (error) {
      console.error('Error getting conversation messages:', error);
      return [];
    }
  }

  /**
   * Edit a message
   */
  async editMessage(messageId: string, newContent: Message['content']): Promise<Message> {
    try {
      const processedContent = this.encryptionEnabled 
        ? await this.encryptMessageContent(newContent)
        : newContent;

      const { data, error } = await supabase
        .from('messages')
        .update({
          content: processedContent,
          is_edited: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', messageId)
        .select()
        .single();

      if (error) throw error;

      // Broadcast edit event
      await this.broadcastMessageEdit(data.conversation_id, data);

      return data;
    } catch (error) {
      console.error('Error editing message:', error);
      throw new Error('Failed to edit message');
    }
  }

  /**
   * Delete a message
   */
  async deleteMessage(messageId: string): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .update({
          is_deleted: true,
          content: { text: 'This message was deleted' },
          updated_at: new Date().toISOString()
        })
        .eq('id', messageId)
        .select()
        .single();

      if (error) throw error;

      // Broadcast delete event
      await this.broadcastMessageDelete(data.conversation_id, messageId);

    } catch (error) {
      console.error('Error deleting message:', error);
      throw new Error('Failed to delete message');
    }
  }

  // =====================================================
  // REAL-TIME FEATURES
  // =====================================================

  /**
   * Subscribe to conversation updates
   */
  async subscribeToConversation(conversationId: string): Promise<void> {
    if (this.realtimeChannels.has(conversationId)) {
      return; // Already subscribed
    }

    const channel = supabase
      .channel(`conversation:${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        (payload) => {
          this.handleNewMessage(payload.new as Message);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        (payload) => {
          this.handleMessageUpdate(payload.new as Message);
        }
      )
      .on('broadcast', { event: 'typing' }, (payload) => {
        this.handleTypingIndicator(payload.payload as TypingIndicator);
      })
      .on('broadcast', { event: 'message_read' }, (payload) => {
        this.handleMessageRead(payload.payload as MessageDeliveryStatus);
      });

    await channel.subscribe();
    this.realtimeChannels.set(conversationId, channel);
  }

  /**
   * Unsubscribe from conversation updates
   */
  async unsubscribeFromConversation(conversationId: string): Promise<void> {
    const channel = this.realtimeChannels.get(conversationId);
    if (channel) {
      await channel.unsubscribe();
      this.realtimeChannels.delete(conversationId);
    }
  }

  /**
   * Send typing indicator
   */
  async sendTypingIndicator(conversationId: string, userId: string, isTyping: boolean): Promise<void> {
    const channel = this.realtimeChannels.get(conversationId);
    if (channel) {
      await channel.send({
        type: 'broadcast',
        event: 'typing',
        payload: {
          conversation_id: conversationId,
          user_id: userId,
          is_typing: isTyping,
          timestamp: new Date().toISOString()
        }
      });

      // Auto-stop typing after 3 seconds
      if (isTyping) {
        const timerId = this.typingTimers.get(`${conversationId}-${userId}`);
        if (timerId) clearTimeout(timerId);

        const newTimerId = setTimeout(() => {
          this.sendTypingIndicator(conversationId, userId, false);
        }, 3000);

        this.typingTimers.set(`${conversationId}-${userId}`, newTimerId);
      }
    }
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(messageId: string, userId: string): Promise<void> {
    try {
      // Update message delivery status
      const { data, error } = await supabase
        .from('message_delivery_status')
        .upsert({
          message_id: messageId,
          user_id: userId,
          status: 'read',
          timestamp: new Date().toISOString()
        });

      if (error) throw error;

      // Get message to broadcast read status
      const { data: message } = await supabase
        .from('messages')
        .select('conversation_id')
        .eq('id', messageId)
        .single();

      if (message) {
        const channel = this.realtimeChannels.get(message.conversation_id);
        if (channel) {
          await channel.send({
            type: 'broadcast',
            event: 'message_read',
            payload: {
              message_id: messageId,
              user_id: userId,
              status: 'read',
              timestamp: new Date().toISOString()
            }
          });
        }
      }

    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  }

  // =====================================================
  // FILE SHARING
  // =====================================================

  /**
   * Upload file for messaging
   */
  async uploadFile(
    file: File,
    conversationId: string,
    uploadedBy: string
  ): Promise<FileUpload> {
    try {
      // Generate unique filename
      const fileExtension = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExtension}`;
      const filePath = `conversations/${conversationId}/${fileName}`;

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('message-files')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('message-files')
        .getPublicUrl(filePath);

      // Save file record
      const { data, error } = await supabase
        .from('file_uploads')
        .insert({
          file_name: file.name,
          file_size: file.size,
          file_type: file.type,
          file_url: urlData.publicUrl,
          uploaded_by: uploadedBy,
          conversation_id: conversationId,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw new Error('Failed to upload file');
    }
  }
}

// Export singleton instance
export const messagingService = MessagingService.getInstance();
