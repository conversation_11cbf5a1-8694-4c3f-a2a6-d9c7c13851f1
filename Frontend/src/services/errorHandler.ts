import { toast } from 'sonner';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  url?: string;
  category?: string;
  userAgent?: string;
  timestamp?: string;
  sessionId?: string;
}

export interface ErrorDetails {
  message: string;
  stack?: string;
  code?: string | number;
  status?: number;
  context?: ErrorContext;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  category?: string // e.g 'network' | 'validation' | 'authentication' | 'authorization' | 'runtime' | 'unknown';
}

export class GlobalErrorHandler {
  private static instance: GlobalErrorHandler;
  private errorQueue: ErrorDetails[] = [];
  private isOnline = navigator.onLine;
  private maxRetries = 3;
  private retryDelay = 1000;

  private constructor() {
    this.setupGlobalHandlers();
    this.setupNetworkHandlers();
  }

  static getInstance(): GlobalErrorHandler {
    if (!GlobalErrorHandler.instance) {
      GlobalErrorHandler.instance = new GlobalErrorHandler();
    }
    return GlobalErrorHandler.instance;
  }

  private setupGlobalHandlers() {
    // Handle unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError({
        message: event.message,
        stack: event.error?.stack,
        context: {
          component: 'Global',
          action: 'JavaScript Error',
          url: event.filename,
          timestamp: new Date().toISOString(),
        },
        severity: 'high',
        category: 'runtime',
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack,
        context: {
          component: 'Global',
          action: 'Unhandled Promise Rejection',
          timestamp: new Date().toISOString(),
        },
        severity: 'high',
        category: 'runtime',
      });
    });
  }

  private setupNetworkHandlers() {
    // Monitor network status
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  public handleError(errorDetails: ErrorDetails): void {
    // Enhance error details with additional context
    const enhancedError = this.enhanceErrorDetails(errorDetails);

    // Log error locally
    this.logError(enhancedError);

    // Show user notification based on severity
    this.showUserNotification(enhancedError);

    // Report error to backend (queue if offline)
    this.reportError(enhancedError);
  }

  public handleApiError(error: any, context?: ErrorContext): void {
    let errorDetails: ErrorDetails;

    if (error.response) {
      // HTTP error response
      errorDetails = {
        message: error.response.data?.message || error.message || 'API request failed',
        code: error.response.status,
        status: error.response.status,
        context: {
          ...context,
          action: 'API Request',
          url: error.config?.url,
        },
        severity: this.getSeverityFromStatus(error.response.status),
        category: this.getCategoryFromStatus(error.response.status),
      };
    } else if (error.request) {
      // Network error
      errorDetails = {
        message: 'Network error - please check your connection',
        context: {
          ...context,
          action: 'Network Request',
        },
        severity: 'medium',
        category: 'network',
      };
    } else {
      // Other error
      errorDetails = {
        message: error.message || 'An unexpected error occurred',
        stack: error.stack,
        context: {
          ...context,
          action: 'Unknown Error',
        },
        severity: 'medium',
        category: 'unknown',
      };
    }

    this.handleError(errorDetails);
  }

  private enhanceErrorDetails(errorDetails: ErrorDetails): ErrorDetails {
    return {
      ...errorDetails,
      context: {
        ...errorDetails.context,
        url: errorDetails.context?.url || window.location.href,
        userAgent: navigator.userAgent,
        timestamp: errorDetails.context?.timestamp || new Date().toISOString(),
        sessionId: this.getSessionId(),
      },
    };
  }

  private logError(errorDetails: ErrorDetails): void {
    const logLevel = this.getLogLevel(errorDetails.severity);
    const logMessage = `[${errorDetails.severity?.toUpperCase()}] ${errorDetails.message}`;

    console.group(`🚨 ${logLevel} Error`);
    console.error('Message:', errorDetails.message);
    console.error('Code:', errorDetails.code);
    console.error('Status:', errorDetails.status);
    console.error('Category:', errorDetails.category);
    console.error('Context:', errorDetails.context);
    if (errorDetails.stack) {
      console.error('Stack:', errorDetails.stack);
    }
    console.groupEnd();

    // Store in localStorage for debugging
    this.storeErrorLocally(errorDetails);
  }

  private showUserNotification(errorDetails: ErrorDetails): void {
    const { severity, message, category } = errorDetails;

    // Don't show notifications for low severity errors
    if (severity === 'low') return;

    let title = 'Error';
    let description = message;

    switch (category) {
      case 'network':
        title = 'Connection Error';
        description = 'Please check your internet connection and try again.';
        break;
      case 'authentication':
        title = 'Authentication Error';
        description = 'Please log in again to continue.';
        break;
      case 'authorization':
        title = 'Access Denied';
        description = 'You don\'t have permission to perform this action.';
        break;
      case 'validation':
        title = 'Validation Error';
        break;
    }

    // Show toast notification
    if (severity === 'critical' || severity === 'high') {
      toast.error(title, {
        description,
        duration: 5000,
      });
    } else {
      toast.warning(title, {
        description,
        duration: 3000,
      });
    }
  }

  private async reportError(errorDetails: ErrorDetails): Promise<void> {
    if (!this.isOnline) {
      this.errorQueue.push(errorDetails);
      return;
    }

    try {
      await fetch('/api/v1/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify(errorDetails),
      });
    } catch (error) {
      console.warn('Failed to report error to backend:', error);
      this.errorQueue.push(errorDetails);
    }
  }

  private async processErrorQueue(): Promise<void> {
    if (!this.isOnline || this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    for (const error of errors) {
      try {
        await this.reportError(error);
      } catch (e) {
        // Re-queue failed errors
        this.errorQueue.push(error);
      }
    }
  }

  private getSeverityFromStatus(status: number): ErrorDetails['severity'] {
    if (status >= 500) return 'high';
    if (status === 401 || status === 403) return 'medium';
    if (status >= 400) return 'medium';
    return 'low';
  }

  private getCategoryFromStatus(status: number): ErrorDetails['category'] {
    switch (status) {
      case 401:
        return 'authentication';
      case 403:
        return 'authorization';
      case 400:
      case 422:
        return 'validation';
      default:
        return status >= 500 ? 'runtime' : 'unknown';
    }
  }

  private getLogLevel(severity?: string): string {
    switch (severity) {
      case 'critical':
        return 'CRITICAL';
      case 'high':
        return 'ERROR';
      case 'medium':
        return 'WARN';
      case 'low':
        return 'INFO';
      default:
        return 'ERROR';
    }
  }

  private storeErrorLocally(errorDetails: ErrorDetails): void {
    try {
      const existingErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      existingErrors.push(errorDetails);
      
      // Keep only last 20 errors
      if (existingErrors.length > 20) {
        existingErrors.splice(0, existingErrors.length - 20);
      }
      
      localStorage.setItem('app_errors', JSON.stringify(existingErrors));
    } catch (e) {
      console.warn('Failed to store error in localStorage:', e);
    }
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('sessionId');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('sessionId', sessionId);
    }
    return sessionId;
  }

  // Public methods for manual error reporting
  public reportNetworkError(error: any, context?: ErrorContext): void {
    this.handleApiError(error, { ...context, category: 'network' });
  }

  public reportValidationError(message: string, context?: ErrorContext): void {
    this.handleError({
      message,
      severity: 'medium',
      category: 'validation',
      context,
    });
  }

  public reportCriticalError(error: Error, context?: ErrorContext): void {
    this.handleError({
      message: error.message,
      stack: error.stack,
      severity: 'critical',
      category: 'runtime',
      context,
    });
  }
}

// Export singleton instance
export const errorHandler = GlobalErrorHandler.getInstance();
