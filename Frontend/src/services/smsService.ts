import { MockDataService } from '@/services/mockDataService';

export interface SMSMessage {
  id: string;
  phone_number: string;
  message: string;
  message_type: 'alert' | 'otp' | 'reminder' | 'confirmation' | 'emergency' | 'marketing';
  status: 'queued' | 'sent' | 'delivered' | 'failed' | 'expired';
  priority: 'low' | 'medium' | 'high' | 'critical';
  scheduled_at?: string;
  sent_at?: string;
  delivered_at?: string;
  failed_reason?: string;
  retry_count: number;
  max_retries: number;
  user_id?: string;
  reference_id?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface SMSTemplate {
  id: string;
  name: string;
  message_type: SMSMessage['message_type'];
  content: string;
  variables: Array<{
    name: string;
    type: 'text' | 'number' | 'date';
    required: boolean;
    default_value?: string;
  }>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface OTPVerification {
  id: string;
  phone_number: string;
  code: string;
  purpose: 'registration' | 'login' | 'password_reset' | 'phone_verification' | 'transaction';
  status: 'pending' | 'verified' | 'expired' | 'failed';
  attempts: number;
  max_attempts: number;
  expires_at: string;
  verified_at?: string;
  created_at: string;
}

export interface SMSCampaign {
  id: string;
  name: string;
  description: string;
  template_id: string;
  target_audience: {
    phone_numbers?: string[];
    filters?: {
      location?: string[];
      user_type?: string[];
      last_activity?: string;
    };
  };
  schedule: {
    send_immediately: boolean;
    scheduled_at?: string;
    timezone?: string;
  };
  status: 'draft' | 'scheduled' | 'sending' | 'completed' | 'failed';
  metrics: {
    total_recipients: number;
    sent_count: number;
    delivered_count: number;
    failed_count: number;
  };
  created_by: string;
  created_at: string;
  sent_at?: string;
}

export interface SMSProvider {
  name: 'twilio' | 'nexmo' | 'africas_talking' | 'termii';
  config: {
    api_key: string;
    api_secret?: string;
    sender_id: string;
    base_url?: string;
  };
  is_active: boolean;
  priority: number;
}

/**
 * SMS Alert System
 * Provides comprehensive SMS messaging capabilities including
 * OTP verification, alerts, reminders, and bulk messaging
 */
export class SMSService {
  private static instance: SMSService;
  private readonly providers: SMSProvider[] = [
    {
      name: 'termii',
      config: {
        api_key: import.meta.env.VITE_TERMII_API_KEY || '',
        sender_id: import.meta.env.VITE_TERMII_SENDER_ID || 'PHCityRent',
        base_url: 'https://api.ng.termii.com/api'
      },
      is_active: true,
      priority: 1
    },
    {
      name: 'twilio',
      config: {
        api_key: import.meta.env.VITE_TWILIO_ACCOUNT_SID || '',
        api_secret: import.meta.env.VITE_TWILIO_AUTH_TOKEN || '',
        sender_id: import.meta.env.VITE_TWILIO_PHONE_NUMBER || ''
      },
      is_active: false,
      priority: 2
    }
  ];

  private messageQueue: Map<string, SMSMessage[]> = new Map();
  private retryQueue: Map<string, SMSMessage> = new Map();

  public static getInstance(): SMSService {
    if (!SMSService.instance) {
      SMSService.instance = new SMSService();
    }
    return SMSService.instance;
  }

  // =====================================================
  // CORE SMS SENDING
  // =====================================================

  /**
   * Send SMS message
   */
  async sendSMS(
    phoneNumber: string,
    message: string,
    messageType: SMSMessage['message_type'] = 'alert',
    options: {
      priority?: SMSMessage['priority'];
      scheduled_at?: string;
      user_id?: string;
      reference_id?: string;
      metadata?: any;
    } = {}
  ): Promise<SMSMessage> {
    try {
      // Validate phone number
      const validatedPhone = this.validatePhoneNumber(phoneNumber);
      if (!validatedPhone) {
        throw new Error('Invalid phone number format');
      }

      // Create SMS record
      const smsData = {
        phone_number: validatedPhone,
        message: message.substring(0, 160), // SMS character limit
        message_type: messageType,
        status: options.scheduled_at ? 'queued' : 'queued',
        priority: options.priority || 'medium',
        scheduled_at: options.scheduled_at,
        retry_count: 0,
        max_retries: messageType === 'otp' ? 3 : 1,
        user_id: options.user_id,
        reference_id: options.reference_id,
        metadata: options.metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('sms_messages')
        .insert(smsData)
        .select()
        .single();

      if (error) throw error;

      // Send immediately if not scheduled
      if (!options.scheduled_at) {
        await this.processSMSMessage(data);
      }

      return data;
    } catch (error) {
      console.error('Error sending SMS:', error);
      throw new Error('Failed to send SMS message');
    }
  }

  /**
   * Send SMS using template
   */
  async sendTemplatedSMS(
    phoneNumber: string,
    templateName: string,
    variables: Record<string, any> = {},
    options: {
      priority?: SMSMessage['priority'];
      user_id?: string;
      reference_id?: string;
    } = {}
  ): Promise<SMSMessage> {
    try {
      // Get template
      const template = await this.getTemplate(templateName);
      if (!template) {
        throw new Error(`SMS template '${templateName}' not found`);
      }

      // Process template variables
      const processedMessage = this.processTemplate(template.content, variables);

      return await this.sendSMS(
        phoneNumber,
        processedMessage,
        template.message_type,
        options
      );
    } catch (error) {
      console.error('Error sending templated SMS:', error);
      throw new Error('Failed to send templated SMS');
    }
  }

  // =====================================================
  // OTP VERIFICATION SYSTEM
  // =====================================================

  /**
   * Generate and send OTP
   */
  async generateOTP(
    phoneNumber: string,
    purpose: OTPVerification['purpose'],
    options: {
      code_length?: number;
      expires_in_minutes?: number;
      max_attempts?: number;
    } = {}
  ): Promise<OTPVerification> {
    try {
      const {
        code_length = 6,
        expires_in_minutes = 10,
        max_attempts = 3
      } = options;

      // Generate OTP code
      const code = this.generateOTPCode(code_length);
      
      // Calculate expiry time
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + expires_in_minutes);

      // Create OTP record
      const { data, error } = await supabase
        .from('otp_verifications')
        .insert({
          phone_number: this.validatePhoneNumber(phoneNumber),
          code,
          purpose,
          status: 'pending',
          attempts: 0,
          max_attempts,
          expires_at: expiresAt.toISOString(),
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Send OTP via SMS
      await this.sendSMS(
        phoneNumber,
        `Your PHCityRent verification code is: ${code}. Valid for ${expires_in_minutes} minutes.`,
        'otp',
        {
          priority: 'high',
          reference_id: data.id,
          metadata: { purpose }
        }
      );

      return data;
    } catch (error) {
      console.error('Error generating OTP:', error);
      throw new Error('Failed to generate OTP');
    }
  }

  /**
   * Verify OTP code
   */
  async verifyOTP(
    phoneNumber: string,
    code: string,
    purpose: OTPVerification['purpose']
  ): Promise<{ success: boolean; message: string; otp?: OTPVerification }> {
    try {
      // Get pending OTP
      const { data: otp, error } = await supabase
        .from('otp_verifications')
        .select('*')
        .eq('phone_number', this.validatePhoneNumber(phoneNumber))
        .eq('purpose', purpose)
        .eq('status', 'pending')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error || !otp) {
        return { success: false, message: 'No pending OTP found' };
      }

      // Check if expired
      if (new Date() > new Date(otp.expires_at)) {
        await supabase
          .from('otp_verifications')
          .update({ status: 'expired' })
          .eq('id', otp.id);
        
        return { success: false, message: 'OTP has expired' };
      }

      // Check attempts
      if (otp.attempts >= otp.max_attempts) {
        await supabase
          .from('otp_verifications')
          .update({ status: 'failed' })
          .eq('id', otp.id);
        
        return { success: false, message: 'Maximum verification attempts exceeded' };
      }

      // Verify code
      if (otp.code !== code) {
        await supabase
          .from('otp_verifications')
          .update({ attempts: otp.attempts + 1 })
          .eq('id', otp.id);
        
        return { 
          success: false, 
          message: `Invalid code. ${otp.max_attempts - otp.attempts - 1} attempts remaining` 
        };
      }

      // Mark as verified
      const { data: verifiedOTP, error: updateError } = await supabase
        .from('otp_verifications')
        .update({
          status: 'verified',
          verified_at: new Date().toISOString()
        })
        .eq('id', otp.id)
        .select()
        .single();

      if (updateError) throw updateError;

      return { 
        success: true, 
        message: 'OTP verified successfully',
        otp: verifiedOTP
      };

    } catch (error) {
      console.error('Error verifying OTP:', error);
      return { success: false, message: 'Failed to verify OTP' };
    }
  }

  // =====================================================
  // ALERT NOTIFICATIONS
  // =====================================================

  /**
   * Send critical alert
   */
  async sendCriticalAlert(
    phoneNumber: string,
    alertMessage: string,
    alertType: string,
    userId?: string
  ): Promise<void> {
    await this.sendSMS(
      phoneNumber,
      `ALERT: ${alertMessage}`,
      'alert',
      {
        priority: 'critical',
        user_id: userId,
        metadata: { alert_type: alertType }
      }
    );
  }

  /**
   * Send appointment reminder
   */
  async sendAppointmentReminder(
    phoneNumber: string,
    appointmentDetails: {
      property_title: string;
      date: string;
      time: string;
      agent_name: string;
      agent_phone: string;
    },
    userId?: string
  ): Promise<void> {
    const message = `Reminder: Property viewing for "${appointmentDetails.property_title}" on ${appointmentDetails.date} at ${appointmentDetails.time}. Agent: ${appointmentDetails.agent_name} (${appointmentDetails.agent_phone})`;
    
    await this.sendSMS(
      phoneNumber,
      message,
      'reminder',
      {
        priority: 'high',
        user_id: userId,
        metadata: { type: 'appointment_reminder' }
      }
    );
  }

  /**
   * Send payment confirmation
   */
  async sendPaymentConfirmation(
    phoneNumber: string,
    paymentDetails: {
      amount: number;
      property_title: string;
      transaction_id: string;
      payment_method: string;
    },
    userId?: string
  ): Promise<void> {
    const message = `Payment confirmed! ₦${paymentDetails.amount.toLocaleString()} for "${paymentDetails.property_title}". Transaction ID: ${paymentDetails.transaction_id}. Method: ${paymentDetails.payment_method}`;
    
    await this.sendSMS(
      phoneNumber,
      message,
      'confirmation',
      {
        priority: 'high',
        user_id: userId,
        metadata: { 
          type: 'payment_confirmation',
          transaction_id: paymentDetails.transaction_id
        }
      }
    );
  }

  // =====================================================
  // BULK MESSAGING
  // =====================================================

  /**
   * Send bulk SMS messages
   */
  async sendBulkSMS(
    recipients: Array<{
      phone_number: string;
      message?: string;
      variables?: Record<string, any>;
    }>,
    templateName?: string,
    options: {
      batch_size?: number;
      delay_between_batches?: number;
    } = {}
  ): Promise<{
    total_sent: number;
    successful: string[];
    failed: Array<{ phone_number: string; error: string }>;
  }> {
    const {
      batch_size = 100,
      delay_between_batches = 1000
    } = options;

    const results = {
      total_sent: 0,
      successful: [] as string[],
      failed: [] as Array<{ phone_number: string; error: string }>
    };

    // Process recipients in batches
    for (let i = 0; i < recipients.length; i += batch_size) {
      const batch = recipients.slice(i, i + batch_size);
      
      const batchPromises = batch.map(async (recipient) => {
        try {
          if (templateName) {
            await this.sendTemplatedSMS(
              recipient.phone_number,
              templateName,
              recipient.variables || {}
            );
          } else if (recipient.message) {
            await this.sendSMS(recipient.phone_number, recipient.message, 'marketing');
          }
          
          results.successful.push(recipient.phone_number);
          results.total_sent++;
        } catch (error) {
          results.failed.push({
            phone_number: recipient.phone_number,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      });

      await Promise.allSettled(batchPromises);

      // Delay between batches
      if (i + batch_size < recipients.length) {
        await new Promise(resolve => setTimeout(resolve, delay_between_batches));
      }
    }

    return results;
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  /**
   * Validate and format phone number
   */
  private validatePhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Handle Nigerian phone numbers
    if (cleaned.startsWith('234')) {
      return `+${cleaned}`;
    } else if (cleaned.startsWith('0') && cleaned.length === 11) {
      return `+234${cleaned.substring(1)}`;
    } else if (cleaned.length === 10) {
      return `+234${cleaned}`;
    }
    
    // For international numbers, assume they're already formatted
    if (cleaned.length > 10) {
      return `+${cleaned}`;
    }
    
    throw new Error('Invalid phone number format');
  }

  /**
   * Generate OTP code
   */
  private generateOTPCode(length: number): string {
    const digits = '0123456789';
    let code = '';
    for (let i = 0; i < length; i++) {
      code += digits.charAt(Math.floor(Math.random() * digits.length));
    }
    return code;
  }

  /**
   * Process template with variables
   */
  private processTemplate(template: string, variables: Record<string, any>): string {
    let processed = template;
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`;
      processed = processed.replace(new RegExp(placeholder, 'g'), String(value));
    }
    return processed;
  }

  /**
   * Get SMS template
   */
  private async getTemplate(templateName: string): Promise<SMSTemplate | null> {
    try {
      const { data, error } = await supabase
        .from('sms_templates')
        .select('*')
        .eq('name', templateName)
        .eq('is_active', true)
        .single();

      if (error) return null;
      return data;
    } catch (error) {
      return null;
    }
  }
}

// Export singleton instance
export const smsService = SMSService.getInstance();
