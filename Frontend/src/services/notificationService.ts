import { toast } from 'sonner';
import { CheckCircle, AlertCircle, AlertTriangle, Info, X, RefreshCw } from 'lucide-react';

export interface NotificationOptions {
  title?: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  dismissible?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';
  richColors?: boolean;
  closeButton?: boolean;
  id?: string;
}

export interface RetryOptions {
  maxRetries?: number;
  retryDelay?: number;
  onRetry: () => Promise<void>;
  retryMessage?: string;
}

class NotificationService {
  private static instance: NotificationService;
  private activeNotifications = new Map<string, string>();
  private retryAttempts = new Map<string, number>();

  private constructor() {}

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Success notifications
  success(message: string, options?: NotificationOptions): string {
    const id = options?.id || this.generateId();
    
    const toastId = toast.success(message, {
      description: options?.description,
      duration: options?.duration || 4000,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
      dismissible: options?.dismissible !== false,
      richColors: options?.richColors !== false,
      closeButton: options?.closeButton,
      id,
    });

    this.activeNotifications.set(id, toastId);
    return id;
  }

  // Error notifications with retry functionality
  error(message: string, options?: NotificationOptions & { retry?: RetryOptions }): string {
    const id = options?.id || this.generateId();
    
    let action = options?.action;
    
    // Add retry action if provided
    if (options?.retry) {
      const retryCount = this.retryAttempts.get(id) || 0;
      const maxRetries = options.retry.maxRetries || 3;
      
      if (retryCount < maxRetries) {
        action = {
          label: options.retry.retryMessage || `Retry (${maxRetries - retryCount} left)`,
          onClick: async () => {
            this.dismiss(id);
            this.retryAttempts.set(id, retryCount + 1);
            
            try {
              await options.retry!.onRetry();
              this.success('Operation completed successfully');
              this.retryAttempts.delete(id);
            } catch (error) {
              setTimeout(() => {
                this.error(message, { ...options, id });
              }, options.retry!.retryDelay || 1000);
            }
          },
        };
      }
    }

    const toastId = toast.error(message, {
      description: options?.description,
      duration: options?.duration || 6000,
      action,
      dismissible: options?.dismissible !== false,
      richColors: options?.richColors !== false,
      closeButton: options?.closeButton !== false,
      id,
    });

    this.activeNotifications.set(id, toastId);
    return id;
  }

  // Warning notifications
  warning(message: string, options?: NotificationOptions): string {
    const id = options?.id || this.generateId();
    
    const toastId = toast.warning(message, {
      description: options?.description,
      duration: options?.duration || 5000,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
      dismissible: options?.dismissible !== false,
      richColors: options?.richColors !== false,
      closeButton: options?.closeButton,
      id,
    });

    this.activeNotifications.set(id, toastId);
    return id;
  }

  // Info notifications
  info(message: string, options?: NotificationOptions): string {
    const id = options?.id || this.generateId();
    
    const toastId = toast.info(message, {
      description: options?.description,
      duration: options?.duration || 4000,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
      dismissible: options?.dismissible !== false,
      richColors: options?.richColors !== false,
      closeButton: options?.closeButton,
      id,
    });

    this.activeNotifications.set(id, toastId);
    return id;
  }

  // Loading notifications
  loading(message: string, options?: NotificationOptions): string {
    const id = options?.id || this.generateId();
    
    const toastId = toast.loading(message, {
      description: options?.description,
      duration: options?.duration || Infinity,
      dismissible: options?.dismissible !== false,
      id,
    });

    this.activeNotifications.set(id, toastId);
    return id;
  }

  // Promise-based notifications
  async promise<T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: NotificationOptions
  ): Promise<T> {
    const id = options?.id || this.generateId();
    
    return toast.promise(promise, {
      loading: messages.loading,
      success: messages.success,
      error: messages.error,
      duration: options?.duration,
      dismissible: options?.dismissible !== false,
      richColors: options?.richColors !== false,
      id,
    });
  }

  // Custom notifications with JSX content
  custom(content: React.ReactNode, options?: NotificationOptions): string {
    const id = options?.id || this.generateId();
    
    const toastId = toast.custom(content, {
      duration: options?.duration || 4000,
      dismissible: options?.dismissible !== false,
      id,
    });

    this.activeNotifications.set(id, toastId);
    return id;
  }

  // Dismiss specific notification
  dismiss(id: string): void {
    const toastId = this.activeNotifications.get(id);
    if (toastId) {
      toast.dismiss(toastId);
      this.activeNotifications.delete(id);
      this.retryAttempts.delete(id);
    }
  }

  // Dismiss all notifications
  dismissAll(): void {
    toast.dismiss();
    this.activeNotifications.clear();
    this.retryAttempts.clear();
  }

  // Update existing notification
  update(id: string, message: string, type: 'success' | 'error' | 'warning' | 'info', options?: NotificationOptions): void {
    this.dismiss(id);
    
    switch (type) {
      case 'success':
        this.success(message, { ...options, id });
        break;
      case 'error':
        this.error(message, { ...options, id });
        break;
      case 'warning':
        this.warning(message, { ...options, id });
        break;
      case 'info':
        this.info(message, { ...options, id });
        break;
    }
  }

  // Specialized notifications for common use cases
  networkError(options?: NotificationOptions & { retry?: RetryOptions }): string {
    return this.error('Network connection failed', {
      description: 'Please check your internet connection and try again.',
      ...options,
    });
  }

  authError(options?: NotificationOptions): string {
    return this.error('Authentication required', {
      description: 'Please log in to continue.',
      action: {
        label: 'Login',
        onClick: () => {
          window.location.href = '/auth';
        },
      },
      ...options,
    });
  }

  permissionError(options?: NotificationOptions): string {
    return this.error('Access denied', {
      description: 'You don\'t have permission to perform this action.',
      ...options,
    });
  }

  validationError(message: string, options?: NotificationOptions): string {
    return this.error(message, {
      description: 'Please check your input and try again.',
      ...options,
    });
  }

  saveSuccess(itemName?: string, options?: NotificationOptions): string {
    return this.success(`${itemName || 'Item'} saved successfully`, {
      description: 'Your changes have been saved.',
      ...options,
    });
  }

  deleteSuccess(itemName?: string, options?: NotificationOptions): string {
    return this.success(`${itemName || 'Item'} deleted successfully`, {
      description: 'The item has been permanently removed.',
      ...options,
    });
  }

  // Batch operations
  batchOperation<T>(
    items: T[],
    operation: (item: T) => Promise<void>,
    messages: {
      loading: string;
      success: string;
      error: string;
    }
  ): Promise<void> {
    const loadingId = this.loading(messages.loading, {
      description: `Processing ${items.length} items...`,
    });

    return new Promise(async (resolve, reject) => {
      let successCount = 0;
      let errorCount = 0;

      for (const item of items) {
        try {
          await operation(item);
          successCount++;
        } catch (error) {
          errorCount++;
        }
      }

      this.dismiss(loadingId);

      if (errorCount === 0) {
        this.success(messages.success, {
          description: `Successfully processed ${successCount} items.`,
        });
        resolve();
      } else if (successCount === 0) {
        this.error(messages.error, {
          description: `Failed to process ${errorCount} items.`,
        });
        reject(new Error('All operations failed'));
      } else {
        this.warning('Partial success', {
          description: `${successCount} succeeded, ${errorCount} failed.`,
        });
        resolve();
      }
    });
  }

  private generateId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const notifications = NotificationService.getInstance();
