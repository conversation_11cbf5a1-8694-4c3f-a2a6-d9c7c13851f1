import apiClient, { ApiResponse } from '@/services/apiClient';
import { PROPERTY_ENDPOINTS } from '@/config/endpoints'; // Using PROPERTY_ENDPOINTS as saved searches are related to properties
import {
  SavedSearch,
  CreateSavedSearchRequest,
  UpdateSavedSearchRequest,
  PropertyServiceError
} from '@/types/property';
import { errorHandler } from '../errorHandler';
import { HTTP_STATUS } from '@/config/api';

/**
 * Service for managing user's saved property searches.
 */
class SavedSearchesService {
  private static instance: SavedSearchesService;

  public static getInstance(): SavedSearchesService {
    if (!SavedSearchesService.instance) {
      SavedSearchesService.instance = new SavedSearchesService();
    }
    return SavedSearchesService.instance;
  }

  // Helper for consistent error handling
  private handleServiceError(
    error: any, action: string, defaultMessage: string,
    defaultStatus: number
  ): PropertyServiceError {
    if (error instanceof PropertyServiceError) return error;
    errorHandler.handleError({
      message: error.message || defaultMessage,
      code: error.statusCode || defaultStatus,
      status: error.statusCode || defaultStatus,
      severity: 'high', // Adjust severity as needed
      category: 'property',
      context: { component: 'PropertyService', action },
    });
    return new PropertyServiceError(
      error.message || defaultMessage, 'UNKNOWN_ERROR', error
    );
  }

  /**
   * Creates a new saved search for the current user.
   * @param createRequest The data for the new saved search.
   * @returns A promise resolving to the created SavedSearch object.
   */
  async createSavedSearch(createRequest: CreateSavedSearchRequest): Promise<SavedSearch> {
    try {
      const response: ApiResponse<SavedSearch> = await apiClient.post(
        PROPERTY_ENDPOINTS.SAVE_SEARCH,
        createRequest
      );
      return response.data;
    } catch (error) {
      this.handleServiceError(
        error,
        'Create saved search',
        'Failed to create saved search',
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Retrieves all saved searches for a specific user.
   * @param userId The ID of the user.
   * @returns A promise resolving to an array of SavedSearch objects.
   */
  async getSavedSearches(userId: string): Promise<SavedSearch[]> {
    try {
      const response: ApiResponse<SavedSearch[]> = await apiClient.get(
        PROPERTY_ENDPOINTS.GET_SAVED_SEARCHES(userId)
      );
      return response.data;
    } catch (error) {
      this.handleServiceError(
        error,
        'Fetch all saved searches for a user',
        'Failed to retrieve saved searches',
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Retrieves a single saved search by its ID.
   * @param id The ID of the saved search.
   * @returns A promise resolving to the SavedSearch object.
   */
  async getSavedSearchById(id: string): Promise<SavedSearch> {
    try {
      const response: ApiResponse<SavedSearch> = await apiClient.get(
        PROPERTY_ENDPOINTS.GET_SAVED_SEARCH_BY_ID(id)
      );
      return response.data; 
    } catch (error) {
      throw this.handleServiceError(
        error, 'Get Saved Search by ID', 'Saved search not found',
        HTTP_STATUS.NOT_FOUND
      );
    }
  }

  /**
   * Updates an existing saved search.
   * @param id The ID of the saved search to update.
   * @param updateRequest The partial data to update the saved search.
   * @returns A promise resolving to the updated SavedSearch object.
   */
  async updateSavedSearch(
    id: string,
    updateRequest: UpdateSavedSearchRequest
  ): Promise<SavedSearch> {
    try {
      const response: ApiResponse<SavedSearch> = await apiClient.patch(
        PROPERTY_ENDPOINTS.UPDATE_SAVED_SEARCH(id),
        updateRequest
      );
      return response.data; 
    } catch (error) {
      throw this.handleServiceError(
        error, 'Update Saved Search', 'Failed to update saved search',
        HTTP_STATUS.BAD_REQUEST
      );
    }
  }

  /**
   * Deletes a saved search.
   * @param id The ID of the saved search to delete.
   * @returns A promise resolving when the saved search is deleted.
   */
  async deleteSavedSearch(id: string): Promise<void> {
    try {
      await apiClient.delete(PROPERTY_ENDPOINTS.DELETE_SAVED_SEARCH(id));
    } catch (error) {
      throw this.handleServiceError(
        error, 'Delete Saved Search', 'Failed to delete saved search',
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Toggles the active status of a saved search.
   * @param id The ID of the saved search.
   * @param isActive The new active status.
   * @returns A promise resolving to the updated SavedSearch object.
   */
  async toggleSavedSearchActive(id: string, isActive: boolean): Promise<SavedSearch> {
    try {
      const response: ApiResponse<SavedSearch> = await apiClient.patch(
        PROPERTY_ENDPOINTS.TOGGLE_SAVED_SEARCH_ACTIVE(id),
        { isActive }
      );
      return response.data; 
    } catch (error) {
      throw this.handleServiceError(
        error, 'Toggle Saved Search Active',
        'Failed to toggle saved search status',
        HTTP_STATUS.BAD_REQUEST);
    }
  }
}

export const savedSearchesService = SavedSearchesService.getInstance();
