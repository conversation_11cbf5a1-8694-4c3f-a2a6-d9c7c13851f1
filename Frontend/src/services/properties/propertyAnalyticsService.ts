import apiClient, { ApiResponse } from '@/services/apiClient';
import { PROPERTY_ENDPOINTS } from '@/config/endpoints';
import { 
    PropertyAnalytics,
    PropertyMarketData,
    PropertyRecommendation
} from '@/types/property';

/**
 * Service for fetching property analytics, market data, and recommendations.
 */
class PropertyAnalyticsService {
  private static instance: PropertyAnalyticsService;

  public static getInstance(): PropertyAnalyticsService {
    if (!PropertyAnalyticsService.instance) {
      PropertyAnalyticsService.instance = new PropertyAnalyticsService();
    }
    return PropertyAnalyticsService.instance;
  }

  /**
   * Fetches detailed analytics for a specific property.
   * @param propertyId The ID of the property.
   * @returns A promise resolving to PropertyAnalytics data.
   */
  async getPropertyAnalytics(propertyId: string): Promise<PropertyAnalytics> {
    const response: ApiResponse<PropertyAnalytics> = await apiClient.get(
      PROPERTY_ENDPOINTS.GET_PROPERTY_ANALYTICS(propertyId)
    );
    return response.data;
  }

  /**
   * Fetches market data for a given location and optional property type.
   * @param location The location for which to fetch market data.
   * @param propertyType Optional property type to filter market data.
   * @returns A promise resolving to PropertyMarketData.
   */
  async getPropertyMarketData(
    location: string, propertyType?: string
): Promise<PropertyMarketData> {
    const params = { location, ...(propertyType && { propertyType }) };
    const response: ApiResponse<PropertyMarketData> = await apiClient.get(
      PROPERTY_ENDPOINTS.GET_PROPERTY_MARKET_DATA,
      { params }
    );
    return response.data;
  }

  /**
   * Fetches property recommendations for a user.
   * @param userId The ID of the user for whom to get recommendations.
   * @param limit The maximum number of recommendations to return.
   * @returns A promise resolving to an array of PropertyRecommendation objects.
   */
  async getPropertyRecommendations(
    userId: string, limit: number = 10
): Promise<PropertyRecommendation[]> {
    const params = { limit };
    const response: ApiResponse<PropertyRecommendation[]> = await apiClient.get(
      PROPERTY_ENDPOINTS.GET_PROPERTY_RECOMMENDATIONS(userId),
      { params }
    );
    return response.data;
  }
}

export const propertyAnalyticsService = PropertyAnalyticsService.getInstance();
