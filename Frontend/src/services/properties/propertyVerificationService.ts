import apiClient, { ApiResponse } from '@/services/apiClient';
import { PROPERTY_ENDPOINTS } from '@/config/endpoints';
import {
  VerificationStep,
  SubmitVerificationStepsRequest,
  UpdateVerificationStatusRequest,
  ScheduleInspectionRequest,
  CompleteInspectionRequest,
  VerificationProgress
} from '@/types/property';

/**
 * Property Verification Service (Frontend)
 * Handles all API calls related to a property's verification process.
 */
export class PropertyVerificationService {
  private static instance: PropertyVerificationService;

  public static getInstance(): PropertyVerificationService {
    if (!PropertyVerificationService.instance) {
      PropertyVerificationService.instance = new PropertyVerificationService();
    }
    return PropertyVerificationService.instance;
  }

  /**
   * Retrieves all verification steps for a given property.
   * @param propertyId The ID of the property.
   * @returns An array of verification steps.
   */
  async getVerificationSteps(propertyId: string): Promise<VerificationStep[]> {
    try {
      const response: ApiResponse<VerificationStep[]> = await apiClient.get(
        PROPERTY_ENDPOINTS.GET_PROPERTY_VERIFICATION_STEPS(propertyId)
      );
      return response.data;
    } catch (error) {
      console.error('Error submitting verification steps:', error);
      throw error;
    }
  }

  /**
   * Retrieves the overall verification progress for a property.
   * @param propertyId The ID of the property.
   * @returns A summary object with progress details.
   */
  async getVerificationProgress(propertyId: string): Promise<VerificationProgress> {
    const response: ApiResponse<VerificationProgress> = await apiClient.get(
      PROPERTY_ENDPOINTS.GET_PROPERTY_VERIFICATION_PROGRESS(propertyId)
    );
    return response.data;
  }

  /**
   * Schedules a new site inspection for a property.
   * @param propertyId The ID of the property.
   * @param data The inspection schedule details.
   * @returns The newly created inspection step.
   */
  async scheduleInspection(
    propertyId: string, data: ScheduleInspectionRequest
  ): Promise<VerificationStep> {
    const response: ApiResponse<VerificationStep> = await apiClient.post(
      PROPERTY_ENDPOINTS.SCHEDULE_INSPECTION(propertyId),
      data
    );
    return response.data;
  }

  /**
   * Marks a scheduled inspection as complete.
   * @param propertyId The ID of the property.
   * @param stepId The ID of the inspection step.
   * @param data The completion details.
   * @returns The updated inspection step.
   */
  async completeInspection(
    propertyId: string,
    stepId: string,
    data: CompleteInspectionRequest
  ): Promise<VerificationStep> {
    const response: ApiResponse<VerificationStep> = await apiClient.patch(
      PROPERTY_ENDPOINTS.COMPLETE_INSPECTION(propertyId, stepId),
      data
    );
    return response.data;
  }

  /**
   * Submits a new verification step for a property.
   * @param propertyId The ID of the property.
   * @param data The data for the new verification step.
   * @returns The newly created verification step.
   */
  async submitVerificationStep(
    propertyId: string,
    data: SubmitVerificationStepsRequest
  ): Promise<VerificationStep> {
    try {
      const response: ApiResponse<VerificationStep> = await apiClient.post(
        PROPERTY_ENDPOINTS.SUBMIT_VERIFICATION_STEP(propertyId),
        data
      );
      return response.data; 
    } catch (error) {
      console.error('Error updating verification status:', error);
      throw error;
    }
  }

  /**
   * Updates the status of a specific verification step.
   * @param propertyId The ID of the property.
   * @param stepId The ID of the verification step.
   * @param data The new status and comments.
   * @returns The updated verification step.
   */
  async updateVerificationStatus(
    propertyId: string,
    stepId: string,
    data: UpdateVerificationStatusRequest
  ): Promise<VerificationStep> {
    try {
      const response: ApiResponse<VerificationStep> = await apiClient.patch(
        PROPERTY_ENDPOINTS.UPDATE_VERIFICATION_STATUS(propertyId, stepId),
        data
      );
      return response.data; 
    } catch (error) {
      console.error('Error updating verification step:', error);
      throw error;
    }
  }
}

// Export a singleton instance for easy use
export const propertyVerificationService = PropertyVerificationService.getInstance();
