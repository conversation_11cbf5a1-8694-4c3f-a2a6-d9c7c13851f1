import apiClient, { ApiResponse } from '@/services/apiClient';
import { PROPERTY_ENDPOINTS } from '@/config/endpoints';
import {
  PropertyImage, ImageUploadRequest,
  ReorderImagesRequest, PropertyServiceError
} from '@/types/property';
import { errorHandler } from '@/services/errorHandler';
import { HTTP_STATUS } from '@/config/api';

/**
 * Service for managing property images.
 * Handles API calls related to uploading, retrieving, deleting, and reordering images.
 */
class PropertyImagesService {
  private static instance: PropertyImagesService;

  public static getInstance(): PropertyImagesService {
    if (!PropertyImagesService.instance) {
      PropertyImagesService.instance = new PropertyImagesService();
    }
    return PropertyImagesService.instance;
  }

  // Helper for consistent error handling
  private handleServiceError(
    error: any, action: string, defaultMessage: string,
    defaultStatus: number
  ): PropertyServiceError {
    if (error instanceof PropertyServiceError) return error;
    errorHandler.handleError({
      message: error.message || defaultMessage,
      code: error.statusCode || defaultStatus,
      status: error.statusCode || defaultStatus,
      severity: 'high', // Adjust severity as needed
      category: 'property',
      context: { component: 'PropertyService', action },
    });
    return new PropertyServiceError(
      error.message || defaultMessage, 'UNKNOWN_ERROR', error
    );
  }

  /**
   * Uploads multiple images for a given property.
   * @param propertyId The ID of the property.
   * @param request The upload request containing files, alt texts, and primary image index.
   * @returns A promise resolving to an array of uploaded PropertyImage objects.
   */
  async uploadPropertyImages(
    propertyId: string,
    request: ImageUploadRequest
  ): Promise<PropertyImage[]> {
    try {
      const formData = new FormData();
    // Append files
    request.files.forEach((file) => {
      formData.append('files', file);
    });
    // Append other fields
    if (request.altTexts) {
      request.altTexts.forEach((text, index) => {
        formData.append(`altTexts[${index}]`, text);
      });
    }
    if (request.isPrimaryIndex !== undefined) {
      formData.append('isPrimaryIndex', request.isPrimaryIndex.toString());
    }

    // Axios automatically sets Content-Type to multipart/form-data for FormData
    const response: ApiResponse<PropertyImage[]> = await apiClient.post(
      PROPERTY_ENDPOINTS.UPLOAD_IMAGE(propertyId),
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data', // Explicitly set for clarity, though Axios handles it
        },
        onUploadProgress: (progressEvent) => {
          if (onprogress) {
            // Calculate and send progress updates
          }
        },
      }
    );
    return response.data; 
    } catch (error) {
      throw this.handleServiceError(
        error, 'Create Property Images',
        'Failed to fetch property images',
        HTTP_STATUS.NOT_FOUND
      );
    }
  }

  /**
   * Retrieves all images for a specific property.
   * @param propertyId The ID of the property.
   * @returns A promise resolving to an array of PropertyImage objects.
   */
  async getPropertyImages(propertyId: string): Promise<PropertyImage[]> {
    try {
      const response: ApiResponse<PropertyImage[]> = await apiClient.get(
        PROPERTY_ENDPOINTS.GET_IMAGES(propertyId)
      );
      return response.data; 
    } catch (error) {
      throw this.handleServiceError(
        error,
        'Get all images',
        'Failed to fetch images',
        HTTP_STATUS.BAD_REQUEST
      );
    }
  }

  /**
   * Deletes a specific image from a property.
   * @param propertyId The ID of the property.
   * @param imageId The ID of the image to delete.
   * @returns A promise resolving when the image is deleted.
   */
  async deleteImage(
    propertyId: string,
    imageId: string
  ): Promise<void> {
    try {
      await apiClient.delete(PROPERTY_ENDPOINTS.DELETE_IMAGE(propertyId, imageId));
    } catch (error) {
      this.handleServiceError(
        error,
        'Delete image',
        'Failed to delete image',
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Reorders images for a property.
   * @param propertyId The ID of the property.
   * @param reorderRequest The request containing the ordered list of image IDs.
   * @returns A promise resolving to an array of reordered PropertyImage objects.
   */
  async reorderImages(
    propertyId: string, reorderRequest: ReorderImagesRequest
  ): Promise<PropertyImage[]> {
    try {
      const response: ApiResponse<PropertyImage[]> = await apiClient.patch( // Using PATCH for reorder
        PROPERTY_ENDPOINTS.REORDER_IMAGES(propertyId),
        reorderRequest
      );
      return response.data;
    } catch (error) {
      throw this.handleServiceError(
        error, 'Reorder Property Images',
        'Failed to reorder images',
        HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
  }
}

export const propertyImagesService = PropertyImagesService.getInstance();
