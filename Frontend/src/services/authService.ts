import { jwtDecode } from 'jwt-decode';
import { AUTH_ENDPOINTS } from '@/config/endpoints/auth';
import { HTTP_STATUS, ERROR_MESSAGES } from '@/config/api';
import apiClient, { ApiResponse } from '@/services/apiClient';
import { errorHandler } from './errorHandler';
// Types matching the backend DTOs and interfaces
export interface LoginDto {
  email: string;
  password: string;
}

export interface RegisterDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone: string;
  role?: 'tenant' | 'landlord' | 'agent' | 'admin';
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    isEmailVerified: boolean;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export interface RefreshTokenDto {
  refreshToken: string;
}

export interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
}

export interface ForgotPasswordDto {
  email: string;
}

export interface ResetPasswordDto {
  token: string;
  newPassword: string;
}

export interface VerifyEmailDto {
  token: string;
}

export interface JwtPayload {
  sub: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

class AuthService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiry: number | null = null;
  private refreshPromise: Promise<AuthResponse> | null = null;

  constructor() {
    // Load tokens from localStorage on initialization
    this.loadTokensFromStorage();
  }

  // Token Management
  private loadTokensFromStorage(): void {
    this.accessToken = localStorage.getItem('accessToken');
    this.refreshToken = localStorage.getItem('refreshToken');
    const expiry = localStorage.getItem('tokenExpiry');
    this.tokenExpiry = expiry ? parseInt(expiry) : null;
  }

  private saveTokensToStorage(
    accessToken: string,
    refreshToken: string,
    expiresIn: number
  ): void {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.tokenExpiry = Date.now() + expiresIn * 1000;

    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
    localStorage.setItem('tokenExpiry', this.tokenExpiry.toString());
  }

  private clearTokensFromStorage(): void {
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;

    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('tokenExpiry');
  }

  public getAccessToken(): string | null {
    return this.accessToken;
  }

  public getRefreshToken(): string | null {
    return this.refreshToken;
  }

  public isTokenExpired(): boolean {
    if (!this.tokenExpiry) return true;
    // Add 30 second buffer to prevent edge cases
    return Date.now() >= this.tokenExpiry - 30000;
  }

  public isTokenValid(token: string): boolean {
    if (!token) return false;

    try {
      const decoded = jwtDecode<JwtPayload>(token);
      const currentTime = Date.now() / 1000;

      // Check if token is expired
      if (decoded.exp && decoded.exp < currentTime) {
        return false;
      }

      // Check if token is issued in the future (clock skew protection)
      if (decoded.iat && decoded.iat > currentTime + 300) {
        // 5 minute tolerance
        return false;
      }

      return true;
    } catch (error) {
      console.error('Token validation failed:', error);
      return false;
    }
  }

   // Utility Methods
   public getCurrentUser(): JwtPayload | null {
    const token = this.getAccessToken();
    if (token && this.isTokenValid(token)) {
      try {
        return jwtDecode<JwtPayload>(token);
      } catch (error) {
        console.error('Failed to decode token:', error);
        return null;
      }
    }
    return null;
  }

  /**
   * Ensures the current access token is valid, refreshing it if necessary.
   * This should be called before any authenticated API request.
   * @returns boolean indicating if a valid token is available
   */
  public async ensureValidToken(): Promise<boolean> {
    if (this.accessToken && !this.isTokenExpired()) return true;

    if (!this.refreshToken) {
      this.clearTokensFromStorage();
      return false; // No refresh token is available, user needs to log in
      }
      if (!this.refreshPromise) {
        // A refresh ops is already in progress, wait for it.
        try {
          await this.refreshPromise;
          return true; // refresh completed successfully
        } catch (error) {
          // refresh failed for another request, log out
          this.clearTokensFromStorage();
          throw error; // re-throw to propagate error
        }
      }

      // Initiate token refresh
      this.refreshPromise = this.refreshAccessToken();
      try {
        await this.refreshPromise;
        return true;
      } catch (error) {
        this.clearTokensFromStorage();
        return false;
      } finally {
        this.refreshPromise = null;
      }
  }

  public async refreshAccessToken(): Promise<AuthResponse> {
    const currentRefreshToken = this.getRefreshToken();
    if (!currentRefreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response: ApiResponse<AuthResponse> = await apiClient.post(
        AUTH_ENDPOINTS.REFRESH, {
        refreshToken: currentRefreshToken
      });
      if (response.data?.accessToken && response.data?.refreshToken) {
        this.saveTokensToStorage(
          response.data?.accessToken,
          response.data?.refreshToken,
          response.data?.expiresIn
        );
        return response.data;
      }
      throw new Error('Refresh token response missing tokens');
    } catch (error) {
      this.clearTokensFromStorage(); // clear tokens if refresh fail
      errorHandler.handleError({
        message: error.message || ERROR_MESSAGES.UNAUTHORIZED,
        code: error.statusCode || ERROR_MESSAGES.UNAUTHORIZED,
        severity: 'high',
        category: 'authentication',
        context: { component: 'authentication', action: 'Refresh tokem' },
      });
      throw error;
    }
  }

  // Auth Methods
  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    try {
      const response: ApiResponse<AuthResponse> = await apiClient.post(
        AUTH_ENDPOINTS.REGISTER, registerDto
      );
      this.saveTokensToStorage(
        response.data?.accessToken,
        response.data?.refreshToken,
        response.data?.expiresIn
      );
      return response.data;   
    } catch (error) {
      errorHandler.handleError({
        message: error.message || ERROR_MESSAGES.GENERIC_ERROR,
        code: error.statusCode || HTTP_STATUS.BAD_REQUEST,
        status: error.statusCode || HTTP_STATUS.BAD_REQUEST,
        severity: 'medium',
        category: 'authentication',
        context: { component: 'AuthService', action: 'Register' },
      });
      throw error
    }
  }

  async login(loginDto: LoginDto): Promise<AuthResponse> {
    try {
      const response: ApiResponse<AuthResponse> = await apiClient.post(
        AUTH_ENDPOINTS.LOGIN, loginDto
      );
      this.saveTokensToStorage(
        response.data?.accessToken,
        response.data?.refreshToken,
        response.data?.expiresIn
      );
      return response.data; 
    } catch (error: any) {
      errorHandler.handleError({
        message: error.message || ERROR_MESSAGES.UNAUTHORIZED,
        code: error.statusCode || HTTP_STATUS.UNAUTHORIZED,
        status: error.statusCode || HTTP_STATUS.UNAUTHORIZED,
        severity: 'medium',
        category: 'authentication',
        context: { component: 'AuthService', action: 'Login' },
      });
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      await apiClient.post(
        AUTH_ENDPOINTS.LOGOUT, { refreshToken: this.refreshToken }
      );
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn(
        'Backend logout failed, proceeding with local logout:', error
      );
    } finally {
      this.clearTokensFromStorage();
    }
  }

  async changePassword(changePasswordDto: ChangePasswordDto): Promise<void> {
    try {
      await this.ensureValidToken() // ensure token is valid before request
      await apiClient.patch(
        AUTH_ENDPOINTS.CHANGE_PASSWORD, changePasswordDto
      ); 
    } catch (error) {
      errorHandler.handleError({
        message: error.message || ERROR_MESSAGES.GENERIC_ERROR,
        code: error.statusCode || HTTP_STATUS.BAD_REQUEST,
        status: error.statusCode || HTTP_STATUS.BAD_REQUEST,
        severity: 'medium',
        category: 'authentication',
        context: { component: 'AuthService', action: 'Change Password' },
      });
      throw error;
    }
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    try {
      await apiClient.post(AUTH_ENDPOINTS.FORGOT_PASSWORD, forgotPasswordDto);
    } catch (error: any) {
      errorHandler.handleError({
        message: error.message || ERROR_MESSAGES.GENERIC_ERROR,
        code: error.statusCode || HTTP_STATUS.BAD_REQUEST,
        status: error.statusCode || HTTP_STATUS.BAD_REQUEST,
        severity: 'medium',
        category: 'authentication',
        context: { component: 'AuthService', action: 'Forgot Password' },
      });
      throw error;
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    try {
      await apiClient.post(AUTH_ENDPOINTS.RESET_PASSWORD, resetPasswordDto);
    } catch (error: any) {
      errorHandler.handleError({
        message: error.message || ERROR_MESSAGES.GENERIC_ERROR,
        code: error.statusCode || HTTP_STATUS.BAD_REQUEST,
        status: error.statusCode || HTTP_STATUS.BAD_REQUEST,
        severity: 'medium',
        category: 'authentication',
        context: { component: 'AuthService', action: 'Reset Password' },
      });
      throw error;
    }
  }

  async verifyEmail(verifyEmailDto: VerifyEmailDto): Promise<void> {
    try {
      await apiClient.post(AUTH_ENDPOINTS.VERIFY_EMAIL, verifyEmailDto);
    } catch (error: any) {
      errorHandler.handleError({
        message: error.message || ERROR_MESSAGES.GENERIC_ERROR,
        code: error.statusCode || HTTP_STATUS.BAD_REQUEST,
        status: error.statusCode || HTTP_STATUS.BAD_REQUEST,
        severity: 'medium',
        category: 'authentication',
        context: { component: 'AuthService', action: 'Verify Email' },
      });
      throw error;
    }
  }

  async getProfile(): Promise<any> {
    try {
      await this.ensureValidToken(); // Ensure token is valid before request
      const response: ApiResponse<AuthResponse['user']> = await apiClient.get(
        AUTH_ENDPOINTS.GET_PROFILE
      );
      return response.data;
    } catch (error: any) {
      errorHandler.handleError({
        message: error.message || ERROR_MESSAGES.GENERIC_ERROR,
        code: error.statusCode || HTTP_STATUS.INTERNAL_SERVER_ERROR,
        status: error.statusCode || HTTP_STATUS.INTERNAL_SERVER_ERROR,
        severity: 'high',
        category: 'authentication',
        context: { component: 'AuthService', action: 'Get Profile' },
      });
      throw error;
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
