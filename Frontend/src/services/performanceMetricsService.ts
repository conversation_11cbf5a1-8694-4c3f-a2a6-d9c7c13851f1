import { MockDataService } from '@/services/mockDataService';

export interface AgentPerformanceMetrics {
  agent_id: string;
  period: { start: string; end: string };
  properties_managed: number;
  properties_listed: number;
  properties_rented: number;
  total_inquiries: number;
  responded_inquiries: number;
  conversion_rate: number;
  response_time_avg: number;
  client_satisfaction: number;
  revenue_generated: number;
  commission_earned: number;
  market_share: number;
  growth_rate: number;
  efficiency_score: number;
  quality_score: number;
  activity_level: 'high' | 'medium' | 'low';
  performance_trend: 'improving' | 'stable' | 'declining';
  ranking: {
    overall: number;
    by_revenue: number;
    by_conversion: number;
    by_satisfaction: number;
  };
  goals: {
    revenue_target: number;
    revenue_achieved: number;
    properties_target: number;
    properties_achieved: number;
    satisfaction_target: number;
    satisfaction_achieved: number;
  };
}

export interface PropertyPerformanceMetrics {
  property_id: string;
  period: { start: string; end: string };
  total_views: number;
  unique_views: number;
  view_to_inquiry_rate: number;
  inquiry_to_visit_rate: number;
  visit_to_application_rate: number;
  application_to_lease_rate: number;
  overall_conversion_rate: number;
  average_time_on_market: number;
  price_competitiveness: number;
  engagement_score: number;
  quality_score: number;
  market_position: 'premium' | 'competitive' | 'budget';
  performance_grade: 'A' | 'B' | 'C' | 'D' | 'F';
  optimization_suggestions: string[];
  comparable_performance: {
    vs_similar_properties: number;
    vs_location_average: number;
    vs_type_average: number;
  };
}

export interface MarketPerformanceMetrics {
  location: string;
  property_type?: string;
  period: { start: string; end: string };
  total_properties: number;
  active_listings: number;
  average_price: number;
  median_price: number;
  price_per_sqft: number;
  days_on_market_avg: number;
  absorption_rate: number;
  inventory_level: number;
  demand_supply_ratio: number;
  price_trend: 'rising' | 'falling' | 'stable';
  market_velocity: number;
  competition_level: 'high' | 'medium' | 'low';
  market_health: 'excellent' | 'good' | 'fair' | 'poor';
  seasonal_factors: Record<string, number>;
  growth_indicators: {
    price_growth: number;
    volume_growth: number;
    demand_growth: number;
  };
}

export interface FinancialPerformanceMetrics {
  period: { start: string; end: string };
  total_revenue: number;
  commission_revenue: number;
  subscription_revenue: number;
  other_revenue: number;
  revenue_growth: number;
  revenue_per_agent: number;
  revenue_per_property: number;
  cost_per_acquisition: number;
  customer_lifetime_value: number;
  profit_margin: number;
  operating_expenses: number;
  marketing_spend: number;
  roi_marketing: number;
  cash_flow: number;
  accounts_receivable: number;
  payment_collection_rate: number;
  financial_health: 'excellent' | 'good' | 'fair' | 'poor';
}

export interface UserEngagementMetrics {
  period: { start: string; end: string };
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  user_retention_rate: number;
  session_duration_avg: number;
  pages_per_session: number;
  bounce_rate: number;
  conversion_rate: number;
  feature_adoption: Record<string, number>;
  user_satisfaction: number;
  support_tickets: number;
  resolution_time_avg: number;
  churn_rate: number;
  engagement_score: number;
  growth_rate: number;
}

/**
 * Performance Metrics Engine
 * Calculates and tracks comprehensive performance metrics across all entities
 */
export class PerformanceMetricsService {
  private static instance: PerformanceMetricsService;
  private metricsCache: Map<string, any> = new Map();
  private calculationQueue: Map<string, Promise<any>> = new Map();
  private cacheExpiry: number = 10 * 60 * 1000; // 10 minutes

  public static getInstance(): PerformanceMetricsService {
    if (!PerformanceMetricsService.instance) {
      PerformanceMetricsService.instance = new PerformanceMetricsService();
    }
    return PerformanceMetricsService.instance;
  }

  // =====================================================
  // AGENT PERFORMANCE METRICS
  // =====================================================

  /**
   * Calculate comprehensive agent performance metrics
   */
  async calculateAgentPerformance(
    agentId: string,
    period: { start: string; end: string }
  ): Promise<AgentPerformanceMetrics> {
    try {
      const cacheKey = `agent-performance-${agentId}-${period.start}-${period.end}`;
      const cached = this.metricsCache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      }

      // Check if calculation is already in progress
      if (this.calculationQueue.has(cacheKey)) {
        return await this.calculationQueue.get(cacheKey)!;
      }

      // Start calculation
      const calculationPromise = this.performAgentCalculation(agentId, period);
      this.calculationQueue.set(cacheKey, calculationPromise);

      try {
        const metrics = await calculationPromise;
        
        // Cache the result
        this.metricsCache.set(cacheKey, {
          data: metrics,
          timestamp: Date.now()
        });

        return metrics;
      } finally {
        this.calculationQueue.delete(cacheKey);
      }
    } catch (error) {
      console.error('Error calculating agent performance:', error);
      throw new Error('Failed to calculate agent performance metrics');
    }
  }

  /**
   * Perform agent performance calculation
   */
  private async performAgentCalculation(
    agentId: string,
    period: { start: string; end: string }
  ): Promise<AgentPerformanceMetrics> {
    // Get agent data in parallel
    const [basicMetrics, inquiryMetrics, revenueMetrics, satisfactionMetrics, goalMetrics] = await Promise.all([
      this.getAgentBasicMetrics(agentId, period),
      this.getAgentInquiryMetrics(agentId, period),
      this.getAgentRevenueMetrics(agentId, period),
      this.getAgentSatisfactionMetrics(agentId, period),
      this.getAgentGoalMetrics(agentId, period)
    ]);

    // Calculate derived metrics
    const conversionRate = inquiryMetrics.total_inquiries > 0 ? 
      (inquiryMetrics.responded_inquiries / inquiryMetrics.total_inquiries) * 100 : 0;

    const efficiencyScore = this.calculateEfficiencyScore({
      responseTime: inquiryMetrics.response_time_avg,
      conversionRate,
      propertiesManaged: basicMetrics.properties_managed
    });

    const qualityScore = this.calculateQualityScore({
      satisfaction: satisfactionMetrics.client_satisfaction,
      conversionRate,
      responseTime: inquiryMetrics.response_time_avg
    });

    // Get rankings
    const ranking = await this.getAgentRankings(agentId, period);

    // Determine performance trend
    const performanceTrend = await this.calculatePerformanceTrend(agentId, period);

    return {
      agent_id: agentId,
      period,
      properties_managed: basicMetrics.properties_managed,
      properties_listed: basicMetrics.properties_listed,
      properties_rented: basicMetrics.properties_rented,
      total_inquiries: inquiryMetrics.total_inquiries,
      responded_inquiries: inquiryMetrics.responded_inquiries,
      conversion_rate: conversionRate,
      response_time_avg: inquiryMetrics.response_time_avg,
      client_satisfaction: satisfactionMetrics.client_satisfaction,
      revenue_generated: revenueMetrics.revenue_generated,
      commission_earned: revenueMetrics.commission_earned,
      market_share: revenueMetrics.market_share,
      growth_rate: revenueMetrics.growth_rate,
      efficiency_score: efficiencyScore,
      quality_score: qualityScore,
      activity_level: this.determineActivityLevel(basicMetrics),
      performance_trend: performanceTrend,
      ranking,
      goals: goalMetrics
    };
  }

  // =====================================================
  // PROPERTY PERFORMANCE METRICS
  // =====================================================

  /**
   * Calculate property performance metrics
   */
  async calculatePropertyPerformance(
    propertyId: string,
    period: { start: string; end: string }
  ): Promise<PropertyPerformanceMetrics> {
    try {
      const cacheKey = `property-performance-${propertyId}-${period.start}-${period.end}`;
      const cached = this.metricsCache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      }

      // Get property performance data
      const { data: performanceData, error } = await supabase.rpc('calculate_property_performance', {
        property_id: propertyId,
        start_date: period.start,
        end_date: period.end
      });

      if (error) throw error;

      const metrics: PropertyPerformanceMetrics = {
        property_id: propertyId,
        period,
        total_views: performanceData.total_views || 0,
        unique_views: performanceData.unique_views || 0,
        view_to_inquiry_rate: performanceData.view_to_inquiry_rate || 0,
        inquiry_to_visit_rate: performanceData.inquiry_to_visit_rate || 0,
        visit_to_application_rate: performanceData.visit_to_application_rate || 0,
        application_to_lease_rate: performanceData.application_to_lease_rate || 0,
        overall_conversion_rate: performanceData.overall_conversion_rate || 0,
        average_time_on_market: performanceData.average_time_on_market || 0,
        price_competitiveness: performanceData.price_competitiveness || 0,
        engagement_score: performanceData.engagement_score || 0,
        quality_score: performanceData.quality_score || 0,
        market_position: this.determineMarketPosition(performanceData),
        performance_grade: this.calculatePerformanceGrade(performanceData),
        optimization_suggestions: this.generateOptimizationSuggestions(performanceData),
        comparable_performance: performanceData.comparable_performance || {
          vs_similar_properties: 0,
          vs_location_average: 0,
          vs_type_average: 0
        }
      };

      // Cache the result
      this.metricsCache.set(cacheKey, {
        data: metrics,
        timestamp: Date.now()
      });

      return metrics;
    } catch (error) {
      console.error('Error calculating property performance:', error);
      throw new Error('Failed to calculate property performance metrics');
    }
  }

  // =====================================================
  // MARKET PERFORMANCE METRICS
  // =====================================================

  /**
   * Calculate market performance metrics
   */
  async calculateMarketPerformance(
    location: string,
    propertyType?: string,
    period: { start: string; end: string } = {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString()
    }
  ): Promise<MarketPerformanceMetrics> {
    try {
      const cacheKey = `market-performance-${location}-${propertyType}-${period.start}-${period.end}`;
      const cached = this.metricsCache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      }

      const { data: marketData, error } = await supabase.rpc('calculate_market_performance', {
        location_filter: location,
        property_type_filter: propertyType,
        start_date: period.start,
        end_date: period.end
      });

      if (error) throw error;

      const metrics: MarketPerformanceMetrics = {
        location,
        property_type: propertyType,
        period,
        total_properties: marketData.total_properties || 0,
        active_listings: marketData.active_listings || 0,
        average_price: marketData.average_price || 0,
        median_price: marketData.median_price || 0,
        price_per_sqft: marketData.price_per_sqft || 0,
        days_on_market_avg: marketData.days_on_market_avg || 0,
        absorption_rate: marketData.absorption_rate || 0,
        inventory_level: marketData.inventory_level || 0,
        demand_supply_ratio: marketData.demand_supply_ratio || 0,
        price_trend: this.determinePriceTrend(marketData),
        market_velocity: marketData.market_velocity || 0,
        competition_level: this.determineCompetitionLevel(marketData),
        market_health: this.assessMarketHealth(marketData),
        seasonal_factors: marketData.seasonal_factors || {},
        growth_indicators: {
          price_growth: marketData.price_growth || 0,
          volume_growth: marketData.volume_growth || 0,
          demand_growth: marketData.demand_growth || 0
        }
      };

      // Cache the result
      this.metricsCache.set(cacheKey, {
        data: metrics,
        timestamp: Date.now()
      });

      return metrics;
    } catch (error) {
      console.error('Error calculating market performance:', error);
      throw new Error('Failed to calculate market performance metrics');
    }
  }
}

// Export singleton instance
export const performanceMetricsService = PerformanceMetricsService.getInstance();
