import axios, { 
  AxiosInstance, AxiosRequestConfig,
  AxiosResponse, AxiosError
} from 'axios';
import { API_CONFIG, HTTP_STATUS, ERROR_MESSAGES } from '@/config/api';
import { errorHandler } from './errorHandler';

export interface ApiResponse<T = any> {
  data: T;
  message: string;
  statusCode: number;
  timestamp: string;
  path: string;
}

export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
  timestamp: string;
  path: string;
}

class ApiClient {
  private axiosInstance: AxiosInstance;
  private retryAttempts: number;
  private retryDelay: number;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.retryAttempts = API_CONFIG.RETRY_ATTEMPTS;
    this.retryDelay = API_CONFIG.RETRY_DELAY;

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor: Add auth token to headers
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('accessToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor: Handle errors and retries
    this.axiosInstance.interceptors.response.use(
      (response) => {
        // Axios wraps response data in `response.data`
        // Ensure our ApiResponse structure is maintained from backend
        return response as AxiosResponse<ApiResponse>;
      },
      async (error: AxiosError<ApiError>) => {
        const { config, response } = error;
        const originalRequest = config;

        // --- Error Handling ---
        if (response) {
          const status = response.status;
          const errorData: ApiError = response.data || {
            message: `HTTP ${status}: ${response.statusText}`,
            statusCode: status,
            timestamp: new Date().toISOString(),
            path: originalRequest?.url || 'unknown',
          };

          if (status === HTTP_STATUS.UNAUTHORIZED) {
            // Token expired or invalid. Clear tokens.
            // authService.ts is responsible for initiating refresh, if applicable.
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            
            errorHandler.handleError({
              message: errorData.message || ERROR_MESSAGES.UNAUTHORIZED,
              code: status,
              status: status,
              severity: 'medium',
              category: 'authentication',
              context: {
                component: 'ApiClient',
                action: 'Request Interceptor',
                url: originalRequest?.url,
              },
            });
          } else if (status === HTTP_STATUS.FORBIDDEN) {
            errorHandler.handleError({
              message: errorData.message || ERROR_MESSAGES.FORBIDDEN,
              code: status,
              status: status,
              severity: 'medium',
              category: 'authorization',
              context: {
                component: 'ApiClient',
                action: 'Request Interceptor',
                url: originalRequest?.url,
              },
            });
          } else if (status >= 500 && status <= 599) {
            // Server error - retry if possible
            const retryCount = (originalRequest as any).__retryCount || 0;
            if (retryCount < this.retryAttempts) {
              (originalRequest as any).__retryCount = retryCount + 1;
              const delay = this.retryDelay * Math.pow(2, retryCount); // Exponential backoff
              await new Promise((resolve) => setTimeout(resolve, delay));
              return this.axiosInstance(originalRequest); // Retry the original request
            }

            errorHandler.handleError({
              message: errorData.message || ERROR_MESSAGES.SERVER_ERROR,
              code: status,
              status: status,
              severity: 'high',
              category: 'runtime',
              context: {
                component: 'ApiClient',
                action: 'Request Interceptor',
                url: originalRequest?.url,
              },
            });
          } else {
            errorHandler.handleError({
              message: errorData.message || ERROR_MESSAGES.GENERIC_ERROR,
              code: status,
              status: status,
              severity: 'medium',
              category: 'validation',
              context: {
                component: 'ApiClient',
                action: 'Request Interceptor',
                url: originalRequest?.url,
              },
            });
          }
          // Always reject the promise so calling code can catch it
          return Promise.reject(new Error(errorData.message || `HTTP Error: ${status}`));
        } else if (error.request) {
          // The request was made but no response was received (e.g., network error, timeout)
          errorHandler.handleError({
            message: ERROR_MESSAGES.NETWORK_ERROR,
            code: 0, // No HTTP status
            status: 0,
            severity: 'high',
            category: 'network',
            context: {
              component: 'ApiClient',
              action: 'Network Error',
              url: originalRequest?.url,
            },
          });
          return Promise.reject(new Error(ERROR_MESSAGES.NETWORK_ERROR));
        } else {
          // Something happened in setting up the request that triggered an Error
          errorHandler.handleError({
            message: error.message || ERROR_MESSAGES.GENERIC_ERROR,
            code: 0,
            status: 0,
            severity: 'critical',
            category: 'client',
            context: {
              component: 'ApiClient',
              action: 'Request Setup Error',
              url: originalRequest?.url,
            },
          });
          return Promise.reject(new Error(error.message || ERROR_MESSAGES.GENERIC_ERROR));
        }
      }
    );
  }

  // --- Public HTTP Methods ---
  public async get<T>(
    endpoint: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.get<ApiResponse<T>>(
      endpoint, config
    );
    return response.data;
  }

  public async post<T>(
    endpoint: string, data?: any, config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.post<ApiResponse<T>>(
      endpoint, data, config
    );
    return response.data;
  }

  public async put<T>(
    endpoint: string, data?: any, config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.put<ApiResponse<T>>(
      endpoint, data, config
    );
    return response.data;
  }

  public async patch<T>(
    endpoint: string, data?: any, config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.patch<ApiResponse<T>>(
      endpoint, data, config
    );
    return response.data;
  }

  public async delete<T>(
    endpoint: string, config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.delete<ApiResponse<T>>(
      endpoint, config
    );
    return response.data;
  }


  // File upload. axios' post/put/patch handle uploads implicitly
  // but I'll keep this for explicit clarity
  public async upload<T>(
    endpoint: string,
    formData: FormData,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.post<ApiResponse<T>>(
      endpoint, formData, config
    );
    return response.data;
  }
}

// Export singleton instance
const apiClient = new ApiClient();
export default apiClient;
