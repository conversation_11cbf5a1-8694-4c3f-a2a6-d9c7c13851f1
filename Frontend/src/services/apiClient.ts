import { API_CONFIG } from '@/config/api';
import { errorHandler } from './errorHandler';

export interface ApiResponse<T = any> {
  data: T;
  message: string;
  statusCode: number;
  timestamp: string;
  path: string;
}

export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
  timestamp: string;
  path: string;
}

class ApiClient {
  private baseURL: string;
  private timeout: number;
  private retryAttempts: number;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.timeout = API_CONFIG.TIMEOUT;
    this.retryAttempts = API_CONFIG.RETRY_ATTEMPTS;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      // Add auth token if available
      const token = localStorage.getItem('accessToken');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData: ApiError = await response.json().catch(() => ({
          message: `HTTP ${response.status}: ${response.statusText}`,
          statusCode: response.status,
          timestamp: new Date().toISOString(),
          path: endpoint,
        }));

        // Handle specific error cases
        if (response.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          
          errorHandler.handleError({
            message: 'Authentication required',
            code: response.status,
            status: response.status,
            severity: 'medium',
            category: 'authentication',
            context: {
              component: 'ApiClient',
              action: 'Request',
              url: endpoint,
            },
          });
        } else if (response.status === 403) {
          errorHandler.handleError({
            message: 'Access denied',
            code: response.status,
            status: response.status,
            severity: 'medium',
            category: 'authorization',
            context: {
              component: 'ApiClient',
              action: 'Request',
              url: endpoint,
            },
          });
        } else if (response.status >= 500) {
          // Server error - retry if possible
          if (retryCount < this.retryAttempts) {
            await this.delay(Math.pow(2, retryCount) * 1000); // Exponential backoff
            return this.makeRequest(endpoint, options, retryCount + 1);
          }

          errorHandler.handleError({
            message: errorData.message || 'Server error',
            code: response.status,
            status: response.status,
            severity: 'high',
            category: 'runtime',
            context: {
              component: 'ApiClient',
              action: 'Request',
              url: endpoint,
            },
          });
        } else {
          errorHandler.handleError({
            message: errorData.message || 'Request failed',
            code: response.status,
            status: response.status,
            severity: 'medium',
            category: 'validation',
            context: {
              component: 'ApiClient',
              action: 'Request',
              url: endpoint,
            },
          });
        }

        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data: ApiResponse<T> = await response.json();
      return data;

    } catch (error: any) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        errorHandler.handleError({
          message: 'Request timeout',
          severity: 'medium',
          category: 'network',
          context: {
            component: 'ApiClient',
            action: 'Request Timeout',
            url: endpoint,
          },
        });
        throw new Error('Request timeout');
      }

      if (!navigator.onLine) {
        errorHandler.handleError({
          message: 'No internet connection',
          severity: 'medium',
          category: 'network',
          context: {
            component: 'ApiClient',
            action: 'Network Error',
            url: endpoint,
          },
        });
        throw new Error('No internet connection');
      }

      // Network error - retry if possible
      if (retryCount < this.retryAttempts && !error.message.includes('HTTP')) {
        await this.delay(Math.pow(2, retryCount) * 1000);
        return this.makeRequest(endpoint, options, retryCount + 1);
      }

      errorHandler.handleApiError(error, {
        component: 'ApiClient',
        action: 'Request',
        url: endpoint,
      });

      throw error;
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // HTTP Methods
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = params ? `${endpoint}?${new URLSearchParams(params).toString()}` : endpoint;
    return this.makeRequest<T>(url, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'DELETE' });
  }

  // File upload
  async upload<T>(endpoint: string, formData: FormData): Promise<ApiResponse<T>> {
    const token = localStorage.getItem('accessToken');
    const headers: HeadersInit = {};
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers,
    });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
