export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4,
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: Record<string, any>;
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  stack?: string;
  tags?: string[];
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  enableRemote: boolean;
  maxStorageEntries: number;
  remoteEndpoint?: string;
  batchSize: number;
  flushInterval: number;
}

class Logger {
  private static instance: Logger;
  private config: LoggerConfig;
  private logBuffer: LogEntry[] = [];
  private flushTimer?: NodeJS.Timeout;
  private sessionId: string;

  private constructor() {
    this.config = {
      level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
      enableConsole: true,
      enableStorage: true,
      enableRemote: process.env.NODE_ENV === 'production',
      maxStorageEntries: 100,
      remoteEndpoint: '/api/v1/logs',
      batchSize: 10,
      flushInterval: 30000, // 30 seconds
    };

    this.sessionId = this.generateSessionId();
    this.startFlushTimer();
    this.setupUnloadHandler();
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  configure(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  debug(message: string, context?: Record<string, any>, component?: string, action?: string): void {
    this.log(LogLevel.DEBUG, message, context, component, action);
  }

  info(message: string, context?: Record<string, any>, component?: string, action?: string): void {
    this.log(LogLevel.INFO, message, context, component, action);
  }

  warn(message: string, context?: Record<string, any>, component?: string, action?: string): void {
    this.log(LogLevel.WARN, message, context, component, action);
  }

  error(message: string, error?: Error, context?: Record<string, any>, component?: string, action?: string): void {
    const logContext = {
      ...context,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : undefined,
    };

    this.log(LogLevel.ERROR, message, logContext, component, action, error?.stack);
  }

  critical(message: string, error?: Error, context?: Record<string, any>, component?: string, action?: string): void {
    const logContext = {
      ...context,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : undefined,
    };

    this.log(LogLevel.CRITICAL, message, logContext, component, action, error?.stack);
    
    // Immediately flush critical logs
    this.flush();
  }

  private log(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    component?: string,
    action?: string,
    stack?: string,
    tags?: string[]
  ): void {
    if (level < this.config.level) {
      return;
    }

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
      component,
      action,
      userId: this.getCurrentUserId(),
      sessionId: this.sessionId,
      stack,
      tags,
    };

    // Console logging
    if (this.config.enableConsole) {
      this.logToConsole(entry);
    }

    // Storage logging
    if (this.config.enableStorage) {
      this.logToStorage(entry);
    }

    // Remote logging
    if (this.config.enableRemote) {
      this.logBuffer.push(entry);
      
      if (this.logBuffer.length >= this.config.batchSize) {
        this.flush();
      }
    }
  }

  private logToConsole(entry: LogEntry): void {
    const levelName = LogLevel[entry.level];
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const prefix = `[${timestamp}] ${levelName}`;
    
    const logData = {
      message: entry.message,
      component: entry.component,
      action: entry.action,
      context: entry.context,
      userId: entry.userId,
      sessionId: entry.sessionId,
    };

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(`${prefix}:`, logData);
        break;
      case LogLevel.INFO:
        console.info(`${prefix}:`, logData);
        break;
      case LogLevel.WARN:
        console.warn(`${prefix}:`, logData);
        break;
      case LogLevel.ERROR:
        console.error(`${prefix}:`, logData);
        if (entry.stack) {
          console.error('Stack trace:', entry.stack);
        }
        break;
      case LogLevel.CRITICAL:
        console.error(`🚨 ${prefix}:`, logData);
        if (entry.stack) {
          console.error('Stack trace:', entry.stack);
        }
        break;
    }
  }

  private logToStorage(entry: LogEntry): void {
    try {
      const existingLogs = JSON.parse(localStorage.getItem('app_logs') || '[]');
      existingLogs.push(entry);
      
      // Keep only the most recent entries
      if (existingLogs.length > this.config.maxStorageEntries) {
        existingLogs.splice(0, existingLogs.length - this.config.maxStorageEntries);
      }
      
      localStorage.setItem('app_logs', JSON.stringify(existingLogs));
    } catch (error) {
      console.warn('Failed to store log entry:', error);
    }
  }

  private async flush(): Promise<void> {
    if (this.logBuffer.length === 0 || !this.config.enableRemote) {
      return;
    }

    const logsToSend = [...this.logBuffer];
    this.logBuffer = [];

    try {
      await fetch(this.config.remoteEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify({ logs: logsToSend }),
      });
    } catch (error) {
      console.warn('Failed to send logs to remote endpoint:', error);
      // Re-add logs to buffer for retry
      this.logBuffer.unshift(...logsToSend);
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }

  private setupUnloadHandler(): void {
    window.addEventListener('beforeunload', () => {
      this.flush();
    });

    window.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.flush();
      }
    });
  }

  private getCurrentUserId(): string | undefined {
    try {
      const token = localStorage.getItem('accessToken');
      if (token) {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.sub;
      }
    } catch (error) {
      // Ignore token parsing errors
    }
    return undefined;
  }

  private generateSessionId(): string {
    let sessionId = sessionStorage.getItem('sessionId');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('sessionId', sessionId);
    }
    return sessionId;
  }

  // Utility methods
  getLogs(): LogEntry[] {
    try {
      return JSON.parse(localStorage.getItem('app_logs') || '[]');
    } catch (error) {
      console.warn('Failed to retrieve logs from storage:', error);
      return [];
    }
  }

  clearLogs(): void {
    localStorage.removeItem('app_logs');
    this.logBuffer = [];
  }

  exportLogs(): string {
    const logs = this.getLogs();
    return JSON.stringify(logs, null, 2);
  }

  // Performance logging
  time(label: string): void {
    console.time(label);
    this.debug(`Timer started: ${label}`, undefined, 'Performance', 'Timer Start');
  }

  timeEnd(label: string): void {
    console.timeEnd(label);
    this.debug(`Timer ended: ${label}`, undefined, 'Performance', 'Timer End');
  }

  // Group logging
  group(label: string): void {
    console.group(label);
    this.debug(`Group started: ${label}`, undefined, 'Logger', 'Group Start');
  }

  groupEnd(): void {
    console.groupEnd();
    this.debug('Group ended', undefined, 'Logger', 'Group End');
  }

  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush();
  }
}

// Export singleton instance
export const logger = Logger.getInstance();

// Export convenience functions
export const log = {
  debug: (message: string, context?: Record<string, any>, component?: string, action?: string) =>
    logger.debug(message, context, component, action),
  info: (message: string, context?: Record<string, any>, component?: string, action?: string) =>
    logger.info(message, context, component, action),
  warn: (message: string, context?: Record<string, any>, component?: string, action?: string) =>
    logger.warn(message, context, component, action),
  error: (message: string, error?: Error, context?: Record<string, any>, component?: string, action?: string) =>
    logger.error(message, error, context, component, action),
  critical: (message: string, error?: Error, context?: Record<string, any>, component?: string, action?: string) =>
    logger.critical(message, error, context, component, action),
};
