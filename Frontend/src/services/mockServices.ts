// Mock services to replace Supabase-dependent services
// This provides simplified implementations for development

import { ApiService } from './apiService';

// Performance Monitoring Service Mock
export const performanceMonitoringService = {
  trackPageLoad: (page: string, loadTime: number) => {
    console.log(`Page load tracked: ${page} - ${loadTime}ms`);
  },
  trackUserAction: (action: string, metadata?: any) => {
    console.log(`User action tracked: ${action}`, metadata);
  },
  getMetrics: async () => {
    return {
      pageLoadTime: 1200,
      userActions: 45,
      errorRate: 0.02,
      uptime: 99.9,
    };
  },
};

// Validation Service Mock
export const validationService = {
  validateEmail: (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  validatePhone: (phone: string) => {
    const phoneRegex = /^\+?[\d\s-()]+$/;
    return phoneRegex.test(phone);
  },
  validatePassword: (password: string) => {
    return password.length >= 8;
  },
  sanitizeInput: (input: string) => {
    return input.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  },
};

// Real-time Data Service Mock
export const realTimeDataService = {
  subscribe: (channel: string, callback: (data: any) => void) => {
    console.log(`Subscribed to channel: ${channel}`);
    // Simulate real-time updates
    const interval = setInterval(() => {
      callback({ type: 'update', data: { timestamp: Date.now() } });
    }, 5000);

    return () => clearInterval(interval);
  },
  publish: (channel: string, data: any) => {
    console.log(`Published to channel: ${channel}`, data);
  },
};

// Email Service Mock
export const emailService = {
  sendEmail: async (to: string, subject: string, body: string) => {
    console.log(`Email sent to: ${to}, Subject: ${subject}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, messageId: `msg_${Date.now()}` };
  },
  sendTemplate: async (to: string, templateId: string, variables: any) => {
    console.log(`Template email sent to: ${to}, Template: ${templateId}`, variables);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, messageId: `msg_${Date.now()}` };
  },
};

// SMS Service Mock
export const smsService = {
  sendSMS: async (to: string, message: string) => {
    console.log(`SMS sent to: ${to}, Message: ${message}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, messageId: `sms_${Date.now()}` };
  },
};

// WhatsApp Service Mock
export const whatsappService = {
  sendMessage: async (to: string, message: string) => {
    console.log(`WhatsApp message sent to: ${to}, Message: ${message}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, messageId: `wa_${Date.now()}` };
  },
  sendTemplate: async (to: string, templateName: string, parameters: any) => {
    console.log(`WhatsApp template sent to: ${to}, Template: ${templateName}`, parameters);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, messageId: `wa_${Date.now()}` };
  },
};

// Messaging Service Mock
export const messagingService = {
  sendMessage: async (userId: string, message: string, type: string = 'info') => {
    console.log(`Message sent to user: ${userId}, Type: ${type}, Message: ${message}`);
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true, messageId: `msg_${Date.now()}` };
  },
  getMessages: async (userId: string) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return [
      {
        id: '1',
        userId,
        message: 'Welcome to PHCityRent!',
        type: 'info',
        read: false,
        createdAt: new Date().toISOString(),
      },
    ];
  },
};

// Dashboard Service Mock
export const dashboardService = {
  getStats: async () => {
    try {
      return await ApiService.getStats();
    } catch (error) {
      console.warn('Failed to fetch stats from API, using fallback:', error);
      return {
        totalUsers: 0,
        totalProperties: 0,
        totalApplications: 0,
        pendingApplications: 0,
        approvedApplications: 0,
        rejectedApplications: 0,
        totalRevenue: 0,
        activeListings: 0,
        verifiedProperties: 0,
        featuredProperties: 0,
      };
    }
  },
  getRecentActivity: async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return [
      {
        id: '1',
        type: 'property_viewed',
        description: 'Property viewed by user',
        timestamp: new Date().toISOString(),
      },
    ];
  },
};

// Market Trend Analysis Service Mock
export const marketTrendAnalysisService = {
  getMarketTrends: async (location: string) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      location,
      averagePrice: 250000,
      priceChange: 5.2,
      demandLevel: 'high',
      supplyLevel: 'medium',
      trends: [
        { month: 'Jan', price: 240000 },
        { month: 'Feb', price: 245000 },
        { month: 'Mar', price: 250000 },
      ],
    };
  },
  getPriceRecommendation: async (propertyData: any) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return {
      recommendedPrice: 275000,
      confidence: 0.85,
      factors: ['location', 'size', 'amenities'],
    };
  },
};

// Performance Metrics Service Mock
export const performanceMetricsService = {
  getMetrics: async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return {
      responseTime: 120,
      throughput: 1500,
      errorRate: 0.01,
      uptime: 99.95,
    };
  },
  trackMetric: (name: string, value: number) => {
    console.log(`Metric tracked: ${name} = ${value}`);
  },
};

// Report Generation Service Mock
export const reportGenerationService = {
  generateReport: async (type: string, parameters: any) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return {
      reportId: `report_${Date.now()}`,
      type,
      status: 'completed',
      downloadUrl: `/reports/report_${Date.now()}.pdf`,
    };
  },
  getReportStatus: async (reportId: string) => {
    await new Promise(resolve => setTimeout(resolve, 200));
    return {
      reportId,
      status: 'completed',
      progress: 100,
    };
  },
};

// Additional services
export const agentService = {
  getAgents: async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return [{ id: '1', name: 'John Agent', email: '<EMAIL>', phone: '+234123456789' }];
  },
  createAgent: async (data: any) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { id: Date.now().toString(), ...data };
  },
};

export const aiRecommendationService = {
  getRecommendations: async (userId: string) => {
    await new Promise(resolve => setTimeout(resolve, 400));
    return [{ id: '1', type: 'property', title: 'Recommended Property', score: 0.95 }];
  },
};

export const authService = {
  login: async (credentials: any) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, token: 'mock-token', user: { id: '1', email: credentials.email } };
  },
  register: async (data: any) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, user: { id: Date.now().toString(), ...data } };
  },
};

export const propertyService = {
  getProperties: async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return [{ id: '1', title: 'Mock Property', price: 250000, location: 'Port Harcourt' }];
  },
  createProperty: async (data: any) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { id: Date.now().toString(), ...data };
  },
};

export const paymentService = {
  processPayment: async (data: any) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { success: true, transactionId: `txn_${Date.now()}` };
  },
  getPaymentHistory: async (userId: string) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return [{ id: '1', amount: 50000, status: 'completed', date: new Date().toISOString() }];
  },
};

export const marketAnalyticsService = {
  getMarketData: async (location: string) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      averagePrice: 275000,
      priceChange: 5.2,
      marketTrend: 'up',
      demandLevel: 'high',
    };
  },
};

export const pricePredictionService = {
  predictPrice: async (propertyData: any) => {
    await new Promise(resolve => setTimeout(resolve, 600));
    return {
      predictedPrice: 280000,
      confidence: 0.87,
      factors: ['location', 'size', 'amenities'],
    };
  },
};

export const intelligentMatchingService = {
  findMatches: async (criteria: any) => {
    await new Promise(resolve => setTimeout(resolve, 400));
    return [{ id: '1', matchScore: 0.92, property: { id: '1', title: 'Perfect Match' } }];
  },
};

export default {
  performanceMonitoringService,
  validationService,
  realTimeDataService,
  emailService,
  smsService,
  whatsappService,
  messagingService,
  dashboardService,
  marketTrendAnalysisService,
  performanceMetricsService,
  reportGenerationService,
  agentService,
  aiRecommendationService,
  authService,
  propertyService,
  paymentService,
  marketAnalyticsService,
  pricePredictionService,
  intelligentMatchingService,
};
