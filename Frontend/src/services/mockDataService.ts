// Mock Data Service to replace Supabase functionality
// This provides mock data and API simulation for development

export interface MockProperty {
  id: string;
  title: string;
  description: string;
  price: number;
  location: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  type: string;
  status: string;
  images: string[];
  landlord_id: string;
  created_at: string;
  updated_at: string;
}

export interface MockUser {
  id: string;
  email: string;
  full_name: string;
  role: string;
  phone?: string;
  created_at: string;
  updated_at: string;
}

export interface MockApplication {
  id: string;
  property_id: string;
  user_id: string;
  status: string;
  application_date: string;
  move_in_date: string;
  monthly_income: number;
  employment_status: string;
  references: any[];
  documents: any[];
  created_at: string;
  updated_at: string;
}

export interface MockPayment {
  id: string;
  user_id: string;
  property_id: string;
  amount: number;
  status: string;
  payment_method: string;
  transaction_id: string;
  created_at: string;
  updated_at: string;
}

export interface MockNotification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: string;
  read: boolean;
  created_at: string;
}

// Mock data
const mockProperties: MockProperty[] = [
  {
    id: '1',
    title: 'Modern 2BR Apartment',
    description: 'Beautiful modern apartment in the heart of the city',
    price: 150000,
    location: 'Lagos, Nigeria',
    bedrooms: 2,
    bathrooms: 2,
    area: 1200,
    type: 'apartment',
    status: 'available',
    images: ['/api/placeholder/400/300'],
    landlord_id: 'landlord1',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: '2',
    title: 'Luxury 3BR House',
    description: 'Spacious luxury house with garden',
    price: 300000,
    location: 'Abuja, Nigeria',
    bedrooms: 3,
    bathrooms: 3,
    area: 2000,
    type: 'house',
    status: 'available',
    images: ['/api/placeholder/400/300'],
    landlord_id: 'landlord2',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

const mockUsers: MockUser[] = [
  {
    id: 'user1',
    email: '<EMAIL>',
    full_name: 'John Tenant',
    role: 'TENANT',
    phone: '+234123456789',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: 'landlord1',
    email: '<EMAIL>',
    full_name: 'Jane Landlord',
    role: 'LANDLORD',
    phone: '+234987654321',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

const mockApplications: MockApplication[] = [
  {
    id: 'app1',
    property_id: '1',
    user_id: 'user1',
    status: 'pending',
    application_date: new Date().toISOString(),
    move_in_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    monthly_income: 200000,
    employment_status: 'employed',
    references: [],
    documents: [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

const mockPayments: MockPayment[] = [
  {
    id: 'pay1',
    user_id: 'user1',
    property_id: '1',
    amount: 150000,
    status: 'completed',
    payment_method: 'card',
    transaction_id: 'txn_123456',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

const mockNotifications: MockNotification[] = [
  {
    id: 'notif1',
    user_id: 'user1',
    title: 'Application Approved',
    message: 'Your rental application has been approved',
    type: 'success',
    read: false,
    created_at: new Date().toISOString(),
  },
  {
    id: 'notif2',
    user_id: 'user-1', // For test compatibility
    title: 'Welcome to PHCityRent',
    message: 'Welcome to our platform! Start exploring properties.',
    type: 'info',
    read: false,
    created_at: new Date().toISOString(),
  },
];

// Mock API responses
export class MockDataService {
  // Simulate API delay
  private static delay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Properties
  static async getProperties(): Promise<MockProperty[]> {
    await this.delay();
    return [...mockProperties];
  }

  static async getProperty(id: string): Promise<MockProperty | null> {
    await this.delay();
    return mockProperties.find(p => p.id === id) || null;
  }

  static async createProperty(propertyData: {
    title: string;
    description: string;
    price: number;
    location: string;
    bedrooms: number;
    bathrooms: number;
    type: string;
  }): Promise<MockProperty> {
    await this.delay();
    const newProperty: MockProperty = {
      id: Date.now().toString(),
      title: propertyData.title,
      description: propertyData.description,
      price: propertyData.price,
      location: propertyData.location,
      bedrooms: propertyData.bedrooms,
      bathrooms: propertyData.bathrooms,
      area: 1000, // default area
      type: propertyData.type,
      status: 'available',
      images: ['/api/placeholder/400/300'],
      landlord_id: 'landlord1',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    mockProperties.push(newProperty);
    return newProperty;
  }

  // Users
  static async getUsers(): Promise<MockUser[]> {
    await this.delay();
    return [...mockUsers];
  }

  static async getUser(id: string): Promise<MockUser | null> {
    await this.delay();
    return mockUsers.find(u => u.id === id) || null;
  }

  // Applications
  static async getApplications(): Promise<MockApplication[]> {
    await this.delay();
    return [...mockApplications];
  }

  static async getApplication(id: string): Promise<MockApplication | null> {
    await this.delay();
    return mockApplications.find(a => a.id === id) || null;
  }

  static async createApplication(applicationData: {
    propertyId: string;
    applicantName: string;
    email: string;
    phone: string;
  }): Promise<any> {
    await this.delay();
    const newApplication = {
      id: Date.now().toString(),
      propertyId: applicationData.propertyId,
      applicantName: applicationData.applicantName,
      email: applicationData.email,
      phone: applicationData.phone,
      status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Also add to mockApplications for consistency
    const mockApp: MockApplication = {
      id: newApplication.id,
      property_id: applicationData.propertyId,
      user_id: 'user1',
      status: 'pending',
      application_date: new Date().toISOString(),
      move_in_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      monthly_income: 200000,
      employment_status: 'employed',
      references: [],
      documents: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    mockApplications.push(mockApp);

    return newApplication;
  }

  // Payments
  static async getPayments(): Promise<MockPayment[]> {
    await this.delay();
    return [...mockPayments];
  }

  static async getPayment(id: string): Promise<MockPayment | null> {
    await this.delay();
    return mockPayments.find(p => p.id === id) || null;
  }

  static async createPayment(
    payment: Omit<MockPayment, 'id' | 'created_at' | 'updated_at'>
  ): Promise<MockPayment> {
    await this.delay();
    const newPayment: MockPayment = {
      ...payment,
      id: Date.now().toString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    mockPayments.push(newPayment);
    return newPayment;
  }

  // Notifications
  static async getNotifications(userId: string): Promise<MockNotification[]> {
    await this.delay();
    return mockNotifications.filter(n => n.user_id === userId);
  }

  static async markNotificationAsRead(id: string): Promise<{ success: boolean }> {
    await this.delay();
    const notification = mockNotifications.find(n => n.id === id);
    if (notification) {
      notification.read = true;
    }
    return { success: true };
  }

  // Statistics
  static async getStats(): Promise<any> {
    await this.delay();
    return {
      totalProperties: mockProperties.length,
      totalUsers: mockUsers.length,
      totalApplications: mockApplications.length,
      totalPayments: mockPayments.length,
      pendingApplications: mockApplications.filter(a => a.status === 'pending').length,
      approvedApplications: mockApplications.filter(a => a.status === 'approved').length,
      rejectedApplications: mockApplications.filter(a => a.status === 'rejected').length,
      totalRevenue: mockPayments.reduce((sum, p) => sum + p.amount, 0),
    };
  }

  // Search
  static async searchProperties(query: string): Promise<MockProperty[]> {
    await this.delay();
    const lowercaseQuery = query.toLowerCase();
    return mockProperties.filter(
      p =>
        p.title.toLowerCase().includes(lowercaseQuery) ||
        p.description.toLowerCase().includes(lowercaseQuery) ||
        p.location.toLowerCase().includes(lowercaseQuery)
    );
  }

  // File upload simulation
  static async uploadFile(
    file: File,
    folder: string
  ): Promise<{ success: boolean; url?: string; fileName?: string; error?: string }> {
    await this.delay(1000);

    if (!file) {
      return {
        success: false,
        error: 'No file provided',
      };
    }

    return {
      success: true,
      url: `/uploads/${folder}/${Date.now()}_${file.name}`,
      fileName: file.name,
    };
  }

  // Authentication methods
  static async signIn(
    email: string,
    password: string
  ): Promise<{ success: boolean; user?: MockUser; token?: string; error?: string }> {
    await this.delay();

    // Mock authentication logic - add admin user if not exists
    if (email === '<EMAIL>' && password === 'Admin123!@#') {
      const adminUser: MockUser = {
        id: 'admin1',
        email: '<EMAIL>',
        full_name: 'Admin User',
        role: 'ADMIN',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      return {
        success: true,
        user: adminUser,
        token: 'mock-jwt-token-' + Date.now(),
      };
    }

    const user = mockUsers.find(u => u.email === email);

    if (!user || password !== 'password123') {
      return {
        success: false,
        error: 'Invalid email or password',
      };
    }

    return {
      success: true,
      user,
      token: 'mock-jwt-token-' + Date.now(),
    };
  }

  static async signUp(userData: {
    email: string;
    password: string;
    fullName: string;
    role: string;
  }): Promise<{ success: boolean; user?: any; error?: string }> {
    await this.delay();

    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === userData.email);
    if (existingUser) {
      return {
        success: false,
        error: 'User already exists',
      };
    }

    // Create new user for internal storage
    const newMockUser: MockUser = {
      id: Date.now().toString(),
      email: userData.email,
      full_name: userData.fullName,
      role: userData.role.toUpperCase(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    mockUsers.push(newMockUser);

    // Return user with expected format for tests
    const userForTest = {
      id: newMockUser.id,
      email: userData.email,
      fullName: userData.fullName, // Test expects fullName, not full_name
      role: userData.role.toUpperCase(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    return {
      success: true,
      user: userForTest,
    };
  }

  static async signOut(): Promise<{ success: boolean }> {
    await this.delay();
    // Mock sign out - in real implementation would clear session
    return { success: true };
  }

  static async resetPassword(email: string): Promise<{ success: boolean; error?: string }> {
    await this.delay();

    // For testing purposes, always return success for any email
    // In real implementation, would check if user exists and send reset email
    return { success: true };
  }

  static async getCurrentUser(): Promise<MockUser | null> {
    await this.delay();
    // Mock current user - in real implementation would get from session
    return mockUsers[0] || null;
  }

  // Real-time subscription simulation
  static subscribeToChanges(table: string, callback: (data: any) => void): () => void {
    // Simulate real-time updates
    const interval = setInterval(() => {
      callback({ type: 'UPDATE', table, data: {} });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }
}

export default MockDataService;
