/**
 * Simple WebSocket Service
 *
 * Basic WebSocket functionality without complex dependencies
 */

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
}

export interface NotificationData {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

export class SimpleWebSocketService {
  private static instance: SimpleWebSocketService;
  private ws: WebSocket | null = null;
  private listeners: Map<string, ((...args: any[]) => void)[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectDelay = 1000;
  private url: string;

  private constructor() {
    this.url = this.getWebSocketUrl();
  }

  static getInstance(): SimpleWebSocketService {
    if (!SimpleWebSocketService.instance) {
      SimpleWebSocketService.instance = new SimpleWebSocketService();
    }
    return SimpleWebSocketService.instance;
  }

  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = process.env.NODE_ENV === 'production' ? window.location.host : 'localhost:3001';
    return `${protocol}//${host}/ws`;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.reconnectAttempts = 0;
          this.emit('connected');
          resolve();
        };

        this.ws.onmessage = event => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('WebSocket disconnected');
          this.emit('disconnected');
          this.scheduleReconnect();
        };

        this.ws.onerror = error => {
          console.error('WebSocket error:', error);
          this.emit('error', error);
          reject(error);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.reconnectAttempts = this.maxReconnectAttempts; // Prevent reconnection
  }

  send(message: Omit<WebSocketMessage, 'timestamp'>): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      const fullMessage: WebSocketMessage = {
        ...message,
        timestamp: new Date().toISOString(),
      };
      this.ws.send(JSON.stringify(fullMessage));
    } else {
      console.warn('WebSocket not connected, message not sent:', message);
    }
  }

  on(event: string, callback: (...args: any[]) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: (...args: any[]) => void): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(callback);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => callback(data));
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    switch (message.type) {
      case 'notification':
        this.emit('notification', message.payload as NotificationData);
        break;
      case 'agent_update':
        this.emit('agentUpdate', message.payload);
        break;
      case 'application_update':
        this.emit('applicationUpdate', message.payload);
        break;
      default:
        this.emit('message', message);
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

      console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

      setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnect failed:', error);
        });
      }, delay);
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  getConnectionState(): string {
    if (!this.ws) return 'disconnected';

    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  // Convenience methods
  subscribeToNotifications(): void {
    this.send({
      type: 'subscribe',
      payload: { channel: 'notifications' },
    });
  }

  subscribeToAgentUpdates(): void {
    this.send({
      type: 'subscribe',
      payload: { channel: 'agents' },
    });
  }
}

// Export singleton instance
export const simpleWebSocket = SimpleWebSocketService.getInstance();

// Mock WebSocket for development/testing
export const createMockWebSocket = () => {
  const mockService = {
    connect: () => Promise.resolve(),
    disconnect: () => {},
    send: (message: any) => console.log('Mock WebSocket send:', message),
    on: (event: string, callback: (...args: any[]) => void) => {
      // Simulate some events for testing
      if (event === 'notification') {
        setTimeout(() => {
          callback({
            id: 'mock-1',
            type: 'info',
            title: 'Mock Notification',
            message: 'This is a mock notification for testing',
            timestamp: new Date().toISOString(),
            read: false,
          });
        }, 2000);
      }
    },
    off: () => {},
    isConnected: () => false,
    getConnectionState: () => 'disconnected',
    subscribeToNotifications: () => {},
    subscribeToAgentUpdates: () => {},
  };

  return mockService;
};
