import { MockDataService } from '@/services/mockDataService';

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  html_content: string;
  text_content: string;
  template_type: 'transactional' | 'marketing' | 'system';
  variables: Array<{
    name: string;
    type: 'text' | 'number' | 'date' | 'url';
    required: boolean;
    default_value?: string;
  }>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EmailCampaign {
  id: string;
  name: string;
  description: string;
  template_id: string;
  subject: string;
  sender_name: string;
  sender_email: string;
  target_audience: {
    segment_ids?: string[];
    filters?: {
      location?: string[];
      property_type?: string[];
      user_type?: string[];
      last_activity?: string;
      subscription_status?: string;
    };
  };
  schedule: {
    send_immediately: boolean;
    scheduled_at?: string;
    timezone?: string;
    recurring?: {
      frequency: 'daily' | 'weekly' | 'monthly';
      interval: number;
      end_date?: string;
    };
  };
  status: 'draft' | 'scheduled' | 'sending' | 'completed' | 'paused' | 'failed';
  metrics: {
    total_recipients: number;
    sent_count: number;
    delivered_count: number;
    opened_count: number;
    clicked_count: number;
    bounced_count: number;
    unsubscribed_count: number;
    spam_count: number;
  };
  created_by: string;
  created_at: string;
  sent_at?: string;
}

export interface EmailAutomation {
  id: string;
  name: string;
  description: string;
  trigger: {
    type: 'user_action' | 'time_based' | 'property_event' | 'system_event';
    conditions: any;
  };
  workflow_steps: Array<{
    id: string;
    type: 'email' | 'delay' | 'condition' | 'action';
    config: any;
    delay_duration?: number;
    next_step?: string;
  }>;
  is_active: boolean;
  created_at: string;
}

export interface EmailDelivery {
  id: string;
  campaign_id?: string;
  automation_id?: string;
  template_id: string;
  recipient_email: string;
  recipient_name?: string;
  subject: string;
  status: 'queued' | 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'spam' | 'unsubscribed';
  sent_at?: string;
  delivered_at?: string;
  opened_at?: string;
  clicked_at?: string;
  bounce_reason?: string;
  tracking_data: {
    opens: number;
    clicks: number;
    user_agent?: string;
    ip_address?: string;
  };
  created_at: string;
}

export interface EmailSubscription {
  id: string;
  email: string;
  user_id?: string;
  subscription_types: string[];
  status: 'subscribed' | 'unsubscribed' | 'bounced' | 'spam';
  preferences: {
    frequency: 'immediate' | 'daily' | 'weekly' | 'monthly';
    categories: string[];
  };
  unsubscribe_token: string;
  created_at: string;
  updated_at: string;
}

/**
 * Email Notification Service
 * Provides comprehensive email marketing and transactional email capabilities
 * including templates, campaigns, automation workflows, and delivery tracking
 */
export class EmailService {
  private static instance: EmailService;
  private readonly SENDGRID_API_KEY = import.meta.env.VITE_SENDGRID_API_KEY;
  private readonly RESEND_API_KEY = import.meta.env.VITE_RESEND_API_KEY;
  private readonly FROM_EMAIL = import.meta.env.VITE_FROM_EMAIL || '<EMAIL>';
  private readonly FROM_NAME = import.meta.env.VITE_FROM_NAME || 'PHCityRent';

  private emailQueue: Map<string, any[]> = new Map();
  private deliveryTracker: Map<string, any> = new Map();

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  // =====================================================
  // TRANSACTIONAL EMAILS
  // =====================================================

  /**
   * Send transactional email using template
   */
  async sendTransactionalEmail(
    templateName: string,
    recipientEmail: string,
    recipientName: string,
    variables: Record<string, any> = {},
    options: {
      subject_override?: string;
      sender_override?: { name: string; email: string };
      attachments?: Array<{ filename: string; content: string; type: string }>;
    } = {}
  ): Promise<EmailDelivery> {
    try {
      // Get template
      const template = await this.getTemplate(templateName);
      if (!template) {
        throw new Error(`Email template '${templateName}' not found`);
      }

      // Process template variables
      const processedSubject = this.processTemplate(
        options.subject_override || template.subject,
        variables
      );
      const processedHtml = this.processTemplate(template.html_content, variables);
      const processedText = this.processTemplate(template.text_content, variables);

      // Send email
      const delivery = await this.sendEmail({
        to: { email: recipientEmail, name: recipientName },
        from: options.sender_override || { email: this.FROM_EMAIL, name: this.FROM_NAME },
        subject: processedSubject,
        html: processedHtml,
        text: processedText,
        attachments: options.attachments,
        template_id: template.id,
        email_type: 'transactional'
      });

      return delivery;
    } catch (error) {
      console.error('Error sending transactional email:', error);
      throw new Error('Failed to send transactional email');
    }
  }

  /**
   * Send welcome email to new users
   */
  async sendWelcomeEmail(userEmail: string, userName: string, userType: 'tenant' | 'agent'): Promise<void> {
    const templateName = userType === 'agent' ? 'agent_welcome' : 'tenant_welcome';
    
    await this.sendTransactionalEmail(templateName, userEmail, userName, {
      user_name: userName,
      user_type: userType,
      login_url: `${window.location.origin}/login`,
      dashboard_url: `${window.location.origin}/dashboard`
    });
  }

  /**
   * Send property inquiry notification
   */
  async sendPropertyInquiryNotification(
    agentEmail: string,
    agentName: string,
    inquiryData: {
      property_title: string;
      inquirer_name: string;
      inquirer_email: string;
      inquirer_phone: string;
      message: string;
      property_url: string;
    }
  ): Promise<void> {
    await this.sendTransactionalEmail('property_inquiry', agentEmail, agentName, {
      agent_name: agentName,
      property_title: inquiryData.property_title,
      inquirer_name: inquiryData.inquirer_name,
      inquirer_email: inquiryData.inquirer_email,
      inquirer_phone: inquiryData.inquirer_phone,
      inquiry_message: inquiryData.message,
      property_url: inquiryData.property_url,
      respond_url: `${window.location.origin}/dashboard/inquiries`
    });
  }

  // =====================================================
  // MARKETING CAMPAIGNS
  // =====================================================

  /**
   * Create email campaign
   */
  async createCampaign(campaignData: Omit<EmailCampaign, 'id' | 'created_at' | 'metrics'>): Promise<EmailCampaign> {
    try {
      const { data, error } = await supabase
        .from('email_campaigns')
        .insert({
          ...campaignData,
          metrics: {
            total_recipients: 0,
            sent_count: 0,
            delivered_count: 0,
            opened_count: 0,
            clicked_count: 0,
            bounced_count: 0,
            unsubscribed_count: 0,
            spam_count: 0
          },
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error creating email campaign:', error);
      throw new Error('Failed to create email campaign');
    }
  }

  /**
   * Execute email campaign
   */
  async executeCampaign(campaignId: string): Promise<void> {
    try {
      // Get campaign details
      const { data: campaign, error } = await supabase
        .from('email_campaigns')
        .select('*')
        .eq('id', campaignId)
        .single();

      if (error) throw error;

      // Get target recipients
      const recipients = await this.getTargetRecipients(campaign.target_audience);

      // Update campaign status
      await supabase
        .from('email_campaigns')
        .update({ 
          status: 'sending',
          metrics: { ...campaign.metrics, total_recipients: recipients.length }
        })
        .eq('id', campaignId);

      // Send emails in batches
      const results = await this.sendBulkEmails(
        recipients,
        campaign.template_id,
        {
          subject: campaign.subject,
          sender: { name: campaign.sender_name, email: campaign.sender_email },
          campaign_id: campaignId
        }
      );

      // Update campaign metrics
      await supabase
        .from('email_campaigns')
        .update({
          status: 'completed',
          sent_at: new Date().toISOString(),
          metrics: {
            ...campaign.metrics,
            total_recipients: recipients.length,
            sent_count: results.successful.length
          }
        })
        .eq('id', campaignId);

    } catch (error) {
      console.error('Error executing email campaign:', error);
      
      // Update campaign status to failed
      await supabase
        .from('email_campaigns')
        .update({ status: 'failed' })
        .eq('id', campaignId);
      
      throw new Error('Failed to execute email campaign');
    }
  }

  // =====================================================
  // EMAIL AUTOMATION
  // =====================================================

  /**
   * Create email automation workflow
   */
  async createAutomation(automationData: Omit<EmailAutomation, 'id' | 'created_at'>): Promise<EmailAutomation> {
    try {
      const { data, error } = await supabase
        .from('email_automations')
        .insert({
          ...automationData,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error creating email automation:', error);
      throw new Error('Failed to create email automation');
    }
  }

  /**
   * Trigger automation workflow
   */
  async triggerAutomation(
    automationId: string,
    triggerData: any,
    recipientEmail: string
  ): Promise<void> {
    try {
      // Get automation workflow
      const { data: automation, error } = await supabase
        .from('email_automations')
        .select('*')
        .eq('id', automationId)
        .eq('is_active', true)
        .single();

      if (error || !automation) return;

      // Execute workflow steps
      await this.executeWorkflowSteps(automation.workflow_steps, triggerData, recipientEmail);

    } catch (error) {
      console.error('Error triggering email automation:', error);
    }
  }

  // =====================================================
  // SUBSCRIPTION MANAGEMENT
  // =====================================================

  /**
   * Subscribe user to email lists
   */
  async subscribeUser(
    email: string,
    subscriptionTypes: string[],
    preferences: EmailSubscription['preferences'],
    userId?: string
  ): Promise<EmailSubscription> {
    try {
      const unsubscribeToken = this.generateUnsubscribeToken();

      const { data, error } = await supabase
        .from('email_subscriptions')
        .insert({
          email,
          user_id: userId,
          subscription_types: subscriptionTypes,
          status: 'subscribed',
          preferences,
          unsubscribe_token: unsubscribeToken,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error subscribing user:', error);
      throw new Error('Failed to subscribe user');
    }
  }

  /**
   * Unsubscribe user from email lists
   */
  async unsubscribeUser(token: string, subscriptionTypes?: string[]): Promise<void> {
    try {
      const updateData = subscriptionTypes 
        ? { 
            subscription_types: subscriptionTypes,
            updated_at: new Date().toISOString()
          }
        : { 
            status: 'unsubscribed',
            updated_at: new Date().toISOString()
          };

      const { error } = await supabase
        .from('email_subscriptions')
        .update(updateData)
        .eq('unsubscribe_token', token);

      if (error) throw error;

    } catch (error) {
      console.error('Error unsubscribing user:', error);
      throw new Error('Failed to unsubscribe user');
    }
  }
}

// Export singleton instance
export const emailService = EmailService.getInstance();
