import { API_CONFIG } from '@/config/api';

export async function findUserByEmail(email: string, token: string) {
  const url = `${API_CONFIG.BASE_URL}/admin/users?search=${encodeURIComponent(email)}&limit=1`;
  const res = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) throw new Error('Failed to search for user');
  
  const data = await handleApiResponse(res);
  // Use the correct property for paginated result
  return data?.data?.[0] || null;
}

export async function updateUserRole(userId: string, role: 'admin' | 'super_admin', token: string) {
  const url = `${API_CONFIG.BASE_URL}/users/${userId}`;
  const res = await fetch(url, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ role }),
  });
  if (!res.ok) {
    const error = await res.json().catch(() => ({}));
    throw new Error(error.message || 'Failed to update user role');
  }
  return res.json();
}

export async function fetchUsers({ page = 1, limit = 20, search = '', token }: { page?: number; limit?: number; search?: string; token: string; }) {
  const params = new URLSearchParams();
  params.append('page', String(page));
  params.append('limit', String(limit));
  if (search) params.append('search', search);
  const url = `${API_CONFIG.BASE_URL}/admin/users?${params.toString()}`;
  const res = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) throw new Error('Failed to fetch users');

  return await handleApiResponse(res);
}

export async function deactivateUser(userId: string, token: string) {
  const url = `${API_CONFIG.BASE_URL}/users/${userId}/deactivate`;
  const res = await fetch(url, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) throw new Error('Failed to deactivate user');

  return await handleApiResponse(res);
}

export async function activateUser(userId: string, token: string) {
  const url = `${API_CONFIG.BASE_URL}/users/${userId}/activate`;
  const res = await fetch(url, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) throw new Error('Failed to activate user');
  return await handleApiResponse(res);
}

export async function deleteUser(userId: string, token: string) {
  const url = `${API_CONFIG.BASE_URL}/users/${userId}`;
  const res = await fetch(url, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) throw new Error('Failed to delete user');
  return true;
} 

async function handleApiResponse(response: Response) {
  const responseData = await response.json();
  
  if (!response.ok) {
    const errorMsg = responseData.message || `Request failed with status ${response.status}`;
    throw new Error(errorMsg);
  }
  
  return responseData.data;
}