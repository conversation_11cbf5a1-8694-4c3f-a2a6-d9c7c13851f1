import { ApiService } from '@/services/apiService';

export interface DashboardMetrics {
  overview: {
    total_properties: number;
    active_properties: number;
    total_agents: number;
    active_agents: number;
    total_inquiries: number;
    conversion_rate: number;
    revenue_this_month: number;
    revenue_growth: number;
  };
  real_time: {
    active_users: number;
    properties_viewed_today: number;
    inquiries_today: number;
    new_registrations_today: number;
    response_time_avg: number;
    system_health: 'excellent' | 'good' | 'fair' | 'poor';
  };
  trends: {
    property_views: Array<{ date: string; count: number }>;
    inquiries: Array<{ date: string; count: number }>;
    revenue: Array<{ date: string; amount: number }>;
    user_growth: Array<{ date: string; count: number }>;
  };
  performance: {
    top_performing_agents: Array<{
      agent_id: string;
      name: string;
      properties_managed: number;
      inquiries_handled: number;
      conversion_rate: number;
      revenue_generated: number;
    }>;
    top_performing_properties: Array<{
      property_id: string;
      title: string;
      views: number;
      inquiries: number;
      conversion_rate: number;
    }>;
    market_hotspots: Array<{
      location: string;
      activity_score: number;
      price_trend: 'rising' | 'falling' | 'stable';
      demand_level: 'high' | 'medium' | 'low';
    }>;
  };
}

export interface CustomDashboard {
  id: string;
  user_id: string;
  name: string;
  description: string;
  layout: {
    widgets: Array<{
      id: string;
      type: string;
      position: { x: number; y: number; w: number; h: number };
      config: any;
    }>;
  };
  filters: {
    date_range: { start: string; end: string };
    locations: string[];
    property_types: string[];
    agents: string[];
  };
  refresh_interval: number;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  metric: string;
  condition: 'greater_than' | 'less_than' | 'equals' | 'percentage_change';
  threshold: number;
  timeframe: string;
  is_active: boolean;
  notification_channels: ('email' | 'sms' | 'push' | 'slack')[];
  recipients: string[];
  last_triggered: string | null;
  created_at: string;
}

export interface NotificationAlert {
  id: string;
  alert_rule_id: string;
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metric_value: number;
  threshold_value: number;
  triggered_at: string;
  acknowledged: boolean;
  acknowledged_by: string | null;
  acknowledged_at: string | null;
}

/**
 * Real-Time Dashboard Service
 * Provides comprehensive analytics, metrics calculation, and dashboard management
 */
export class DashboardService {
  private static instance: DashboardService;
  private metricsCache: Map<string, any> = new Map();
  private alertRules: Map<string, AlertRule> = new Map();
  private cacheExpiry: number = 5 * 60 * 1000; // 5 minutes
  private realTimeInterval: NodeJS.Timeout | null = null;

  public static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService();
    }
    return DashboardService.instance;
  }

  // =====================================================
  // REAL-TIME METRICS CALCULATION
  // =====================================================

  /**
   * Get comprehensive dashboard metrics with real-time updates
   */
  async getDashboardMetrics(
    timeRange: { start: string; end: string },
    filters?: {
      locations?: string[];
      property_types?: string[];
      agents?: string[];
    }
  ): Promise<DashboardMetrics> {
    try {
      const cacheKey = `dashboard-metrics-${JSON.stringify({ timeRange, filters })}`;
      const cached = this.metricsCache.get(cacheKey);

      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      }

      // Calculate metrics in parallel for performance
      const [overview, realTime, trends, performance] = await Promise.all([
        this.calculateOverviewMetrics(timeRange, filters),
        this.calculateRealTimeMetrics(),
        this.calculateTrendMetrics(timeRange, filters),
        this.calculatePerformanceMetrics(timeRange, filters),
      ]);

      const metrics: DashboardMetrics = {
        overview,
        real_time: realTime,
        trends,
        performance,
      };

      // Cache the result
      this.metricsCache.set(cacheKey, {
        data: metrics,
        timestamp: Date.now(),
      });

      return metrics;
    } catch (error) {
      console.error('Error getting dashboard metrics:', error);
      throw new Error('Failed to fetch dashboard metrics');
    }
  }

  /**
   * Calculate overview metrics
   */
  private async calculateOverviewMetrics(
    timeRange: { start: string; end: string },
    filters?: any
  ): Promise<DashboardMetrics['overview']> {
    try {
      // Get property metrics
      const { data: propertyMetrics, error: propError } = await supabase.rpc(
        'get_property_overview',
        {
          start_date: timeRange.start,
          end_date: timeRange.end,
          location_filters: filters?.locations || null,
          property_type_filters: filters?.property_types || null,
        }
      );

      if (propError) throw propError;

      // Get agent metrics
      const { data: agentMetrics, error: agentError } = await supabase.rpc('get_agent_overview', {
        start_date: timeRange.start,
        end_date: timeRange.end,
        agent_filters: filters?.agents || null,
      });

      if (agentError) throw agentError;

      // Get financial metrics
      const { data: financialMetrics, error: finError } = await supabase.rpc(
        'get_financial_overview',
        {
          start_date: timeRange.start,
          end_date: timeRange.end,
        }
      );

      if (finError) throw finError;

      return {
        total_properties: propertyMetrics?.total_properties || 0,
        active_properties: propertyMetrics?.active_properties || 0,
        total_agents: agentMetrics?.total_agents || 0,
        active_agents: agentMetrics?.active_agents || 0,
        total_inquiries: propertyMetrics?.total_inquiries || 0,
        conversion_rate: propertyMetrics?.conversion_rate || 0,
        revenue_this_month: financialMetrics?.revenue_this_month || 0,
        revenue_growth: financialMetrics?.revenue_growth || 0,
      };
    } catch (error) {
      console.error('Error calculating overview metrics:', error);
      return {
        total_properties: 0,
        active_properties: 0,
        total_agents: 0,
        active_agents: 0,
        total_inquiries: 0,
        conversion_rate: 0,
        revenue_this_month: 0,
        revenue_growth: 0,
      };
    }
  }

  /**
   * Calculate real-time metrics
   */
  private async calculateRealTimeMetrics(): Promise<DashboardMetrics['real_time']> {
    try {
      const { data: realTimeData, error } = await supabase.rpc('get_realtime_metrics');

      if (error) throw error;

      return {
        active_users: realTimeData?.active_users || 0,
        properties_viewed_today: realTimeData?.properties_viewed_today || 0,
        inquiries_today: realTimeData?.inquiries_today || 0,
        new_registrations_today: realTimeData?.new_registrations_today || 0,
        response_time_avg: realTimeData?.response_time_avg || 0,
        system_health: this.calculateSystemHealth(realTimeData),
      };
    } catch (error) {
      console.error('Error calculating real-time metrics:', error);
      return {
        active_users: 0,
        properties_viewed_today: 0,
        inquiries_today: 0,
        new_registrations_today: 0,
        response_time_avg: 0,
        system_health: 'poor',
      };
    }
  }

  /**
   * Calculate trend metrics
   */
  private async calculateTrendMetrics(
    timeRange: { start: string; end: string },
    filters?: any
  ): Promise<DashboardMetrics['trends']> {
    try {
      const { data: trendData, error } = await supabase.rpc('get_trend_metrics', {
        start_date: timeRange.start,
        end_date: timeRange.end,
        filters: filters || {},
      });

      if (error) throw error;

      return {
        property_views: trendData?.property_views || [],
        inquiries: trendData?.inquiries || [],
        revenue: trendData?.revenue || [],
        user_growth: trendData?.user_growth || [],
      };
    } catch (error) {
      console.error('Error calculating trend metrics:', error);
      return {
        property_views: [],
        inquiries: [],
        revenue: [],
        user_growth: [],
      };
    }
  }

  /**
   * Calculate performance metrics
   */
  private async calculatePerformanceMetrics(
    timeRange: { start: string; end: string },
    filters?: any
  ): Promise<DashboardMetrics['performance']> {
    try {
      const [topAgents, topProperties, marketHotspots] = await Promise.all([
        this.getTopPerformingAgents(timeRange, filters),
        this.getTopPerformingProperties(timeRange, filters),
        this.getMarketHotspots(timeRange, filters),
      ]);

      return {
        top_performing_agents: topAgents,
        top_performing_properties: topProperties,
        market_hotspots: marketHotspots,
      };
    } catch (error) {
      console.error('Error calculating performance metrics:', error);
      return {
        top_performing_agents: [],
        top_performing_properties: [],
        market_hotspots: [],
      };
    }
  }

  // =====================================================
  // DASHBOARD CUSTOMIZATION
  // =====================================================

  /**
   * Create custom dashboard
   */
  async createCustomDashboard(
    dashboardData: Omit<CustomDashboard, 'id' | 'created_at' | 'updated_at'>
  ): Promise<CustomDashboard> {
    try {
      const { data, error } = await supabase
        .from('custom_dashboards')
        .insert({
          ...dashboardData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error creating custom dashboard:', error);
      throw new Error('Failed to create custom dashboard');
    }
  }

  /**
   * Update custom dashboard
   */
  async updateCustomDashboard(
    dashboardId: string,
    updates: Partial<CustomDashboard>
  ): Promise<CustomDashboard> {
    try {
      const { data, error } = await supabase
        .from('custom_dashboards')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', dashboardId)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error updating custom dashboard:', error);
      throw new Error('Failed to update custom dashboard');
    }
  }

  /**
   * Get user's custom dashboards
   */
  async getUserDashboards(userId: string): Promise<CustomDashboard[]> {
    try {
      const { data, error } = await supabase
        .from('custom_dashboards')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting user dashboards:', error);
      return [];
    }
  }

  // =====================================================
  // ALERT AND NOTIFICATION SYSTEM
  // =====================================================

  /**
   * Create alert rule
   */
  async createAlertRule(
    alertData: Omit<AlertRule, 'id' | 'created_at' | 'last_triggered'>
  ): Promise<AlertRule> {
    try {
      const { data, error } = await supabase
        .from('alert_rules')
        .insert({
          ...alertData,
          created_at: new Date().toISOString(),
          last_triggered: null,
        })
        .select()
        .single();

      if (error) throw error;

      // Cache the alert rule
      this.alertRules.set(data.id, data);

      return data;
    } catch (error) {
      console.error('Error creating alert rule:', error);
      throw new Error('Failed to create alert rule');
    }
  }

  /**
   * Check alert rules and trigger notifications
   */
  async checkAlertRules(): Promise<NotificationAlert[]> {
    try {
      const { data: rules, error } = await supabase
        .from('alert_rules')
        .select('*')
        .eq('is_active', true);

      if (error) throw error;

      const triggeredAlerts: NotificationAlert[] = [];

      for (const rule of rules) {
        const shouldTrigger = await this.evaluateAlertRule(rule);

        if (shouldTrigger) {
          const alert = await this.triggerAlert(rule, shouldTrigger.value);
          triggeredAlerts.push(alert);
        }
      }

      return triggeredAlerts;
    } catch (error) {
      console.error('Error checking alert rules:', error);
      return [];
    }
  }

  /**
   * Evaluate if alert rule should trigger
   */
  private async evaluateAlertRule(rule: AlertRule): Promise<{ value: number } | null> {
    try {
      const { data: metricValue, error } = await supabase.rpc('get_metric_value', {
        metric_name: rule.metric,
        timeframe: rule.timeframe,
      });

      if (error) throw error;

      const value = metricValue?.value || 0;
      let shouldTrigger = false;

      switch (rule.condition) {
        case 'greater_than':
          shouldTrigger = value > rule.threshold;
          break;
        case 'less_than':
          shouldTrigger = value < rule.threshold;
          break;
        case 'equals':
          shouldTrigger = Math.abs(value - rule.threshold) < 0.01;
          break;
        case 'percentage_change':
          const previousValue = metricValue?.previous_value || 0;
          const percentageChange =
            previousValue > 0 ? ((value - previousValue) / previousValue) * 100 : 0;
          shouldTrigger = Math.abs(percentageChange) >= rule.threshold;
          break;
      }

      return shouldTrigger ? { value } : null;
    } catch (error) {
      console.error('Error evaluating alert rule:', error);
      return null;
    }
  }

  /**
   * Trigger alert notification
   */
  private async triggerAlert(rule: AlertRule, metricValue: number): Promise<NotificationAlert> {
    try {
      const alert: Omit<NotificationAlert, 'id'> = {
        alert_rule_id: rule.id,
        title: `Alert: ${rule.name}`,
        message: this.generateAlertMessage(rule, metricValue),
        severity: this.calculateAlertSeverity(rule, metricValue),
        metric_value: metricValue,
        threshold_value: rule.threshold,
        triggered_at: new Date().toISOString(),
        acknowledged: false,
        acknowledged_by: null,
        acknowledged_at: null,
      };

      const { data, error } = await supabase
        .from('notification_alerts')
        .insert(alert)
        .select()
        .single();

      if (error) throw error;

      // Send notifications through configured channels
      await this.sendNotifications(rule, data);

      // Update last triggered time
      await supabase
        .from('alert_rules')
        .update({ last_triggered: new Date().toISOString() })
        .eq('id', rule.id);

      return data;
    } catch (error) {
      console.error('Error triggering alert:', error);
      throw new Error('Failed to trigger alert');
    }
  }

  /**
   * Send notifications through configured channels
   */
  private async sendNotifications(rule: AlertRule, alert: NotificationAlert): Promise<void> {
    try {
      for (const channel of rule.notification_channels) {
        switch (channel) {
          case 'email':
            await this.sendEmailNotification(rule, alert);
            break;
          case 'sms':
            await this.sendSMSNotification(rule, alert);
            break;
          case 'push':
            await this.sendPushNotification(rule, alert);
            break;
          case 'slack':
            await this.sendSlackNotification(rule, alert);
            break;
        }
      }
    } catch (error) {
      console.error('Error sending notifications:', error);
    }
  }

  /**
   * Start real-time monitoring
   */
  startRealTimeMonitoring(intervalMs: number = 60000): void {
    if (this.realTimeInterval) {
      clearInterval(this.realTimeInterval);
    }

    this.realTimeInterval = setInterval(async () => {
      try {
        await this.checkAlertRules();
        // Clear expired cache entries
        this.clearExpiredCache();
      } catch (error) {
        console.error('Error in real-time monitoring:', error);
      }
    }, intervalMs);
  }

  /**
   * Stop real-time monitoring
   */
  stopRealTimeMonitoring(): void {
    if (this.realTimeInterval) {
      clearInterval(this.realTimeInterval);
      this.realTimeInterval = null;
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  private calculateSystemHealth(data: any): 'excellent' | 'good' | 'fair' | 'poor' {
    const responseTime = data?.response_time_avg || 1000;
    const errorRate = data?.error_rate || 0;

    if (responseTime < 200 && errorRate < 0.01) return 'excellent';
    if (responseTime < 500 && errorRate < 0.05) return 'good';
    if (responseTime < 1000 && errorRate < 0.1) return 'fair';
    return 'poor';
  }

  private generateAlertMessage(rule: AlertRule, value: number): string {
    return `${rule.description} Current value: ${value}, Threshold: ${rule.threshold}`;
  }

  private calculateAlertSeverity(
    rule: AlertRule,
    value: number
  ): 'low' | 'medium' | 'high' | 'critical' {
    const deviation = Math.abs(value - rule.threshold) / rule.threshold;

    if (deviation > 0.5) return 'critical';
    if (deviation > 0.3) return 'high';
    if (deviation > 0.1) return 'medium';
    return 'low';
  }

  private clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, value] of this.metricsCache.entries()) {
      if (now - value.timestamp > this.cacheExpiry) {
        this.metricsCache.delete(key);
      }
    }
  }

  private async sendEmailNotification(rule: AlertRule, alert: NotificationAlert): Promise<void> {
    // Implementation for email notifications
    console.log('Sending email notification:', alert.title);
  }

  private async sendSMSNotification(rule: AlertRule, alert: NotificationAlert): Promise<void> {
    // Implementation for SMS notifications
    console.log('Sending SMS notification:', alert.title);
  }

  private async sendPushNotification(rule: AlertRule, alert: NotificationAlert): Promise<void> {
    // Implementation for push notifications
    console.log('Sending push notification:', alert.title);
  }

  private async sendSlackNotification(rule: AlertRule, alert: NotificationAlert): Promise<void> {
    // Implementation for Slack notifications
    console.log('Sending Slack notification:', alert.title);
  }

  private async getTopPerformingAgents(timeRange: any, filters: any): Promise<any[]> {
    // Implementation for top performing agents
    return [];
  }

  private async getTopPerformingProperties(timeRange: any, filters: any): Promise<any[]> {
    // Implementation for top performing properties
    return [];
  }

  private async getMarketHotspots(timeRange: any, filters: any): Promise<any[]> {
    // Implementation for market hotspots
    return [];
  }
}

// Export singleton instance
export const dashboardService = DashboardService.getInstance();
