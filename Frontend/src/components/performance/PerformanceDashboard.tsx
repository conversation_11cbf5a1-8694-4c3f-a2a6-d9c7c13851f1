// =====================================================
// PERFORMANCE DASHBOARD COMPONENT
// Real-time performance monitoring and optimization dashboard
// =====================================================

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Activity, 
  Zap, 
  Image, 
  Database, 
  Package, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp, 
  TrendingDown,
  Clock,
  Eye,
  Gauge,
  BarChart3,
  Settings,
  RefreshCw,
  Download,
  Target,
  Layers,
  Monitor
} from 'lucide-react';
import {
  usePerformanceMonitoring,
  useImageOptimization,
  useCaching,
  useBundleOptimization,
  useWebVitals,
  usePerformanceRecommendations
} from '@/hooks/usePerformanceOptimization';
import Navbar from '@/components/navigation/Navbar';

interface PerformanceDashboardProps {
  userId?: string;
}

const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({ userId }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  // Performance hooks
  const { metrics, alerts, acknowledgeAlert, clearAlerts } = usePerformanceMonitoring();
  const { stats: imageStats, getOptimizationStats } = useImageOptimization();
  const { cacheStats } = useCaching();
  const { analysis: bundleAnalysis, budgetStatus, analyzeBundleComposition } = useBundleOptimization();
  const { vitals, scores } = useWebVitals();
  const { recommendations, refreshRecommendations } = usePerformanceRecommendations();

  // Refresh all data
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        getOptimizationStats(),
        analyzeBundleComposition(),
        refreshRecommendations()
      ]);
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Get performance score color
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Get metric status
  const getMetricStatus = (value: number, good: number, poor: number) => {
    if (value <= good) return { status: 'good', color: 'bg-green-500' };
    if (value <= poor) return { status: 'needs-improvement', color: 'bg-yellow-500' };
    return { status: 'poor', color: 'bg-red-500' };
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <Navbar />
      
      {/* Hero Section */}
      <div className="relative overflow-hidden pt-16">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-indigo-600/5"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl"></div>
        <div className="absolute top-20 right-1/4 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl"></div>
        
        <div className="relative container mx-auto px-6 pt-8 pb-12">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
            <div className="mb-6 lg:mb-0">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                  <Gauge className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 bg-clip-text text-transparent">
                    Performance Dashboard
                  </h1>
                  <p className="text-lg text-gray-600 mt-1">
                    Real-time monitoring and optimization insights
                  </p>
                </div>
              </div>
              
              {/* Overall Performance Score */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 px-4 py-2 bg-white/80 border border-gray-200 rounded-full">
                  <div className={`w-3 h-3 rounded-full ${scores.overall >= 90 ? 'bg-green-500' : scores.overall >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`}></div>
                  <span className="text-sm font-medium text-gray-700">
                    Performance Score: <span className={getScoreColor(scores.overall)}>{scores.overall}/100</span>
                  </span>
                </div>
                
                {alerts.filter(a => !a.acknowledged).length > 0 && (
                  <div className="flex items-center space-x-2 px-3 py-1.5 bg-red-50 border border-red-200 rounded-full">
                    <AlertTriangle className="h-3 w-3 text-red-600" />
                    <span className="text-sm font-medium text-red-700">
                      {alerts.filter(a => !a.acknowledged).length} Active Alerts
                    </span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex flex-wrap items-center gap-3">
              <Button 
                variant="outline" 
                size="lg"
                onClick={handleRefresh}
                disabled={refreshing}
                className="border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-300"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh Data
              </Button>
              
              <Button 
                variant="outline" 
                size="lg"
                className="border-2 border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all duration-300"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
              
              <Button 
                variant="outline" 
                size="lg"
                className="border-2 border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-300"
              >
                <Settings className="h-4 w-4 mr-2" />
                Configure
              </Button>
            </div>
          </div>

          {/* Core Web Vitals Cards */}
          <div className="grid gap-6 md:grid-cols-3 mb-8">
            {/* LCP Card */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-green-500/10 hover:shadow-green-500/20 transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl shadow-lg">
                    <Eye className="h-6 w-6 text-white" />
                  </div>
                  <div className={`w-3 h-3 rounded-full ${getMetricStatus(vitals.lcp || 0, 2500, 4000).color}`}></div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">Largest Contentful Paint</h3>
                  <p className="text-3xl font-bold text-gray-900">{vitals.lcp ? `${Math.round(vitals.lcp)}ms` : '--'}</p>
                  <div className="flex items-center space-x-2">
                    <Progress value={scores.lcp} className="flex-1" />
                    <span className={`text-sm font-medium ${getScoreColor(scores.lcp)}`}>{scores.lcp}/100</span>
                  </div>
                </div>
              </div>
            </div>

            {/* FID Card */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-blue-500/10 hover:shadow-blue-500/20 transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <div className={`w-3 h-3 rounded-full ${getMetricStatus(vitals.fid || 0, 100, 300).color}`}></div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">First Input Delay</h3>
                  <p className="text-3xl font-bold text-gray-900">{vitals.fid ? `${Math.round(vitals.fid)}ms` : '--'}</p>
                  <div className="flex items-center space-x-2">
                    <Progress value={scores.fid} className="flex-1" />
                    <span className={`text-sm font-medium ${getScoreColor(scores.fid)}`}>{scores.fid}/100</span>
                  </div>
                </div>
              </div>
            </div>

            {/* CLS Card */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-purple-500/10 hover:shadow-purple-500/20 transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl shadow-lg">
                    <Layers className="h-6 w-6 text-white" />
                  </div>
                  <div className={`w-3 h-3 rounded-full ${getMetricStatus(vitals.cls || 0, 0.1, 0.25).color}`}></div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">Cumulative Layout Shift</h3>
                  <p className="text-3xl font-bold text-gray-900">{vitals.cls ? vitals.cls.toFixed(3) : '--'}</p>
                  <div className="flex items-center space-x-2">
                    <Progress value={scores.cls} className="flex-1" />
                    <span className={`text-sm font-medium ${getScoreColor(scores.cls)}`}>{scores.cls}/100</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Tabs */}
      <div className="container mx-auto px-6 -mt-4 relative z-10">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
          <TabsList className="grid w-full grid-cols-5 bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl p-2 shadow-lg">
            <TabsTrigger value="overview" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-600 data-[state=active]:text-white">
              <Monitor className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="images" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white">
              <Image className="h-4 w-4 mr-2" />
              Images
            </TabsTrigger>
            <TabsTrigger value="caching" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-600 data-[state=active]:text-white">
              <Database className="h-4 w-4 mr-2" />
              Caching
            </TabsTrigger>
            <TabsTrigger value="bundle" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-600 data-[state=active]:text-white">
              <Package className="h-4 w-4 mr-2" />
              Bundle
            </TabsTrigger>
            <TabsTrigger value="recommendations" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-teal-500 data-[state=active]:to-cyan-600 data-[state=active]:text-white">
              <Target className="h-4 w-4 mr-2" />
              Recommendations
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Active Alerts */}
            {alerts.filter(a => !a.acknowledged).length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Active Performance Alerts</h3>
                {alerts.filter(a => !a.acknowledged).map(alert => (
                  <Alert key={alert.id} className={`border-l-4 ${
                    alert.severity === 'critical' ? 'border-red-500 bg-red-50' :
                    alert.severity === 'high' ? 'border-orange-500 bg-orange-50' :
                    'border-yellow-500 bg-yellow-50'
                  }`}>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle className="flex items-center justify-between">
                      {alert.message}
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => acknowledgeAlert(alert.id)}
                      >
                        Acknowledge
                      </Button>
                    </AlertTitle>
                    <AlertDescription>
                      {alert.metric}: {alert.value} (threshold: {alert.threshold})
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            )}

            {/* Performance Metrics Grid */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {/* Cache Hit Rate */}
              <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Cache Hit Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">{cacheStats.hitRate.toFixed(1)}%</div>
                  <div className="flex items-center space-x-2 mt-2">
                    <Progress value={cacheStats.hitRate} className="flex-1" />
                    <span className="text-xs text-gray-500">{cacheStats.hits}/{cacheStats.hits + cacheStats.misses}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Bundle Size */}
              <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Bundle Size</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {budgetStatus ? `${Math.round(budgetStatus.currentSize / 1024)}KB` : '--'}
                  </div>
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge variant={budgetStatus?.budgetStatus === 'within' ? 'default' : 'destructive'}>
                      {budgetStatus?.budgetStatus || 'Unknown'}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Image Optimization */}
              <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Images Optimized</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">{imageStats?.totalImages || 0}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {imageStats?.averageCompression.toFixed(1) || 0}% avg compression
                  </div>
                </CardContent>
              </Card>

              {/* Response Time */}
              <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Avg Response Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {cacheStats.averageResponseTime.toFixed(0)}ms
                  </div>
                  <div className="flex items-center space-x-1 mt-1">
                    <TrendingDown className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600">Improving</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Other tabs would be implemented similarly */}
          <TabsContent value="images">
            <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle>Image Optimization</CardTitle>
                <CardDescription>Monitor and optimize image delivery performance</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Image optimization content would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="caching">
            <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle>Caching Performance</CardTitle>
                <CardDescription>Multi-level cache statistics and optimization</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Caching performance content would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="bundle">
            <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle>Bundle Analysis</CardTitle>
                <CardDescription>Code splitting and bundle size optimization</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Bundle analysis content would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="recommendations">
            <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle>Performance Recommendations</CardTitle>
                <CardDescription>AI-powered optimization suggestions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recommendations.map((rec, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'default' : 'secondary'}>
                          {rec.priority} priority
                        </Badge>
                        <span className="text-sm text-gray-500">
                          Est. savings: {Math.round(rec.estimatedSavings / 1024)}KB
                        </span>
                      </div>
                      <h4 className="font-medium text-gray-900 mb-1">{rec.description}</h4>
                      <p className="text-sm text-gray-600">{rec.implementation}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default PerformanceDashboard;
