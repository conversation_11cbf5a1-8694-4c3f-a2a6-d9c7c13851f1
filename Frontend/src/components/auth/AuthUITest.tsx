import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  Lock, 
  Mail, 
  Key, 
  CheckCircle, 
  AlertCircle,
  Eye,
  Settings
} from 'lucide-react';
import ChangePasswordModal from './ChangePasswordModal';
import UserMenuWithPassword from './UserMenuWithPassword';
import { useEnhancedAuth } from '@/hooks/useEnhancedAuth';
import { toast } from 'sonner';

const AuthUITest: React.FC = () => {
  const { user, signOut } = useEnhancedAuth();
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  const runTest = (testName: string, testFunction: () => boolean) => {
    try {
      const result = testFunction();
      setTestResults(prev => ({ ...prev, [testName]: result }));
      if (result) {
        toast.success(`✅ ${testName} passed`);
      } else {
        toast.error(`❌ ${testName} failed`);
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, [testName]: false }));
      toast.error(`❌ ${testName} failed: ${error}`);
    }
  };

  const testSignOutButton = () => {
    // Check if sign out button is visible in UserMenuWithPassword
    const userMenu = document.querySelector('[data-testid="user-menu"]');
    return userMenu !== null;
  };

  const testChangePasswordModal = () => {
    // Check if change password modal can be opened
    const changePasswordTrigger = document.querySelector('[data-testid="change-password-trigger"]');
    return changePasswordTrigger !== null;
  };

  const testPasswordResetRoute = () => {
    // Check if password reset route is accessible
    return window.location.pathname === '/auth/reset-password' || 
           document.querySelector('[href="/auth/reset-password"]') !== null;
  };

  const testEmailVerificationRoute = () => {
    // Check if email verification route is accessible
    return window.location.pathname === '/auth/verify-email' || 
           document.querySelector('[href="/auth/verify-email"]') !== null;
  };

  const TestCard = ({ 
    title, 
    description, 
    icon: Icon, 
    testName, 
    testFunction,
    demoComponent 
  }: {
    title: string;
    description: string;
    icon: React.ElementType;
    testName: string;
    testFunction: () => boolean;
    demoComponent?: React.ReactNode;
  }) => {
    const testResult = testResults[testName];
    
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon className="h-5 w-5" />
            {title}
            {testResult !== undefined && (
              <Badge variant={testResult ? "default" : "destructive"}>
                {testResult ? "PASS" : "FAIL"}
              </Badge>
            )}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {demoComponent && (
            <div className="p-4 border rounded-lg bg-gray-50">
              <p className="text-sm font-medium mb-2">Demo:</p>
              {demoComponent}
            </div>
          )}
          
          <Button 
            onClick={() => runTest(testName, testFunction)}
            variant="outline"
            className="w-full"
          >
            Run Test
          </Button>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Authentication UI Test Suite</h1>
        <p className="text-gray-600">
          Test all authentication UI components and flows
        </p>
        {user && (
          <Badge variant="outline" className="mt-2">
            Logged in as: {user.email}
          </Badge>
        )}
      </div>

      <Separator />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Sign Out Button Test */}
        <TestCard
          title="Sign Out Button"
          description="Tests if the sign out button appears in the UI and is functional"
          icon={User}
          testName="signOutButton"
          testFunction={testSignOutButton}
          demoComponent={
            user ? (
              <div className="flex items-center justify-center">
                <UserMenuWithPassword />
              </div>
            ) : (
              <p className="text-sm text-gray-500">Please log in to test sign out button</p>
            )
          }
        />

        {/* Change Password Modal Test */}
        <TestCard
          title="Change Password Modal"
          description="Tests if the change password modal opens and functions correctly"
          icon={Lock}
          testName="changePasswordModal"
          testFunction={testChangePasswordModal}
          demoComponent={
            <div className="flex items-center justify-center">
              <ChangePasswordModal>
                <Button variant="outline" data-testid="change-password-trigger">
                  <Lock className="h-4 w-4 mr-2" />
                  Change Password
                </Button>
              </ChangePasswordModal>
            </div>
          }
        />

        {/* Password Reset UI Test */}
        <TestCard
          title="Password Reset UI"
          description="Tests if the password reset form is accessible and functional"
          icon={Key}
          testName="passwordResetUI"
          testFunction={testPasswordResetRoute}
          demoComponent={
            <div className="flex items-center justify-center">
              <Button 
                variant="outline"
                onClick={() => window.open('/auth/reset-password', '_blank')}
              >
                <Key className="h-4 w-4 mr-2" />
                Open Password Reset
              </Button>
            </div>
          }
        />

        {/* Email Verification UI Test */}
        <TestCard
          title="Email Verification UI"
          description="Tests if the email verification form is accessible and functional"
          icon={Mail}
          testName="emailVerificationUI"
          testFunction={testEmailVerificationRoute}
          demoComponent={
            <div className="flex items-center justify-center">
              <Button 
                variant="outline"
                onClick={() => window.open('/auth/verify-email', '_blank')}
              >
                <Mail className="h-4 w-4 mr-2" />
                Open Email Verification
              </Button>
            </div>
          }
        />
      </div>

      {/* Test Results Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Test Results Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(testResults).map(([testName, result]) => (
              <div key={testName} className="flex items-center gap-2">
                {result ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm">{testName}</span>
              </div>
            ))}
          </div>
          
          {Object.keys(testResults).length === 0 && (
            <p className="text-gray-500 text-center">No tests run yet</p>
          )}
        </CardContent>
      </Card>

      {/* Manual Testing Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Manual Testing Instructions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-semibold">1. Sign Out Button Test:</h4>
            <ul className="list-disc list-inside text-sm space-y-1 ml-4">
              <li>Log in to the application</li>
              <li>Click on your user avatar/menu in the top navigation</li>
              <li>Verify the "Sign Out" button is visible at the bottom of the dropdown</li>
              <li>Click "Sign Out" and verify you are logged out</li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold">2. Change Password Modal Test:</h4>
            <ul className="list-disc list-inside text-sm space-y-1 ml-4">
              <li>Log in to the application</li>
              <li>Open the user menu and click "Change Password"</li>
              <li>Verify the modal opens with current password, new password, and confirm password fields</li>
              <li>Test password strength indicator</li>
              <li>Test form validation</li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold">3. Password Reset UI Test:</h4>
            <ul className="list-disc list-inside text-sm space-y-1 ml-4">
              <li>Navigate to /auth/reset-password</li>
              <li>Verify the form has token input and new password fields</li>
              <li>Test password strength indicator</li>
              <li>Test form validation</li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold">4. Email Verification UI Test:</h4>
            <ul className="list-disc list-inside text-sm space-y-1 ml-4">
              <li>Navigate to /auth/verify-email</li>
              <li>Verify the form has 6-digit code input</li>
              <li>Test the resend functionality</li>
              <li>Test form validation</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthUITest;
