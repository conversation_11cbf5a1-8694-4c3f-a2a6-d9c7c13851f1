import React from 'react';
import { Link } from 'react-router-dom';
import { useEnhancedAuth } from '@/hooks/auth/useEnhancedAuth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  User,
  LogOut,
  Settings,
  Shield,
  Home,
  Building2,
  UserCheck,
  ChevronDown,
  Crown,
  Briefcase,
  CreditCard,
  BarChart3,
  MessageSquare,
  Gauge,
  Lock
} from 'lucide-react';
import ChangePasswordModal from './ChangePasswordModal';

const UserMenuWithPassword = () => {
  const { user, signOut, isAdmin } = useEnhancedAuth();

  if (!user) return null;

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'landlord':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'tenant':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'agent':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return <Crown className="h-3 w-3" />;
      case 'landlord':
        return <Building2 className="h-3 w-3" />;
      case 'tenant':
        return <Home className="h-3 w-3" />;
      case 'agent':
        return <Briefcase className="h-3 w-3" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  const getQuickActions = (role: string) => {
    const baseActions = [
      { icon: User, label: 'Profile', href: '/profile' },
      { icon: Settings, label: 'Settings', href: '/settings' },
    ];

    switch (role.toLowerCase()) {
      case 'admin':
        return [
          ...baseActions,
          { icon: Shield, label: 'Admin Panel', href: '/admin' },
          { icon: BarChart3, label: 'Analytics', href: '/admin/analytics' },
          { icon: UserCheck, label: 'User Management', href: '/admin/users' },
        ];
      case 'landlord':
        return [
          ...baseActions,
          { icon: Building2, label: 'My Properties', href: '/landlord/properties' },
          { icon: CreditCard, label: 'Payments', href: '/landlord/payments' },
          { icon: BarChart3, label: 'Analytics', href: '/landlord/analytics' },
        ];
      case 'tenant':
        return [
          ...baseActions,
          { icon: Home, label: 'My Rentals', href: '/tenant/rentals' },
          { icon: CreditCard, label: 'Payments', href: '/tenant/payments' },
          { icon: MessageSquare, label: 'Messages', href: '/tenant/messages' },
        ];
      case 'agent':
        return [
          ...baseActions,
          { icon: Building2, label: 'Listings', href: '/agent/listings' },
          { icon: UserCheck, label: 'Clients', href: '/agent/clients' },
          { icon: BarChart3, label: 'Performance', href: '/agent/performance' },
        ];
      default:
        return baseActions;
    }
  };

  const quickActions = getQuickActions(user.role);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
        <Avatar className="h-8 w-8 ring-2 ring-white shadow-sm">
          <AvatarImage src={user.avatar} alt={user.name} />
          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-sm font-semibold">
            {getInitials(user.name)}
          </AvatarFallback>
        </Avatar>
        
        <div className="hidden md:block text-left">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium text-gray-900 truncate max-w-32">
              {user.name}
            </p>
            <Badge 
              variant="outline" 
              className={`text-xs px-2 py-0.5 ${getRoleColor(user.role)} flex items-center gap-1`}
            >
              {getRoleIcon(user.role)}
              {user.role}
            </Badge>
          </div>
          <p className="text-xs text-gray-500 truncate max-w-40">
            {user.email}
          </p>
        </div>
        
        <ChevronDown className="h-4 w-4 text-gray-400" />
      </DropdownMenuTrigger>

      <DropdownMenuContent 
        className="w-80 p-2" 
        align="end" 
        sideOffset={8}
      >
        {/* User Info Header */}
        <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg mb-2">
          <Avatar className="h-12 w-12 ring-2 ring-white shadow-sm">
            <AvatarImage src={user.avatar} alt={user.name} />
            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
              {getInitials(user.name)}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <p className="font-semibold text-gray-900 truncate">
                {user.name}
              </p>
              <Badge 
                variant="outline" 
                className={`text-xs px-2 py-0.5 ${getRoleColor(user.role)} flex items-center gap-1`}
              >
                {getRoleIcon(user.role)}
                {user.role}
              </Badge>
            </div>
            <p className="text-sm text-gray-600 truncate">
              {user.email}
            </p>
            {user.phone && (
              <p className="text-xs text-gray-500 truncate">
                {user.phone}
              </p>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        {isAdmin && (
          <>
            <div className="py-2">
              <DropdownMenuLabel className="text-xs text-gray-500 uppercase tracking-wider font-semibold px-2 py-1">
                Admin
              </DropdownMenuLabel>
              
              <Link to="/admin">
                <DropdownMenuItem className="cursor-pointer rounded-lg mx-1 px-3 py-2 hover:bg-gray-50 transition-colors">
                  <Shield className="mr-3 h-4 w-4" />
                  <span>Admin Panel</span>
                </DropdownMenuItem>
              </Link>

              <Link to="/admin/analytics">
                <DropdownMenuItem className="cursor-pointer rounded-lg mx-1 px-3 py-2 hover:bg-gray-50 transition-colors">
                  <BarChart3 className="mr-3 h-4 w-4" />
                  <span>Analytics</span>
                </DropdownMenuItem>
              </Link>
            </div>

            <DropdownMenuSeparator />
          </>
        )}

        {/* Role-specific Actions */}
        <div className="py-2">
          <DropdownMenuLabel className="text-xs text-gray-500 uppercase tracking-wider font-semibold px-2 py-1">
            Quick Actions
          </DropdownMenuLabel>
          
          {quickActions.slice(2).map((action, index) => (
            <Link key={index} to={action.href}>
              <DropdownMenuItem className="cursor-pointer rounded-lg mx-1 px-3 py-2 hover:bg-gray-50 transition-colors">
                <action.icon className="mr-3 h-4 w-4" />
                <span>{action.label}</span>
              </DropdownMenuItem>
            </Link>
          ))}
        </div>

        <DropdownMenuSeparator />

        {/* User Actions */}
        <div className="py-2">
          <DropdownMenuLabel className="text-xs text-gray-500 uppercase tracking-wider font-semibold px-2 py-1">
            Account
          </DropdownMenuLabel>
          
          <Link to="/profile">
            <DropdownMenuItem className="cursor-pointer rounded-lg mx-1 px-3 py-2 hover:bg-gray-50 transition-colors">
              <User className="mr-3 h-4 w-4" />
              <span>Profile</span>
            </DropdownMenuItem>
          </Link>

          <DropdownMenuItem className="cursor-pointer rounded-lg mx-1 px-3 py-2 hover:bg-gray-50 transition-colors">
            <Settings className="mr-3 h-4 w-4" />
            <span>Settings</span>
          </DropdownMenuItem>

          <ChangePasswordModal>
            <DropdownMenuItem className="cursor-pointer rounded-lg mx-1 px-3 py-2 hover:bg-gray-50 transition-colors">
              <Lock className="mr-3 h-4 w-4" />
              <span>Change Password</span>
            </DropdownMenuItem>
          </ChangePasswordModal>
        </div>

        <DropdownMenuSeparator />

        <DropdownMenuItem 
          className="cursor-pointer rounded-lg mx-1 px-3 py-2 text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors font-medium"
          onClick={async () => {
            try {
              await signOut();
              window.location.href = '/';
            } catch (error) {
              console.error('Sign out failed:', error);
            }
          }}
        >
          <LogOut className="mr-3 h-4 w-4" />
          <span>Sign Out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserMenuWithPassword;
