import React, { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Mail, CheckCircle, AlertCircle, ArrowLeft, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useEnhancedAuth } from '@/hooks/useEnhancedAuth';
import { toast } from 'sonner';

const emailVerificationSchema = z.object({
  token: z.string().min(6, 'Verification token must be at least 6 characters').max(10, 'Verification token must be at most 10 characters'),
});

type EmailVerificationFormData = z.infer<typeof emailVerificationSchema>;

const EmailVerificationForm: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [tokenValue, setTokenValue] = useState('');
  const { verifyEmail, resendVerificationEmail } = useEnhancedAuth();
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const email = searchParams.get('email') || '';

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<EmailVerificationFormData>({
    resolver: zodResolver(emailVerificationSchema),
  });

  const token = watch('token');

  // Pre-fill token from URL params
  useEffect(() => {
    const urlToken = searchParams.get('token');
    if (urlToken) {
      setValue('token', urlToken);
      setTokenValue(urlToken);
    }
  }, [searchParams, setValue]);

  // Handle resend cooldown
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  // Auto-submit when token is complete
  useEffect(() => {
    if (token && token.length >= 6) {
      handleSubmit(onSubmit)();
    }
  }, [token, handleSubmit]);

  const handleTokenInput = (value: string, index: number) => {
    const newToken = tokenValue.split('');
    newToken[index] = value;
    const updatedToken = newToken.join('').slice(0, 6);
    
    setTokenValue(updatedToken);
    setValue('token', updatedToken);

    // Move to next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    if (e.key === 'Backspace' && !tokenValue[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, 6);
    setTokenValue(pastedData);
    setValue('token', pastedData);
  };

  const onSubmit = async (data: EmailVerificationFormData) => {
    setIsLoading(true);
    try {
      await verifyEmail(data.token);
      setIsSuccess(true);
      toast.success('Email verified successfully');
      
      // Redirect to dashboard after 3 seconds
      setTimeout(() => {
        navigate('/dashboard');
      }, 3000);
    } catch (error: any) {
      toast.error(error.message || 'Failed to verify email');
      setTokenValue('');
      setValue('token', '');
      inputRefs.current[0]?.focus();
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendEmail = async () => {
    if (resendCooldown > 0) return;
    
    setIsResending(true);
    try {
      await resendVerificationEmail(email);
      toast.success('Verification email sent');
      setResendCooldown(60); // 60 second cooldown
    } catch (error: any) {
      toast.error(error.message || 'Failed to resend verification email');
    } finally {
      setIsResending(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Email Verified!</h2>
                <p className="text-gray-600 mt-2">
                  Your email has been successfully verified. You will be redirected to your dashboard shortly.
                </p>
              </div>
              <Button 
                onClick={() => navigate('/dashboard')}
                className="w-full"
              >
                Go to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/auth')}
              className="p-1"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Verify Your Email
              </CardTitle>
              <CardDescription>
                Enter the 6-digit code sent to {email || 'your email'}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Token Input */}
            <div className="space-y-2">
              <Label>Verification Code</Label>
              <div className="flex gap-2 justify-center">
                {[0, 1, 2, 3, 4, 5].map((index) => (
                  <Input
                    key={index}
                    ref={(el) => (inputRefs.current[index] = el)}
                    type="text"
                    maxLength={1}
                    value={tokenValue[index] || ''}
                    onChange={(e) => handleTokenInput(e.target.value, index)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                    onPaste={handlePaste}
                    className="w-12 h-12 text-center text-lg font-semibold"
                    disabled={isLoading}
                  />
                ))}
              </div>
              
              {/* Alternative single input for mobile */}
              <div className="md:hidden">
                <Input
                  type="text"
                  placeholder="Enter 6-digit code"
                  value={tokenValue}
                  onChange={(e) => {
                    const value = e.target.value.slice(0, 6);
                    setTokenValue(value);
                    setValue('token', value);
                  }}
                  className="text-center text-lg tracking-widest"
                  disabled={isLoading}
                />
              </div>
              
              {errors.token && (
                <p className="text-sm text-red-600 text-center">{errors.token.message}</p>
              )}
            </div>

            {/* Info Alert */}
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="text-sm">
                  <p>Check your email for a 6-digit verification code.</p>
                  <p className="mt-1">The code expires in 15 minutes.</p>
                </div>
              </AlertDescription>
            </Alert>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading || !token || token.length < 6}
            >
              {isLoading ? 'Verifying...' : 'Verify Email'}
            </Button>

            {/* Resend Section */}
            <div className="text-center space-y-2">
              <p className="text-sm text-gray-600">
                Didn't receive the code?
              </p>
              <Button
                type="button"
                variant="ghost"
                onClick={handleResendEmail}
                disabled={isResending || resendCooldown > 0}
                className="text-sm"
              >
                {isResending ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : resendCooldown > 0 ? (
                  `Resend in ${resendCooldown}s`
                ) : (
                  'Resend Code'
                )}
              </Button>
            </div>

            {/* Help Text */}
            <div className="text-center">
              <p className="text-xs text-gray-500">
                Having trouble? Check your spam folder or{' '}
                <Button
                  type="button"
                  variant="link"
                  className="text-xs p-0 h-auto"
                  onClick={() => navigate('/support')}
                >
                  contact support
                </Button>
              </p>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmailVerificationForm;
