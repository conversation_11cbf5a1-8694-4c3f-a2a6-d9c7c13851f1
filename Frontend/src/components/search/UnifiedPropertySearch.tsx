import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select, SelectContent, SelectItem,
  SelectTrigger, SelectValue
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, Filter, X, MapPin, Home, Bed, Bath, DollarSign } from 'lucide-react';
import { SearchFilters } from '@/types/property';

interface UnifiedPropertySearchProps {
  onFiltersChange: (filters: SearchFilters) => void;
  onSearchResults?: (results: any[]) => void;
  showAdvanced?: boolean;
  compact?: boolean;
}

const UnifiedPropertySearch = ({ 
  onFiltersChange, 
  onSearchResults,
  showAdvanced = true,
  compact = false
}: UnifiedPropertySearchProps) => {
  const [filters, setFilters] = useState<SearchFilters>({
    search: '',
    location: 'all',
    propertyType: 'all',
    minPrice: 0,
    maxPrice: 5000000,
    minBedrooms: undefined,
    maxBedrooms: undefined,
    minBathrooms: undefined,
    maxBathrooms: undefined,
    amenities: [],
    isVerified: false,
    isFeatured: false
  });

  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  const [priceRange, setPriceRange] = useState<[number, number]>([
    filters.minPrice || 0,
    filters.maxPrice || 5000000
  ]);

  const portHarcourtAreas = [
    "Old Government Residential Area (Old GRA)",
    "New Government Residential Area (New GRA)", 
    "Trans Amadi",
    "D-Line",
    "Eliozu",
    "Rumuola",
    "Ada George",
    "Mile 3",
    "Rumuokoro",
    "Woji",
    "Choba",
    "Alakahia"
  ];

  const commonAmenities = [
    "Swimming Pool", "Gym", "Security", "Generator", 
    "Air Conditioning", "Parking Space", "Garden", "Boys Quarters",
    "Internet", "Water Supply", "CCTV", "Fence"
  ];

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
    updateActiveFilters(newFilters);
  };

  const updateActiveFilters = (currentFilters: SearchFilters) => {
    const active: string[] = [];
    
    if (currentFilters.search) active.push(`Search: ${currentFilters.search}`);
    if (currentFilters.location !== 'all') active.push(`Location: ${currentFilters.location}`);
    if (currentFilters.propertyType !== 'all') active.push(`Type: ${currentFilters.propertyType}`);
    if (currentFilters.minBedrooms) active.push(`Bedrooms: ${currentFilters.minBedrooms}`);
    if (currentFilters.minBathrooms) active.push(`Bathrooms: ${currentFilters.minBathrooms}`);
    if (currentFilters.minPrice > 0 || currentFilters.maxPrice < 5000000) {
      active.push(
        `Price: ₦${currentFilters.minPrice?.toLocaleString()}-₦${currentFilters.maxPrice?.toLocaleString()}`
      );
    }
    if (currentFilters.amenities.length > 0) active.push(`Amenities: ${currentFilters.amenities.length}`);
    if (currentFilters.isVerified) active.push('Verified Only');
    if (currentFilters.isFeatured) active.push('Featured Only');
    
    setActiveFilters(active);
  };

  const clearAllFilters = () => {
    const defaultFilters: SearchFilters = {
      search: '',
      location: 'all',
      propertyType: 'all',
      minPrice: 0,
      maxPrice: 5000000,
      minBedrooms: undefined,
      maxBedrooms: undefined,
      minBathrooms: undefined,
      maxBathrooms: undefined,
      amenities: [],
      isVerified: false,
      isFeatured: false
    };
    setFilters(defaultFilters);
    onFiltersChange(defaultFilters);
    setActiveFilters([]);
  };

  const toggleAmenity = (amenity: string) => {
    const newAmenities = filters.amenities.includes(amenity)
      ? filters.amenities.filter(a => a !== amenity)
      : [...filters.amenities, amenity];
    
    handleFilterChange('amenities', newAmenities);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onFiltersChange(filters);
    // Search logic will be handled by parent component through onFiltersChange
  };

  // Synchronize local priceRange with filters state
  useEffect(() => {
    setPriceRange([filters.minPrice || 0, filters.maxPrice || 5000000]);
  }, [filters.minPrice, filters.maxPrice]);

  useEffect(() => {
    onFiltersChange(filters);
  }, []);

  if (compact) {
    return (
      <Card className="p-4">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search properties..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit" className="bg-primary hover:bg-primary/90">
              Search
            </Button>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Select value={filters.location} onValueChange={(value) => handleFilterChange('location', value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Areas</SelectItem>
                {portHarcourtAreas.map((area) => (
                  <SelectItem key={area} value={area}>{area}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={filters.propertyType} onValueChange={(value) => handleFilterChange('propertyType', value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="apartment">Apartment</SelectItem>
                <SelectItem value="house">House</SelectItem>
                <SelectItem value="land">Land</SelectItem>
                <SelectItem value="commercial">Commercial</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.minBedrooms !== undefined ? String(filters.minBedrooms) : ''}
              onValueChange={(value) => handleFilterChange('minBedrooms', parseInt(value, 10))}
            >
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Beds" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any Beds</SelectItem>
                {[1, 2, 3, 4, 5].map(b => (
                  <SelectItem key={b} value={String(b)}>{b}+ Beds</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={filters.minBathrooms !== undefined ? String(filters.minBathrooms) : ''}
              onValueChange={(value) => handleFilterChange('minBathrooms', parseInt(value, 10))}
            >
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Baths" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any Baths</SelectItem>
                {[1, 2, 3, 4, 5].map(b => (
                  <SelectItem key={b} value={String(b)}>{b}+ Baths</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </form>
      </Card>
    );
  }

  return (
    <Card className="p-6 md:p-8">
      <form onSubmit={handleSearch} className="space-y-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search Input */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3.5 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search for properties, keywords..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Location Select */}
          <div className="flex-1">
            <Select
              value={filters.location}
              onValueChange={(value) => handleFilterChange('location', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Areas</SelectItem>
                {portHarcourtAreas.map((area) => (
                  <SelectItem key={area} value={area}>{area}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Price Slider */}
          <div className="w-full md:w-[300px]">
            <label className="text-sm font-medium mb-2 flex items-center">
              <DollarSign className="w-4 h-4 mr-2" />
              Price Range: ₦{priceRange[0]?.toLocaleString()} - ₦{priceRange[1]?.toLocaleString()}
            </label>
            <Slider
              value={priceRange}
              onValueChange={(value: [number, number]) => {
                const [min, max] = value;
                setPriceRange(value);
                handleFilterChange('minPrice', min);
                handleFilterChange('maxPrice', max);
              }}
              min={0}
              max={5000000}
              step={100000}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button type="submit" className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              Search
            </Button>
            {showAdvanced && (
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="w-4 h-4" />
                {showAdvancedFilters ? 'Hide Filters' : 'More Filters'}
              </Button>
            )}
          </div>
        </div>

        {/* Advanced Filters Section */}
        {showAdvancedFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t mt-6">
            {/* Property Type Select */}
            <div>
              <label className="text-sm font-medium flex items-center mb-2">
                <Home className="w-4 h-4 mr-2" />
                Property Type
              </label>
              <Select
                value={filters.propertyType}
                onValueChange={(value) => handleFilterChange('propertyType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="apartment">Apartment</SelectItem>
                  <SelectItem value="house">House</SelectItem>
                  <SelectItem value="land">Land</SelectItem>
                  <SelectItem value="commercial">Commercial</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Bedrooms Select */}
            <div>
              <label className="text-sm font-medium flex items-center mb-2">
                <Bed className="w-4 h-4 mr-2" />
                Bedrooms
              </label>
              <Select
                value={filters.minBedrooms !== undefined ? String(filters.minBedrooms) : ''}
                onValueChange={(value) => handleFilterChange('minBedrooms', parseInt(value, 10))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any</SelectItem>
                  {[1, 2, 3, 4, 5].map(b => (
                    <SelectItem key={b} value={String(b)}>{b}+</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Bathrooms Select */}
            <div>
              <label className="text-sm font-medium flex items-center mb-2">
                <Bath className="w-4 h-4 mr-2" />
                Bathrooms
              </label>
              <Select
                value={filters.minBathrooms !== undefined ? String(filters.minBathrooms) : ''}
                onValueChange={(value) => handleFilterChange('minBathrooms', parseInt(value, 10))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any</SelectItem>
                  {[1, 2, 3, 4, 5].map(b => (
                    <SelectItem key={b} value={String(b)}>{b}+</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Amenities Checkboxes */}
            <div className="md:col-span-3 mt-4">
              <label className="text-sm font-medium flex items-center mb-2">
                <Filter className="w-4 h-4 mr-2" />
                Amenities
              </label>
              <div className="flex flex-wrap gap-4">
                {commonAmenities.map((amenity) => (
                  <div key={amenity} className="flex items-center space-x-2">
                    <Checkbox
                      id={amenity}
                      checked={filters.amenities?.includes(amenity)}
                      onCheckedChange={() => toggleAmenity(amenity)}
                    />
                    <label htmlFor={amenity} className="text-sm font-medium leading-none">
                      {amenity}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Featured and Verified Checkboxes */}
            <div className="md:col-span-3 mt-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isFeatured"
                    checked={filters.isFeatured}
                    onCheckedChange={(checked) => handleFilterChange('isFeatured', checked)}
                  />
                  <label htmlFor="isFeatured" className="text-sm font-medium leading-none">
                    Featured Properties
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isVerified"
                    checked={filters.isVerified}
                    onCheckedChange={(checked) => handleFilterChange('isVerified', checked)}
                  />
                  <label htmlFor="isVerified" className="text-sm font-medium leading-none">
                    Verified Properties Only
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}
      </form>
      {/* Active Filters Display */}
      <div className="flex flex-wrap gap-2 pt-4 mt-6 border-t">
        {activeFilters.map((filter, index) => (
          <Badge key={index} variant="secondary" className="pl-3 pr-1 py-1">
            {filter}
            <X className="ml-1 w-3 h-3 cursor-pointer" />
          </Badge>
        ))}
      </div>
    </Card>
  );
};

export default UnifiedPropertySearch;