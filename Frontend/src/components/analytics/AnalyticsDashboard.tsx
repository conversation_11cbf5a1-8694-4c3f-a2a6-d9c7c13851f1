import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { TrendingUp, TrendingDown, Users, Home, DollarSign, Activity, Download, Settings, Bell, RefreshCw, BarChart3, Eye, Target, Zap, ArrowUpRight, ArrowDownRight, Sparkles, Filter, Calendar, Globe } from 'lucide-react';
import { useDashboardMetrics, usePerformanceAnalytics, useMarketTrends, useCustomReports } from '@/hooks/useAnalytics';
import { format, subDays } from 'date-fns';
import Navbar from '@/components/navigation/Navbar';

interface AnalyticsDashboardProps {
  userId: string;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ userId }) => {
  const [dateRange, setDateRange] = useState({
    start: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
    end: format(new Date(), 'yyyy-MM-dd')
  });
  
  const [selectedLocation, setSelectedLocation] = useState<string>('all');
  const [selectedPropertyType, setSelectedPropertyType] = useState<string>('all');

  const {
    metrics,
    customDashboards,
    isLoading,
    realTimeEnabled,
    toggleRealTime,
    refreshMetrics,
    createCustomDashboard,
    createAlert
  } = useDashboardMetrics(dateRange, {
    locations: selectedLocation !== 'all' ? [selectedLocation] : undefined,
    property_types: selectedPropertyType !== 'all' ? [selectedPropertyType] : undefined
  });

  const {
    priceTrends,
    demandPatterns,
    seasonalTrends,
    marketCycle,
    competitiveAnalysis,
    switchAnalysisType
  } = useMarketTrends(selectedLocation !== 'all' ? selectedLocation : 'Port Harcourt');

  const {
    reportConfigs,
    generateReport,
    isGenerating
  } = useCustomReports(userId);

  // Sample data for charts
  const sampleTrendData = [
    { date: '2024-01', views: 1200, inquiries: 89, revenue: 45000 },
    { date: '2024-02', views: 1350, inquiries: 102, revenue: 52000 },
    { date: '2024-03', views: 1180, inquiries: 95, revenue: 48000 },
    { date: '2024-04', views: 1420, inquiries: 118, revenue: 58000 },
    { date: '2024-05', views: 1580, inquiries: 134, revenue: 65000 },
    { date: '2024-06', views: 1650, inquiries: 142, revenue: 71000 }
  ];

  const sampleMarketData = [
    { location: 'GRA', properties: 45, avgPrice: 2500000, demand: 85 },
    { location: 'Trans Amadi', properties: 32, avgPrice: 1800000, demand: 72 },
    { location: 'Old GRA', properties: 28, avgPrice: 3200000, demand: 91 },
    { location: 'Woji', properties: 38, avgPrice: 2100000, demand: 68 },
    { location: 'Ada George', properties: 25, avgPrice: 1600000, demand: 76 }
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <Navbar />

      {/* Hero Section with Glassmorphism */}
      <div className="relative overflow-hidden pt-16">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-indigo-600/5"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl"></div>
        <div className="absolute top-20 right-1/4 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl"></div>

        <div className="relative container mx-auto px-6 pt-8 pb-12">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
            <div className="mb-6 lg:mb-0">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 bg-clip-text text-transparent">
                    Analytics & Intelligence
                  </h1>
                  <p className="text-lg text-gray-600 mt-1">
                    Advanced business insights and performance metrics
                  </p>
                </div>
              </div>

              {/* Status Indicators */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 px-3 py-1.5 bg-green-50 border border-green-200 rounded-full">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-green-700">Live Data</span>
                </div>
                <div className="flex items-center space-x-2 px-3 py-1.5 bg-blue-50 border border-blue-200 rounded-full">
                  <Eye className="h-3 w-3 text-blue-600" />
                  <span className="text-sm font-medium text-blue-700">Real-time Monitoring</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap items-center gap-3">
              <Button
                variant={realTimeEnabled ? "default" : "outline"}
                size="lg"
                onClick={toggleRealTime}
                className={`${realTimeEnabled
                  ? 'bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg shadow-green-500/25'
                  : 'border-2 border-gray-200 hover:border-green-300 hover:bg-green-50'
                } transition-all duration-300 transform hover:scale-105`}
              >
                <Activity className={`h-4 w-4 mr-2 ${realTimeEnabled ? 'animate-pulse' : ''}`} />
                Real-time {realTimeEnabled ? 'ON' : 'OFF'}
              </Button>

              <Button
                variant="outline"
                size="lg"
                onClick={refreshMetrics}
                className="border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 transform hover:scale-105"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-2 border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-300 transform hover:scale-105"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-300 transform hover:scale-105"
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>

          {/* Advanced Filters with Glassmorphism */}
          <div className="backdrop-blur-xl bg-white/70 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-blue-500/10">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl">
                  <Filter className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800">Smart Filters</h3>
              </div>
              <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-blue-200">
                <Sparkles className="h-3 w-3 mr-1" />
                AI-Powered
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                  Date Range
                </label>
                <DatePickerWithRange
                  value={{ from: new Date(dateRange.start), to: new Date(dateRange.end) }}
                  onChange={(range) => {
                    if (range?.from && range?.to) {
                      setDateRange({
                        start: format(range.from, 'yyyy-MM-dd'),
                        end: format(range.to, 'yyyy-MM-dd')
                      });
                    }
                  }}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Globe className="h-4 w-4 mr-2 text-green-600" />
                  Location
                </label>
                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger className="w-full bg-white/80 border-gray-200 hover:border-green-300 transition-colors">
                    <SelectValue placeholder="Select Location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    <SelectItem value="GRA">GRA</SelectItem>
                    <SelectItem value="Trans Amadi">Trans Amadi</SelectItem>
                    <SelectItem value="Old GRA">Old GRA</SelectItem>
                    <SelectItem value="Woji">Woji</SelectItem>
                    <SelectItem value="Ada George">Ada George</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 flex items-center">
                  <Home className="h-4 w-4 mr-2 text-purple-600" />
                  Property Type
                </label>
                <Select value={selectedPropertyType} onValueChange={setSelectedPropertyType}>
                  <SelectTrigger className="w-full bg-white/80 border-gray-200 hover:border-purple-300 transition-colors">
                    <SelectValue placeholder="Property Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="apartment">Apartment</SelectItem>
                    <SelectItem value="house">House</SelectItem>
                    <SelectItem value="duplex">Duplex</SelectItem>
                    <SelectItem value="bungalow">Bungalow</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced KPI Cards with Glassmorphism */}
      <div className="container mx-auto px-6 -mt-4 relative z-10">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          {/* Total Properties Card */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
            <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-blue-500/10 hover:shadow-blue-500/20 transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                  <Home className="h-6 w-6 text-white" />
                </div>
                <div className="flex items-center space-x-1 text-green-600">
                  <ArrowUpRight className="h-4 w-4" />
                  <span className="text-sm font-medium">+12%</span>
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-600">Total Properties</h3>
                <p className="text-3xl font-bold text-gray-900">{(metrics?.overview?.total_properties || 1247).toLocaleString()}</p>
                <p className="text-sm text-gray-500">from last month</p>
              </div>
            </div>
          </div>

          {/* Active Users Card */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
            <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-green-500/10 hover:shadow-green-500/20 transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl shadow-lg">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div className="flex items-center space-x-1 text-green-600">
                  <ArrowUpRight className="h-4 w-4" />
                  <span className="text-sm font-medium">+8%</span>
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-600">Active Users</h3>
                <p className="text-3xl font-bold text-gray-900">{(metrics?.real_time?.active_users || 3456).toLocaleString()}</p>
                <p className="text-sm text-gray-500">from last week</p>
              </div>
            </div>
          </div>

          {/* Revenue Card */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
            <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-purple-500/10 hover:shadow-purple-500/20 transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl shadow-lg">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <div className="flex items-center space-x-1 text-green-600">
                  <ArrowUpRight className="h-4 w-4" />
                  <span className="text-sm font-medium">+{metrics?.overview?.revenue_growth || 15.2}%</span>
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-600">Monthly Revenue</h3>
                <p className="text-3xl font-bold text-gray-900">
                  ₦{(metrics?.overview?.revenue_this_month || 12500000).toLocaleString()}
                </p>
                <p className="text-sm text-gray-500">from last month</p>
              </div>
            </div>
          </div>

          {/* Conversion Rate Card */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
            <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-orange-500/10 hover:shadow-orange-500/20 transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl shadow-lg">
                  <Target className="h-6 w-6 text-white" />
                </div>
                <div className="flex items-center space-x-1 text-green-600">
                  <ArrowUpRight className="h-4 w-4" />
                  <span className="text-sm font-medium">+2.1%</span>
                </div>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-600">Conversion Rate</h3>
                <p className="text-3xl font-bold text-gray-900">{metrics?.overview?.conversion_rate || 24.8}%</p>
                <p className="text-sm text-gray-500">from last month</p>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Analytics Tabs */}
        <Tabs defaultValue="overview" className="space-y-8">
          <div className="flex justify-center">
            <TabsList className="grid w-full max-w-2xl grid-cols-4 bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl p-2 shadow-2xl shadow-blue-500/10">
              <TabsTrigger
                value="overview"
                className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                Overview
              </TabsTrigger>
              <TabsTrigger
                value="performance"
                className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Performance
              </TabsTrigger>
              <TabsTrigger
                value="market"
                className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <Activity className="h-4 w-4 mr-2" />
                Market Trends
              </TabsTrigger>
              <TabsTrigger
                value="reports"
                className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <Download className="h-4 w-4 mr-2" />
                Reports
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="overview" className="space-y-8">
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-7">
              {/* Main Chart */}
              <div className="col-span-4 group relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl blur opacity-10 group-hover:opacity-20 transition-opacity"></div>
                <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-8 shadow-2xl shadow-blue-500/10 hover:shadow-blue-500/20 transition-all duration-300">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl">
                        <TrendingUp className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-800">Property Views & Inquiries</h3>
                        <p className="text-sm text-gray-600">Real-time engagement metrics</p>
                      </div>
                    </div>
                    <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200">
                      <ArrowUpRight className="h-3 w-3 mr-1" />
                      +15.3%
                    </Badge>
                  </div>
                  <ResponsiveContainer width="100%" height={350}>
                    <LineChart data={sampleTrendData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis dataKey="date" stroke="#64748b" />
                      <YAxis stroke="#64748b" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          border: 'none',
                          borderRadius: '12px',
                          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Legend />
                      <Line type="monotone" dataKey="views" stroke="#3b82f6" strokeWidth={3} dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }} />
                      <Line type="monotone" dataKey="inquiries" stroke="#10b981" strokeWidth={3} dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Market Distribution */}
              <div className="col-span-3 group relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-3xl blur opacity-10 group-hover:opacity-20 transition-opacity"></div>
                <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-8 shadow-2xl shadow-purple-500/10 hover:shadow-purple-500/20 transition-all duration-300">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl">
                        <Globe className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-800">Market Distribution</h3>
                        <p className="text-sm text-gray-600">By location</p>
                      </div>
                    </div>
                  </div>
                  <ResponsiveContainer width="100%" height={350}>
                    <PieChart>
                      <Pie
                        data={sampleMarketData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ location, percent }) => `${location} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="properties"
                      >
                        {sampleMarketData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          border: 'none',
                          borderRadius: '12px',
                          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={sampleTrendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`₦${Number(value).toLocaleString()}`, 'Revenue']} />
                    <Area type="monotone" dataKey="revenue" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Location Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={sampleMarketData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="location" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="demand" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Agents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics?.performance?.top_performing_agents?.slice(0, 5).map((agent, index) => (
                    <div key={agent.agent_id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{agent.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {agent.properties_managed} properties
                        </p>
                      </div>
                      <Badge variant="secondary">
                        {agent.conversion_rate}% conversion
                      </Badge>
                    </div>
                  )) || (
                    <p className="text-muted-foreground">No agent data available</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Properties</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics?.performance?.top_performing_properties?.slice(0, 5).map((property, index) => (
                    <div key={property.property_id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{property.title}</p>
                        <p className="text-sm text-muted-foreground">
                          {property.views} views
                        </p>
                      </div>
                      <Badge variant="secondary">
                        {property.conversion_rate}%
                      </Badge>
                    </div>
                  )) || (
                    <p className="text-muted-foreground">No property data available</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Market Hotspots</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics?.performance?.market_hotspots?.slice(0, 5).map((hotspot, index) => (
                    <div key={hotspot.location} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{hotspot.location}</p>
                        <p className="text-sm text-muted-foreground">
                          Activity: {hotspot.activity_score}
                        </p>
                      </div>
                      <Badge 
                        variant={hotspot.price_trend === 'rising' ? 'default' : 
                                hotspot.price_trend === 'falling' ? 'destructive' : 'secondary'}
                      >
                        {hotspot.price_trend === 'rising' ? <TrendingUp className="h-3 w-3" /> :
                         hotspot.price_trend === 'falling' ? <TrendingDown className="h-3 w-3" /> :
                         <Activity className="h-3 w-3" />}
                      </Badge>
                    </div>
                  )) || (
                    <p className="text-muted-foreground">No hotspot data available</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="market" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Market Trend Analysis</CardTitle>
              <CardDescription>
                Comprehensive market insights and predictions for Port Harcourt real estate
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold mb-2">Price Trends</h4>
                  {priceTrends ? (
                    <div className="space-y-2">
                      <p>Direction: <Badge>{priceTrends.trend_direction}</Badge></p>
                      <p>Change: {priceTrends.price_change_percentage}%</p>
                      <p>Confidence: {Math.round(priceTrends.trend_confidence * 100)}%</p>
                    </div>
                  ) : (
                    <p className="text-muted-foreground">Loading price trends...</p>
                  )}
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Market Cycle</h4>
                  {marketCycle ? (
                    <div className="space-y-2">
                      <p>Current Phase: <Badge>{marketCycle.current_phase}</Badge></p>
                      <p>Next Phase: {marketCycle.predicted_next_phase}</p>
                      <p>Time to Next: {marketCycle.time_to_next_phase} months</p>
                    </div>
                  ) : (
                    <p className="text-muted-foreground">Loading market cycle...</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Custom Reports</h3>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
          </div>
          
          <div className="grid gap-4 md:grid-cols-2">
            {reportConfigs.map((config) => (
              <Card key={config.id}>
                <CardHeader>
                  <CardTitle>{config.name}</CardTitle>
                  <CardDescription>{config.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">{config.type}</Badge>
                    <Button 
                      size="sm" 
                      onClick={() => generateReport(config.id!)}
                      disabled={isGenerating}
                    >
                      {isGenerating ? 'Generating...' : 'Generate'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
