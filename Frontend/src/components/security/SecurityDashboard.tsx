// =====================================================
// SECURITY DASHBOARD COMPONENT
// Comprehensive security monitoring and management interface
// =====================================================

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Shield, 
  AlertTriangle, 
  Lock, 
  Eye, 
  Activity, 
  Users, 
  FileText, 
  Settings,
  RefreshCw,
  Download,
  Bell,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Zap,
  Database,
  Key,
  Globe,
  Monitor
} from 'lucide-react';
import { useSecurityDashboard, useSecurityMonitoring } from '@/hooks/useSecurity';
import Navbar from '@/components/navigation/Navbar';

interface SecurityDashboardProps {
  userId?: string;
}

const SecurityDashboard: React.FC<SecurityDashboardProps> = ({ userId }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState<'hour' | 'day' | 'week'>('day');

  const { dashboardData, isLoading, loadDashboardData } = useSecurityDashboard();
  const { securityEvents, securityAlerts, resolveEvent, updateAlertStatus } = useSecurityMonitoring();

  // Refresh dashboard data
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadDashboardData();
    } catch (error) {
      console.error('Failed to refresh dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Get security score based on metrics
  const getSecurityScore = () => {
    if (!dashboardData?.securityMetrics) return 0;
    
    const metrics = dashboardData.securityMetrics;
    let score = 100;
    
    // Deduct points for security issues
    score -= metrics.eventsBySeverity?.critical * 20;
    score -= metrics.eventsBySeverity?.high * 10;
    score -= metrics.eventsBySeverity?.medium * 5;
    score -= metrics.eventsBySeverity?.low * 1;
    
    // Deduct points for active alerts
    score -= metrics.activeAlerts * 5;
    
    return Math.max(0, Math.min(100, score));
  };

  // Get security status color
  const getSecurityStatusColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    if (score >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  // Get threat level
  const getThreatLevel = (score: number) => {
    if (score >= 90) return { level: 'Low', color: 'bg-green-500' };
    if (score >= 70) return { level: 'Medium', color: 'bg-yellow-500' };
    if (score >= 50) return { level: 'High', color: 'bg-orange-500' };
    return { level: 'Critical', color: 'bg-red-500' };
  };

  const securityScore = getSecurityScore();
  const threatLevel = getThreatLevel(securityScore);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-red-50/30 to-orange-50/20">
      <Navbar />
      
      {/* Hero Section */}
      <div className="relative overflow-hidden pt-16">
        <div className="absolute inset-0 bg-gradient-to-r from-red-600/5 via-orange-600/5 to-yellow-600/5"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-red-400/10 rounded-full blur-3xl"></div>
        <div className="absolute top-20 right-1/4 w-80 h-80 bg-orange-400/10 rounded-full blur-3xl"></div>
        
        <div className="relative container mx-auto px-6 pt-8 pb-12">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
            <div className="mb-6 lg:mb-0">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-3 bg-gradient-to-r from-red-500 to-orange-600 rounded-2xl shadow-lg">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-red-800 to-orange-800 bg-clip-text text-transparent">
                    Security Dashboard
                  </h1>
                  <p className="text-lg text-gray-600 mt-1">
                    Comprehensive security monitoring and threat protection
                  </p>
                </div>
              </div>
              
              {/* Security Score */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 px-4 py-2 bg-white/80 border border-gray-200 rounded-full">
                  <div className={`w-3 h-3 rounded-full ${threatLevel.color}`}></div>
                  <span className="text-sm font-medium text-gray-700">
                    Security Score: <span className={getSecurityStatusColor(securityScore)}>{securityScore}/100</span>
                  </span>
                </div>
                
                <div className="flex items-center space-x-2 px-3 py-1.5 bg-white/80 border border-gray-200 rounded-full">
                  <span className="text-sm font-medium text-gray-700">
                    Threat Level: <span className={`font-bold ${threatLevel.level === 'Low' ? 'text-green-600' : threatLevel.level === 'Medium' ? 'text-yellow-600' : threatLevel.level === 'High' ? 'text-orange-600' : 'text-red-600'}`}>
                      {threatLevel.level}
                    </span>
                  </span>
                </div>

                {securityAlerts.filter(a => a.status === 'open').length > 0 && (
                  <div className="flex items-center space-x-2 px-3 py-1.5 bg-red-50 border border-red-200 rounded-full">
                    <AlertTriangle className="h-3 w-3 text-red-600" />
                    <span className="text-sm font-medium text-red-700">
                      {securityAlerts.filter(a => a.status === 'open').length} Active Alerts
                    </span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex flex-wrap items-center gap-3">
              <Button 
                variant="outline" 
                size="lg"
                onClick={handleRefresh}
                disabled={refreshing}
                className="border-2 border-gray-200 hover:border-red-300 hover:bg-red-50 transition-all duration-300"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh Data
              </Button>
              
              <Button 
                variant="outline" 
                size="lg"
                className="border-2 border-gray-200 hover:border-orange-300 hover:bg-orange-50 transition-all duration-300"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
              
              <Button 
                variant="outline" 
                size="lg"
                className="border-2 border-gray-200 hover:border-yellow-300 hover:bg-yellow-50 transition-all duration-300"
              >
                <Settings className="h-4 w-4 mr-2" />
                Configure
              </Button>
            </div>
          </div>

          {/* Security Metrics Cards */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
            {/* Total Events */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-blue-500/10 hover:shadow-blue-500/20 transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                    <Activity className="h-6 w-6 text-white" />
                  </div>
                  <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">Security Events</h3>
                  <p className="text-3xl font-bold text-gray-900">
                    {dashboardData?.securityMetrics?.totalEvents || 0}
                  </p>
                  <div className="flex items-center space-x-2">
                    <TrendingDown className="h-3 w-3 text-green-600" />
                    <span className="text-sm text-green-600">-12% from yesterday</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Active Alerts */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-pink-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-red-500/10 hover:shadow-red-500/20 transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl shadow-lg">
                    <AlertTriangle className="h-6 w-6 text-white" />
                  </div>
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">Active Alerts</h3>
                  <p className="text-3xl font-bold text-gray-900">
                    {dashboardData?.securityMetrics?.activeAlerts || 0}
                  </p>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-3 w-3 text-orange-600" />
                    <span className="text-sm text-orange-600">Requires attention</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Blocked Attacks */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-green-500/10 hover:shadow-green-500/20 transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl shadow-lg">
                    <Shield className="h-6 w-6 text-white" />
                  </div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">Blocked Attacks</h3>
                  <p className="text-3xl font-bold text-gray-900">
                    {dashboardData?.securityMetrics?.blockedAttacks || 0}
                  </p>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    <span className="text-sm text-green-600">Successfully blocked</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Response Time */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-violet-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-purple-500/10 hover:shadow-purple-500/20 transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-purple-500 to-violet-600 rounded-2xl shadow-lg">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">Avg Response Time</h3>
                  <p className="text-3xl font-bold text-gray-900">
                    {dashboardData?.securityMetrics?.responseTime?.average ? 
                      `${Math.round(dashboardData.securityMetrics.responseTime.average / 1000)}s` : 
                      '--'
                    }
                  </p>
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-3 w-3 text-blue-600" />
                    <span className="text-sm text-blue-600">Optimal performance</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Security Tabs */}
      <div className="container mx-auto px-6 -mt-4 relative z-10">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
          <TabsList className="grid w-full grid-cols-5 bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl p-2 shadow-lg">
            <TabsTrigger value="overview" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-500 data-[state=active]:to-orange-600 data-[state=active]:text-white">
              <Monitor className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="events" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-600 data-[state=active]:text-white">
              <Activity className="h-4 w-4 mr-2" />
              Events
            </TabsTrigger>
            <TabsTrigger value="alerts" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-yellow-500 data-[state=active]:to-orange-600 data-[state=active]:text-white">
              <Bell className="h-4 w-4 mr-2" />
              Alerts
            </TabsTrigger>
            <TabsTrigger value="encryption" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white">
              <Key className="h-4 w-4 mr-2" />
              Encryption
            </TabsTrigger>
            <TabsTrigger value="threats" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-600 data-[state=active]:text-white">
              <Globe className="h-4 w-4 mr-2" />
              Threats
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Active Security Alerts */}
            {securityAlerts.filter(a => a.status === 'open').length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Active Security Alerts</h3>
                {securityAlerts.filter(a => a.status === 'open').slice(0, 5).map(alert => (
                  <Alert key={alert.id} className={`border-l-4 ${
                    alert.severity === 'critical' ? 'border-red-500 bg-red-50' :
                    alert.severity === 'high' ? 'border-orange-500 bg-orange-50' :
                    alert.severity === 'medium' ? 'border-yellow-500 bg-yellow-50' :
                    'border-blue-500 bg-blue-50'
                  }`}>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle className="flex items-center justify-between">
                      {alert.title}
                      <div className="flex space-x-2">
                        <Badge variant={alert.severity === 'critical' ? 'destructive' : 'default'}>
                          {alert.severity}
                        </Badge>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => updateAlertStatus(alert.id, 'investigating')}
                        >
                          Investigate
                        </Button>
                      </div>
                    </AlertTitle>
                    <AlertDescription>
                      {alert.description}
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            )}

            {/* Security Metrics Grid */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {/* Rate Limiting Stats */}
              <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Rate Limiting</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {dashboardData?.rateLimitStats?.totalViolations || 0}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Violations blocked today
                  </div>
                </CardContent>
              </Card>

              {/* CSRF Protection */}
              <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">CSRF Protection</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {dashboardData?.csrfStats?.totalAttacks || 0}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Attacks prevented today
                  </div>
                </CardContent>
              </Card>

              {/* Encryption Status */}
              <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Encryption</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {dashboardData?.encryptionStats?.activeKeys || 0}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Active encryption keys
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Other tabs would be implemented similarly */}
          <TabsContent value="events">
            <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle>Security Events</CardTitle>
                <CardDescription>Recent security events and incidents</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Security events content would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="alerts">
            <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle>Security Alerts</CardTitle>
                <CardDescription>Active and resolved security alerts</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Security alerts content would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="encryption">
            <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle>Encryption Management</CardTitle>
                <CardDescription>Encryption keys and data protection status</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Encryption management content would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="threats">
            <Card className="backdrop-blur-xl bg-white/80 border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle>Threat Intelligence</CardTitle>
                <CardDescription>Known threats and IP reputation data</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Threat intelligence content would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SecurityDashboard;
