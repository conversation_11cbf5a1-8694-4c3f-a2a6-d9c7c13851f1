
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useCreateProperty, useUpdateProperty, Property } from '@/hooks/useProperties';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import PropertyImageUpload from './PropertyImageUpload';
import { PropertyInsert } from '@/types/property';

const propertySchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters'),
  description: z.string().min(20, 'Description must be at least 20 characters'),
  location: z.string().min(5, 'Location must be at least 5 characters'),
  price_per_year: z.number().min(1, 'Price must be greater than 0'),
  property_type: z.string().min(1, 'Property type is required'),
  bedrooms: z.number().min(0, 'Number of bedrooms must be 0 or greater'),
  bathrooms: z.number().min(0, 'Number of bathrooms must be 0 or greater'),
  area_sqft: z.number().min(1, 'Area must be greater than 0'),
  amenities: z.array(z.string()),
  images: z.array(z.string()).optional(),
});

type PropertyFormValues = z.infer<typeof propertySchema>;

interface PropertyCreationFormProps {
  property?: Property;
  onSuccess: () => void;
}

const PROPERTY_TYPES = [
  'Apartment',
  'House',
  'Villa',
  'Townhouse',
  'Land',
  'Commercial',
  'Office',
  'Shop',
  'Warehouse',
];

const PropertyCreationForm = ({ property, onSuccess }: PropertyCreationFormProps) => {
  const { toast } = useToast();
  const createProperty = useCreateProperty();
  const updateProperty = useUpdateProperty();

  const form = useForm<PropertyFormValues>({
    resolver: zodResolver(propertySchema),
    defaultValues: property ? {
      title: property.title,
      description: property.description,
      location: property.location,
      price_per_year: property.price_per_year,
      property_type: property.property_type,
      bedrooms: property.bedrooms,
      bathrooms: property.bathrooms,
      area_sqft: property.area_sqft,
      amenities: property.amenities || [],
      images: property.images,
    } : {
      title: '',
      description: '',
      location: '',
      price_per_year: 0,
      property_type: '',
      bedrooms: 0,
      bathrooms: 0,
      area_sqft: 0,
      amenities: [],
      images: [],
    },
  });

  const onSubmit = async (data: PropertyFormValues) => {
    try {
      if (property) {
        await updateProperty.mutateAsync({
          id: property.id,
          updates: data,
        });
        toast({
          title: 'Success',
          description: 'Property updated successfully',
        });
      } else {
        const propertyData: PropertyInsert = {
          ...data,
          amenities: data.amenities || [],
        };
        await createProperty.mutateAsync(propertyData);
        toast({
          title: 'Success',
          description: 'Property created successfully',
        });
      }
      onSuccess();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Modern 3 Bedroom Apartment" {...field} />
                    </FormControl>
                    <FormDescription>
                      A clear, descriptive title for your property
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="property_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Property Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select property type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {PROPERTY_TYPES.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input placeholder="123 Main Street, Port Harcourt" {...field} />
                    </FormControl>
                    <FormDescription>
                      Full address of the property
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="price_per_year"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price per Year (₦)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="1000000"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Annual rent in Naira
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bedrooms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bedrooms</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bathrooms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bathrooms</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="area_sqft"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Area (sq ft)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="mt-6">
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your property in detail..."
                        className="min-h-[150px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Include key features, amenities, and any special conditions
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="mt-6">
              <FormField
                control={form.control}
                name="images"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Property Images</FormLabel>
                    <FormControl>
                      <PropertyImageUpload
                        images={field.value || []}
                        onImagesChange={field.onChange}
                      />
                    </FormControl>
                    <FormDescription>
                      Upload high-quality images of your property (max 10 images)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => onSuccess()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createProperty.isPending || updateProperty.isPending}
          >
            {createProperty.isPending || updateProperty.isPending ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {property ? 'Updating...' : 'Creating...'}
              </div>
            ) : (
              property ? 'Update Property' : 'Create Property'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default PropertyCreationForm;
