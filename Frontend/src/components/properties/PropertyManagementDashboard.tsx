import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  useMyProperties, usePropertyStats,
  useDeleteProperty,
} from '@/hooks/properties/useProperties';
import { useAuth } from '@/hooks/auth/useAuth';
import PropertyCreationForm from './PropertyCreationForm';
import { Plus, Edit, Eye, Trash2, MapPin, Star, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  AlertDialog, AlertDialogAction, AlertDialogCancel,
  AlertDialogContent, AlertDialogDescription, AlertDialogFooter,
  AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import { Property, PropertyStatusEnum } from '@/types/property';

const PropertyManagementDashboard = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);

  // Get properties for the current user (landlord/agent)
  const { data: propertiesData, isLoading: isLoadingProperties, isError, error } = useMyProperties();
  const { data: stats, isLoading: isLoadingStats } = usePropertyStats();
  const deleteProperty = useDeleteProperty();

  if (isLoadingProperties) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>An error occurred: {error.message}</div>;
  }
  console.log('Raw response from useMyProperties', JSON.stringify(propertiesData));
  const properties: Property[] = propertiesData?.data || [];

  const handleCreateProperty = () => {
    setSelectedProperty(null); // Ensure selectedProperty is reset
    setShowCreateForm(true);
  };

  const handleEditProperty = (property: Property) => {
    setSelectedProperty(property);
    setShowCreateForm(true);
  };

  const handleDeleteProperty = async (propertyId: string) => {
    try {
      await deleteProperty.mutateAsync(propertyId);
      toast({
        title: "Property Deleted",
        description: "The property has been successfully deleted.",
      });
    } catch (err: any) { // Catch block error typing
      toast({
        title: "Error",
        description: err.message || "Failed to delete the property. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (showCreateForm) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">
            {selectedProperty ? 'Edit Property' : 'Create New Property'}
          </h2>
          <Button variant="outline" onClick={() => {
            setShowCreateForm(false);
            setSelectedProperty(null);
          }}>
            Back to Dashboard
          </Button>
        </div>
        <PropertyCreationForm
          property={selectedProperty}
          onSuccess={() => {
            setShowCreateForm(false);
            setSelectedProperty(null);
          }}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Property Management</h2>
          <p className="text-gray-600">Manage your property listings</p>
        </div>
        <Button onClick={handleCreateProperty} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Add New Property
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Properties</p>
                {isLoadingStats ? (
                  <div className='h-6 w-12 bg-gray-200 rounded animate-pulse'
                  />
                ) : (
                  <p className="text-2xl font-bold">{stats?.data?.totalProperties || 0}</p>
                )}
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <MapPin className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Available</p>
                {isLoadingStats ? (
                  <div className='h-6 w-12 bg-gray-200 rounded animate-pulse'
                  />
                ) : (
                  <p className="text-2xl font-bold">
                  {stats?.data?.availableProperties || 0}
                </p>
                )}
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Eye className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Featured</p>
                {isLoadingStats ? (
                  <div className='h-6 w-12 bg-gray-200 rounded animate-pulse' /
                  >
                ) : (
                  <p className="text-2xl font-bold">
                  {stats?.data?.featuredProperties || 0}
                </p>
                )}
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Star className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Verified</p>
                {isLoadingStats ? (
                  <div className='h-6 w-12 bg-gray-200 rounded animate-pulse' /
                  >
                ) : (
                  <p className="text-2xl font-bold">
                  {stats?.data?.verifiedProperties || 0}
                </p>
                )}
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <CheckCircle className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Properties List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Properties</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingProperties ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
            </div>
          ) : properties.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">You haven't created any properties yet.</p>
              <Button onClick={handleCreateProperty}>Create Your First Property</Button>
            </div>
          ) : (
            <div className="space-y-4">
              {properties.map((property) => (
                <div key={property.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg">{property.title}</h3>
                        <Badge variant={property.status === PropertyStatusEnum.AVAILABLE ? "default" : "secondary"}>
                          {property.status}
                        </Badge>
                        {property.isFeatured && (
                          <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-200">
                            Featured
                          </Badge>
                        )}
                        {property.isVerified && (
                          <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                            Verified
                          </Badge>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          {property.location}
                        </div>
                        <div>
                          {property.propertyType} • {property.bedrooms} bed • {property.bathrooms} bath
                        </div>
                        <div className="font-medium text-gray-900">
                          ₦{property.pricePerYear.toLocaleString()}/year
                        </div>
                        <div>
                          Created {new Date(property.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`/properties/${property.id}`, '_blank')}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditProperty(property)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This action cannot be undone. This will permanently delete the property
                              and remove it from our servers.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteProperty(property.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PropertyManagementDashboard;
