
import React, { useState } from 'react';
import { Search, Filter, MapPin, Bed, Bath, Home, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useProperties } from '@/hooks/properties';
import { Badge } from '@/components/ui/badge';
import { PROPERTY_TYPES } from '@/types/property';

interface SearchFilters {
  query: string;
  location: string;
  propertyType: string;
  minPrice: number;
  maxPrice: number;
  bedrooms: number;
  bathrooms: number;
};

const WorkingPropertiesSearch = () => {
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    location: '',
    propertyType: '',
    minPrice: 0,
    maxPrice: 0,
    bedrooms: 0,
    bathrooms: 0,
  });

  const { data, isLoading, error } = useProperties(filters);

  const properties = data?.properties || [];
  const totalCount = data?.totalCount || 0;

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      maximumFractionDigits: 0,
    }).format(price);
  };

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      query: '',
      location: '',
      propertyType: '',
      minPrice: 0,
      maxPrice: 0,
      bedrooms: 0,
      bathrooms: 0,
    });
  };

  const activeFiltersCount = Object.values(filters).filter(value => 
    value !== '' && value !== 0
  ).length;

  return (
    <div className="space-y-8">
      {/* Search Header */}
      <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-3xl p-8 text-white">
        <h1 className="text-3xl font-bold mb-6">Find Your Perfect Home</h1>

        {/* Main Search Bar */}
        <div className="flex items-center space-x-2 bg-white rounded-xl p-2 shadow-lg">
          <Search className="h-6 w-6 text-gray-500 ml-2" />
          <Input
            placeholder="Search for a location, title, or keyword..."
            className="flex-1 border-none focus-visible:ring-0 text-lg text-gray-800 placeholder:text-gray-400"
            value={filters.query}
            onChange={(e) => updateFilter('query', e.target.value)}
          />
        </div>
      </div>

      {/* Filter and Results Section */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar Filters */}
        <div className="w-full lg:w-1/4 space-y-4 bg-gray-50 p-6 rounded-3xl shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-gray-800">Filters</h3>
            {activeFiltersCount > 0 && (
              <Button variant="ghost" className="text-sm text-gray-500" onClick={clearFilters}>
                <X className="h-4 w-4 mr-1" />
                Clear all
              </Button>
            )}
          </div>

          <div className="space-y-4">
            {/* Location Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700">Location</label>
              <Input
                placeholder="e.g. Government Residential Area (GRA)"
                value={filters.location}
                onChange={(e) => updateFilter('location', e.target.value)}
                className="mt-1"
              />
            </div>
            
            {/* Property Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700">Property Type</label>
              <Select onValueChange={(value) => updateFilter('propertyType', value === 'any' ? '' : value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Any Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="any">Any Type</SelectItem>
                  {PROPERTY_TYPES.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Bedrooms Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700">Bedrooms</label>
              <Input
                type="number"
                placeholder="Any"
                value={filters.bedrooms > 0 ? filters.bedrooms : ''}
                onChange={(e) => updateFilter('bedrooms', parseInt(e.target.value) || 0)}
                className="mt-1"
              />
            </div>

            {/* Price Range Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700">Price Range</label>
              <div className="flex gap-2 mt-1">
                <Input
                  type="number"
                  placeholder="Min Price"
                  value={filters.minPrice > 0 ? filters.minPrice : ''}
                  onChange={(e) => updateFilter('minPrice', parseInt(e.target.value) || 0)}
                />
                <Input
                  type="number"
                  placeholder="Max Price"
                  value={filters.maxPrice > 0 ? filters.maxPrice : ''}
                  onChange={(e) => updateFilter('maxPrice', parseInt(e.target.value) || 0)}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Property Results */}
        <div className="w-full lg:w-3/4">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {isLoading ? 'Loading...' : `${totalCount} Properties Found`}
            </h2>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="bg-orange-100 text-orange-600">
                <Filter className="h-4 w-4 mr-1" />
                {activeFiltersCount} Active Filters
              </Badge>
            )}
          </div>
          
          {error && <p className="text-red-500">An error occurred: {error.message}</p>}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {properties.map((property) => (
              <Card
              key={property.id}
              className="overflow-hidden rounded-3xl shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <img
                  src={property.images[0]?.url || 'https://placehold.co/600x400/E5E7EB/A1A1AA?text=No+Image'}
                  alt={property.title}
                  className="w-full h-56 object-cover"
                />
                <CardContent className="p-4 space-y-4">
                  <h3 className="text-xl font-semibold text-gray-900">
                    {property.title}
                  </h3>

                  <div className="flex items-center text-gray-600">
                    <MapPin className="w-4 h-4 mr-2" />
                    <span className="text-sm">{property.location}</span>
                  </div>

                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Bed className="w-4 h-4 mr-1" />
                      <span>{property.bedrooms} bed</span>
                    </div>
                    <div className="flex items-center">
                      <Bath className="w-4 h-4 mr-1" />
                      <span>{property.bathrooms} bath</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-2xl font-bold text-orange-600">
                        {formatPrice(property.pricePerYear / 12)}
                      </p>
                      <p className="text-sm text-gray-500">per month</p>
                    </div>
                    <Button 
                      onClick={() => window.location.href = `/properties/${property.id}`}
                      className="bg-orange-500 hover:bg-orange-600"
                    >
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkingPropertiesSearch;
