import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MessageSquare, Mail, Phone, Send, Users, TrendingUp, Clock, CheckCircle, Zap, ArrowUpRight, Sparkles, Globe, Smartphone, AtSign, MessageCircle, Activity, Settings, RefreshCw, Filter, Target } from 'lucide-react';
import { useWhatsAppMessaging, useEmailCampaigns, useInAppChat, useSMSAlerts } from '@/hooks/useCommunication';
import Navbar from '@/components/navigation/Navbar';

interface CommunicationDashboardProps {
  userId: string;
}

const CommunicationDashboard: React.FC<CommunicationDashboardProps> = ({ userId }) => {
  const [selectedChannel, setSelectedChannel] = useState<'whatsapp' | 'email' | 'sms' | 'in_app'>('whatsapp');
  const [messageText, setMessageText] = useState('');
  const [recipientPhone, setRecipientPhone] = useState('');
  const [recipientEmail, setRecipientEmail] = useState('');

  const {
    messages: whatsappMessages,
    campaigns: whatsappCampaigns,
    sendMessage: sendWhatsAppMessage,
    createCampaign: createWhatsAppCampaign,
    isSending: isWhatsAppSending
  } = useWhatsAppMessaging();

  const {
    campaigns: emailCampaigns,
    templates: emailTemplates,
    sendTransactionalEmail,
    createCampaign: createEmailCampaign,
    isSending: isEmailSending
  } = useEmailCampaigns();

  const {
    conversation,
    messages: chatMessages,
    sendMessage: sendChatMessage,
    isSending: isChatSending
  } = useInAppChat('sample-conversation-id');

  const {
    smsMessages,
    sendSMS,
    generateOTP,
    verifyOTP,
    isSending: isSMSSending
  } = useSMSAlerts(userId);

  // Sample data for demonstration
  const communicationStats = {
    whatsapp: {
      messages_sent: 1250,
      delivery_rate: 98.5,
      response_rate: 45.2,
      active_conversations: 23
    },
    email: {
      emails_sent: 3420,
      delivery_rate: 96.8,
      open_rate: 32.1,
      click_rate: 8.7
    },
    sms: {
      messages_sent: 890,
      delivery_rate: 99.2,
      response_rate: 28.5
    },
    in_app: {
      active_conversations: 45,
      avg_response_time: 12,
      satisfaction_score: 4.6
    }
  };

  const handleSendMessage = async () => {
    if (!messageText.trim()) return;

    try {
      switch (selectedChannel) {
        case 'whatsapp':
          if (recipientPhone) {
            await sendWhatsAppMessage(recipientPhone, messageText);
          }
          break;
        case 'email':
          if (recipientEmail) {
            await sendTransactionalEmail('general_message', recipientEmail, 'User', {
              message: messageText
            });
          }
          break;
        case 'sms':
          if (recipientPhone) {
            await sendSMS(recipientPhone, messageText);
          }
          break;
        case 'in_app':
          await sendChatMessage(userId, 'text', { text: messageText });
          break;
      }
      setMessageText('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-emerald-50/20">
      <Navbar />

      {/* Hero Section with Glassmorphism */}
      <div className="relative overflow-hidden pt-16">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-r from-green-600/5 via-blue-600/5 to-purple-600/5"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-green-400/10 rounded-full blur-3xl"></div>
        <div className="absolute top-20 right-1/4 w-80 h-80 bg-blue-400/10 rounded-full blur-3xl"></div>

        <div className="relative container mx-auto px-6 pt-8 pb-12">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
            <div className="mb-6 lg:mb-0">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl shadow-lg">
                  <MessageSquare className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-green-800 to-emerald-800 bg-clip-text text-transparent">
                    Communication Center
                  </h1>
                  <p className="text-lg text-gray-600 mt-1">
                    Unified messaging across WhatsApp, Email, SMS & Chat
                  </p>
                </div>
              </div>

              {/* Status Indicators */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 px-3 py-1.5 bg-green-50 border border-green-200 rounded-full">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-green-700">All Systems Online</span>
                </div>
                <div className="flex items-center space-x-2 px-3 py-1.5 bg-blue-50 border border-blue-200 rounded-full">
                  <Zap className="h-3 w-3 text-blue-600" />
                  <span className="text-sm font-medium text-blue-700">Real-time Messaging</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap items-center gap-3">
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all duration-300 transform hover:scale-105"
              >
                <Users className="h-4 w-4 mr-2" />
                Contacts
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 transform hover:scale-105"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Analytics
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-2 border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-300 transform hover:scale-105"
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>

          {/* Enhanced Communication Stats with Glassmorphism */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
            {/* WhatsApp Stats */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-green-500/10 hover:shadow-green-500/20 transition-all duration-300 transform hover:scale-105">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl shadow-lg">
                    <MessageSquare className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex items-center space-x-1 text-green-600">
                    <ArrowUpRight className="h-4 w-4" />
                    <span className="text-sm font-medium">{communicationStats.whatsapp.delivery_rate}%</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">WhatsApp Messages</h3>
                  <p className="text-3xl font-bold text-gray-900">{communicationStats.whatsapp.messages_sent.toLocaleString()}</p>
                  <p className="text-sm text-gray-500">delivery rate</p>
                </div>
              </div>
            </div>

            {/* Email Stats */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-blue-500/10 hover:shadow-blue-500/20 transition-all duration-300 transform hover:scale-105">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                    <Mail className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex items-center space-x-1 text-blue-600">
                    <ArrowUpRight className="h-4 w-4" />
                    <span className="text-sm font-medium">{communicationStats.email.open_rate}%</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">Email Campaigns</h3>
                  <p className="text-3xl font-bold text-gray-900">{communicationStats.email.emails_sent.toLocaleString()}</p>
                  <p className="text-sm text-gray-500">open rate</p>
                </div>
              </div>
            </div>

            {/* SMS Stats */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-orange-500/10 hover:shadow-orange-500/20 transition-all duration-300 transform hover:scale-105">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl shadow-lg">
                    <Phone className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex items-center space-x-1 text-orange-600">
                    <ArrowUpRight className="h-4 w-4" />
                    <span className="text-sm font-medium">{communicationStats.sms.delivery_rate}%</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">SMS Alerts</h3>
                  <p className="text-3xl font-bold text-gray-900">{communicationStats.sms.messages_sent.toLocaleString()}</p>
                  <p className="text-sm text-gray-500">delivery rate</p>
                </div>
              </div>
            </div>

            {/* In-App Chat Stats */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-3xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
              <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-6 shadow-2xl shadow-purple-500/10 hover:shadow-purple-500/20 transition-all duration-300 transform hover:scale-105">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl shadow-lg">
                    <MessageCircle className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex items-center space-x-1 text-purple-600">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm font-medium">{communicationStats.in_app.avg_response_time}min</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-600">Active Chats</h3>
                  <p className="text-3xl font-bold text-gray-900">{communicationStats.in_app.active_conversations}</p>
                  <p className="text-sm text-gray-500">avg response time</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Communication Interface */}
      <div className="container mx-auto px-6 -mt-4 relative z-10">
        <div className="grid gap-8 md:grid-cols-3 mb-8">
          {/* Enhanced Message Composer */}
          <div className="md:col-span-2 group relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl blur opacity-10 group-hover:opacity-20 transition-opacity"></div>
            <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-8 shadow-2xl shadow-blue-500/10 hover:shadow-blue-500/20 transition-all duration-300">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl">
                    <Send className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800">Quick Message</h3>
                    <p className="text-sm text-gray-600">Send across all communication channels</p>
                  </div>
                </div>
                <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-blue-200">
                  <Sparkles className="h-3 w-3 mr-1" />
                  Multi-Channel
                </Badge>
              </div>

              <div className="space-y-6">
                {/* Channel Selection */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <Target className="h-4 w-4 mr-2 text-blue-600" />
                    Communication Channel
                  </label>
                  <Select value={selectedChannel} onValueChange={(value: any) => setSelectedChannel(value)}>
                    <SelectTrigger className="w-full bg-white/80 border-gray-200 hover:border-blue-300 transition-colors">
                      <SelectValue placeholder="Select Channel" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="whatsapp">
                        <div className="flex items-center">
                          <div className="p-1 bg-green-100 rounded mr-3">
                            <MessageSquare className="h-4 w-4 text-green-600" />
                          </div>
                          <div>
                            <span className="font-medium">WhatsApp</span>
                            <p className="text-xs text-gray-500">Business messaging</p>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="email">
                        <div className="flex items-center">
                          <div className="p-1 bg-blue-100 rounded mr-3">
                            <Mail className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <span className="font-medium">Email</span>
                            <p className="text-xs text-gray-500">Professional communication</p>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="sms">
                        <div className="flex items-center">
                          <div className="p-1 bg-orange-100 rounded mr-3">
                            <Phone className="h-4 w-4 text-orange-600" />
                          </div>
                          <div>
                            <span className="font-medium">SMS</span>
                            <p className="text-xs text-gray-500">Instant alerts</p>
                          </div>
                        </div>
                      </SelectItem>
                      <SelectItem value="in_app">
                        <div className="flex items-center">
                          <div className="p-1 bg-purple-100 rounded mr-3">
                            <MessageCircle className="h-4 w-4 text-purple-600" />
                          </div>
                          <div>
                            <span className="font-medium">In-App Chat</span>
                            <p className="text-xs text-gray-500">Real-time messaging</p>
                          </div>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Recipient Input */}
                {(selectedChannel === 'whatsapp' || selectedChannel === 'sms') && (
                  <div className="space-y-3">
                    <label className="text-sm font-medium text-gray-700 flex items-center">
                      <Smartphone className="h-4 w-4 mr-2 text-green-600" />
                      Phone Number
                    </label>
                    <Input
                      placeholder="+234 ************"
                      value={recipientPhone}
                      onChange={(e) => setRecipientPhone(e.target.value)}
                      className="bg-white/80 border-gray-200 hover:border-green-300 transition-colors"
                    />
                  </div>
                )}

                {selectedChannel === 'email' && (
                  <div className="space-y-3">
                    <label className="text-sm font-medium text-gray-700 flex items-center">
                      <AtSign className="h-4 w-4 mr-2 text-blue-600" />
                      Email Address
                    </label>
                    <Input
                      placeholder="<EMAIL>"
                      value={recipientEmail}
                      onChange={(e) => setRecipientEmail(e.target.value)}
                      className="bg-white/80 border-gray-200 hover:border-blue-300 transition-colors"
                    />
                  </div>
                )}

                {/* Message Input */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-gray-700">Message</label>
                  <Textarea
                    placeholder="Type your message here..."
                    value={messageText}
                    onChange={(e) => setMessageText(e.target.value)}
                    rows={4}
                    className="bg-white/80 border-gray-200 hover:border-gray-300 transition-colors resize-none"
                  />
                </div>

                {/* Send Button */}
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-500">
                    {messageText.length}/160 characters
                  </div>
                  <Button
                    onClick={handleSendMessage}
                    disabled={!messageText.trim() || isWhatsAppSending || isEmailSending || isSMSSending || isChatSending}
                    className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg shadow-blue-500/25 transition-all duration-300 transform hover:scale-105"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    Send Message
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Recent Activity */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-3xl blur opacity-10 group-hover:opacity-20 transition-opacity"></div>
            <div className="relative backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl p-8 shadow-2xl shadow-purple-500/10 hover:shadow-purple-500/20 transition-all duration-300">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl">
                    <Activity className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800">Recent Activity</h3>
                    <p className="text-sm text-gray-600">Live communication updates</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" className="hover:bg-purple-50">
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-4 p-3 bg-green-50/50 rounded-2xl border border-green-100">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800">WhatsApp message sent</p>
                    <p className="text-xs text-gray-500">2 minutes ago</p>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Delivered
                  </Badge>
                </div>

                <div className="flex items-center space-x-4 p-3 bg-blue-50/50 rounded-2xl border border-blue-100">
                  <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800">Email campaign launched</p>
                    <p className="text-xs text-gray-500">15 minutes ago</p>
                  </div>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-blue-200">
                    <Activity className="h-3 w-3 mr-1" />
                    Sending
                  </Badge>
                </div>

                <div className="flex items-center space-x-4 p-3 bg-orange-50/50 rounded-2xl border border-orange-100">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800">SMS alert sent</p>
                    <p className="text-xs text-gray-500">1 hour ago</p>
                  </div>
                  <Badge variant="secondary" className="bg-orange-100 text-orange-700 border-orange-200">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Delivered
                  </Badge>
                </div>

                <div className="flex items-center space-x-4 p-3 bg-purple-50/50 rounded-2xl border border-purple-100">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800">New chat message</p>
                    <p className="text-xs text-gray-500">2 hours ago</p>
                  </div>
                  <Badge variant="secondary" className="bg-purple-100 text-purple-700 border-purple-200">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Read
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Communication Channels Tabs */}
      <Tabs defaultValue="whatsapp" className="space-y-4">
        <TabsList>
          <TabsTrigger value="whatsapp">WhatsApp</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="sms">SMS</TabsTrigger>
          <TabsTrigger value="chat">In-App Chat</TabsTrigger>
        </TabsList>

        <TabsContent value="whatsapp" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Recent WhatsApp Messages</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {whatsappMessages.slice(0, 5).map((message, index) => (
                    <div key={message.id || index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{message.content?.text || 'Media message'}</p>
                        <p className="text-sm text-muted-foreground">
                          {message.direction === 'outbound' ? 'Sent' : 'Received'} • {message.timestamp}
                        </p>
                      </div>
                      <Badge variant={message.status === 'delivered' ? 'default' : 'secondary'}>
                        {message.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>WhatsApp Campaigns</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {whatsappCampaigns.slice(0, 3).map((campaign) => (
                    <div key={campaign.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{campaign.name}</h4>
                        <Badge variant={campaign.status === 'completed' ? 'default' : 'secondary'}>
                          {campaign.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{campaign.description}</p>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Recipients: {campaign.metrics.total_recipients}</span>
                        <span>Sent: {campaign.metrics.sent_count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="email" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Email Campaigns</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {emailCampaigns.slice(0, 5).map((campaign) => (
                    <div key={campaign.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{campaign.name}</h4>
                        <Badge variant={campaign.status === 'completed' ? 'default' : 'secondary'}>
                          {campaign.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{campaign.subject}</p>
                      <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                        <span>Sent: {campaign.metrics.sent_count}</span>
                        <span>Opened: {campaign.metrics.opened_count}</span>
                        <span>Clicked: {campaign.metrics.clicked_count}</span>
                        <span>Rate: {((campaign.metrics.opened_count / campaign.metrics.sent_count) * 100).toFixed(1)}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Email Templates</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {emailTemplates.slice(0, 5).map((template) => (
                    <div key={template.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{template.name}</h4>
                        <Badge variant={template.is_active ? 'default' : 'secondary'}>
                          {template.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{template.subject}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Type: {template.template_type}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sms" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Recent SMS Messages</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {smsMessages.slice(0, 5).map((message) => (
                    <div key={message.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{message.phone_number}</span>
                        <Badge variant={message.status === 'delivered' ? 'default' : 'secondary'}>
                          {message.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{message.message}</p>
                      <div className="flex justify-between text-xs text-muted-foreground mt-2">
                        <span>Type: {message.message_type}</span>
                        <span>Priority: {message.priority}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>OTP Verification</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex space-x-2">
                    <Input placeholder="Phone number" className="flex-1" />
                    <Button onClick={() => generateOTP('+2348012345678', 'phone_verification')}>
                      Send OTP
                    </Button>
                  </div>
                  <div className="flex space-x-2">
                    <Input placeholder="Verification code" className="flex-1" />
                    <Button onClick={() => verifyOTP('+2348012345678', '123456', 'phone_verification')}>
                      Verify
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="chat" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>In-App Conversations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {chatMessages.slice(0, 5).map((message) => (
                  <div key={message.id} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">User {message.sender_id}</span>
                      <Badge variant={message.status === 'read' ? 'default' : 'secondary'}>
                        {message.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{message.content?.text}</p>
                    <p className="text-xs text-muted-foreground mt-1">{message.created_at}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CommunicationDashboard;
