import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Text,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Badge,
  Button,
  Flex,
  Avatar,
  VStack,
  HStack,
  Divider,
  useColorModeValue,
  SimpleGrid,
  Icon,
  Progress,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useToast,
  Spinner,
  Center,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Wrap,
  WrapItem,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FiUsers,
  FiHome,
  FiTrendingUp,
  FiDollarSign,
  FiActivity,
  FiAlertCircle,
  FiCheckCircle,
  FiClock,
  FiEye,
  FiUserPlus,
  FiSettings,
  FiBarChart3,
  FiPieChart,
  FiCalendar,
  FiBell,
  FiSearch,
  FiFilter,
  FiDownload,
  FiRefreshCw,
  FiMoreVertical,
  FiArrowUp,
  FiArrowDown,
  FiMinus,
  FiStar,
  FiMapPin,
  FiPhone,
  FiMail,
} from 'react-icons/fi';
import { useAuth } from '@/hooks/auth/useAuth';
import { IconType } from 'react-icons';

// Motion components
const MotionBox = motion(Box);
const MotionCard = motion(Card);

interface DashboardStats {
  totalUsers: number;
  totalProperties: number;
  totalRevenue: number;
  activeListings: number;
  pendingApplications: number;
  monthlyGrowth: number;
  userGrowth: number;
  revenueGrowth: number;
  propertyGrowth: number;
}

interface RecentActivity {
  id: string;
  type: 'user_registration' | 'property_listing' | 'application' | 'payment';
  description: string;
  timestamp: Date;
  status: 'success' | 'pending' | 'failed';
  user?: {
    name: string;
    avatar?: string;
  };
}

interface QuickStat {
  label: string;
  value: string;
  change: number;
  icon: IconType;
  color: string;
  bgColor: string;
}

const ModernAdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const toast = useToast();
  const [activeTab, setActiveTab] = useState(0);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Color mode values
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.300');
  const headingColor = useColorModeValue('gray.800', 'white');
  const activityBg = useColorModeValue('gray.50', 'gray.700');
  const activityHoverBg = useColorModeValue('blue.50', 'blue.900');

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  const fetchDashboardData = async () => {
    setIsLoading(true);
    setRefreshing(true);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with actual API calls
      const mockStats: DashboardStats = {
        totalUsers: 1247,
        totalProperties: 89,
        totalRevenue: 125000000, // 125M Naira
        activeListings: 67,
        pendingApplications: 23,
        monthlyGrowth: 12.5,
        userGrowth: 8.2,
        revenueGrowth: 15.7,
        propertyGrowth: 5.3,
      };

      const mockActivity: RecentActivity[] = [
        {
          id: '1',
          type: 'user_registration',
          description: 'New premium user registered',
          timestamp: new Date(Date.now() - 1000 * 60 * 15),
          status: 'success',
          user: { name: 'John Doe', avatar: 'https://bit.ly/sage-adebayo' },
        },
        {
          id: '2',
          type: 'property_listing',
          description: 'Luxury apartment listed in Victoria Island',
          timestamp: new Date(Date.now() - 1000 * 60 * 30),
          status: 'success',
          user: { name: 'Sarah Johnson', avatar: 'https://bit.ly/kent-c-dodds' },
        },
        {
          id: '3',
          type: 'application',
          description: 'High-value rental application submitted',
          timestamp: new Date(Date.now() - 1000 * 60 * 45),
          status: 'pending',
          user: { name: 'Mike Chen', avatar: 'https://bit.ly/ryan-florence' },
        },
        {
          id: '4',
          type: 'payment',
          description: 'Payment processed: ₦2,500,000',
          timestamp: new Date(Date.now() - 1000 * 60 * 60),
          status: 'success',
          user: { name: 'Emma Wilson', avatar: 'https://bit.ly/prosper-baba' },
        },
        {
          id: '5',
          type: 'property_listing',
          description: 'Commercial property added to portfolio',
          timestamp: new Date(Date.now() - 1000 * 60 * 90),
          status: 'success',
          user: { name: 'David Brown', avatar: 'https://bit.ly/code-beast' },
        },
      ];

      setStats(mockStats);
      setRecentActivity(mockActivity);

      toast({
        title: 'Dashboard Updated',
        description: 'Latest data has been loaded successfully.',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load dashboard data. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'user_registration':
        return FiUserPlus;
      case 'property_listing':
        return FiHome;
      case 'application':
        return FiClock;
      case 'payment':
        return FiDollarSign;
      default:
        return FiActivity;
    }
  };

  const getStatusColor = (status: RecentActivity['status']) => {
    switch (status) {
      case 'success':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'failed':
        return 'red';
      default:
        return 'gray';
    }
  };

  const quickStats: QuickStat[] = stats
    ? [
        {
          label: 'Total Users',
          value: stats.totalUsers.toLocaleString(),
          change: stats.userGrowth,
          icon: FiUsers,
          color: 'blue.500',
          bgColor: 'blue.50',
        },
        {
          label: 'Properties',
          value: stats.totalProperties.toString(),
          change: stats.propertyGrowth,
          icon: FiHome,
          color: 'green.500',
          bgColor: 'green.50',
        },
        {
          label: 'Revenue',
          value: formatCurrency(stats.totalRevenue),
          change: stats.revenueGrowth,
          icon: FiDollarSign,
          color: 'purple.500',
          bgColor: 'purple.50',
        },
        {
          label: 'Active Listings',
          value: stats.activeListings.toString(),
          change: stats.monthlyGrowth,
          icon: FiTrendingUp,
          color: 'orange.500',
          bgColor: 'orange.50',
        },
      ]
    : [];

  if (isLoading && !stats) {
    return (
      <Center h="100vh" bg={bgColor}>
        <VStack spacing={4}>
          <Spinner size="xl" color="blue.500" thickness="4px" />
          <Text color={textColor}>Loading your dashboard...</Text>
        </VStack>
      </Center>
    );
  }

  return (
    <Box minH="100vh" bg={bgColor}>
      {/* Header */}
      <Box bg={cardBg} borderBottom="1px" borderColor={borderColor} shadow="sm">
        <Container maxW="7xl" py={6}>
          <Flex justify="space-between" align="center">
            <VStack align="start" spacing={1}>
              <Heading size="lg" color={headingColor}>
                Admin Dashboard
              </Heading>
              <Text color={textColor} fontSize="sm">
                Welcome back, {user?.firstName || 'Admin'} 👋
              </Text>
            </VStack>

            <HStack spacing={3}>
              <InputGroup size="md" maxW="300px">
                <InputLeftElement pointerEvents="none">
                  <Icon as={FiSearch} color="gray.400" />
                </InputLeftElement>
                <Input placeholder="Search..." bg={cardBg} />
              </InputGroup>

              <IconButton
                aria-label="Notifications"
                icon={<FiBell />}
                variant="ghost"
                position="relative"
              />

              <Button
                leftIcon={<FiRefreshCw />}
                onClick={fetchDashboardData}
                isLoading={refreshing}
                loadingText="Refreshing"
                variant="outline"
                size="sm"
              >
                Refresh
              </Button>

              <Button leftIcon={<FiDownload />} colorScheme="blue" size="sm">
                Export
              </Button>
            </HStack>
          </Flex>
        </Container>
      </Box>

      {/* Main Content */}
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Stats Grid */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
            {quickStats.map((stat, index) => (
              <MotionCard
                key={stat.label}
                bg={cardBg}
                shadow="lg"
                borderRadius="xl"
                border="1px"
                borderColor={borderColor}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                _hover={{
                  transform: 'translateY(-4px)',
                  shadow: 'xl',
                  borderColor: 'blue.200',
                }}
                cursor="pointer"
              >
                <CardBody p={6}>
                  <Flex justify="space-between" align="start">
                    <VStack align="start" spacing={2}>
                      <Text fontSize="sm" color={textColor} fontWeight="medium">
                        {stat.label}
                      </Text>
                      <Heading size="lg" color={headingColor}>
                        {stat.value}
                      </Heading>
                      <HStack spacing={1}>
                        <Icon
                          as={stat.change > 0 ? FiArrowUp : stat.change < 0 ? FiArrowDown : FiMinus}
                          color={
                            stat.change > 0 ? 'green.500' : stat.change < 0 ? 'red.500' : 'gray.500'
                          }
                          boxSize={3}
                        />
                        <Text
                          fontSize="xs"
                          color={
                            stat.change > 0 ? 'green.500' : stat.change < 0 ? 'red.500' : 'gray.500'
                          }
                          fontWeight="medium"
                        >
                          {Math.abs(stat.change)}% from last month
                        </Text>
                      </HStack>
                    </VStack>

                    <Box
                      p={3}
                      bg={stat.bgColor}
                      borderRadius="lg"
                      transition="all 0.2s"
                      _groupHover={{ transform: 'scale(1.1)' }}
                    >
                      <Icon as={stat.icon} boxSize={6} color={stat.color} />
                    </Box>
                  </Flex>
                </CardBody>
              </MotionCard>
            ))}
          </SimpleGrid>

          {/* Main Dashboard Content */}
          <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={8}>
            {/* Recent Activity */}
            <MotionCard
              bg={cardBg}
              shadow="lg"
              borderRadius="xl"
              border="1px"
              borderColor={borderColor}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <CardHeader pb={4}>
                <Flex justify="space-between" align="center">
                  <HStack spacing={3}>
                    <Icon as={FiActivity} color="blue.500" boxSize={5} />
                    <Heading size="md" color={headingColor}>
                      Recent Activity
                    </Heading>
                  </HStack>
                  <Menu>
                    <MenuButton
                      as={IconButton}
                      icon={<FiMoreVertical />}
                      variant="ghost"
                      size="sm"
                    />
                    <MenuList>
                      <MenuItem>View All</MenuItem>
                      <MenuItem>Export</MenuItem>
                      <MenuItem>Filter</MenuItem>
                    </MenuList>
                  </Menu>
                </Flex>
              </CardHeader>
              <CardBody pt={0}>
                <VStack spacing={4} align="stretch">
                  {recentActivity.map((activity, index) => (
                    <MotionBox
                      key={activity.id}
                      p={4}
                      bg={activityBg}
                      borderRadius="lg"
                      border="1px"
                      borderColor={borderColor}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                      _hover={{
                        bg: activityHoverBg,
                        borderColor: 'blue.200',
                        transform: 'translateX(4px)',
                      }}
                      cursor="pointer"
                    >
                      <Flex align="start" gap={4}>
                        <Avatar
                          size="sm"
                          src={activity.user?.avatar}
                          name={activity.user?.name}
                          bg={`${getStatusColor(activity.status)}.500`}
                        />
                        <Box flex={1}>
                          <Flex justify="space-between" align="start" mb={1}>
                            <Text fontSize="sm" fontWeight="medium" color={headingColor}>
                              {activity.description}
                            </Text>
                            <Badge
                              colorScheme={getStatusColor(activity.status)}
                              variant="subtle"
                              borderRadius="full"
                              px={2}
                              py={1}
                              fontSize="xs"
                            >
                              {activity.status}
                            </Badge>
                          </Flex>
                          <HStack spacing={2} fontSize="xs" color={textColor}>
                            <Icon as={getActivityIcon(activity.type)} />
                            <Text>{activity.user?.name}</Text>
                            <Text>•</Text>
                            <Text>{formatTimeAgo(activity.timestamp)}</Text>
                          </HStack>
                        </Box>
                      </Flex>
                    </MotionBox>
                  ))}
                </VStack>
                <Divider my={4} />
                <Button variant="ghost" size="sm" width="full" color="blue.500">
                  View All Activity
                </Button>
              </CardBody>
            </MotionCard>

            {/* Quick Actions & System Status */}
            <VStack spacing={6} align="stretch">
              {/* Quick Actions */}
              <MotionCard
                bg={cardBg}
                shadow="lg"
                borderRadius="xl"
                border="1px"
                borderColor={borderColor}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                <CardHeader pb={4}>
                  <HStack spacing={3}>
                    <Icon as={FiSettings} color="purple.500" boxSize={5} />
                    <Heading size="md" color={headingColor}>
                      Quick Actions
                    </Heading>
                  </HStack>
                </CardHeader>
                <CardBody pt={0}>
                  <SimpleGrid columns={2} spacing={3}>
                    <Button
                      leftIcon={<FiUserPlus />}
                      variant="outline"
                      size="sm"
                      height="60px"
                      flexDirection="column"
                      gap={1}
                      _hover={{
                        bg: 'blue.50',
                        borderColor: 'blue.300',
                        transform: 'translateY(-2px)',
                      }}
                    >
                      <Text fontSize="xs">Add User</Text>
                    </Button>
                    <Button
                      leftIcon={<FiHome />}
                      variant="outline"
                      size="sm"
                      height="60px"
                      flexDirection="column"
                      gap={1}
                      _hover={{
                        bg: 'green.50',
                        borderColor: 'green.300',
                        transform: 'translateY(-2px)',
                      }}
                    >
                      <Text fontSize="xs">Add Property</Text>
                    </Button>
                    <Button
                      leftIcon={<FiBarChart3 />}
                      variant="outline"
                      size="sm"
                      height="60px"
                      flexDirection="column"
                      gap={1}
                      _hover={{
                        bg: 'purple.50',
                        borderColor: 'purple.300',
                        transform: 'translateY(-2px)',
                      }}
                    >
                      <Text fontSize="xs">Reports</Text>
                    </Button>
                    <Button
                      leftIcon={<FiSettings />}
                      variant="outline"
                      size="sm"
                      height="60px"
                      flexDirection="column"
                      gap={1}
                      _hover={{
                        bg: 'orange.50',
                        borderColor: 'orange.300',
                        transform: 'translateY(-2px)',
                      }}
                    >
                      <Text fontSize="xs">Settings</Text>
                    </Button>
                  </SimpleGrid>
                </CardBody>
              </MotionCard>

              {/* System Status */}
              <MotionCard
                bg={cardBg}
                shadow="lg"
                borderRadius="xl"
                border="1px"
                borderColor={borderColor}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8 }}
              >
                <CardHeader pb={4}>
                  <HStack spacing={3}>
                    <Icon as={FiCheckCircle} color="green.500" boxSize={5} />
                    <Heading size="md" color={headingColor}>
                      System Status
                    </Heading>
                  </HStack>
                </CardHeader>
                <CardBody pt={0}>
                  <VStack spacing={4} align="stretch">
                    <Box>
                      <Flex justify="space-between" align="center" mb={2}>
                        <Text fontSize="sm" color={textColor}>
                          API Server
                        </Text>
                        <Badge colorScheme="green" variant="subtle">
                          Online
                        </Badge>
                      </Flex>
                      <Progress value={98} colorScheme="green" size="sm" borderRadius="full" />
                    </Box>
                    <Box>
                      <Flex justify="space-between" align="center" mb={2}>
                        <Text fontSize="sm" color={textColor}>
                          Database
                        </Text>
                        <Badge colorScheme="green" variant="subtle">
                          Healthy
                        </Badge>
                      </Flex>
                      <Progress value={95} colorScheme="green" size="sm" borderRadius="full" />
                    </Box>
                    <Box>
                      <Flex justify="space-between" align="center" mb={2}>
                        <Text fontSize="sm" color={textColor}>
                          Storage
                        </Text>
                        <Badge colorScheme="yellow" variant="subtle">
                          Warning
                        </Badge>
                      </Flex>
                      <Progress value={78} colorScheme="yellow" size="sm" borderRadius="full" />
                    </Box>
                    <Box>
                      <Flex justify="space-between" align="center" mb={2}>
                        <Text fontSize="sm" color={textColor}>
                          CDN
                        </Text>
                        <Badge colorScheme="green" variant="subtle">
                          Optimal
                        </Badge>
                      </Flex>
                      <Progress value={99} colorScheme="green" size="sm" borderRadius="full" />
                    </Box>
                  </VStack>
                  <Divider my={4} />
                  <Button variant="ghost" size="sm" width="full" color="blue.500">
                    View Detailed Status
                  </Button>
                </CardBody>
              </MotionCard>
            </VStack>
          </Grid>
        </VStack>
      </Container>
    </Box>
  );
};

export default ModernAdminDashboard;
