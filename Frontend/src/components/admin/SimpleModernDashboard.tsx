import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Users,
  Home,
  TrendingUp,
  DollarSign,
  Activity,
  CheckCircle,
  Clock,
  UserPlus,
  Settings,
  BarChart3,
  RefreshCw,
  Download,
  Bell,
  Search,
  ArrowUp,
  ArrowDown,
  Eye,
  AlertTriangle,
} from 'lucide-react';
import { useAuth } from '@/hooks/auth/useAuth';

interface DashboardStats {
  totalUsers: number;
  totalProperties: number;
  totalRevenue: number;
  activeListings: number;
  pendingApplications: number;
  monthlyGrowth: number;
  userGrowth: number;
  revenueGrowth: number;
  propertyGrowth: number;
}

interface RecentActivity {
  id: string;
  type: 'user_registration' | 'property_listing' | 'application' | 'payment';
  description: string;
  timestamp: Date;
  status: 'success' | 'pending' | 'failed';
  user?: {
    name: string;
    avatar?: string;
  };
}

const SimpleModernDashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setIsLoading(true);
    setRefreshing(true);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data - replace with actual API calls
      const mockStats: DashboardStats = {
        totalUsers: 1247,
        totalProperties: 89,
        totalRevenue: 125000000, // 125M Naira
        activeListings: 67,
        pendingApplications: 23,
        monthlyGrowth: 12.5,
        userGrowth: 8.2,
        revenueGrowth: 15.7,
        propertyGrowth: 5.3,
      };

      const mockActivity: RecentActivity[] = [
        {
          id: '1',
          type: 'user_registration',
          description: 'New premium user registered',
          timestamp: new Date(Date.now() - 1000 * 60 * 15),
          status: 'success',
          user: { name: 'John Doe' },
        },
        {
          id: '2',
          type: 'property_listing',
          description: 'Luxury apartment listed in Victoria Island',
          timestamp: new Date(Date.now() - 1000 * 60 * 30),
          status: 'success',
          user: { name: 'Sarah Johnson' },
        },
        {
          id: '3',
          type: 'application',
          description: 'High-value rental application submitted',
          timestamp: new Date(Date.now() - 1000 * 60 * 45),
          status: 'pending',
          user: { name: 'Mike Chen' },
        },
        {
          id: '4',
          type: 'payment',
          description: 'Payment processed: ₦2,500,000',
          timestamp: new Date(Date.now() - 1000 * 60 * 60),
          status: 'success',
          user: { name: 'Emma Wilson' },
        },
      ];

      setStats(mockStats);
      setRecentActivity(mockActivity);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'user_registration':
        return UserPlus;
      case 'property_listing':
        return Home;
      case 'application':
        return Clock;
      case 'payment':
        return DollarSign;
      default:
        return Activity;
    }
  };

  const getStatusColor = (status: RecentActivity['status']) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading && !stats) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600 text-lg">Loading your modern dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Modern Admin Dashboard
              </h1>
              <p className="text-gray-600 mt-1">Welcome back, {user?.firstName || 'Admin'} 👋</p>
            </div>

            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <Button variant="outline" size="sm" className="relative">
                <Bell className="h-4 w-4" />
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
              </Button>

              <Button
                onClick={fetchDashboardData}
                disabled={refreshing}
                variant="outline"
                size="sm"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>

              <Button
                size="sm"
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Total Users */}
            <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600">Total Users</p>
                    <p className="text-3xl font-bold text-blue-900">
                      {stats.totalUsers.toLocaleString()}
                    </p>
                    <div className="flex items-center mt-2">
                      <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs text-green-600 font-medium">
                        {stats.userGrowth}% from last month
                      </span>
                    </div>
                  </div>
                  <div className="p-3 bg-blue-500 rounded-full">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Properties */}
            <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600">Properties</p>
                    <p className="text-3xl font-bold text-green-900">{stats.totalProperties}</p>
                    <div className="flex items-center mt-2">
                      <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs text-green-600 font-medium">
                        {stats.propertyGrowth}% from last month
                      </span>
                    </div>
                  </div>
                  <div className="p-3 bg-green-500 rounded-full">
                    <Home className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Revenue */}
            <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600">Revenue</p>
                    <p className="text-3xl font-bold text-purple-900">
                      {formatCurrency(stats.totalRevenue)}
                    </p>
                    <div className="flex items-center mt-2">
                      <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs text-green-600 font-medium">
                        {stats.revenueGrowth}% from last month
                      </span>
                    </div>
                  </div>
                  <div className="p-3 bg-purple-500 rounded-full">
                    <DollarSign className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Active Listings */}
            <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600">Active Listings</p>
                    <p className="text-3xl font-bold text-orange-900">{stats.activeListings}</p>
                    <div className="flex items-center mt-2">
                      <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs text-green-600 font-medium">
                        {stats.monthlyGrowth}% from last month
                      </span>
                    </div>
                  </div>
                  <div className="p-3 bg-orange-500 rounded-full">
                    <TrendingUp className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Dashboard Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-3">
                    <Activity className="h-5 w-5 text-blue-500" />
                    Recent Activity
                  </CardTitle>
                  <Button variant="ghost" size="sm">
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => {
                    const IconComponent = getActivityIcon(activity.type);
                    return (
                      <div
                        key={activity.id}
                        className="flex items-start gap-4 p-4 rounded-lg bg-gray-50 hover:bg-blue-50 transition-colors duration-200 cursor-pointer"
                      >
                        <div className="p-2 bg-white rounded-full shadow-sm">
                          <IconComponent className="h-4 w-4 text-gray-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">
                            {activity.description}
                          </p>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-xs text-gray-500">{activity.user?.name}</span>
                            <span className="text-xs text-gray-400">•</span>
                            <span className="text-xs text-gray-500">
                              {formatTimeAgo(activity.timestamp)}
                            </span>
                          </div>
                        </div>
                        <Badge className={getStatusColor(activity.status)}>{activity.status}</Badge>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <Settings className="h-5 w-5 text-purple-500" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    className="h-16 flex-col gap-2 hover:bg-blue-50 hover:border-blue-300"
                  >
                    <UserPlus className="h-5 w-5" />
                    <span className="text-xs">Add User</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-16 flex-col gap-2 hover:bg-green-50 hover:border-green-300"
                  >
                    <Home className="h-5 w-5" />
                    <span className="text-xs">Add Property</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-16 flex-col gap-2 hover:bg-purple-50 hover:border-purple-300"
                  >
                    <BarChart3 className="h-5 w-5" />
                    <span className="text-xs">Reports</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-16 flex-col gap-2 hover:bg-orange-50 hover:border-orange-300"
                  >
                    <Settings className="h-5 w-5" />
                    <span className="text-xs">Settings</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* System Status */}
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  System Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">API Server</span>
                      <Badge className="bg-green-100 text-green-800">Online</Badge>
                    </div>
                    <Progress value={98} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">Database</span>
                      <Badge className="bg-green-100 text-green-800">Healthy</Badge>
                    </div>
                    <Progress value={95} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">Storage</span>
                      <Badge className="bg-yellow-100 text-yellow-800">Warning</Badge>
                    </div>
                    <Progress value={78} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">CDN</span>
                      <Badge className="bg-green-100 text-green-800">Optimal</Badge>
                    </div>
                    <Progress value={99} className="h-2" />
                  </div>
                </div>
                <Button variant="ghost" size="sm" className="w-full mt-4 text-blue-600">
                  View Detailed Status
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleModernDashboard;
