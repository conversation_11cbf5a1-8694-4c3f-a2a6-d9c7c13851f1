/**
 * Simplified Admin Agent Management Component
 *
 * Basic version to test if the component renders without issues.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ApiService } from '@/services/apiService';
import { useToast } from '@/hooks/use-toast';
import { useSimpleErrorHandler } from '@/hooks/useSimpleErrorHandler';
import { simpleWebSocket } from '@/services/simpleWebSocket';
import {
  Users,
  Search,
  Download,
  Filter,
  UserCheck,
  UserX,
  Eye,
  MoreHorizontal,
  MapPin,
  Phone,
  Mail,
  Star,
} from 'lucide-react';

interface SimpleAgent {
  id: string;
  agent_id: string;
  full_name: string;
  email: string;
  whatsapp_number: string;
  operating_areas: string[];
  verification_status: 'verified' | 'pending' | 'rejected' | 'suspended';
  total_properties: number;
  total_earnings: number;
  client_rating: number;
}

const AdminAgentManagementSimple: React.FC = () => {
  const [agents, setAgents] = useState<SimpleAgent[]>([]);
  const [filteredAgents, setFilteredAgents] = useState<SimpleAgent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);

  const { toast } = useToast();
  const {
    executeApiCall,
    isLoading: apiLoading,
    error,
  } = useSimpleErrorHandler('AdminAgentManagement');

  useEffect(() => {
    loadAgents();
    initializeWebSocket();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const initializeWebSocket = async () => {
    try {
      await simpleWebSocket.connect();
      simpleWebSocket.subscribeToAgentUpdates();

      // Listen for agent updates
      const handleAgentUpdate = (update: any) => {
        console.log('Agent update received:', update);
        // Refresh agents list when updates are received
        loadAgents();

        toast({
          title: 'Agent Updated',
          description: `Agent ${update.agentName || 'information'} has been updated`,
        });
      };

      simpleWebSocket.on('agentUpdate', handleAgentUpdate);

      return () => {
        simpleWebSocket.off('agentUpdate', handleAgentUpdate);
      };
    } catch (error) {
      console.log('WebSocket connection failed for agent updates:', error);
    }
  };

  const loadAgents = async () => {
    setLoading(true);

    const result = await executeApiCall(
      async () => {
        // Try to get real agent data, fallback to mock data
        try {
          const response = await ApiService.getAgents();
          return response;
        } catch (error) {
          // Fallback to mock data if API fails
          console.log('Using mock data for agents');
          return [
            {
              id: '1',
              agent_id: 'AGT001',
              full_name: 'John Doe',
              email: '<EMAIL>',
              whatsapp_number: '+2348012345678',
              operating_areas: ['Victoria Island', 'Ikoyi'],
              verification_status: 'verified',
              total_properties: 25,
              total_earnings: 2500000,
              client_rating: 4.8,
            },
            {
              id: '2',
              agent_id: 'AGT002',
              full_name: 'Jane Smith',
              email: '<EMAIL>',
              whatsapp_number: '+2348087654321',
              operating_areas: ['Lekki', 'Ajah'],
              verification_status: 'pending',
              total_properties: 18,
              total_earnings: 1800000,
              client_rating: 4.6,
            },
            {
              id: '3',
              agent_id: 'AGT003',
              full_name: 'Mike Wilson',
              email: '<EMAIL>',
              whatsapp_number: '+2348098765432',
              operating_areas: ['Surulere', 'Yaba'],
              verification_status: 'suspended',
              total_properties: 12,
              total_earnings: 1200000,
              client_rating: 4.2,
            },
          ];
        }
      },
      'loadAgents',
      { retryable: true, maxRetries: 2 }
    );

    if (result) {
      setAgents(result);
    }
    setLoading(false);
  };

  // Filter agents based on search and status
  useEffect(() => {
    let filtered = agents;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(
        agent =>
          agent.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          agent.agent_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
          agent.operating_areas.some(area => area.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(agent => agent.verification_status === statusFilter);
    }

    setFilteredAgents(filtered);
  }, [agents, searchTerm, statusFilter]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      verified: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      rejected: 'bg-red-100 text-red-800',
      suspended: 'bg-gray-100 text-gray-800',
    };

    const color = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return <Badge className={color}>{status.charAt(0).toUpperCase() + status.slice(1)}</Badge>;
  };

  const handleStatusUpdate = async (agentId: string, newStatus: string) => {
    const result = await executeApiCall(async () => {
      // In real implementation, this would call the API
      console.log(`Updating agent ${agentId} status to ${newStatus}`);
      return { success: true };
    }, 'updateAgentStatus');

    if (result) {
      // Update local state
      setAgents(prev =>
        prev.map(agent =>
          agent.id === agentId ? { ...agent, verification_status: newStatus as any } : agent
        )
      );

      toast({
        title: 'Status Updated',
        description: `Agent status has been updated to ${newStatus}`,
      });
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedAgents.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select agents to perform bulk actions',
        variant: 'destructive',
      });
      return;
    }

    const result = await executeApiCall(async () => {
      console.log(`Performing bulk action: ${action} on agents:`, selectedAgents);
      return { success: true };
    }, 'bulkAction');

    if (result) {
      toast({
        title: 'Bulk Action Completed',
        description: `${action} applied to ${selectedAgents.length} agents`,
      });
      setSelectedAgents([]);
      loadAgents(); // Reload data
    }
  };

  const exportAgents = () => {
    const csvContent = [
      ['Agent ID', 'Name', 'Email', 'Phone', 'Status', 'Properties', 'Earnings', 'Rating'],
      ...filteredAgents.map(agent => [
        agent.agent_id,
        agent.full_name,
        agent.email,
        agent.whatsapp_number,
        agent.verification_status,
        agent.total_properties.toString(),
        agent.total_earnings.toString(),
        agent.client_rating.toString(),
      ]),
    ]
      .map(row => row.join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `agents-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);

    toast({
      title: 'Export Complete',
      description: `Exported ${filteredAgents.length} agents to CSV`,
    });
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Agent Management</h2>
          <p className="text-gray-600">Manage and monitor all verified agents</p>
        </div>

        <div className="flex items-center gap-4">
          {selectedAgents.length > 0 && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('verify')}
                className="flex items-center gap-2"
              >
                <UserCheck className="w-4 h-4" />
                Verify ({selectedAgents.length})
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('suspend')}
                className="flex items-center gap-2"
              >
                <UserX className="w-4 h-4" />
                Suspend ({selectedAgents.length})
              </Button>
            </div>
          )}

          <Button variant="outline" onClick={exportAgents} className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Agents</p>
                <p className="text-2xl font-bold">{agents.length}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Verified</p>
                <p className="text-2xl font-bold text-green-600">
                  {agents.filter(a => a.verification_status === 'verified').length}
                </p>
              </div>
              <Users className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {agents.filter(a => a.verification_status === 'pending').length}
                </p>
              </div>
              <Users className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Suspended</p>
                <p className="text-2xl font-bold text-red-600">
                  {agents.filter(a => a.verification_status === 'suspended').length}
                </p>
              </div>
              <Users className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search agents by name, email, ID, or location..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Filter className="w-4 h-4 text-gray-500" />
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="verified">Verified</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={loadAgents}
                disabled={loading || apiLoading}
              >
                Refresh
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Agents List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={
                    selectedAgents.length === filteredAgents.length && filteredAgents.length > 0
                  }
                  onChange={e => {
                    if (e.target.checked) {
                      setSelectedAgents(filteredAgents.map(agent => agent.id));
                    } else {
                      setSelectedAgents([]);
                    }
                  }}
                  className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <span>Agents ({filteredAgents.length})</span>
              </div>

              {error && (
                <div className="text-sm text-red-600 bg-red-50 px-2 py-1 rounded">{error}</div>
              )}
            </div>

            <div className="flex items-center gap-2">
              {(loading || apiLoading) && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
              {selectedAgents.length > 0 && (
                <span className="text-sm text-blue-600">{selectedAgents.length} selected</span>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="animate-pulse">
                  <div className="flex items-center space-x-4 p-4 border rounded-lg">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                    </div>
                    <div className="w-20 h-6 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredAgents.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No agents found</h3>
              <p className="text-gray-600">Try adjusting your search criteria.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAgents.map(agent => (
                <div
                  key={agent.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedAgents.includes(agent.id)}
                      onChange={e => {
                        if (e.target.checked) {
                          setSelectedAgents(prev => [...prev, agent.id]);
                        } else {
                          setSelectedAgents(prev => prev.filter(id => id !== agent.id));
                        }
                      }}
                      className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                    />

                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                      {agent.full_name
                        .split(' ')
                        .map(n => n[0])
                        .join('')
                        .toUpperCase()}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {agent.full_name}
                        </h3>
                        <span className="text-xs text-gray-500">({agent.agent_id})</span>
                        {getStatusBadge(agent.verification_status)}
                      </div>

                      <div className="flex items-center gap-4 text-xs text-gray-600">
                        <span className="flex items-center gap-1">
                          <Mail className="w-3 h-3" />
                          {agent.email}
                        </span>
                        <span className="flex items-center gap-1">
                          <Phone className="w-3 h-3" />
                          {agent.whatsapp_number}
                        </span>
                        <span className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {agent.operating_areas.join(', ')}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-6 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{agent.total_properties}</div>
                        <p className="text-xs text-gray-500">Properties</p>
                      </div>

                      <div className="text-center">
                        <div className="font-medium">
                          ₦{(agent.total_earnings / 1000000).toFixed(1)}M
                        </div>
                        <p className="text-xs text-gray-500">Earnings</p>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 text-yellow-500 fill-current" />
                          <span className="font-medium">{agent.client_rating.toFixed(1)}</span>
                        </div>
                        <p className="text-xs text-gray-500">Rating</p>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleStatusUpdate(
                              agent.id,
                              agent.verification_status === 'verified' ? 'suspended' : 'verified'
                            )
                          }
                          disabled={apiLoading}
                        >
                          {agent.verification_status === 'verified' ? (
                            <>
                              <UserX className="w-3 h-3 mr-1" />
                              Suspend
                            </>
                          ) : (
                            <>
                              <UserCheck className="w-3 h-3 mr-1" />
                              Verify
                            </>
                          )}
                        </Button>

                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminAgentManagementSimple;
