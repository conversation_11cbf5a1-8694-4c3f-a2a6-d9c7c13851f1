import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Shield, Crown, Mail } from 'lucide-react';
import { findUserByEmail, updateUserRole } from '@/services/userService';
import { useAuth } from '@/hooks/auth/useAuth';

// Email validation regex
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

const AdminPromotion = () => {
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<'admin' | 'super_admin'>('admin');
  const [loading, setLoading] = useState(false);
  const [emailError, setEmailError] = useState('');
  const { toast } = useToast();
  const { user, token } = useAuth();

  const validateEmail = (email: string): boolean => {
    if (!email.trim()) {
      setEmailError('Email address is required');
      return false;
    }
    if (!EMAIL_REGEX.test(email)) {
      setEmailError('Please enter a valid email address');
      return false;
    }
    setEmailError('');
    return true;
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (emailError && value.trim()) {
      validateEmail(value);
    }
  };

  const handlePromoteUser = async () => {
    if (!validateEmail(email)) {
      return;
    }
    
    if (!user || !token) {
      toast({
        title: "Error",
        description: "You must be logged in as admin",
        variant: "destructive"
      });
      return;
    }
    setLoading(true);
    try {
      // 1. Find user by email
      const foundUser = await findUserByEmail(email, token);
      console.log('User to promote', foundUser);
      if (!foundUser) {
        toast({
          title: "Error",
          description: "User not found",
          variant: "destructive"
        });
        console.log('User to promote not found');
        return;
      }
      // 2. Update user role
      await updateUserRole(foundUser.id, role, token);
      toast({
        title: "Success",
        description: `User promoted to ${role} successfully`,
      });
      setEmail('');
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to promote user",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Crown className="h-5 w-5" />
          Promote User
        </CardTitle>
        <CardDescription>
          Grant admin or super admin privileges to users
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Email Address
          </label>
          <Input
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => handleEmailChange(e.target.value)}
            className={emailError ? "border-red-500" : ""}
          />
          {emailError && (
            <p className="text-sm text-red-500 mt-1">{emailError}</p>
          )}
        </div>
        
        <div className="space-y-2">
          <label className="text-sm font-medium">Role</label>
          <Select value={role} onValueChange={(value: 'admin' | 'super_admin') => setRole(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="admin">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Admin
                </div>
              </SelectItem>
              <SelectItem value="super_admin">
                <div className="flex items-center gap-2">
                  <Crown className="h-4 w-4" />
                  Super Admin
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button 
          onClick={handlePromoteUser} 
          disabled={loading}
          className="w-full"
        >
          {loading ? 'Promoting...' : 'Promote User'}
        </Button>
      </CardContent>
    </Card>
  );
};

export default AdminPromotion;