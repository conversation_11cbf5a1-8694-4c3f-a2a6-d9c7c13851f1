import React, { useEffect, useState } from 'react';
import { fetchUsers, updateUserRole, deactivateUser, activateUser, deleteUser } from '@/services/userService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/auth/useAuth';

const PAGE_SIZE = 20;

const AdminUsersList = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [users, setUsers] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(false);
  const [selected, setSelected] = useState<string[]>([]);
  const [editingRoleId, setEditingRoleId] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [modalUser, setModalUser] = useState<any | null>(null);

  const isAllSelected = users.length > 0 && selected.length === users.length;
  const toggleSelectAll = () => {
    if (isAllSelected) setSelected([]);
    else setSelected(users.map(u => u.id));
  };
  const toggleSelect = (id: string) => {
    setSelected(sel => sel.includes(id) ? sel.filter(sid => sid !== id) : [...sel, id]);
  };

  const clearSelection = () => setSelected([]);

  // Bulk actions
  const handleBulkPromote = async () => {
    if (!user || !token || selected.length === 0) return;
    const role = window.prompt('Enter role to promote to (admin or super_admin):', 'admin');
    if (!role || (role !== 'admin' && role !== 'super_admin')) {
      toast({ title: 'Error', description: 'Invalid role selected', variant: 'destructive' });
      return;
    }
    try {
      await Promise.all(selected.map(id => updateUserRole(id, role, token)));
      toast({ title: 'Success', description: `Selected users promoted to ${role}.` });
      loadUsers();
      clearSelection();
    } catch (error: any) {
      toast({ title: 'Error', description: error.message || 'Failed to promote users', variant: 'destructive' });
    }
  };
  const handleBulkDeactivate = async () => {
    if (!user || !token || selected.length === 0) return;
    try {
      await Promise.all(selected.map(id => deactivateUser(id, token)));
      toast({ title: 'Success', description: 'Selected users deactivated.' });
      loadUsers();
      clearSelection();
    } catch (error: any) {
      toast({ title: 'Error', description: error.message || 'Failed to deactivate users', variant: 'destructive' });
    }
  };
  const handleBulkActivate = async () => {
    if (!user || !token || selected.length === 0) return;
    try {
      await Promise.all(selected.map(id => activateUser(id, token)));
      toast({ title: 'Success', description: 'Selected users activated.' });
      loadUsers();
      clearSelection();
    } catch (error: any) {
      toast({ title: 'Error', description: error.message || 'Failed to activate users', variant: 'destructive' });
    }
  };
  const handleBulkDelete = async () => {
    if (!user || !token || selected.length === 0) return;
    if (!window.confirm('Are you sure you want to delete the selected users? This action cannot be undone.')) return;
    try {
      await Promise.all(selected.map(id => deleteUser(id, token)));
      toast({ title: 'Success', description: 'Selected users deleted.' });
      loadUsers();
      clearSelection();
    } catch (error: any) {
      toast({ title: 'Error', description: error.message || 'Failed to delete users', variant: 'destructive' });
    }
  };

  const token = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : '';

  const loadUsers = async () => {
    if (!user || !token) return;
    setLoading(true);
    try {
      const data = await fetchUsers({ page, limit: PAGE_SIZE, search, token });
      setUsers(data?.data || []);
      setTotal(data.meta?.total || 0);
    } catch (error: any) {
      toast({ title: 'Error', description: error.message || 'Failed to load users', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, search]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    setPage(1);
  };

  const handlePromote = async (userId: string) => {
    if (!token) return;
    // Prompt for role selection
    const role = window.prompt('Enter role to promote to (admin or super_admin):', 'admin');
    if (!role || (role !== 'admin' && role !== 'super_admin')) {
      toast({ title: 'Error', description: 'Invalid role selected', variant: 'destructive' });
      return;
    }
    try {
      await updateUserRole(userId, role, token);
      toast({ title: 'Success', description: `User promoted to ${role}.` });
      loadUsers();
    } catch (error: any) {
      toast({ title: 'Error', description: error.message || 'Failed to promote user', variant: 'destructive' });
    }
  };

  const handleDeactivate = async (userId: string) => {
    if (!token) return;
    try {
      await deactivateUser(userId, token);
      toast({ title: 'Success', description: 'User deactivated.' });
      loadUsers();
    } catch (error: any) {
      toast({ title: 'Error', description: error.message || 'Failed to deactivate user', variant: 'destructive' });
    }
  };

  const handleActivate = async (userId: string) => {
    if (!token) return;
    try {
      await activateUser(userId, token);
      toast({ title: 'Success', description: 'User activated.' });
      loadUsers();
    } catch (error: any) {
      toast({ title: 'Error', description: error.message || 'Failed to activate user', variant: 'destructive' });
    }
  };

  const handleDelete = async (userId: string) => {
    if (!token) return;
    if (!window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) return;
    try {
      await deleteUser(userId, token);
      toast({ title: 'Success', description: 'User deleted.' });
      loadUsers();
    } catch (error: any) {
      toast({ title: 'Error', description: error.message || 'Failed to delete user', variant: 'destructive' });
    }
  };

  const handleRoleChange = async (userId: string, newRole: string) => {
    if (!token) return;
    try {
      await updateUserRole(userId, newRole as any, token);
      toast({ title: 'Success', description: `Role updated to ${newRole}.` });
      setEditingRoleId(null);
      loadUsers();
    } catch (error: any) {
      toast({ title: 'Error', description: error.message || 'Failed to update role', variant: 'destructive' });
    }
  };

  // Filter users client-side
  const filteredUsers = users.filter(u => {
    const statusMatch = statusFilter === 'all' || (statusFilter === 'active' ? u.isActive : !u.isActive);
    const roleMatch = roleFilter === 'all' || u.role === roleFilter;
    return statusMatch && roleMatch;
  });

  const openModal = (user: any, e: React.MouseEvent) => {
    // Prevent modal on checkbox or action button click
    if ((e.target as HTMLElement).tagName === 'INPUT' || (e.target as HTMLElement).tagName === 'BUTTON' || (e.target as HTMLElement).tagName === 'SELECT') return;
    setModalUser(user);
  };
  const closeModal = () => setModalUser(null);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>All Users</CardTitle>
      </CardHeader>
      <CardContent>
        {selected.length > 0 && (
          <div className="mb-4 flex flex-wrap gap-2 items-center bg-orange-50 border border-orange-200 rounded-lg p-2">
            <span className="font-medium">{selected.length} selected</span>
            <Button size="sm" variant="outline" onClick={handleBulkPromote}>Promote</Button>
            <Button size="sm" variant="outline" onClick={handleBulkDeactivate}>Deactivate</Button>
            <Button size="sm" variant="outline" onClick={handleBulkActivate}>Activate</Button>
            <Button size="sm" variant="destructive" onClick={handleBulkDelete}>Delete</Button>
            <Button size="sm" variant="ghost" onClick={clearSelection}>Clear</Button>
          </div>
        )}
        <div className="flex items-center mb-4 gap-2">
          <Input
            placeholder="Search by name or email"
            value={search}
            onChange={handleSearchChange}
            className="max-w-xs"
          />
          <select value={statusFilter} onChange={e => setStatusFilter(e.target.value)} className="border rounded px-2 py-1">
            <option value="all">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
          <select value={roleFilter} onChange={e => setRoleFilter(e.target.value)} className="border rounded px-2 py-1">
            <option value="all">All Roles</option>
            <option value="admin">admin</option>
            <option value="super_admin">super_admin</option>
            <option value="agent">agent</option>
            <option value="landlord">landlord</option>
            <option value="tenant">tenant</option>
          </select>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm border">
            <thead>
              <tr className="bg-gray-100">
                <th className="p-2 border">
                  <input type="checkbox" checked={isAllSelected} onChange={toggleSelectAll} />
                </th>
                <th className="p-2 border">Name</th>
                <th className="p-2 border">Email</th>
                <th className="p-2 border">Role</th>
                <th className="p-2 border">Status</th>
                <th className="p-2 border">Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr><td colSpan={6} className="text-center p-4">Loading...</td></tr>
              ) : filteredUsers.length === 0 ? (
                <tr><td colSpan={6} className="text-center p-4">No users found.</td></tr>
              ) : (
                filteredUsers.map((u) => (
                  <tr key={u.id} onClick={e => openModal(u, e)} style={{ cursor: 'pointer' }}>
                    <td className="p-2 border text-center">
                      <input type="checkbox" checked={selected.includes(u.id)} onChange={() => toggleSelect(u.id)} onClick={e => e.stopPropagation()} />
                    </td>
                    <td className="p-2 border">{u.fullName || `${u.firstName} ${u.lastName}`}</td>
                    <td className="p-2 border">{u.email}</td>
                    <td className="p-2 border capitalize" onClick={e => { e.stopPropagation(); setEditingRoleId(u.id); }} style={{ cursor: 'pointer', minWidth: 120 }}>
                      {editingRoleId === u.id ? (
                        <select
                          value={u.role}
                          onChange={e => handleRoleChange(u.id, e.target.value)}
                          onBlur={() => setEditingRoleId(null)}
                          autoFocus
                          className="border rounded px-2 py-1"
                        >
                          <option value="admin">admin</option>
                          <option value="super_admin">super_admin</option>
                          <option value="agent">agent</option>
                          <option value="landlord">landlord</option>
                          <option value="tenant">tenant</option>
                        </select>
                      ) : (
                        u.role
                      )}
                    </td>
                    <td className="p-2 border">{u.isActive ? 'Active' : 'Inactive'}</td>
                    <td className="p-2 border flex gap-2">
                      <Button size="sm" variant="outline" onClick={e => { e.stopPropagation(); handlePromote(u.id); }} disabled={u.role === 'admin' || u.role === 'super_admin'}>Promote</Button>
                      {u.isActive ? (
                        <Button size="sm" variant="outline" onClick={e => { e.stopPropagation(); handleDeactivate(u.id); }}>Deactivate</Button>
                      ) : (
                        <Button size="sm" variant="outline" onClick={e => { e.stopPropagation(); handleActivate(u.id); }}>Activate</Button>
                      )}
                      <Button size="sm" variant="destructive" onClick={e => { e.stopPropagation(); handleDelete(u.id); }}>Delete</Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        <div className="flex justify-between items-center mt-4">
          <span>Page {page} of {Math.ceil(total / PAGE_SIZE) || 1}</span>
          <div className="flex gap-2">
            <Button size="sm" onClick={() => setPage((p) => Math.max(1, p - 1))} disabled={page === 1}>Prev</Button>
            <Button size="sm" onClick={() => setPage((p) => p + 1)} disabled={page * PAGE_SIZE >= total}>Next</Button>
          </div>
        </div>
      </CardContent>
      {/* User Details Modal */}
      {modalUser && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40" onClick={closeModal}>
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 relative" onClick={e => e.stopPropagation()}>
            <button className="absolute top-2 right-2 text-gray-400 hover:text-gray-700" onClick={closeModal}>&times;</button>
            <h2 className="text-xl font-bold mb-2">User Details</h2>
            <div className="mb-4 space-y-1">
              <div><b>Name:</b> {modalUser.fullName || `${modalUser.firstName} ${modalUser.lastName}`}</div>
              <div><b>Email:</b> {modalUser.email}</div>
              <div><b>Role:</b> {editingRoleId === modalUser.id ? (
                <select
                  value={modalUser.role}
                  onChange={e => handleRoleChange(modalUser.id, e.target.value)}
                  onBlur={() => setEditingRoleId(null)}
                  autoFocus
                  className="border rounded px-2 py-1"
                >
                  <option value="admin">admin</option>
                  <option value="super_admin">super_admin</option>
                  <option value="agent">agent</option>
                  <option value="landlord">landlord</option>
                  <option value="tenant">tenant</option>
                </select>
              ) : (
                <span className="cursor-pointer underline" onClick={() => setEditingRoleId(modalUser.id)}>{modalUser.role}</span>
              )}</div>
              <div><b>Status:</b> {modalUser.isActive ? 'Active' : 'Inactive'}</div>
              <div><b>Phone:</b> {modalUser.phone}</div>
              <div><b>Created:</b> {modalUser.createdAt ? new Date(modalUser.createdAt).toLocaleString() : ''}</div>
              <div><b>Updated:</b> {modalUser.updatedAt ? new Date(modalUser.updatedAt).toLocaleString() : ''}</div>
              <div><b>ID:</b> {modalUser.id}</div>
            </div>
            <div className="flex gap-2 mt-4">
              <Button size="sm" variant="outline" onClick={() => handlePromote(modalUser.id)} disabled={modalUser.role === 'admin' || modalUser.role === 'super_admin'}>Promote</Button>
              {modalUser.isActive ? (
                <Button size="sm" variant="outline" onClick={() => handleDeactivate(modalUser.id)}>Deactivate</Button>
              ) : (
                <Button size="sm" variant="outline" onClick={() => handleActivate(modalUser.id)}>Activate</Button>
              )}
              <Button size="sm" variant="destructive" onClick={() => handleDelete(modalUser.id)}>Delete</Button>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default AdminUsersList; 