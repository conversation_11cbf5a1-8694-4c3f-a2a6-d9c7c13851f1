import React from 'react';
import ErrorBoundary from './ErrorBoundary';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface PageErrorBoundaryProps {
  children: React.ReactNode;
  pageName?: string;
}

const PageErrorFallback: React.FC<{ pageName?: string; onRetry: () => void }> = ({ 
  pageName, 
  onRetry 
}) => (
  <div className="min-h-screen flex items-center justify-center p-4">
    <div className="text-center max-w-md">
      <div className="mx-auto mb-6 w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
        <AlertTriangle className="w-10 h-10 text-red-600" />
      </div>
      
      <h1 className="text-3xl font-bold text-gray-900 mb-4">
        Page Error
      </h1>
      
      <p className="text-gray-600 mb-6">
        {pageName ? `The ${pageName} page` : 'This page'} encountered an error and couldn't load properly.
      </p>
      
      <div className="space-y-3">
        <Button onClick={onRetry} className="w-full">
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
        
        <Button 
          onClick={() => window.location.href = '/'} 
          variant="outline" 
          className="w-full"
        >
          <Home className="w-4 h-4 mr-2" />
          Go to Homepage
        </Button>
      </div>
    </div>
  </div>
);

const PageErrorBoundary: React.FC<PageErrorBoundaryProps> = ({ 
  children, 
  pageName 
}) => {
  return (
    <ErrorBoundary
      level="page"
      showDetails={process.env.NODE_ENV === 'development'}
      fallback={
        <PageErrorFallback 
          pageName={pageName} 
          onRetry={() => window.location.reload()} 
        />
      }
      onError={(error, errorInfo) => {
        console.error(`Page Error in ${pageName || 'Unknown Page'}:`, error, errorInfo);
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default PageErrorBoundary;
