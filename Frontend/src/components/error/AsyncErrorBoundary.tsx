import React, { useState, useEffect } from 'react';
import ErrorBoundary from './ErrorBoundary';

interface AsyncErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error) => void;
}

const AsyncErrorBoundary: React.FC<AsyncErrorBoundaryProps> = ({ 
  children, 
  fallback,
  onError 
}) => {
  const [asyncError, setAsyncError] = useState<Error | null>(null);

  // Reset async error when children change
  useEffect(() => {
    setAsyncError(null);
  }, [children]);

  // Global error handler for unhandled promise rejections
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      
      // Create an error object if the rejection reason isn't already an Error
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason));
      
      setAsyncError(error);
      
      if (onError) {
        onError(error);
      }
      
      // Prevent the default browser behavior
      event.preventDefault();
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [onError]);

  // If there's an async error, throw it to be caught by the ErrorBoundary
  if (asyncError) {
    throw asyncError;
  }

  return (
    <ErrorBoundary
      level="component"
      fallback={fallback}
      onError={(error, errorInfo) => {
        console.error('Async Error Boundary caught error:', error, errorInfo);
        if (onError) {
          onError(error);
        }
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

// Hook to manually trigger async errors
export const useAsyncError = () => {
  const [, setError] = useState<Error | null>(null);
  
  return (error: Error) => {
    setError(() => {
      throw error;
    });
  };
};

export default AsyncErrorBoundary;
