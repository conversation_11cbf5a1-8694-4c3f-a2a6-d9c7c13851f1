import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON><PERSON>, Wifi, WifiOff, Server, Database, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';

interface FallbackProps {
  error?: Error;
  retry?: () => void;
  isRetrying?: boolean;
  message?: string;
  description?: string;
}

// Generic error fallback
export const ErrorFallback: React.FC<FallbackProps> = ({
  error,
  retry,
  isRetrying = false,
  message = 'Something went wrong',
  description = 'An unexpected error occurred. Please try again.',
}) => (
  <Alert className="border-red-200 bg-red-50">
    <AlertTriangle className="h-4 w-4 text-red-600" />
    <AlertDescription className="flex items-center justify-between">
      <div>
        <div className="font-medium text-red-800">{message}</div>
        <div className="text-sm text-red-600 mt-1">{description}</div>
        {error && process.env.NODE_ENV === 'development' && (
          <details className="mt-2">
            <summary className="text-xs cursor-pointer">Error Details</summary>
            <pre className="text-xs mt-1 text-red-500">{error.message}</pre>
          </details>
        )}
      </div>
      {retry && (
        <Button
          onClick={retry}
          disabled={isRetrying}
          size="sm"
          variant="outline"
          className="ml-4"
        >
          <RefreshCw className={`w-3 h-3 mr-1 ${isRetrying ? 'animate-spin' : ''}`} />
          {isRetrying ? 'Retrying...' : 'Retry'}
        </Button>
      )}
    </AlertDescription>
  </Alert>
);

// Network error fallback
export const NetworkErrorFallback: React.FC<FallbackProps> = ({
  retry,
  isRetrying = false,
}) => (
  <Card className="border-orange-200 bg-orange-50">
    <CardHeader className="text-center pb-4">
      <div className="mx-auto mb-2 w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
        <WifiOff className="w-6 h-6 text-orange-600" />
      </div>
      <CardTitle className="text-orange-800">Connection Problem</CardTitle>
      <CardDescription className="text-orange-600">
        Unable to connect to the server. Please check your internet connection.
      </CardDescription>
    </CardHeader>
    {retry && (
      <CardContent className="text-center">
        <Button
          onClick={retry}
          disabled={isRetrying}
          variant="outline"
          className="border-orange-300 text-orange-700 hover:bg-orange-100"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
          {isRetrying ? 'Reconnecting...' : 'Try Again'}
        </Button>
      </CardContent>
    )}
  </Card>
);

// Server error fallback
export const ServerErrorFallback: React.FC<FallbackProps> = ({
  retry,
  isRetrying = false,
}) => (
  <Card className="border-red-200 bg-red-50">
    <CardHeader className="text-center pb-4">
      <div className="mx-auto mb-2 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
        <Server className="w-6 h-6 text-red-600" />
      </div>
      <CardTitle className="text-red-800">Server Error</CardTitle>
      <CardDescription className="text-red-600">
        The server is experiencing issues. Please try again in a moment.
      </CardDescription>
    </CardHeader>
    {retry && (
      <CardContent className="text-center">
        <Button
          onClick={retry}
          disabled={isRetrying}
          variant="outline"
          className="border-red-300 text-red-700 hover:bg-red-100"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
          {isRetrying ? 'Retrying...' : 'Try Again'}
        </Button>
      </CardContent>
    )}
  </Card>
);

// Data loading fallback
export const DataLoadingFallback: React.FC<{ message?: string }> = ({
  message = 'Loading data...',
}) => (
  <Card>
    <CardHeader>
      <div className="flex items-center space-x-2">
        <Skeleton className="h-4 w-4 rounded-full" />
        <Skeleton className="h-4 w-32" />
      </div>
    </CardHeader>
    <CardContent className="space-y-3">
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
    </CardContent>
  </Card>
);

// Empty state fallback
export const EmptyStateFallback: React.FC<{
  title?: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}> = ({
  title = 'No data available',
  description = 'There is no data to display at the moment.',
  action,
}) => (
  <Card className="border-gray-200 bg-gray-50">
    <CardHeader className="text-center pb-4">
      <div className="mx-auto mb-2 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
        <Database className="w-6 h-6 text-gray-400" />
      </div>
      <CardTitle className="text-gray-700">{title}</CardTitle>
      <CardDescription className="text-gray-500">{description}</CardDescription>
    </CardHeader>
    {action && (
      <CardContent className="text-center">
        <Button onClick={action.onClick} variant="outline">
          {action.label}
        </Button>
      </CardContent>
    )}
  </Card>
);

// Timeout error fallback
export const TimeoutErrorFallback: React.FC<FallbackProps> = ({
  retry,
  isRetrying = false,
}) => (
  <Card className="border-yellow-200 bg-yellow-50">
    <CardHeader className="text-center pb-4">
      <div className="mx-auto mb-2 w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
        <Clock className="w-6 h-6 text-yellow-600" />
      </div>
      <CardTitle className="text-yellow-800">Request Timeout</CardTitle>
      <CardDescription className="text-yellow-600">
        The request took too long to complete. Please try again.
      </CardDescription>
    </CardHeader>
    {retry && (
      <CardContent className="text-center">
        <Button
          onClick={retry}
          disabled={isRetrying}
          variant="outline"
          className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
          {isRetrying ? 'Retrying...' : 'Try Again'}
        </Button>
      </CardContent>
    )}
  </Card>
);

// Offline fallback
export const OfflineFallback: React.FC<{
  onRetry?: () => void;
}> = ({ onRetry }) => (
  <Alert className="border-gray-300 bg-gray-50">
    <WifiOff className="h-4 w-4 text-gray-600" />
    <AlertDescription className="flex items-center justify-between">
      <div>
        <div className="font-medium text-gray-800">You're offline</div>
        <div className="text-sm text-gray-600 mt-1">
          Check your internet connection and try again.
        </div>
      </div>
      {onRetry && (
        <Button
          onClick={onRetry}
          size="sm"
          variant="outline"
          className="ml-4"
        >
          <Wifi className="w-3 h-3 mr-1" />
          Retry
        </Button>
      )}
    </AlertDescription>
  </Alert>
);

// Maintenance mode fallback
export const MaintenanceFallback: React.FC = () => (
  <Card className="border-blue-200 bg-blue-50">
    <CardHeader className="text-center">
      <div className="mx-auto mb-4 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
        <Server className="w-8 h-8 text-blue-600" />
      </div>
      <CardTitle className="text-blue-800">Maintenance Mode</CardTitle>
      <CardDescription className="text-blue-600">
        We're currently performing maintenance to improve your experience.
        Please check back in a few minutes.
      </CardDescription>
    </CardHeader>
  </Card>
);

// Component-specific fallbacks
export const PropertyListFallback: React.FC<FallbackProps> = (props) => (
  <ErrorFallback
    {...props}
    message="Failed to load properties"
    description="Unable to fetch property listings. Please try again."
  />
);

export const UserProfileFallback: React.FC<FallbackProps> = (props) => (
  <ErrorFallback
    {...props}
    message="Failed to load profile"
    description="Unable to fetch user profile information. Please try again."
  />
);

export const DashboardFallback: React.FC<FallbackProps> = (props) => (
  <ErrorFallback
    {...props}
    message="Dashboard unavailable"
    description="Unable to load dashboard data. Please refresh the page."
  />
);
