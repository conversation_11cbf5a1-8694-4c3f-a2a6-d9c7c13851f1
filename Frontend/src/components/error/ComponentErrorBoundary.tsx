import React from 'react';
import ErrorBoundary from './ErrorBoundary';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ComponentErrorBoundaryProps {
  children: React.ReactNode;
  componentName?: string;
  fallbackComponent?: React.ReactNode;
}

const ComponentErrorFallback: React.FC<{ 
  componentName?: string; 
  onRetry: () => void;
}> = ({ componentName, onRetry }) => (
  <Alert className="border-red-200 bg-red-50">
    <AlertTriangle className="h-4 w-4 text-red-600" />
    <AlertDescription className="flex items-center justify-between">
      <span>
        {componentName ? `${componentName} failed to load` : 'Component failed to load'}
      </span>
      <Button 
        onClick={onRetry} 
        size="sm" 
        variant="outline"
        className="ml-4"
      >
        <RefreshCw className="w-3 h-3 mr-1" />
        Retry
      </Button>
    </AlertDescription>
  </Alert>
);

const ComponentErrorBoundary: React.FC<ComponentErrorBoundaryProps> = ({ 
  children, 
  componentName,
  fallbackComponent
}) => {
  const [retryKey, setRetryKey] = React.useState(0);

  const handleRetry = () => {
    setRetryKey(prev => prev + 1);
  };

  return (
    <ErrorBoundary
      key={retryKey}
      level="component"
      showDetails={process.env.NODE_ENV === 'development'}
      fallback={
        fallbackComponent || (
          <ComponentErrorFallback 
            componentName={componentName} 
            onRetry={handleRetry}
          />
        )
      }
      onError={(error, errorInfo) => {
        console.error(`Component Error in ${componentName || 'Unknown Component'}:`, error, errorInfo);
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default ComponentErrorBoundary;
