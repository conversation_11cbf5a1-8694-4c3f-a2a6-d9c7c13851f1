// Supabase client configuration
// This is a placeholder file for testing purposes

export const supabase = {
  auth: {
    getSession: () => Promise.resolve({ data: { session: null }, error: null }),
    onAuthStateChange: () => ({ unsubscribe: () => {} }),
    signInWithPassword: () => Promise.resolve({ data: { session: null }, error: null }),
    signUp: () => Promise.resolve({ data: { session: null }, error: null }),
    signOut: () => Promise.resolve({ error: null }),
  },
  from: () => ({
    select: () => ({}),
    eq: () => ({}),
    insert: () => ({}),
    update: () => ({}),
    delete: () => ({}),
  }),
};

export default supabase;
