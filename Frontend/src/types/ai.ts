// AI-powered features type definitions

// Re-export types from service files
export type {
  UserPreferences,
  UserBehavior,
  PropertyFeatures as AIPropertyFeatures,
  RecommendationScore,
  SimilarUser
} from '@/services/aiRecommendationService';

export type {
  PropertyFeatures,
  PricePrediction,
  MarketTrend,
  PriceHistory
} from '@/services/pricePredictionService';

export type {
  MarketInsight,
  ComparativeMarketAnalysis,
  InvestmentOpportunity,
  DemandForecast
} from '@/services/marketAnalyticsService';

export type {
  MatchingCriteria,
  PropertyMatch,
  AgentClientMatch,
  LeadScore,
  SmartNotification,
  ConversionPrediction
} from '@/services/intelligentMatchingService';

// Additional AI-specific types
export interface PropertyRecommendation {
  property_id: string;
  score: number;
  reasons: string[];
  property: any;
  match_factors: {
    budget_match: number;
    location_match: number;
    amenity_match: number;
    lifestyle_match: number;
    commute_score: number;
  };
  recommendation_type: 'content' | 'collaborative' | 'hybrid' | 'trending';
  confidence: number;
}

export interface AIAnalytics {
  user_id: string;
  recommendation_accuracy: number;
  prediction_accuracy: number;
  engagement_score: number;
  conversion_rate: number;
  learning_progress: {
    preferences_learned: number;
    behavior_patterns: number;
    prediction_confidence: number;
  };
  model_performance: {
    recommendation_model: number;
    price_prediction_model: number;
    matching_model: number;
  };
}

export interface MachineLearningModel {
  model_id: string;
  model_type: 'recommendation' | 'price_prediction' | 'matching' | 'scoring';
  version: string;
  accuracy: number;
  training_data_size: number;
  last_trained: string;
  performance_metrics: {
    precision: number;
    recall: number;
    f1_score: number;
    mae?: number; // Mean Absolute Error for regression models
    rmse?: number; // Root Mean Square Error for regression models
  };
  feature_importance: Record<string, number>;
}

export interface PredictiveAnalytics {
  property_id?: string;
  user_id?: string;
  predictions: {
    price_movement: {
      direction: 'up' | 'down' | 'stable';
      magnitude: number;
      confidence: number;
      timeframe: string;
    };
    demand_forecast: {
      demand_level: 'high' | 'medium' | 'low';
      trend: 'increasing' | 'decreasing' | 'stable';
      seasonal_factors: Record<string, number>;
    };
    conversion_likelihood: {
      probability: number;
      timeline_days: number;
      key_factors: string[];
    };
  };
  market_signals: Array<{
    signal_type: string;
    strength: number;
    impact: 'positive' | 'negative' | 'neutral';
    description: string;
  }>;
}

export interface AIInsight {
  insight_id: string;
  type: 'market_trend' | 'price_anomaly' | 'demand_shift' | 'opportunity' | 'risk';
  title: string;
  description: string;
  confidence_level: number;
  impact_level: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommended_actions: string[];
  data_sources: string[];
  generated_at: string;
  expires_at?: string;
}

export interface PersonalizationProfile {
  user_id: string;
  preferences: {
    property_types: string[];
    locations: string[];
    budget_range: { min: number; max: number };
    amenities: string[];
    lifestyle: string[];
  };
  behavior_patterns: {
    search_frequency: number;
    viewing_patterns: Record<string, number>;
    interaction_preferences: string[];
    decision_timeline: number;
  };
  communication_preferences: {
    channels: string[];
    frequency: 'high' | 'medium' | 'low';
    optimal_times: string[];
    content_types: string[];
  };
  learning_indicators: {
    preference_stability: number;
    behavior_consistency: number;
    feedback_quality: number;
  };
}

export interface SmartRecommendationEngine {
  engine_id: string;
  algorithms: {
    content_based: {
      enabled: boolean;
      weight: number;
      features: string[];
    };
    collaborative_filtering: {
      enabled: boolean;
      weight: number;
      similarity_threshold: number;
    };
    hybrid_approach: {
      enabled: boolean;
      combination_method: 'weighted' | 'switching' | 'cascade';
    };
    deep_learning: {
      enabled: boolean;
      model_type: string;
      embedding_dimensions: number;
    };
  };
  performance_metrics: {
    accuracy: number;
    diversity: number;
    novelty: number;
    coverage: number;
  };
  real_time_learning: boolean;
  explanation_generation: boolean;
}

export interface AIConfiguration {
  recommendation_engine: SmartRecommendationEngine;
  price_prediction: {
    model_type: 'linear_regression' | 'random_forest' | 'neural_network' | 'ensemble';
    features: string[];
    update_frequency: 'daily' | 'weekly' | 'monthly';
    confidence_threshold: number;
  };
  matching_algorithm: {
    similarity_metrics: string[];
    weighting_strategy: 'static' | 'dynamic' | 'learned';
    diversity_factor: number;
    explanation_depth: 'basic' | 'detailed' | 'comprehensive';
  };
  learning_parameters: {
    learning_rate: number;
    batch_size: number;
    regularization: number;
    early_stopping: boolean;
  };
}

// Error types for AI services
export class AIServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

// Utility types
export type AIModelType = 'recommendation' | 'price_prediction' | 'matching' | 'scoring' | 'classification';
export type ConfidenceLevel = 'very_high' | 'high' | 'medium' | 'low' | 'very_low';
export type RecommendationType = 'content' | 'collaborative' | 'hybrid' | 'trending' | 'personalized';
export type PredictionAccuracy = 'excellent' | 'good' | 'fair' | 'poor';

// Constants
export const AI_CONFIDENCE_THRESHOLDS = {
  VERY_HIGH: 0.9,
  HIGH: 0.8,
  MEDIUM: 0.6,
  LOW: 0.4,
  VERY_LOW: 0.2
} as const;

export const AI_MODEL_PERFORMANCE_THRESHOLDS = {
  EXCELLENT: 0.95,
  GOOD: 0.85,
  FAIR: 0.75,
  POOR: 0.65
} as const;

export const RECOMMENDATION_WEIGHTS = {
  CONTENT_BASED: 0.3,
  COLLABORATIVE: 0.25,
  BEHAVIOR: 0.25,
  MARKET: 0.2
} as const;
