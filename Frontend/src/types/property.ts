export enum PropertyStatusEnum {
  AVAILABLE = 'available',
  RENTED = 'rented',
  UNDER_MAINTENANCE = 'under_maintenance',
  UNAVAILABLE = 'unavailable',
}

/**
 * Main Property interface
 */
export interface Property {
  id: string;
  title: string;
  description: string;
  location: string;
  pricePerYear: number;
  propertyType: string;
  bedrooms: number;
  bathrooms: number;
  areaSqft: number;
  isAvailable: boolean;
  isVerified: boolean;
  isFeatured: boolean;
  status: PropertyStatus;
  images: PropertyImage[]; // Array of image URLs
  amenities: string[];
  landlordId: string;
  agentId?: string;
  createdAt: string;
  updatedAt: string;
  viewsCount?: number; // Added based on backend service
  inquiriesCount?: number; // Added based on backend service
  metadata?: Record<string, any>;
}

/**
 * Interface for a single property image with detailed metadata.
 * This is the definitive declaration to avoid conflicts.
 */
export interface PropertyImage {
  id: string;
  propertyId: string;
  url: string;
  thumbnailUrl: string;
  altText: string;
  orderIndex: number;
  fileSize: number;
  fileType: string;
  fileName: string;
  width: number;
  height: number;
  isPrimary: boolean;
  uploadedAt: string;
}

// Enhanced Property types
export interface PropertyWithAnalytics extends Property {
  viewsCount: number;
  uniqueViews: number;
  views_last_30_days: number;
  lastViewed: string | null;
  inquiriesCount: number;
  inquiries_last_30_days: number;
  respondedInquiries: number;
  uniqueInquirers: number;
  lastInquiry: string | null;
  firstInquiry: string | null;
  inquiryConversionRate: number;
  responseRate: number;
  pricePercentileInLocation: number;
  daysOnMarket: number | null;
}

export interface PropertyVerificationStep {
  id: string;
  propertyId: string;
  stepName: string;
  stepOrder: number;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  required: boolean;
  description: string;
  completionData: Record<string, any> | null;
  completedBy: string | null;
  completedAt: string | null;
  createdAt: string;
}

export interface PropertyDocument {
  id: string;
  propertyId: string;
  documentType: 'title_deed' | 'survey_plan' | 'building_approval' | 'tax_receipt' | 'other';
  documentName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
  verificationStatus: 'pending' | 'verified' | 'rejected';
  uploadedBy: string;
  uploadedAt: string;
  verifiedBy: string | null;
  verifiedAt: string | null;
}

export interface PropertyInspection {
  id: string;
  propertyId: string;
  inspectorId: string;
  inspectionType: 'initial' | 'follow_up' | 'maintenance' | 'final';
  scheduledDate: string;
  completedDate: string | null;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  inspectionNotes: string;
  qualityScore: number | null;
  issuesFound: string[];
  recommendations: string[];
  photos: string[];
  createdAt: string;
}

export interface PropertyAnalytics {
  propertyId: string;
  totalViews: number;
  uniqueViews: number;
  viewSources: Record<string, number>;
  inquiryRate: number;
  conversionRate: number;
  averageTimeOnPage: number;
  bounceRate: number;
  popularTimes: Record<string, number>;
  demographicData: {
    ageGroups: Record<string, number>;
    locations: Record<string, number>;
    devices: Record<string, number>;
  };
  performanceScore: number;
  marketPosition: {
    priceRank: number;
    viewRank: number;
    inquiryRank: number;
  };
  calculatedAt: string;
}

export interface SearchFilters {
  propertyType?: string;
  status?: PropertyStatus;
  minPrice?: number;
  maxPrice?: number;
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
  location?: string;
  search?: string;
  cit?: string;
  state?: string;
  furnishingStatus?: string;
  isFeatured?: boolean;
  isVerified?: boolean;
  landlordId?: string;
  agentId?: string;
  amenities?: string[];
}

export interface SavedSearch {
  id: string;
  userId: string;
  name: string;
  filters: SearchFilters;
  alertFrequency: 'immediate' | 'daily' | 'weekly' | 'monthly' | 'never';
  isActive: boolean;
  lastRun: string | null;
  resultsCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface PropertyAlert {
  id: string;
  userId: string;
  propertyId: string;
  alertType: 'price_drop' | 'new_match' | 'back_available' | 'status_change';
  message: string;
  isRead: boolean;
  createdAt: string;
}

export interface VirtualTour {
  id: string;
  propertyId: string;
  tourType: '360_photos' | 'video_walkthrough' | 'interactive_3d';
  tourUrl: string;
  thumbnailUrl: string;
  durationMinutes: number | null;
  viewCount: number;
  isActive: boolean;
  createdAt: string;
}

// Request/Response types
export interface CreatePropertyRequest {
  title: string;
  description: string;
  location: string;
  pricePerYear: number;
  bedrooms: number;
  bathrooms: number;
  areaSqft?: number;
  propertyType: string;
  amenities: string[];
  contactWhatsapp?: string;
  contactEmail?: string;
  agentId?: string;
  landlordId?: string;
}

export interface PropertyImage {
  id: string;
  propertyId: string;
  url: string;
  thumbnailUrl: string;
  altText: string;
  orderIndex: number;
  isPrimary: boolean;
  fileSize: number;
  fileType: string;
  width: number;
  height: number;
  createdAt: string;
}

export interface UpdatePropertyRequest {
  title?: string;
  description?: string;
  location?: string;
  pricePerYear?: number;
  bedrooms?: number;
  bathrooms?: number;
  areaSqft?: number;
  propertyType?: string;
  amenities?: string[];
  contactWhatsapp?: string;
  contactEmail?: string;
  isAvailable?: boolean;
  featured?: boolean;
}

export interface ImageUploadRequest {
  property_id: string;
  files: File[];
  altTexts?: string[];
  isPrimaryIndex?: number;
}

export interface ImageUploadProgress {
  fileName: string;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
  url?: string;
}

export interface PropertySearchResult {
  properties: PropertyWithAnalytics[];
  totalCount: number;
  page: number;
  pageSize: number;
  filtersApplied: SearchFilters;
  searchMetadata: {
    searchTimeMs: number;
    suggestions: string[];
    facets: Record<string, Record<string, number>>;
  };
}

export interface PropertyMarketData {
  location: string;
  propertyType: string;
  averagePrice: number;
  medianPrice: number;
  pricePerSqft: number;
  totalProperties: number;
  availableProperties: number;
  averageDaysOnMarket: number;
  priceTrend: 'rising' | 'falling' | 'stable';
  demandScore: number;
  competitionLevel: 'low' | 'medium' | 'high';
}

export interface PropertyRecommendation {
  property: PropertyWithAnalytics;
  score: number;
  reasons: string[];
  matchPercentage: number;
}

export interface PropertySearchResult {
  data: PropertyWithAnalytics[];
  totalCount: number;
  page: number;
  pageSize: number;
  filtersApplied: SearchFilters;
  searchMetadata: {
    searchTimeMs: number;
    suggestions: string[];
    facets: Record<string, Record<string, number>>;
  };
}

export interface CreatePropertyRequest {
  title: string;
  description: string;
  location: string;
  pricePerYear: number;
  propertyType: string;
  bedrooms: number;
  bathrooms: number;
  areaSqft?: number;
  amenities: string[];
  images?: string[]; // Initial images, will be updated by images service
  // Add other fields from your CreatePropertyDto
  city?: string;
  state?: string;
  country?: string;
  furnishingStatus?: string;
  agentId?: string;
}

export interface SavedSearch {
  id: string;
  userId: string;
  name: string;
  filters: SearchFilters; // Filters used for this saved search
  alertFrequency: 'immediate' | 'daily' | 'weekly' | 'monthly' | 'never';
  isActive: boolean;
  resultsCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface SubmitVerificationStepsRequest {
  stepType: VerificationStepType;
  files?: string[];
  comments?: string;
}

export interface UpdateVerificationStatusRequest {
  status: VerificationStepStatus;
  comments?: string;
}

export interface ScheduleInspectionRequest {
  scheduledDate: string;
  notes?: string;
}

export interface CompleteInspectionRequest {
  status: VerificationStepStatus;
  report?: string;
}

// New type for verification progress
export interface VerificationProgress {
  total: number;
  completed: number;
  pending: number;
  progress: number; // A percentage value from 0-100
}


export interface CreateSavedSearchRequest {
  name: string;
  filters: SearchFilters;
  alertFrequency?: 'daily' | 'weekly' | 'monthly' | 'never';
}

export interface ReorderImagesRequest {
  imageIds: string[]; // Ordered list of image IDs
}

export interface UpdateSavedSearchRequest extends Partial<CreateSavedSearchRequest> {
  isActive?: boolean;
}

// Enums for Verification
export enum VerificationStepType {
  DOCUMENT_UPLOAD = 'document_upload',
  SITE_INSPECTION = 'site_inspection',
  BACKGROUND_CHECK = 'background_check',
}

export enum VerificationStepStatus {
  PENDING = 'pending',
  IN_REVIEW = 'in_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

// Interface for the Verification Step entity
export interface VerificationStep {
  id: string;
  propertyId: string;
  stepType: VerificationStepType;
  status: VerificationStepStatus;
  comments?: string;
  files?: string[]; // URLs of uploaded files
  createdAt: string;
  updatedAt: string;
}

// Request types for API calls
export interface SubmitVerificationStepsRequest {
  stepType: VerificationStepType;
  files?: string[];
  comments?: string;
}

export interface UpdateVerificationStatusRequest {
  status: VerificationStepStatus;
  comments?: string;
}

// Error types
export class PropertyServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'PropertyServiceError';
  }
}

// Utility types
export type PropertyStatus = 'available' | 'rented' | 'maintenance' | 'pending_approval';
export type PropertyType = 'apartment' | 'house' | 'duplex' | 'bungalow' | 'flat' | 'studio' | 'penthouse';
export type VerificationStatus = 'pending' | 'in_progress' | 'verified' | 'rejected';
export type InspectionStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
export type DocumentType = 'title_deed' | 'survey_plan' | 'building_approval' | 'tax_receipt' | 'other';

// Constants
export const PROPERTY_TYPES: PropertyType[] = [
  'apartment', 'house', 'duplex', 'bungalow', 'flat', 'studio', 'penthouse'
];

export const AMENITIES_LIST = [
  'Parking', 'Generator', 'Security', 'Water', 'Internet', 'Air Conditioning',
  'Swimming Pool', 'Gym', 'Elevator', 'Balcony', 'Garden', 'Garage',
  'Furnished', 'Kitchen Appliances', 'Laundry', 'Storage', 'Pet Friendly',
  'Wheelchair Accessible', 'CCTV', 'Intercom', 'Backup Power', 'Borehole'
];

export const LOCATIONS_PORT_HARCOURT = [
  'Government Residential Area (GRA)',
  'Old GRA',
  'New GRA',
  'Trans Amadi',
  'Woji',
  'Eagle Island',
  'D-Line',
  'Aba Road',
  'East West Road',
  'Ada George',
  'Eliozu',
  'Choba',
  'Alakahia',
  'Port Harcourt Township',
  'Mile 1',
  'Mile 2',
  'Mile 3',
  'Rumuola',
  'Rumuokwuta',
  'Rumuigbo'
];
