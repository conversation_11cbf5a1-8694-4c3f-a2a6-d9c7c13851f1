// Analytics and Reporting System Types

// Re-export types from service files
export type {
  DashboardMetrics,
  CustomDashboard,
  AlertRule,
  NotificationAlert
} from '@/services/dashboardService';

export type {
  AgentPerformanceMetrics,
  PropertyPerformanceMetrics,
  MarketPerformanceMetrics,
  FinancialPerformanceMetrics,
  UserEngagementMetrics
} from '@/services/performanceMetricsService';

export type {
  PriceTrendAnalysis,
  DemandPatternAnalysis,
  SeasonalTrendAnalysis,
  MarketCyclePrediction,
  CompetitiveAnalysis,
  TrendAlert
} from '@/services/marketTrendAnalysisService';

export type {
  ReportConfig,
  ReportTemplate,
  GeneratedReport,
  ReportData,
  ExportOptions
} from '@/services/reportGenerationService';

// Additional analytics-specific types
export interface AnalyticsFilter {
  date_range: { start: string; end: string };
  locations?: string[];
  property_types?: string[];
  agents?: string[];
  price_range?: { min: number; max: number };
  custom_filters?: Record<string, any>;
}

export interface MetricDefinition {
  id: string;
  name: string;
  description: string;
  category: 'performance' | 'financial' | 'engagement' | 'market';
  data_type: 'number' | 'percentage' | 'currency' | 'duration' | 'count';
  calculation_method: string;
  unit: string;
  format: string;
  is_kpi: boolean;
  target_value?: number;
  benchmark_value?: number;
  trend_direction: 'higher_better' | 'lower_better' | 'neutral';
}

export interface KPICard {
  id: string;
  metric_id: string;
  title: string;
  current_value: number;
  previous_value: number;
  change_percentage: number;
  trend: 'up' | 'down' | 'stable';
  status: 'excellent' | 'good' | 'warning' | 'critical';
  target_value?: number;
  unit: string;
  format: string;
  last_updated: string;
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'heatmap' | 'gauge';
  title: string;
  data_source: string;
  x_axis: {
    field: string;
    label: string;
    type: 'category' | 'datetime' | 'numeric';
  };
  y_axis: {
    field: string;
    label: string;
    type: 'numeric';
    format?: string;
  };
  series?: Array<{
    field: string;
    label: string;
    color?: string;
  }>;
  options: {
    show_legend: boolean;
    show_grid: boolean;
    show_tooltip: boolean;
    animation: boolean;
    responsive: boolean;
  };
}

export interface DashboardWidget {
  id: string;
  type: 'kpi' | 'chart' | 'table' | 'text' | 'metric' | 'map';
  title: string;
  description?: string;
  position: { x: number; y: number; w: number; h: number };
  config: KPICard | ChartConfig | any;
  data_source: string;
  refresh_interval: number;
  is_visible: boolean;
  permissions: string[];
}

export interface AnalyticsInsight {
  id: string;
  type: 'trend' | 'anomaly' | 'opportunity' | 'risk' | 'recommendation';
  title: string;
  description: string;
  confidence: number;
  impact_level: 'high' | 'medium' | 'low';
  category: string;
  data_points: Array<{
    metric: string;
    value: number;
    context: string;
  }>;
  recommended_actions: string[];
  generated_at: string;
  expires_at?: string;
}

export interface BenchmarkComparison {
  metric: string;
  current_value: number;
  benchmark_value: number;
  benchmark_type: 'industry' | 'market' | 'peer' | 'historical';
  performance_rating: 'excellent' | 'above_average' | 'average' | 'below_average' | 'poor';
  percentile_rank: number;
  improvement_potential: number;
  context: string;
}

export interface PerformanceGoal {
  id: string;
  entity_id: string;
  entity_type: 'agent' | 'property' | 'team' | 'company';
  metric: string;
  target_value: number;
  current_value: number;
  progress_percentage: number;
  target_date: string;
  status: 'on_track' | 'at_risk' | 'behind' | 'achieved';
  created_by: string;
  created_at: string;
}

export interface DataVisualization {
  id: string;
  name: string;
  type: 'dashboard' | 'report' | 'chart' | 'table';
  config: any;
  data_query: string;
  filters: AnalyticsFilter;
  sharing: {
    is_public: boolean;
    shared_with: string[];
    access_level: 'view' | 'edit';
  };
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface AnalyticsEvent {
  id: string;
  event_type: string;
  entity_type: string;
  entity_id: string;
  user_id: string;
  properties: Record<string, any>;
  timestamp: string;
  session_id?: string;
  ip_address?: string;
  user_agent?: string;
}

export interface MetricCalculation {
  metric_id: string;
  entity_id: string;
  entity_type: string;
  value: number;
  calculation_date: string;
  period_start: string;
  period_end: string;
  calculation_method: string;
  data_sources: string[];
  confidence_level: number;
}

export interface AnalyticsConfiguration {
  user_id: string;
  default_dashboard: string;
  preferred_date_range: string;
  default_filters: AnalyticsFilter;
  notification_preferences: {
    email_alerts: boolean;
    push_notifications: boolean;
    alert_frequency: 'immediate' | 'daily' | 'weekly';
    alert_types: string[];
  };
  visualization_preferences: {
    chart_theme: 'light' | 'dark' | 'auto';
    default_chart_type: string;
    animation_enabled: boolean;
    color_scheme: string[];
  };
}

export interface ReportSchedule {
  id: string;
  report_config_id: string;
  name: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  schedule_time: string;
  timezone: string;
  recipients: Array<{
    email: string;
    name: string;
    role: string;
  }>;
  format: 'pdf' | 'excel' | 'csv' | 'email';
  is_active: boolean;
  last_run: string | null;
  next_run: string;
  created_by: string;
  created_at: string;
}

export interface AnalyticsAuditLog {
  id: string;
  action: 'view' | 'create' | 'update' | 'delete' | 'export' | 'share';
  resource_type: 'dashboard' | 'report' | 'chart' | 'data';
  resource_id: string;
  user_id: string;
  details: Record<string, any>;
  ip_address: string;
  user_agent: string;
  timestamp: string;
}

// Utility types
export type TimeRange = '1h' | '24h' | '7d' | '30d' | '90d' | '1y' | 'custom';
export type MetricTrend = 'increasing' | 'decreasing' | 'stable' | 'volatile';
export type PerformanceStatus = 'excellent' | 'good' | 'average' | 'poor' | 'critical';
export type AlertSeverity = 'info' | 'warning' | 'error' | 'critical';
export type ExportFormat = 'pdf' | 'excel' | 'csv' | 'json' | 'png' | 'svg';

// Constants
export const METRIC_CATEGORIES = {
  PERFORMANCE: 'performance',
  FINANCIAL: 'financial',
  ENGAGEMENT: 'engagement',
  MARKET: 'market',
  OPERATIONAL: 'operational'
} as const;

export const CHART_TYPES = {
  LINE: 'line',
  BAR: 'bar',
  PIE: 'pie',
  AREA: 'area',
  SCATTER: 'scatter',
  HEATMAP: 'heatmap',
  GAUGE: 'gauge',
  TABLE: 'table'
} as const;

export const DASHBOARD_LAYOUTS = {
  GRID: 'grid',
  MASONRY: 'masonry',
  FLEX: 'flex'
} as const;

export const PERFORMANCE_THRESHOLDS = {
  EXCELLENT: 0.9,
  GOOD: 0.75,
  AVERAGE: 0.6,
  POOR: 0.4,
  CRITICAL: 0.2
} as const;

export const DEFAULT_DATE_RANGES = {
  TODAY: { start: 'today', end: 'today' },
  YESTERDAY: { start: 'yesterday', end: 'yesterday' },
  LAST_7_DAYS: { start: '7d', end: 'now' },
  LAST_30_DAYS: { start: '30d', end: 'now' },
  LAST_90_DAYS: { start: '90d', end: 'now' },
  LAST_YEAR: { start: '1y', end: 'now' },
  THIS_MONTH: { start: 'month_start', end: 'now' },
  LAST_MONTH: { start: 'last_month_start', end: 'last_month_end' }
} as const;

// Error types
export class AnalyticsError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AnalyticsError';
  }
}

export class ReportGenerationError extends Error {
  constructor(
    message: string,
    public reportId: string,
    public stage: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ReportGenerationError';
  }
}

export class DataVisualizationError extends Error {
  constructor(
    message: string,
    public chartType: string,
    public dataSource: string,
    public details?: any
  ) {
    super(message);
    this.name = 'DataVisualizationError';
  }
}
