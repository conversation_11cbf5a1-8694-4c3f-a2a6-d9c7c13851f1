// Property endpoints
export const PROPERTY_ENDPOINTS = {
    // Property CRUD 
    CREATE: '/properties/create',
    SEARCH: '/properties/search',
    MY_PROPERTIES: '/properties/my-properties',
    PROPERTY_STATS: '/properties/stats',
    GET_PROPERTY_BY_ID: (id: string) => `/properties/${id}`,
    UPDATE_PROPERTY: (id: string) => `/properties/${id}`,
    UPDATE_PROPERTY_STATUS: (id: string) => `/properties/${id}/status`,
    TOGGLE_PROPERTY_FEATURED_STATUS: (id: string) => `/properties/${id}/toggle-feature`,
    TOGGLE_PROPERTY_VERIFIED_STATUS: (id: string) => `/properties/${id}/toggle-verified`,
    RECORD_PROPERTY_INQUIRY: (id: string) => `/properties/${id}/inquire`,
    DELETE_PROPERTY: (id: string) => `/properties/${id}`,
    UPLOAD_IMAGE: (propertyId: string) => `/properties/${propertyId}/images/upload`,
    GET_IMAGES: (propertyId: string) => `/properties/${propertyId}/images`,
    DELETE_IMAGE: (propertyId: string, imageId: string) => `/properties/${propertyId}/images/${imageId}`,
    REORDER_IMAGES: (propertyId: string) => `/properties/${propertyId}/images/reorder`,
    // Saved Searches
    SAVE_SEARCH: '/saved-searches',
    GET_SAVED_SEARCHES: (userId: string) => `/saved-searches/user/${userId}`,
    GET_SAVED_SEARCH_BY_ID: (id: string) => `/saved-searches/${id}`,
    UPDATE_SAVED_SEARCH: (id: string) => `/saved-searches/${id}`,
    DELETE_SAVED_SEARCH: (id: string) => `/saved-searches/${id}`,
    TOGGLE_SAVED_SEARCH_ACTIVE: (id: string) => `/saved-searches/${id}/toggle-active`,
    // Property Verification
    GET_PROPERTY_VERIFICATION_STEPS: (id: string) => `/properties/${id}/verification/steps`,
    SUBMIT_VERIFICATION_STEP: (id: string) => `/properties/${id}/verification/submit-step`,
    UPDATE_VERIFICATION_STATUS: (id: string, stepId: string) => `/properties/${id}/verification/status/${stepId}`,
    SCHEDULE_INSPECTION: (id: string) => `/properties/${id}/verification/schedule-inspection`,
    COMPLETE_INSPECTION: (id: string, stepId: string) => `properties/${id}/verification/complete-inspection/${stepId}`,
    GET_PROPERTY_VERIFICATION_PROGRESS: (id: string) => `properties/${id}/verification/progress`,
    // Property Analytics
    GET_PROPERTY_ANALYTICS: (id: string) => `/properties/${id}/analytics`,
    GET_PROPERTY_MARKET_DATA: '/properties/market-data',
    GET_PROPERTY_RECOMMENDATIONS: (id: string) => `/properties/${id}/recommendations`,
    // Stats
    STATS: '/properties/stats',
  } as const;