// Environment Configuration
export const ENV = {
  NODE_ENV: import.meta.env.VITE_NODE_ENV || 'development',
  APP_NAME: import.meta.env.VITE_APP_NAME || 'PHCityRent',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  APP_DESCRIPTION: import.meta.env.VITE_APP_DESCRIPTION || 'Port Harcourt Real Estate Platform',
} as const;

export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api/v1',
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000'),
  RETRY_ATTEMPTS: parseInt(import.meta.env.VITE_API_RETRY_ATTEMPTS || '3'),
  RETRY_DELAY: parseInt(import.meta.env.VITE_API_RETRY_DELAY || '1000'),
} as const;

export const AUTH_CONFIG = {
  JWT_STORAGE_KEY: import.meta.env.VITE_JWT_STORAGE_KEY || 'accessToken',
  REFRESH_TOKEN_STORAGE_KEY: import.meta.env.VITE_REFRESH_TOKEN_STORAGE_KEY || 'refreshToken',
  TOKEN_REFRESH_THRESHOLD: parseInt(import.meta.env.VITE_TOKEN_REFRESH_THRESHOLD || '300000'),
  AUTO_LOGOUT_ENABLED: import.meta.env.VITE_AUTO_LOGOUT_ENABLED === 'true',
  SESSION_TIMEOUT: parseInt(import.meta.env.VITE_SESSION_TIMEOUT || '900000'),
} as const;

export const PAYMENT_CONFIG = {
  PAYSTACK_PUBLIC_KEY: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || '',
  FLUTTERWAVE_PUBLIC_KEY: import.meta.env.VITE_FLUTTERWAVE_PUBLIC_KEY || '',
} as const;

export const GOOGLE_CONFIG = {
  MAPS_API_KEY: import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '',
  ANALYTICS_ID: import.meta.env.VITE_GOOGLE_ANALYTICS_ID || '',
  TAG_MANAGER_ID: import.meta.env.VITE_GOOGLE_TAG_MANAGER_ID || '',
} as const;

export const SOCIAL_CONFIG = {
  FACEBOOK_APP_ID: import.meta.env.VITE_FACEBOOK_APP_ID || '',
  TWITTER_API_KEY: import.meta.env.VITE_TWITTER_API_KEY || '',
  INSTAGRAM_ACCESS_TOKEN: import.meta.env.VITE_INSTAGRAM_ACCESS_TOKEN || '',
} as const;

export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
  ENABLE_ERROR_REPORTING: import.meta.env.VITE_ENABLE_ERROR_REPORTING === 'true',
  ENABLE_PERFORMANCE_MONITORING: import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true',
  ENABLE_DEBUG_MODE: import.meta.env.VITE_ENABLE_DEBUG_MODE === 'true',
  ENABLE_MOCK_DATA: import.meta.env.VITE_ENABLE_MOCK_DATA === 'true',
  ENABLE_SERVICE_WORKER: import.meta.env.VITE_ENABLE_SERVICE_WORKER === 'true',
} as const;

export const UI_CONFIG = {
  DEFAULT_THEME: import.meta.env.VITE_DEFAULT_THEME || 'light',
  ENABLE_DARK_MODE: import.meta.env.VITE_ENABLE_DARK_MODE === 'true',
  DEFAULT_LANGUAGE: import.meta.env.VITE_DEFAULT_LANGUAGE || 'en',
  ENABLE_RTL: import.meta.env.VITE_ENABLE_RTL === 'true',
} as const;

export const MAP_CONFIG = {
  DEFAULT_CENTER: {
    LAT: parseFloat(import.meta.env.VITE_DEFAULT_MAP_CENTER_LAT || '4.8156'),
    LNG: parseFloat(import.meta.env.VITE_DEFAULT_MAP_CENTER_LNG || '7.0498'),
  },
  DEFAULT_ZOOM: parseInt(import.meta.env.VITE_DEFAULT_MAP_ZOOM || '12'),
  STYLE: import.meta.env.VITE_MAP_STYLE || 'roadmap',
} as const;

export const FILE_CONFIG = {
  MAX_FILE_SIZE: parseInt(import.meta.env.VITE_MAX_FILE_SIZE || '10485760'),
  ALLOWED_IMAGE_TYPES: (import.meta.env.VITE_ALLOWED_IMAGE_TYPES || 'image/jpeg,image/png,image/webp').split(','),
  MAX_IMAGES_PER_PROPERTY: parseInt(import.meta.env.VITE_MAX_IMAGES_PER_PROPERTY || '20'),
} as const;

export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: parseInt(import.meta.env.VITE_DEFAULT_PAGE_SIZE || '20'),
  MAX_PAGE_SIZE: parseInt(import.meta.env.VITE_MAX_PAGE_SIZE || '100'),
} as const;

export const CACHE_CONFIG = {
  ENABLED: import.meta.env.VITE_CACHE_ENABLED === 'true',
  TTL: parseInt(import.meta.env.VITE_CACHE_TTL || '300000'),
  MAX_SIZE: parseInt(import.meta.env.VITE_CACHE_MAX_SIZE || '50'),
} as const;

export const ERROR_CONFIG = {
  BOUNDARY_ENABLED: import.meta.env.VITE_ERROR_BOUNDARY_ENABLED === 'true',
  GLOBAL_HANDLER_ENABLED: import.meta.env.VITE_GLOBAL_ERROR_HANDLER_ENABLED === 'true',
  REPORTING_ENDPOINT: import.meta.env.VITE_ERROR_REPORTING_ENDPOINT || '/api/v1/errors/report',
  LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL || 'info',
} as const;

export const NOTIFICATION_CONFIG = {
  TOAST_POSITION: import.meta.env.VITE_TOAST_POSITION || 'top-right',
  TOAST_DURATION: parseInt(import.meta.env.VITE_TOAST_DURATION || '5000'),
  ENABLE_PUSH_NOTIFICATIONS: import.meta.env.VITE_ENABLE_PUSH_NOTIFICATIONS === 'true',
} as const;

export const DEV_TOOLS_CONFIG = {
  ENABLE_REDUX_DEVTOOLS: import.meta.env.VITE_ENABLE_REDUX_DEVTOOLS === 'true',
  ENABLE_REACT_QUERY_DEVTOOLS: import.meta.env.VITE_ENABLE_REACT_QUERY_DEVTOOLS === 'true',
  ENABLE_STORYBOOK: import.meta.env.VITE_ENABLE_STORYBOOK === 'true',
  ENABLE_TESTING_PLAYGROUND: import.meta.env.VITE_ENABLE_TESTING_PLAYGROUND === 'true',
} as const;

export const PERFORMANCE_CONFIG = {
  ENABLE_LAZY_LOADING: import.meta.env.VITE_ENABLE_LAZY_LOADING === 'true',
  ENABLE_CODE_SPLITTING: import.meta.env.VITE_ENABLE_CODE_SPLITTING === 'true',
  ENABLE_PRELOADING: import.meta.env.VITE_ENABLE_PRELOADING === 'true',
  BUNDLE_ANALYZER: import.meta.env.VITE_BUNDLE_ANALYZER === 'true',
} as const;

export const SECURITY_CONFIG = {
  ENABLE_CSP: import.meta.env.VITE_ENABLE_CSP === 'true',
  ENABLE_HTTPS_REDIRECT: import.meta.env.VITE_ENABLE_HTTPS_REDIRECT === 'true',
  SECURE_COOKIES: import.meta.env.VITE_SECURE_COOKIES === 'true',
} as const;

export const CONTACT_CONFIG = {
  WHATSAPP_BUSINESS_NUMBER: import.meta.env.VITE_WHATSAPP_BUSINESS_NUMBER || '',
  WHATSAPP_SUPPORT_ENABLED: import.meta.env.VITE_WHATSAPP_SUPPORT_ENABLED === 'true',
  SUPPORT_EMAIL: import.meta.env.VITE_SUPPORT_EMAIL || '<EMAIL>',
  SUPPORT_PHONE: import.meta.env.VITE_SUPPORT_PHONE || '',
  COMPANY_ADDRESS: import.meta.env.VITE_COMPANY_ADDRESS || '',
} as const;

export const SOCIAL_LINKS = {
  FACEBOOK_URL: import.meta.env.VITE_FACEBOOK_URL || '',
  TWITTER_URL: import.meta.env.VITE_TWITTER_URL || '',
  INSTAGRAM_URL: import.meta.env.VITE_INSTAGRAM_URL || '',
  LINKEDIN_URL: import.meta.env.VITE_LINKEDIN_URL || '',
} as const;

export const SEO_CONFIG = {
  DEFAULT_META_TITLE: import.meta.env.VITE_DEFAULT_META_TITLE || 'PHCityRent - Port Harcourt Real Estate Platform',
  DEFAULT_META_DESCRIPTION: import.meta.env.VITE_DEFAULT_META_DESCRIPTION || 'Find your perfect home in Port Harcourt with PHCityRent',
  DEFAULT_META_KEYWORDS: import.meta.env.VITE_DEFAULT_META_KEYWORDS || 'real estate,Port Harcourt,Nigeria,rental,property',
  DEFAULT_OG_IMAGE: import.meta.env.VITE_DEFAULT_OG_IMAGE || '/images/og-image.jpg',
} as const;

export const PWA_CONFIG = {
  ENABLED: import.meta.env.VITE_PWA_ENABLED === 'true',
  NAME: import.meta.env.VITE_PWA_NAME || 'PHCityRent',
  SHORT_NAME: import.meta.env.VITE_PWA_SHORT_NAME || 'PHCityRent',
  THEME_COLOR: import.meta.env.VITE_PWA_THEME_COLOR || '#3B82F6',
  BACKGROUND_COLOR: import.meta.env.VITE_PWA_BACKGROUND_COLOR || '#FFFFFF',
} as const;

export const CDN_CONFIG = {
  URL: import.meta.env.VITE_CDN_URL || '',
  STATIC_ASSETS_CDN: import.meta.env.VITE_STATIC_ASSETS_CDN || '',
} as const;

export const MONITORING_CONFIG = {
  SENTRY_DSN: import.meta.env.VITE_SENTRY_DSN || '',
  SENTRY_ENVIRONMENT: import.meta.env.VITE_SENTRY_ENVIRONMENT || ENV.NODE_ENV,
  SENTRY_RELEASE: import.meta.env.VITE_SENTRY_RELEASE || ENV.APP_VERSION,
  SENTRY_TRACES_SAMPLE_RATE: parseFloat(import.meta.env.VITE_SENTRY_TRACES_SAMPLE_RATE || '1.0'),
  NEW_RELIC_BROWSER_LICENSE_KEY: import.meta.env.VITE_NEW_RELIC_BROWSER_LICENSE_KEY || '',
  NEW_RELIC_APPLICATION_ID: import.meta.env.VITE_NEW_RELIC_APPLICATION_ID || '',
} as const;

// Environment validation
export const validateEnvironment = (): void => {
  const requiredVars = [
    'VITE_API_BASE_URL',
  ];

  const missingVars = requiredVars.filter(varName => !import.meta.env[varName]);

  if (missingVars.length > 0) {
    console.error('Missing required environment variables:', missingVars);
    if (ENV.NODE_ENV === 'production') {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }
  }
};

// Environment info for debugging
export const getEnvironmentInfo = () => ({
  environment: ENV.NODE_ENV,
  appName: ENV.APP_NAME,
  appVersion: ENV.APP_VERSION,
  apiBaseUrl: API_CONFIG.BASE_URL,
  featureFlags: FEATURE_FLAGS,
  buildTime: new Date().toISOString(),
});

// Check if we're in development
export const isDevelopment = () => ENV.NODE_ENV === 'development';
export const isStaging = () => ENV.NODE_ENV === 'staging';
export const isProduction = () => ENV.NODE_ENV === 'production';

// Initialize environment validation
if (typeof window !== 'undefined') {
  validateEnvironment();
}
