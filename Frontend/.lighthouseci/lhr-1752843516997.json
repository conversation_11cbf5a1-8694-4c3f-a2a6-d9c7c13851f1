{"lighthouseVersion": "12.6.1", "requestedUrl": "http://localhost:4173/auth", "mainDocumentUrl": "http://localhost:4173/auth", "finalDisplayedUrl": "http://localhost:4173/auth", "finalUrl": "http://localhost:4173/auth", "fetchTime": "2025-07-18T12:58:27.476Z", "gatherMode": "navigation", "runWarnings": [], "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "environment": {"networkUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "hostUserAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36", "benchmarkIndex": 2917.5, "credits": {"axe-core": "4.10.3"}}, "audits": {"is-on-https": {"id": "is-on-https", "title": "Uses HTTPS", "description": "All sites should be protected with HTTPS, even ones that don't handle sensitive data. This includes avoiding [mixed content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), where some resources are loaded over HTTP despite the initial request being served over HTTPS. HTTPS prevents intruders from tampering with or passively listening in on the communications between your app and your users, and is a prerequisite for HTTP/2 and many new web platform APIs. [Learn more about HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "redirects-http": {"id": "redirects-http", "title": "Redirects HTTP traffic to HTTPS", "description": "Make sure that you redirect all HTTP traffic to HTTPS in order to enable secure web features for all your users. [Learn more](https://developer.chrome.com/docs/lighthouse/pwa/redirects-http/).", "score": null, "scoreDisplayMode": "notApplicable"}, "viewport": {"id": "viewport", "title": "Has a `<meta name=\"viewport\">` tag with `width` or `initial-scale`", "description": "A `<meta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "metricSavings": {"INP": 0}, "details": {"type": "debugdata", "viewportContent": "width=device-width, initial-scale=1.0"}, "guidanceLevel": 3}, "first-contentful-paint": {"id": "first-contentful-paint", "title": "First Contentful Paint", "description": "First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).", "score": 0.02, "scoreDisplayMode": "numeric", "numericValue": 6604.4694, "numericUnit": "millisecond", "displayValue": "6.6 s", "scoringOptions": {"p10": 1800, "median": 3000}}, "largest-contentful-paint": {"id": "largest-contentful-paint", "title": "Largest Contentful Paint", "description": "Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0.08, "scoreDisplayMode": "numeric", "numericValue": 6622.4694, "numericUnit": "millisecond", "displayValue": "6.6 s", "scoringOptions": {"p10": 2500, "median": 4000}}, "first-meaningful-paint": {"id": "first-meaningful-paint", "title": "First Meaningful Paint", "description": "First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).", "score": null, "scoreDisplayMode": "notApplicable"}, "speed-index": {"id": "speed-index", "title": "Speed Index", "description": "Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).", "score": 0.37, "scoreDisplayMode": "numeric", "numericValue": 6604.4694, "numericUnit": "millisecond", "displayValue": "6.6 s", "scoringOptions": {"p10": 3387, "median": 5800}}, "screenshot-thumbnails": {"id": "screenshot-thumbnails", "title": "Screenshot Thumbnails", "description": "This is what the load of your site looked like.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "filmstrip", "scale": 3681, "items": [{"timing": 460, "timestamp": 227774559798, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 920, "timestamp": 227775019923, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 1380, "timestamp": 227775480048, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 1841, "timestamp": 227775940173, "data": "data:image/jpeg;base64,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"}, {"timing": 2301, "timestamp": 227776400298, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAECAwQFBgcI/8QASxAAAQMCAgQKCQIDBAkEAwAAAQACAwQRBRITITFRBhUWQVJUVZKT0hQXImFxgZSh0TKRB6OkIzVzsiUzNkJTYnKCsSQmdMGis/D/xAAbAQEBAQEBAQEBAAAAAAAAAAAAAQIDBAUGB//EADYRAAIBAQQIAwgCAgMBAAAAAAABAgMEEVKREhMUFSFRU9ExofAFFkFhcYGSsSIzQsEGI+E0/9oADAMBAAIRAxEAPwC2iIgCIiAIiIAiIgCIiAIiIAoPMpRAU6t6at6n5J8kBChTzqEAREQBERAEREAREQBERAEREAREQFaIiAIiIAiIgCIiAIiIAiIgCg/FCo1ICfmoPxTUiAFQiIAiIgCIiAIiIAiIgCIiAIiIAiIgK0REAREQBERAEREAREQBEUGyAa/cmv3KNSakBJuoRQgCIiAIiIAiIgCIiAIiIAiIgCIiAIiICtERAEREAREQBERAEREAUfJPmnzQD5KD8FPzUH4oAoREAREQBERAEREAREQBERAEREAREQBERAVoiIAiIgCIiAIiIAiKNaAjUmpTrTWgI1KFOvWoQBERAEREAREQBERAEREAREQBERAEREAREQFaIouEBKKLqLoCpFTdLoCpFTcpcoCpRq3qFCAq1byo+ahEAREQBEuougJRRdLoCUUXS6AlFF0ugJRRdLoCUUXS6AlFF0ugJRLpdAEREBtzSQ9D7lU+iQ9D7lZKpX8+VttHUlmz+lbBZelHJdiwaSHofcqPRIeh9yr5ULStlo6ks2XYLL0o5LsY/osPQ+5Q0sPQ+5V9HLe2WjqSzZdgsvSj+K7GP6NF0PuVHo0XQ+5V9QtK2WjqPNjYLL0o5LsWfRouh9yqfR4uj9yr6pW1a6+N5suwWXpR/Fdi16PHf9P3Kj0ePo/cq7zotq1V8bzZdgsvSj+K7FkwR9H7lQYGdH7q8VSbLStVfG82Xd9l6UfxXYtaFnR+6GFm77q4oK0rTWxvNl3fZelH8V2LeiZu+6jRN3fdXFBW1aa2N5sbvsnSj+K7FvRt3KNG3cripWlaa2N5su77J0o/iuxQWN3KMjdyrKhbVoq4nmy7vsnSj+K7FGRu5CwblVzqHLStFXE82N32TpR/FdinKNygtCqUFaVerieZV7OsnSj+K7FJAsosqjsULar1MTzLu6ydKP4rsQoUlQtKtUxPMbusnSj+K7EXKZihULSrVMTzLu6ydKP4rsdEqVUqV+SQIKFCoK0ikFCiFbRSlQpULaB0bOCdRLjlJh0NRE8VMAqWzgHKGWJuf2+6wzwfqGUeLVMr2Mbh0ohcLfrcXWsP/PzXTU2MUTOBTKhlTGzFoKZ1CIy4Z3NL2kEDbYNWTjGN4S2TCpoZopIKutbXVUTHBxi9hos4D33NvcvtbNZ3HSTu+Pjz4LJn51W62qei1fxa8PFx4vNcEcJFhNYa2jgqKeam9KkbHG6WMtBuQLi+3aj8KrDXVdNTU81S6mkdG8wxl2wkX1bNi73E8WpxOxrp8PdBLiEUzXiudM4APBzAHUwW1HWFi+msq6KeHCsXpcPqI8Ulnle+YMErC4lrgf8AeAFtXP8Asq7HSXBSv/f0/wBm4+1bQ7pOKS+9y4+Luvfy+pwlLQ1dY97aSlnnczW4RRlxHxsFENDV1AvT0s8oz6O7Iy72t2obfcu3p6z0rCqimoMapqbEG4gZpqh8mgFQy1sw91+b3K5jOPUvoGPyYVWsikmronR6N+R7gGNDnNG2xIOv3rCslNR0nL9fP9fE77yrubhGC8Uvjw4pXvh4O+9fJHBvoaxkMkr6SobFE7I95jIax24m2oql1FVtpBVOpZxSk2ExjOQ/92xei4xjdNUYrwni4xhkoZMPtAwTAsdJlB9nXYuuTs1q7i+N0L6WoqKB1B6FLRaFsUla4OHs/o0IuARvt8112Onx/n4d35HNe1q/8U6fjd5pO768fLwOHwLAm4nQVtZNXQ0dPSuY175Gk/quBsVrF8ArMOrKaBuSs9JjEsDqa7xI07CBa63vAuoiZwdxqm9Kw2GpmfCY217mhjgCSdTr3/bctvU1WHT4xDLxpD6czD3RyilqdFBI8EARtef0i19ltnMtU7PTnSi/i+7/ANFr+0K9G0ziuMV8Lvkmn4c/jf8ALh4nnb6KrZVilfSztqibCExkPJ/6bXWfhfB+rrayrpZmupJqamfUubMwtJDbare+67ufFqB1ZSCLEKOCrdhctNHOJzI2GW4sC869/tFYuA18eH4llxzGaTEGsw2dhYJR7N3sOj0n+8SAfgtxslNS4u9GJ+1rRKm3GNzu+d74+K4XXcPjnz43AuD1Zi9fS04Y+nZUh5jmljIY7KCTY8+zmWqpYXVFRHE0OJe4D2Rc/Ic69BmxOI8LsNxbDq2h9FfTFscFRM2MUtmEGNwH6fdq1rz2nkEU7JHNzhjg7Lci9veNi4VqSgroePH9K4+lZrVVqqU5cOC4cnfJNfa5XmdiOFOpads8bzIwfrI2C98rh7j9iCFg1lPJSzaKYNDwASAb2uLr0fAazD6rDZZnSMcQSZWyABw+I2a7a7ajtXBcIKiKqxiqnpzeJ7rgr51mtE5y1cl4EsNpq1KjpzXga5CiFe9H1UUnYo51J2KFtFIKsNqYnFoa8EuJaBbXcbVfKwoqLR1RqBJeV36zbU4cwtzWXSN3xMSck1ooyyoUlQqjodEqVUqV+WR4y5DBLO4tgifIQLkNaSrcjHMtnaRcXF1l4fPFC5+nLsjrXbo2yB3xBIt8QdS22HV0LpYzHIaaOHIXlzm3c0OeS3WRcWcNlzq2L1UqUZpcbmeWtXnTbejekc2VMjS0lrgQQs6trhUUwhOchmiyA7G5WZXW+Jsfkr8+KMfSyQASCN4fdthYkkFp281lVCF7/kdNbUuX8TUOaQASCARcXG0KlbmDFA2mpo3TVMboYjGHMAdl9su1XI2ggH/pG1U4RiFLSVc0s0cgY94OjYLgsubtIuPdtuNWxbVODaWl4kdaolJ6Hh58foahULcNxFgNORNUMEbMoaGC0RyWzM9raTr5v31ocTj9DlhDnuc9xJL476TWPad7Vr6tx+OtbVOOL1mXXT4fw9ZevpxNNzot9Ni8MktS5z6giaMNdZli4+1q/WbDWNRzDVs1BY9BVR0+GvfLlfI2TLFHca2uA0l9wsAP+4rerjfdpBV5tXuHLz+3rxNQVSfgt1U4jTzlwlfUPu0/2hY3N+oODbX2CxG3n2KzXYiypq6VztL6NEblmoHW8kkc17WHyV0Ir/I3GtN/4esjVKCt/VYvDK8ujlqY3uja18oYC6SxfcG7ybEObtJ/TsVhmJRtMB01S0MjyNaGNIhdltmZr231837rpq4r/L1mZjaKjV7h++3ryNMgBIJDSQBc2GxbybFaWWGQZJWyZJGts0WeXsaMx16jdpJ27VFTjbp4qxhkqAZtI1p1foLmOa069gs4e7MtqnHEFXqu66n6yNEjGOke1jGlz3EAAC5JPMt/huL0tJSU8b2zvdG9ry21wHB97j2rfp1bL++yUmORMkglldUNmbozNI1ocZspPskk7LZf2+C3GnHhfIsrRVV+jT8PXI50qFs6CtigoKmnmMtpdeWMWzatV3X2A8xB91tq2NTjlLJIwtZM9rBmaXtF8zXZo73cdQ1g+4mwVjCLV7ZudepGWioX+voc0pkY5hAc0tJAIuOY7Ct+7G4DFSs0bm6NoBsy5Y/Rlpe0l1rlxzEWFyBr1XVuHEY34w6pNRMwCmLNM4ASFwiy5gL7Sde35rerj8GZjaKnFuF1yb/88DQoVu3YuzSsyPnazO5znhozFxja0Ptf9WYF23n2rJdi1OMMN5JXF8kofFYf2t4o2hz9eq7gXc+sHn1rapx48Q7TUV3/AF+Prkcydil0b2C7mlovbWOf/wDiFvsQx1krXGka6A5HNjDWkGIFzDlDsx9kBp2AbdmtSMdjdUmaV9S+Ug2lcLujJDBqIeCf0kbRqPyW9CPheaVoqtaWr5+vA50qFuKzEoqjD5YLvDnSukaxkeRty69z7Rvq91xvsFp1GkvA9FKcpq+SuIKhSVCqOp0SpW75MY32bUd1U8l8b7NqO6vzqstbA8mfK2yz9SOaNKVBW7PBfG+zKjuqOS+N9mVHdWlZq2B5Mu2WfqRzRpEK3XJbG+zKnuqDwXxvsyp7q2rNWwPJl2yz9SOaNIoW75LY52ZU91RyWxzsyp7q0rNWwPJjbbP1I5o0qoW95LY52ZU91U8lcd7Mqe6tqzVsDyZdts/UjmjR86Ld8lcdv/ddT3U5K472XU91bVnq4Hky7bZ+pHNGjKpK3p4KY72XU91QeCmPdl1PdWlZ6uF5Mu22bqRzRolBW85J492XU91DwUx7sqp7q2qFXC8mXbbN1I5o0Sg2W95J492XU91RyTx7sqp7q2qFXC8httm6kc0aJUrfckse7Kqu6o5JY/2VVd1aVCpheRdts3UjmjQlQt8eCOP9lVXdUckcf7Kqu6tqjUwvIu22bqRzRoedQ5b7kjj/AGVVd1DwRx/smq7q0qNTC8ht1m6kc0aBQVvuSHCDsmq7qHgjwg7Jqu6tKjUwvIqt1m6kc0aA7FC354IcILf3TVd1RyP4Qdk1XdW1SqYXkXbrN1I5o0B51C3/ACQ4QdkVXdUcj+EPZFV3VpUp4XkNus3UjmjQFQt+eB/CHsir7icj+EPZFX3FtUp8mXbrN1I5o+lURF90/lAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAWn4ZVc9BwQxyspJDHU09DPNE8AHK9sbiDY6toC3CIDy6n4YYpT4CammqaCulZT1FVIXVTakAQsjdlBjawNJzOFiDbUdexXcT4f4hholhnZQuqqasfDIAwtbNGNEfYzPFnWl12zn2b5bbO3fwgw0SPjjnfO5hyuFPC+bKdxyNNio4/oehXfQT+RY19Lmszepqcnkc3wY4RVMuN45xjUf+ho6YzZSBZmWqqmF19v6Ym/stNhfDbF4KKcYm0U09RLT1EM2IR5I6eCYkG4aRmawi1yR+ttyF33H9D0a76CfyJx/Q9Gu+gn8ia+nzWY1NTk8jzzDOGuLVMGFwy1dLHJLVQBznM/tKlr6tzHaPmyta0NOon2hrBtfZUfDPGnyYE2enw//SMMVRYER52vcGljC+QXc0XcbB20Cw/Uex4/oehXfQT+RRx/Q9Cu+gn8ia+nzWY1NTk8jiZOHuItwyqmdxW2aCpbHIcwdHGwiQ2B0oD3+xaxcx2u+XYD6PSTCppIZ2hwbKxrwHNLSLi+sHWPgVruP6HoV30E/kU8f0PRrvoJ/IjrU8SzGqqcnkbVFquP6Ho130E/kTj+h6Nd9BP5FNdTxLMuqnhZtUWq4/ouhXfQT+ROP6LoV30E/kTXU8SzJqp8mbVFquP6LoV30E/kTj+i6Fd9BP5E11PEsxqp8mbVFquP6LoV30E/kTj+i6Fd9BP5E11PEsxqp8mbVFquP6LoV30E/kTj+h6Nd9BP5E11PEsxqp4WbVFquP6Ho130E/kTj+h6Nd9BP5E11PEsy6qeFm1Rarj+h6Nd9BP5E4/oejXfQT+RNdTxLMaqeFm1Rarj+h6Nd9BP5E4/oejXfQT+RNdTxLMaqeFm1Rarj+h6Nd9BP5E4/oejXfQT+RNdTxLMaqeFm1Ral3CHDmAuldUxMG18tJMxo+JLQAtnTzRVELJoJGyRPF2vabgjeCrGpGTui7ySpyir5K4rREWzAWg4VzSOFLQRXPpJc6RoOUvY23sg813OaL7iVv1z2Pf7R4N/hy/54VwtP9b+37O1n/sX3/Rn0uDUkVOyOaGOYtFvab7I9zW7Gj4K7xTh/Uabwgq8TnkpqJ8kDA+W7WsaeclwA5xv3hap+K1cT2e1DMBIxkjG072FgLgNZc7Vt3G/3XRUopXJHN1JN3tmy4pw/qNN4QTinD+o03hBWOFUskHBfGJYXujljo5nMew2LSGEgg8xWrwzhFI+h9MrJIpmuMTRBTU7mva6RwaAS51jrPNZXVxfwJrJczd8U4f1Gm8IJxTh/UabwgtUeFMLaprZKWoiphTzTzSvteIxPDXNLQSTr3X2i19dsij4RUtY9kdPDUPmMmidGMpLDYG5IdltZwOondt1Jqo8hrHzM3inD+o03hBOKcP6jTeEFTU4pBTzyQPbIZWGL2QBdwkcWgjXsuDfdYrFh4RUkrIpBFUNjmMege5gAma97Wh7dey7m3vY2OxNXHkXWPmZnFOH9RpvCCcU4f1Gm8IKzBjEVRVMp4YKh7y+VhIAs3RuyuJN9lyLc/uVup4QUVPiZoZC/SAhhd7Ng8tzBtr3uRbXa3vumrjyJrHzMrinD+o03hBOKcP6jTeEFg03COGeSJpo62NshYM72ts0P/QTZxNnHVq2HbZGcIIaiWSKFr4pY5I2ubIGuJa8kA6nath26xzhNVHkNY+ZncU4f1Gm8IJxTh/UabwgowKsfiGCUFZK1rZKiBkrmt2AuaCbfus5NXHkXTlzMLinD+o03hBOKcP6jTeEFmopq48hpy5mFxTh/UabwgnFOH9RpvCCzUTVx5DTlzMLinD+o03hBOKcP6jTeEFmomrjyGnLmYXFOH9RpvCCcU4f1Gm8ILNRNXHkNOXMwuKcP6jTeEE4pw/qNN4QWaiauPIacuZhcU4f1Gm8IJxTh/Uabwgs1E1ceQ05czC4qoR+ilijd0oxkcPmLFaXD2PwnhC2lBAp6vMLdJ4bmD7cxIDwbbS0HnXTrnsV/wBq8H/xD/8ApmXGtBR0ZRVzvXmzvRm5aUXxVz/R0KIi9J5guex/VwhwcnYI5df/AHwroVqeEeHPr6aN8ADqiBxc1jjYSAgtcwnmuCdfMbHmXGvFyg7vl5O87UGlNX/PzRsqmFtRCY3lwBIILTrBBuD+4CxXYcJMomqqmVgcHZHObYkG42DeAtNT8J4aZmjxO8MjdX9taNx+IdYH4tJB9yu8sMJ6zF48fmUVqpYiuy1V/ibTHqOTEMDxGihLWy1NNJCwvNgC5pAv7taxocChjp46d1VVS08bo3Rxvc2zCxwc21gDtaFicsMJ6zF48fmTlhhPWYvHj8yu1UsSJs1XCzMlwCile5zxKczZmvAfYObKQ5wPzAItrFlkUmGsp5GSOnqJ5WlxDpX32gC1gABqA2fFavlhhPWYvHj8ycsMJ6zF48fmTaqWJDZquFmwq8LZVY3R10rW2pWPDNZuXO1XPNYDN3ubnsN4OUbafQh9Ro2ta2EaT/UBrg5oZusWt232AbAsblhhPWYvHj8ycsMJ6zF48fmTaqWJDZquFmzw7CoKBwdE6Vz7yEue65Je4OcT8wkmFxPrJKgSzt0hzSRtfZj3Zctzz7Lar21DUtZywwnrMXjx+ZOWGE9Zi8ePzJtVLEhs1XCzZDCKYNa0Z7AQge1/wjdv32rGouDtJSFuSSd4a2NjA5wsxrCS0CwG8+8rG5YYT1mLx4/MnLDCesxePH5k2qliQ2arhZu8PpI6Chp6SDNoYI2xMzG5sBYXV9c7ywwnrMXjx+ZOWGE9Zi8ePzKbTSxIbNVws6JFzvLDCesxePH5k5YYT1mLx4/Mm00sSLs1XCzokXO8sMJ6zF48fmTlhhPWYvHj8ybTSxIbNVws6JFzvLDCesxePH5k5YYT1mLx4/Mm00sSGzVcLOiRc7ywwnrMXjx+ZOWGE9Zi8ePzJtNLEhs1XCzokXO8sMJ6zF48fmTlhhPWYvHj8ybTSxIbNVws6JFzvLDCesxePH5k5YYT1mLx4/Mm00sSGzVcLOiXPYpr4V4RbmkN/Bl/IVL+F2HuaRTSRSy8zRIHE/JmYn5BTgdHUVOIHFKxj4xlIiY8Wc4usC8t/wB3U1oA2gA31krnOrGq1Gm7+KeRuFKVJOVRXcGjokRF6zyhEVmuq4KCiqKurkEVNTxulleRcNa0XJ1bgEBeRa3DMcw3EopJKSqa5sbgx4kaY3NJFwC1wBFwbjVrWcZogXgysBYLuGYeyPfuQFxFQ2aJ1ssjDfZZw16r/wDhQJ4Te0sepuc+0P07/ggLiLEqMSo6eSnjlqYw+okEUQBuXOsTbV/0n9lloAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAtdwkw92L8HcUw2OQRvrKWWna9wuGl7C25/dbFEBwHCL+Hra7B20dDUtEr82nmqyZnvOiMbSHOuWhoOxtr69e29NVwCnnq8YkMtEY65lix0brufnidmLr5h/q9gOUkg5dVj3+e/6WkjeEzHoO+35V0mS48zrf4a108cDocYZT1EVOxoMUIa0TMkflfZth/q5ZGHUL3B1LLrP4eOe6sjpJ6WKnkgmiikMRM4D6cQtjc6+uNtswHubuufQcx6Dvt+UzHoO+35V0mLkcO3gLoOElNXUgw+KliqYqloFPaVgZCYzG1w1BpJzfEn4rulTmPQd9vymY9B32/Kjd5SpFTmPQd9vymY9B32/KgKkVOY9B32/KZj0Hfb8oCpFTmPQd9vymY9B32/KAqRU5j0Hfb8pmPQd9vygKkVOY9B32/KZj0Hfb8oCpFTmPQd9vymY9B32/KAqRU5j0Hfb8pmPQd9vygKkVOY9B32/KZj0Hfb8oCpFTmPQd9vymY9B32/KAqRU5j0Hfb8pmPQd9vygKkVOY9B32VQIcLjYgCIiAKmQXYRv1KpQ/Z8x/5QEqzDVU80skUM8UkkZs9jXAlvxHMqMTndTUE80eXMxtwXbB7ytNSSU9O+kbTYnTVT2WhDAWlzmuc251G99V1bgdEiLicOiqoMFgjpInQVZdTMkdFh74pGtMjQ+7nXDtRNzbeUSvI2dsi4iduMxVTp4jWS1EdLWxQF0epzhI0x5rC1y0aidtgs7BnYtUVETaqedtOJnO1MNy0MbZri6NurNfWAN19SuiLzqUWjxOoro8cpqOnzGGqDXh7bf2QjN5L+5wLG/ElaqnrMYbSwyvNc6a0L6yN1PqidpW52x2bdwyl+zNqaCDc65cLzsUXP4TJiFVXMfUPqo6dslS4NdFkDgJAIwbi9st7bLqxLV1zseqY6eWqfo6yKNsIivEIjGwvJdbUfaJ27QN+tcLzp0XF01XjElLWueauOwgczSRuLrlz87biIW1BoOVrg26y4qjEpKvDszq5rJIWB7HR2c13tXc46PKea4u0iw1a7K6IvOpRcdh9VwlkqI/SISGyi+UssIzEwtdc22Pkykf8ALchXKJ+K1EsUIqMQEDxCZJpYAx7X2fpGi7dmpmu1gTqKaIvOtRcpQemzcIqOSpNY4sdUiQPhyxRi9o8rrWN2+839y6tRq4J3hERQoREQBERAEREARFpcfpp5iC0OfF7OVrdYabnMSLG9xYD2Tb7oDdKlos91ufWtfgUdRFTuE7XMZqyMcdY1e0fcCdYHN7tg2I/WfgP/ALQEoiIAoffKbC5GuylEABBFxrBSw3BUljTvHwJCjRt3v75QFaKjRt3v75TRt3v75QFaKjRt3v75TRt3v75QECnhFSagRM05ZkMmX2st72vuurio0bd7++U0bd7++UBWqWRsY6RzGNa6Q5nkD9RsBc/IAfJRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtUtOZzjzbB700bf8AmPxcVUNQsNiAIiIAqZHsijdJI4NY0Xc4mwA3qpaPhs63BXE7f8ErFSehBz5I3ShpzUObuLD+G/B5ji04iLg21RSEfuGqnlzwd7Q/kSeVeJFUr8pv+0YY+fc/ZL/jdmxSzXY9v5dcHO0f5EnlUcuuDnaP8iTyrxBUlaXt60YY5PuX3bsuKWa7HuPLvg52j/Ik8qcu+DnaP8iTyrw7mVK1vy0YVk+4927Lilmux7ny84N9o/yJPKo5ecG+0f5EnlXhig7Fpe26/Jefcvu1ZcUs12PdOXvBvtH+RJ5U5e8Gu0v5EnlXhJULS9s1+S8+5fdqy4pZrse7cveDXaX8iXypy+4NdpfyJfKvCCoK1vivyXn3HuzZcUs12PeOX/BrtL+RL5VHL/gz2l/Il8q8GUFaXtetyXn3L7s2XFLNdj3rl/wZ7S/kS+VPWBwZ7S/kS+VeCKkrS9rVuS8+492bLilmux756weDHaf9PL5U9YPBjtP+nl8q8CKhaXtStyXn3L7sWTFLNdj371hcGO0/6eXyqPWFwY7T/p5fKvAedU860vadXkvPuX3YsmKWa7H0B6wuC/af9PL5U9YfBftP+nl8q+f3KlaXtKryXn3HuxZMUs12PoL1h8F+0/6eXyqPWJwW7U/p5fKvn0qCtL2hU5L19y+69kxSzXY+g/WJwW7U/p5fKnrF4Ldqf08vlXz2dmpUqq31OS9fce69kxSzXY+hfWLwW7U/p5fKuhwnE6LF6NtVhtQyogcbZm8x3EbQfcV8sFeyfwKd/ofExzekD/KF6bPapVZ6MkfM9r+wqFis+upSd6a8buyPT0RF7j8sFoOG5/8Aa2Jf4JW/XP8ADf8A2XxL/CK42n+mf0f6O9m/uh9V+zwwqFJUFfztH9OIVJVSpK2ijmVKq5lStopCg7FKg7FtFKSoUnYoXRFM/Cg8elSQBpqIotIy7Q4izm3IB5wLn3WupxN0stDSz1bQKmV8ji4tDXPZZgaTbbrz6zt1rAa90bw+NzmuGsOabEJUTS1Er5Z5Hyyu1ue9xJPzK9CmtDROGpbqqfD/AH4XXfT4kU4DqiIFucF4GW9s2vZfmXa4hhgkbNL6O2jjDKstjdSsilbljuG6rtezc/bt1rhkc4naSebautGqqaaavvOdpssq8lKMrrr/AIX+P3Oy5K0WkLXenMjbOIhMXNLZm6J787PZ2XaOc6jv2a7DKCkHCXAGxxufS1gjkdFOQ+13uaQdQBHs7udaipxGeelbTu0TIWuDssUTWZnAEAmw1mxP7lYV9YXd1qd6cYnGnY67jJVKnjw8rv3x+13xOgqsHo2YbNIx1QKmOjjq7lzchzSBpba1+e97rnCqifiqSuc5KTVyuPdZ6UqaanLSvZTzqFPOoRHoBVKlyhbQIKgqSocto0UnYo51UdipK2gQV7F/Az+6sT/+Q3/KF46V7D/Az+6sS/8AkN/yhe2x/wBqPh/8j/8Ahl9V+z1IIgRfXP52Fi4hTRVlLLTzszxSNLXN3grKUEXUaTVzKm070eeTfw9oM5LairAPNmbq/wDxVr1e0XWavvN8q9FMYKp0Q3BePdtl6aPdvS19Rnnfq9ous1feb5VHq8oes1neb5V6LohuCaIbgm7rLgQ3pbOozzr1eUPWazvN8qj1d0PWazvN8q9G0Q3BNENwV3fZsCG9LZ1Gec+rqh6zWd5vlT1dUPWazvN8q9G0Q3BNENwTd9mwIu9bZ1Gecermg6zWd5vlUerig6zW95nlXpGiG4JohuCuwWfAhvW2dRnm/q4oOtVveZ5U9W9B1qt7zPKvSNENwTRDcE2Gz4EN62zqM829W1B1qt7zPKo9W1B1qt7zPKvStENwTRDcFdioYEN62zqM819WuH9are8zyqPVph/Wq3vM8q9L0Q3BNENwV2OhhQ3rbOqzzT1aYf1qu7zPKo9WeH9aru8zyr0zRDcE0Q3BNkoYUN7Wzqs8z9WWH9aru8zyqPVlh3Wq7vM8q9N0Q3BNENwV2SjhQ3tbOqzzL1Y4d1uu7zPKo9WGHdbru8zyr07RDcE0Q3BNlo4UN7W3qs8x9WGHdbru8zyp6r8O63X95nlXp2iG4JohuCuzUsJd7W3qs8w9V+G9br+8zyp6rsN63X95nlXp+iG4JohuCuz0sI3tbeqzzFv8LMMO2sxDvM8q7fgrwfo+D1AaWha+znZ3vebued5/YbFuBGArjW2Wo0oQd8Uca9vtNojoVZtokIiLoeQIiIBcbwlxvCWG4JYbggFxvCXG8JYbglhuCAXG8JcbwlhuCWG4IBcbwlxvCWG4JYbggFxvCXG8JYbglhuCAXG8JcbwlhuCWG4IAtfNjNBC+RkkxBjuDaNxBItcAgWJFxqFytgtTLgVNLO+R0k+RzjI2LMMrHkgucNV7m3OSNZta6A2rHB7GuF7OFxcEH9jsU3G9EIB2hALjeEuN4Sw3BLDcEAuN4S43hLDcEsNwQC43hLjeEsNwSw3BALjeEuN4Sw3BLDcEAuN4S43hLDcEsNwQC43hEsNwQC2xAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBaThXWehUtK91T6NC6pY2WTMG2br5zsW7RZnFyi0jUJKMk2jjzj9bTiJmZkjHucYXysOeqGksGttYA5dd7G4sbbVnUVbX19ToXzRQtdeVjo4zcBkti03OvMANeq1yuiRco0pJ8ZcDrKrBrhG5+vX/AIcy2vrn4NRubVwvq3VYhke2OzR7RBBbf/7CxDwlrH1tHSRGnFTMwAtczVnIf7Q9q+W7Rzc9r3XYojpS4XSKq0ON8DiqrhTiDYIZ4qeBsM5cIjJqBc0MGjJJFnF5eBa/6NhWzx3HpMPxSjpWaO8pjzMe3W4OeG+ycw2XubNPNe110SIqU7n/ACDq021dDn8TmeD2O1dbTSyVrYriihqxoYyLZw+7bXN7ZPusKl4U1E9M17pqOKMyuZ6S9l4yQxjg2zXn2iXEanH9J1XXZomrnclpE1sL29E5OXhFVM9MJNMySF5a6BzHZ4WaQNErzexaWkv5tXPtIqpeEsrmRifQiSZ8bYCGkCdpnLHObr2ZcrhuzbSF1SJq5336Q1lO67R8/XrM4wY9iT6eKSSalga4QyufojZrHSFjgbu9wN/j8Vt6J1W2nxR76xzmMuyJ5Akc1zQcziGjef0gc3vst4isaUlxcrxOrF8IxuOHpsfqhCx8DhVODpPaZJpYyRHf2SA24vrIIuNg5lck4S149I0L6SVkEU02kbE4CRsbYiABm1X0h16+bbz9jNEyaJ8UrQ6N4LXNPOCrdHSQUURjpowxpJcdZJJPOSdZK5qjUXDSNutSfHQ4msxnGHYfWtgOjBla0xBwN3uzgOA+AN1jcGccqMTqnRymCRojL3mJpGhdmsGuuTckaxs2Lo1bp4I6aFsUDAyNuxo5l1cJ6d+lwOanT1bjo8eZcREXU4hERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAfKjsXxPtGt8d35Vp2L4p2lW+O78qyQrbmoC87GMV7SrfHf8AlUHGMV7TrvHf+VZLVSWIC/xxivadd47/AMpxxivadd9Q/wDKx8iZEBf44xXtKu+of+U44xXtKu+of+VYyJkQF/jjFe0q76h/5TjjFe0q76h/5VjImRAX+OMV7SrvqH/lOOMV7SrvqH/lY+RMiAyOOMV7TrvqH/lOOMV7TrvqH/lY+RMiAv8AG+Ldp131D/ynG+Ldp131D/yrGRMioL/G+Ldp131D/wApxvi3add9Q/8AKsZEyKAv8b4t2nXfUP8AynG+Ldp131D/AMqxkTJ70Bf44xbtOu+of+U44xbtOu+of+VYye9MnvQF/jjFu0676h/5TjjFe0676h/5VjImRAX+OMV7TrvqH/lOOMV7TrvqH/lWMiZEBkDGMV7TrvqH/lSMYxXtKu8d/wCVjZFORAZQxjFe0q7x3/lXG4xinaVb47/ysINVYagM5uMYp2jW+O78q5xvinaNb47vysBrVXZAVkKkhXFBCAtFqjKFdslkBZypkV2yWQFrImVXbJZAWsqZVdslkBayplV2yWQFrKmVXbJZUFrKmVXbJZAWsqZVdsllAWsqZVdslkBZy+5MvuV6yW1oCzkTIrtksgLWRMiu2SyAtZFORXLJZAUBqkBV2U2QFICmymylAVJZEQCyiylEBFkspRARZLKUQEWSylEBFkspRUEWRSiAhFKa0BCKdahQEIpRAQllKICLJZSiAiyWUogIsllKICLJZSiAIiICUREAREQBERAEREAREVAREQBERAPmnzRFAPmoREAREQBERAEREAREQBERAEREAREVBKIigCIiAIiIAiIgCIioCIiAJrRRqQE60UakUAREQBERAEREAREQBERAEREAREVAREQEoiKAIiIAiIgCIioCIiAIiIB8k+Sj5p81ASoREAREQBERAEREAREQBERAEREAREVAREQEoiKAIiIAiIqAiIgCIiAjmQoigCIiAIiIAiIgCIiAIiIAiIgCIioCIiAIiIAiIgP/2Q=="}, {"timing": 2761, "timestamp": 227776860423, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAECAwQFBgcI/8QASxAAAQMCAgQKCQIDBAkEAwAAAQACAwQRBRITITFRBhUWQVJUVZKT0hQXImFxgZSh0TKRB6OkIzVzsiUzNkJTYnKCsSQmdMGis/D/xAAbAQEBAQEBAQEBAAAAAAAAAAAAAQIDBAUGB//EADYRAAIBAQQIAwgCAgMBAAAAAAABAgMEEVKREhMUFSFRU9ExofAFFkFhcYGSsSIzQsEGI+E0/9oADAMBAAIRAxEAPwC2iIgCIiAIiIAiIgCIiAIiIAoPMpRAU6t6at6n5J8kBChTzqEAREQBERAEREAREQBERAEREAREQFaIiAIiIAiIgCIiAIiIAiIgCg/FCo1ICfmoPxTUiAFQiIAiIgCIiAIiIAiIgCIiAIiIAiIgK0REAREQBERAEREAREQBEUGyAa/cmv3KNSakBJuoRQgCIiAIiIAiIgCIiAIiIAiIgCIiAIiICtERAEREAREQBERAEREAUfJPmnzQD5KD8FPzUH4oAoREAREQBERAEREAREQBERAEREAREQBERAVoiIAiIgCIiAIiIAiKNaAjUmpTrTWgI1KFOvWoQBERAEREAREQBERAEREAREQBERAEREAREQFaIouEBKKLqLoCpFTdLoCpFTcpcoCpRq3qFCAq1byo+ahEAREQBEuougJRRdLoCUUXS6AlFF0ugJRRdLoCUUXS6AlFF0ugJRLpdAEREBtzSQ9D7lU+iQ9D7lZKpX8+VttHUlmz+lbBZelHJdiwaSHofcqPRIeh9yr5ULStlo6ks2XYLL0o5LsY/osPQ+5Q0sPQ+5V9HLe2WjqSzZdgsvSj+K7GP6NF0PuVHo0XQ+5V9QtK2WjqPNjYLL0o5LsWfRouh9yqfR4uj9yr6pW1a6+N5suwWXpR/Fdi16PHf9P3Kj0ePo/cq7zotq1V8bzZdgsvSj+K7FkwR9H7lQYGdH7q8VSbLStVfG82Xd9l6UfxXYtaFnR+6GFm77q4oK0rTWxvNl3fZelH8V2LeiZu+6jRN3fdXFBW1aa2N5sbvsnSj+K7FvRt3KNG3cripWlaa2N5su77J0o/iuxQWN3KMjdyrKhbVoq4nmy7vsnSj+K7FGRu5CwblVzqHLStFXE82N32TpR/FdinKNygtCqUFaVerieZV7OsnSj+K7FJAsosqjsULar1MTzLu6ydKP4rsQoUlQtKtUxPMbusnSj+K7EXKZihULSrVMTzLu6ydKP4rsdEqVUqV+SQIKFCoK0ikFCiFbRSlQpULaB0bOCdRLjlJh0NRE8VMAqWzgHKGWJuf2+6wzwfqGUeLVMr2Mbh0ohcLfrcXWsP/PzXTU2MUTOBTKhlTGzFoKZ1CIy4Z3NL2kEDbYNWTjGN4S2TCpoZopIKutbXVUTHBxi9hos4D33NvcvtbNZ3HSTu+Pjz4LJn51W62qei1fxa8PFx4vNcEcJFhNYa2jgqKeam9KkbHG6WMtBuQLi+3aj8KrDXVdNTU81S6mkdG8wxl2wkX1bNi73E8WpxOxrp8PdBLiEUzXiudM4APBzAHUwW1HWFi+msq6KeHCsXpcPqI8Ulnle+YMErC4lrgf8AeAFtXP8Asq7HSXBSv/f0/wBm4+1bQ7pOKS+9y4+Luvfy+pwlLQ1dY97aSlnnczW4RRlxHxsFENDV1AvT0s8oz6O7Iy72t2obfcu3p6z0rCqimoMapqbEG4gZpqh8mgFQy1sw91+b3K5jOPUvoGPyYVWsikmronR6N+R7gGNDnNG2xIOv3rCslNR0nL9fP9fE77yrubhGC8Uvjw4pXvh4O+9fJHBvoaxkMkr6SobFE7I95jIax24m2oql1FVtpBVOpZxSk2ExjOQ/92xei4xjdNUYrwni4xhkoZMPtAwTAsdJlB9nXYuuTs1q7i+N0L6WoqKB1B6FLRaFsUla4OHs/o0IuARvt8112Onx/n4d35HNe1q/8U6fjd5pO768fLwOHwLAm4nQVtZNXQ0dPSuY175Gk/quBsVrF8ArMOrKaBuSs9JjEsDqa7xI07CBa63vAuoiZwdxqm9Kw2GpmfCY217mhjgCSdTr3/bctvU1WHT4xDLxpD6czD3RyilqdFBI8EARtef0i19ltnMtU7PTnSi/i+7/ANFr+0K9G0ziuMV8Lvkmn4c/jf8ALh4nnb6KrZVilfSztqibCExkPJ/6bXWfhfB+rrayrpZmupJqamfUubMwtJDbare+67ufFqB1ZSCLEKOCrdhctNHOJzI2GW4sC869/tFYuA18eH4llxzGaTEGsw2dhYJR7N3sOj0n+8SAfgtxslNS4u9GJ+1rRKm3GNzu+d74+K4XXcPjnz43AuD1Zi9fS04Y+nZUh5jmljIY7KCTY8+zmWqpYXVFRHE0OJe4D2Rc/Ic69BmxOI8LsNxbDq2h9FfTFscFRM2MUtmEGNwH6fdq1rz2nkEU7JHNzhjg7Lci9veNi4VqSgroePH9K4+lZrVVqqU5cOC4cnfJNfa5XmdiOFOpads8bzIwfrI2C98rh7j9iCFg1lPJSzaKYNDwASAb2uLr0fAazD6rDZZnSMcQSZWyABw+I2a7a7ajtXBcIKiKqxiqnpzeJ7rgr51mtE5y1cl4EsNpq1KjpzXga5CiFe9H1UUnYo51J2KFtFIKsNqYnFoa8EuJaBbXcbVfKwoqLR1RqBJeV36zbU4cwtzWXSN3xMSck1ooyyoUlQqjodEqVUqV+WR4y5DBLO4tgifIQLkNaSrcjHMtnaRcXF1l4fPFC5+nLsjrXbo2yB3xBIt8QdS22HV0LpYzHIaaOHIXlzm3c0OeS3WRcWcNlzq2L1UqUZpcbmeWtXnTbejekc2VMjS0lrgQQs6trhUUwhOchmiyA7G5WZXW+Jsfkr8+KMfSyQASCN4fdthYkkFp281lVCF7/kdNbUuX8TUOaQASCARcXG0KlbmDFA2mpo3TVMboYjGHMAdl9su1XI2ggH/pG1U4RiFLSVc0s0cgY94OjYLgsubtIuPdtuNWxbVODaWl4kdaolJ6Hh58foahULcNxFgNORNUMEbMoaGC0RyWzM9raTr5v31ocTj9DlhDnuc9xJL476TWPad7Vr6tx+OtbVOOL1mXXT4fw9ZevpxNNzot9Ni8MktS5z6giaMNdZli4+1q/WbDWNRzDVs1BY9BVR0+GvfLlfI2TLFHca2uA0l9wsAP+4rerjfdpBV5tXuHLz+3rxNQVSfgt1U4jTzlwlfUPu0/2hY3N+oODbX2CxG3n2KzXYiypq6VztL6NEblmoHW8kkc17WHyV0Ir/I3GtN/4esjVKCt/VYvDK8ujlqY3uja18oYC6SxfcG7ybEObtJ/TsVhmJRtMB01S0MjyNaGNIhdltmZr231837rpq4r/L1mZjaKjV7h++3ryNMgBIJDSQBc2GxbybFaWWGQZJWyZJGts0WeXsaMx16jdpJ27VFTjbp4qxhkqAZtI1p1foLmOa069gs4e7MtqnHEFXqu66n6yNEjGOke1jGlz3EAAC5JPMt/huL0tJSU8b2zvdG9ry21wHB97j2rfp1bL++yUmORMkglldUNmbozNI1ocZspPskk7LZf2+C3GnHhfIsrRVV+jT8PXI50qFs6CtigoKmnmMtpdeWMWzatV3X2A8xB91tq2NTjlLJIwtZM9rBmaXtF8zXZo73cdQ1g+4mwVjCLV7ZudepGWioX+voc0pkY5hAc0tJAIuOY7Ct+7G4DFSs0bm6NoBsy5Y/Rlpe0l1rlxzEWFyBr1XVuHEY34w6pNRMwCmLNM4ASFwiy5gL7Sde35rerj8GZjaKnFuF1yb/88DQoVu3YuzSsyPnazO5znhozFxja0Ptf9WYF23n2rJdi1OMMN5JXF8kofFYf2t4o2hz9eq7gXc+sHn1rapx48Q7TUV3/AF+Prkcydil0b2C7mlovbWOf/wDiFvsQx1krXGka6A5HNjDWkGIFzDlDsx9kBp2AbdmtSMdjdUmaV9S+Ug2lcLujJDBqIeCf0kbRqPyW9CPheaVoqtaWr5+vA50qFuKzEoqjD5YLvDnSukaxkeRty69z7Rvq91xvsFp1GkvA9FKcpq+SuIKhSVCqOp0SpW75MY32bUd1U8l8b7NqO6vzqstbA8mfK2yz9SOaNKVBW7PBfG+zKjuqOS+N9mVHdWlZq2B5Mu2WfqRzRpEK3XJbG+zKnuqDwXxvsyp7q2rNWwPJl2yz9SOaNIoW75LY52ZU91RyWxzsyp7q0rNWwPJjbbP1I5o0qoW95LY52ZU91U8lcd7Mqe6tqzVsDyZdts/UjmjR86Ld8lcdv/ddT3U5K472XU91bVnq4Hky7bZ+pHNGjKpK3p4KY72XU91QeCmPdl1PdWlZ6uF5Mu22bqRzRolBW85J492XU91DwUx7sqp7q2qFXC8mXbbN1I5o0Sg2W95J492XU91RyTx7sqp7q2qFXC8httm6kc0aJUrfckse7Kqu6o5JY/2VVd1aVCpheRdts3UjmjQlQt8eCOP9lVXdUckcf7Kqu6tqjUwvIu22bqRzRoedQ5b7kjj/AGVVd1DwRx/smq7q0qNTC8ht1m6kc0aBQVvuSHCDsmq7qHgjwg7Jqu6tKjUwvIqt1m6kc0aA7FC354IcILf3TVd1RyP4Qdk1XdW1SqYXkXbrN1I5o0B51C3/ACQ4QdkVXdUcj+EPZFV3VpUp4XkNus3UjmjQFQt+eB/CHsir7icj+EPZFX3FtUp8mXbrN1I5o+lURF90/lAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAWn4ZVc9BwQxyspJDHU09DPNE8AHK9sbiDY6toC3CIDy6n4YYpT4CammqaCulZT1FVIXVTakAQsjdlBjawNJzOFiDbUdexXcT4f4hholhnZQuqqasfDIAwtbNGNEfYzPFnWl12zn2b5bbO3fwgw0SPjjnfO5hyuFPC+bKdxyNNio4/oehXfQT+RY19Lmszepqcnkc3wY4RVMuN45xjUf+ho6YzZSBZmWqqmF19v6Ym/stNhfDbF4KKcYm0U09RLT1EM2IR5I6eCYkG4aRmawi1yR+ttyF33H9D0a76CfyJx/Q9Gu+gn8ia+nzWY1NTk8jzzDOGuLVMGFwy1dLHJLVQBznM/tKlr6tzHaPmyta0NOon2hrBtfZUfDPGnyYE2enw//SMMVRYER52vcGljC+QXc0XcbB20Cw/Uex4/oehXfQT+RRx/Q9Cu+gn8ia+nzWY1NTk8jiZOHuItwyqmdxW2aCpbHIcwdHGwiQ2B0oD3+xaxcx2u+XYD6PSTCppIZ2hwbKxrwHNLSLi+sHWPgVruP6HoV30E/kU8f0PRrvoJ/IjrU8SzGqqcnkbVFquP6Ho130E/kTj+h6Nd9BP5FNdTxLMuqnhZtUWq4/ouhXfQT+ROP6LoV30E/kTXU8SzJqp8mbVFquP6LoV30E/kTj+i6Fd9BP5E11PEsxqp8mbVFquP6LoV30E/kTj+i6Fd9BP5E11PEsxqp8mbVFquP6LoV30E/kTj+h6Nd9BP5E11PEsxqp4WbVFquP6Ho130E/kTj+h6Nd9BP5E11PEsy6qeFm1Rarj+h6Nd9BP5E4/oejXfQT+RNdTxLMaqeFm1Rarj+h6Nd9BP5E4/oejXfQT+RNdTxLMaqeFm1Rarj+h6Nd9BP5E4/oejXfQT+RNdTxLMaqeFm1Ral3CHDmAuldUxMG18tJMxo+JLQAtnTzRVELJoJGyRPF2vabgjeCrGpGTui7ySpyir5K4rREWzAWg4VzSOFLQRXPpJc6RoOUvY23sg813OaL7iVv1z2Pf7R4N/hy/54VwtP9b+37O1n/sX3/Rn0uDUkVOyOaGOYtFvab7I9zW7Gj4K7xTh/Uabwgq8TnkpqJ8kDA+W7WsaeclwA5xv3hap+K1cT2e1DMBIxkjG072FgLgNZc7Vt3G/3XRUopXJHN1JN3tmy4pw/qNN4QTinD+o03hBWOFUskHBfGJYXujljo5nMew2LSGEgg8xWrwzhFI+h9MrJIpmuMTRBTU7mva6RwaAS51jrPNZXVxfwJrJczd8U4f1Gm8IJxTh/UabwgtUeFMLaprZKWoiphTzTzSvteIxPDXNLQSTr3X2i19dsij4RUtY9kdPDUPmMmidGMpLDYG5IdltZwOondt1Jqo8hrHzM3inD+o03hBOKcP6jTeEFTU4pBTzyQPbIZWGL2QBdwkcWgjXsuDfdYrFh4RUkrIpBFUNjmMege5gAma97Wh7dey7m3vY2OxNXHkXWPmZnFOH9RpvCCcU4f1Gm8IKzBjEVRVMp4YKh7y+VhIAs3RuyuJN9lyLc/uVup4QUVPiZoZC/SAhhd7Ng8tzBtr3uRbXa3vumrjyJrHzMrinD+o03hBOKcP6jTeEFg03COGeSJpo62NshYM72ts0P/QTZxNnHVq2HbZGcIIaiWSKFr4pY5I2ubIGuJa8kA6nath26xzhNVHkNY+ZncU4f1Gm8IJxTh/UabwgowKsfiGCUFZK1rZKiBkrmt2AuaCbfus5NXHkXTlzMLinD+o03hBOKcP6jTeEFmopq48hpy5mFxTh/UabwgnFOH9RpvCCzUTVx5DTlzMLinD+o03hBOKcP6jTeEFmomrjyGnLmYXFOH9RpvCCcU4f1Gm8ILNRNXHkNOXMwuKcP6jTeEE4pw/qNN4QWaiauPIacuZhcU4f1Gm8IJxTh/Uabwgs1E1ceQ05czC4qoR+ilijd0oxkcPmLFaXD2PwnhC2lBAp6vMLdJ4bmD7cxIDwbbS0HnXTrnsV/wBq8H/xD/8ApmXGtBR0ZRVzvXmzvRm5aUXxVz/R0KIi9J5guex/VwhwcnYI5df/AHwroVqeEeHPr6aN8ADqiBxc1jjYSAgtcwnmuCdfMbHmXGvFyg7vl5O87UGlNX/PzRsqmFtRCY3lwBIILTrBBuD+4CxXYcJMomqqmVgcHZHObYkG42DeAtNT8J4aZmjxO8MjdX9taNx+IdYH4tJB9yu8sMJ6zF48fmUVqpYiuy1V/ibTHqOTEMDxGihLWy1NNJCwvNgC5pAv7taxocChjp46d1VVS08bo3Rxvc2zCxwc21gDtaFicsMJ6zF48fmTlhhPWYvHj8yu1UsSJs1XCzMlwCile5zxKczZmvAfYObKQ5wPzAItrFlkUmGsp5GSOnqJ5WlxDpX32gC1gABqA2fFavlhhPWYvHj8ycsMJ6zF48fmTaqWJDZquFmwq8LZVY3R10rW2pWPDNZuXO1XPNYDN3ubnsN4OUbafQh9Ro2ta2EaT/UBrg5oZusWt232AbAsblhhPWYvHj8ycsMJ6zF48fmTaqWJDZquFmzw7CoKBwdE6Vz7yEue65Je4OcT8wkmFxPrJKgSzt0hzSRtfZj3Zctzz7Lar21DUtZywwnrMXjx+ZOWGE9Zi8ePzJtVLEhs1XCzZDCKYNa0Z7AQge1/wjdv32rGouDtJSFuSSd4a2NjA5wsxrCS0CwG8+8rG5YYT1mLx4/MnLDCesxePH5k2qliQ2arhZu8PpI6Chp6SDNoYI2xMzG5sBYXV9c7ywwnrMXjx+ZOWGE9Zi8ePzKbTSxIbNVws6JFzvLDCesxePH5k5YYT1mLx4/Mm00sSLs1XCzokXO8sMJ6zF48fmTlhhPWYvHj8ybTSxIbNVws6JFzvLDCesxePH5k5YYT1mLx4/Mm00sSGzVcLOiRc7ywwnrMXjx+ZOWGE9Zi8ePzJtNLEhs1XCzokXO8sMJ6zF48fmTlhhPWYvHj8ybTSxIbNVws6JFzvLDCesxePH5k5YYT1mLx4/Mm00sSGzVcLOiXPYpr4V4RbmkN/Bl/IVL+F2HuaRTSRSy8zRIHE/JmYn5BTgdHUVOIHFKxj4xlIiY8Wc4usC8t/wB3U1oA2gA31krnOrGq1Gm7+KeRuFKVJOVRXcGjokRF6zyhEVmuq4KCiqKurkEVNTxulleRcNa0XJ1bgEBeRa3DMcw3EopJKSqa5sbgx4kaY3NJFwC1wBFwbjVrWcZogXgysBYLuGYeyPfuQFxFQ2aJ1ssjDfZZw16r/wDhQJ4Te0sepuc+0P07/ggLiLEqMSo6eSnjlqYw+okEUQBuXOsTbV/0n9lloAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAtdwkw92L8HcUw2OQRvrKWWna9wuGl7C25/dbFEBwHCL+Hra7B20dDUtEr82nmqyZnvOiMbSHOuWhoOxtr69e29NVwCnnq8YkMtEY65lix0brufnidmLr5h/q9gOUkg5dVj3+e/6WkjeEzHoO+35V0mS48zrf4a108cDocYZT1EVOxoMUIa0TMkflfZth/q5ZGHUL3B1LLrP4eOe6sjpJ6WKnkgmiikMRM4D6cQtjc6+uNtswHubuufQcx6Dvt+UzHoO+35V0mLkcO3gLoOElNXUgw+KliqYqloFPaVgZCYzG1w1BpJzfEn4rulTmPQd9vymY9B32/Kjd5SpFTmPQd9vymY9B32/KgKkVOY9B32/KZj0Hfb8oCpFTmPQd9vymY9B32/KAqRU5j0Hfb8pmPQd9vygKkVOY9B32/KZj0Hfb8oCpFTmPQd9vymY9B32/KAqRU5j0Hfb8pmPQd9vygKkVOY9B32/KZj0Hfb8oCpFTmPQd9vymY9B32/KAqRU5j0Hfb8pmPQd9vygKkVOY9B32VQIcLjYgCIiAKmQXYRv1KpQ/Z8x/5QEqzDVU80skUM8UkkZs9jXAlvxHMqMTndTUE80eXMxtwXbB7ytNSSU9O+kbTYnTVT2WhDAWlzmuc251G99V1bgdEiLicOiqoMFgjpInQVZdTMkdFh74pGtMjQ+7nXDtRNzbeUSvI2dsi4iduMxVTp4jWS1EdLWxQF0epzhI0x5rC1y0aidtgs7BnYtUVETaqedtOJnO1MNy0MbZri6NurNfWAN19SuiLzqUWjxOoro8cpqOnzGGqDXh7bf2QjN5L+5wLG/ElaqnrMYbSwyvNc6a0L6yN1PqidpW52x2bdwyl+zNqaCDc65cLzsUXP4TJiFVXMfUPqo6dslS4NdFkDgJAIwbi9st7bLqxLV1zseqY6eWqfo6yKNsIivEIjGwvJdbUfaJ27QN+tcLzp0XF01XjElLWueauOwgczSRuLrlz87biIW1BoOVrg26y4qjEpKvDszq5rJIWB7HR2c13tXc46PKea4u0iw1a7K6IvOpRcdh9VwlkqI/SISGyi+UssIzEwtdc22Pkykf8ALchXKJ+K1EsUIqMQEDxCZJpYAx7X2fpGi7dmpmu1gTqKaIvOtRcpQemzcIqOSpNY4sdUiQPhyxRi9o8rrWN2+839y6tRq4J3hERQoREQBERAEREARFpcfpp5iC0OfF7OVrdYabnMSLG9xYD2Tb7oDdKlos91ufWtfgUdRFTuE7XMZqyMcdY1e0fcCdYHN7tg2I/WfgP/ALQEoiIAoffKbC5GuylEABBFxrBSw3BUljTvHwJCjRt3v75QFaKjRt3v75TRt3v75QFaKjRt3v75TRt3v75QECnhFSagRM05ZkMmX2st72vuurio0bd7++U0bd7++UBWqWRsY6RzGNa6Q5nkD9RsBc/IAfJRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtUtOZzjzbB700bf8AmPxcVUNQsNiAIiIAqZHsijdJI4NY0Xc4mwA3qpaPhs63BXE7f8ErFSehBz5I3ShpzUObuLD+G/B5ji04iLg21RSEfuGqnlzwd7Q/kSeVeJFUr8pv+0YY+fc/ZL/jdmxSzXY9v5dcHO0f5EnlUcuuDnaP8iTyrxBUlaXt60YY5PuX3bsuKWa7HuPLvg52j/Ik8qcu+DnaP8iTyrw7mVK1vy0YVk+4927Lilmux7ny84N9o/yJPKo5ecG+0f5EnlXhig7Fpe26/Jefcvu1ZcUs12PdOXvBvtH+RJ5U5e8Gu0v5EnlXhJULS9s1+S8+5fdqy4pZrse7cveDXaX8iXypy+4NdpfyJfKvCCoK1vivyXn3HuzZcUs12PeOX/BrtL+RL5VHL/gz2l/Il8q8GUFaXtetyXn3L7s2XFLNdj3rl/wZ7S/kS+VPWBwZ7S/kS+VeCKkrS9rVuS8+492bLilmux756weDHaf9PL5U9YPBjtP+nl8q8CKhaXtStyXn3L7sWTFLNdj371hcGO0/6eXyqPWFwY7T/p5fKvAedU860vadXkvPuX3YsmKWa7H0B6wuC/af9PL5U9YfBftP+nl8q+f3KlaXtKryXn3HuxZMUs12PoL1h8F+0/6eXyqPWJwW7U/p5fKvn0qCtL2hU5L19y+69kxSzXY+g/WJwW7U/p5fKnrF4Ldqf08vlXz2dmpUqq31OS9fce69kxSzXY+hfWLwW7U/p5fKuhwnE6LF6NtVhtQyogcbZm8x3EbQfcV8sFeyfwKd/ofExzekD/KF6bPapVZ6MkfM9r+wqFis+upSd6a8buyPT0RF7j8sFoOG5/8Aa2Jf4JW/XP8ADf8A2XxL/CK42n+mf0f6O9m/uh9V+zwwqFJUFfztH9OIVJVSpK2ijmVKq5lStopCg7FKg7FtFKSoUnYoXRFM/Cg8elSQBpqIotIy7Q4izm3IB5wLn3WupxN0stDSz1bQKmV8ji4tDXPZZgaTbbrz6zt1rAa90bw+NzmuGsOabEJUTS1Er5Z5Hyyu1ue9xJPzK9CmtDROGpbqqfD/AH4XXfT4kU4DqiIFucF4GW9s2vZfmXa4hhgkbNL6O2jjDKstjdSsilbljuG6rtezc/bt1rhkc4naSebautGqqaaavvOdpssq8lKMrrr/AIX+P3Oy5K0WkLXenMjbOIhMXNLZm6J787PZ2XaOc6jv2a7DKCkHCXAGxxufS1gjkdFOQ+13uaQdQBHs7udaipxGeelbTu0TIWuDssUTWZnAEAmw1mxP7lYV9YXd1qd6cYnGnY67jJVKnjw8rv3x+13xOgqsHo2YbNIx1QKmOjjq7lzchzSBpba1+e97rnCqifiqSuc5KTVyuPdZ6UqaanLSvZTzqFPOoRHoBVKlyhbQIKgqSocto0UnYo51UdipK2gQV7F/Az+6sT/+Q3/KF46V7D/Az+6sS/8AkN/yhe2x/wBqPh/8j/8Ahl9V+z1IIgRfXP52Fi4hTRVlLLTzszxSNLXN3grKUEXUaTVzKm070eeTfw9oM5LairAPNmbq/wDxVr1e0XWavvN8q9FMYKp0Q3BePdtl6aPdvS19Rnnfq9ous1feb5VHq8oes1neb5V6LohuCaIbgm7rLgQ3pbOozzr1eUPWazvN8qj1d0PWazvN8q9G0Q3BNENwV3fZsCG9LZ1Gec+rqh6zWd5vlT1dUPWazvN8q9G0Q3BNENwTd9mwIu9bZ1Gecermg6zWd5vlUerig6zW95nlXpGiG4JohuCuwWfAhvW2dRnm/q4oOtVveZ5U9W9B1qt7zPKvSNENwTRDcE2Gz4EN62zqM829W1B1qt7zPKo9W1B1qt7zPKvStENwTRDcFdioYEN62zqM819WuH9are8zyqPVph/Wq3vM8q9L0Q3BNENwV2OhhQ3rbOqzzT1aYf1qu7zPKo9WeH9aru8zyr0zRDcE0Q3BNkoYUN7Wzqs8z9WWH9aru8zyqPVlh3Wq7vM8q9N0Q3BNENwV2SjhQ3tbOqzzL1Y4d1uu7zPKo9WGHdbru8zyr07RDcE0Q3BNlo4UN7W3qs8x9WGHdbru8zyp6r8O63X95nlXp2iG4JohuCuzUsJd7W3qs8w9V+G9br+8zyp6rsN63X95nlXp+iG4JohuCuz0sI3tbeqzzFv8LMMO2sxDvM8q7fgrwfo+D1AaWha+znZ3vebued5/YbFuBGArjW2Wo0oQd8Uca9vtNojoVZtokIiLoeQIiIBcbwlxvCWG4JYbggFxvCXG8JYbglhuCAXG8JcbwlhuCWG4IBcbwlxvCWG4JYbggFxvCXG8JYbglhuCAXG8JcbwlhuCWG4IAtfNjNBC+RkkxBjuDaNxBItcAgWJFxqFytgtTLgVNLO+R0k+RzjI2LMMrHkgucNV7m3OSNZta6A2rHB7GuF7OFxcEH9jsU3G9EIB2hALjeEuN4Sw3BLDcEAuN4S43hLDcEsNwQC43hLjeEsNwSw3BALjeEuN4Sw3BLDcEAuN4S43hLDcEsNwQC43hEsNwQC2xAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBaThXWehUtK91T6NC6pY2WTMG2br5zsW7RZnFyi0jUJKMk2jjzj9bTiJmZkjHucYXysOeqGksGttYA5dd7G4sbbVnUVbX19ToXzRQtdeVjo4zcBkti03OvMANeq1yuiRco0pJ8ZcDrKrBrhG5+vX/AIcy2vrn4NRubVwvq3VYhke2OzR7RBBbf/7CxDwlrH1tHSRGnFTMwAtczVnIf7Q9q+W7Rzc9r3XYojpS4XSKq0ON8DiqrhTiDYIZ4qeBsM5cIjJqBc0MGjJJFnF5eBa/6NhWzx3HpMPxSjpWaO8pjzMe3W4OeG+ycw2XubNPNe110SIqU7n/ACDq021dDn8TmeD2O1dbTSyVrYriihqxoYyLZw+7bXN7ZPusKl4U1E9M17pqOKMyuZ6S9l4yQxjg2zXn2iXEanH9J1XXZomrnclpE1sL29E5OXhFVM9MJNMySF5a6BzHZ4WaQNErzexaWkv5tXPtIqpeEsrmRifQiSZ8bYCGkCdpnLHObr2ZcrhuzbSF1SJq5336Q1lO67R8/XrM4wY9iT6eKSSalga4QyufojZrHSFjgbu9wN/j8Vt6J1W2nxR76xzmMuyJ5Akc1zQcziGjef0gc3vst4isaUlxcrxOrF8IxuOHpsfqhCx8DhVODpPaZJpYyRHf2SA24vrIIuNg5lck4S149I0L6SVkEU02kbE4CRsbYiABm1X0h16+bbz9jNEyaJ8UrQ6N4LXNPOCrdHSQUURjpowxpJcdZJJPOSdZK5qjUXDSNutSfHQ4msxnGHYfWtgOjBla0xBwN3uzgOA+AN1jcGccqMTqnRymCRojL3mJpGhdmsGuuTckaxs2Lo1bp4I6aFsUDAyNuxo5l1cJ6d+lwOanT1bjo8eZcREXU4hERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAfKjsXxPtGt8d35Vp2L4p2lW+O78qyQrbmoC87GMV7SrfHf8AlUHGMV7TrvHf+VZLVSWIC/xxivadd47/AMpxxivadd9Q/wDKx8iZEBf44xXtKu+of+U44xXtKu+of+VYyJkQF/jjFe0q76h/5TjjFe0q76h/5VjImRAX+OMV7SrvqH/lOOMV7SrvqH/lY+RMiAyOOMV7TrvqH/lOOMV7TrvqH/lY+RMiAv8AG+Ldp131D/ynG+Ldp131D/yrGRMioL/G+Ldp131D/wApxvi3add9Q/8AKsZEyKAv8b4t2nXfUP8AynG+Ldp131D/AMqxkTJ70Bf44xbtOu+of+U44xbtOu+of+VYye9MnvQF/jjFu0676h/5TjjFe0676h/5VjImRAX+OMV7TrvqH/lOOMV7TrvqH/lWMiZEBkDGMV7TrvqH/lSMYxXtKu8d/wCVjZFORAZQxjFe0q7x3/lXG4xinaVb47/ysINVYagM5uMYp2jW+O78q5xvinaNb47vysBrVXZAVkKkhXFBCAtFqjKFdslkBZypkV2yWQFrImVXbJZAWsqZVdslkBayplV2yWQFrKmVXbJZUFrKmVXbJZAWsqZVdsllAWsqZVdslkBZy+5MvuV6yW1oCzkTIrtksgLWRMiu2SyAtZFORXLJZAUBqkBV2U2QFICmymylAVJZEQCyiylEBFkspRARZLKUQEWSylEBFkspRUEWRSiAhFKa0BCKdahQEIpRAQllKICLJZSiAiyWUogIsllKICLJZSiAIiICUREAREQBERAEREAREVAREQBERAPmnzRFAPmoREAREQBERAEREAREQBERAEREAREVBKIigCIiAIiIAiIgCIioCIiAJrRRqQE60UakUAREQBERAEREAREQBERAEREAREVAREQEoiKAIiIAiIgCIioCIiAIiIB8k+Sj5p81ASoREAREQBERAEREAREQBERAEREAREVAREQEoiKAIiIAiIqAiIgCIiAjmQoigCIiAIiIAiIgCIiAIiIAiIgCIioCIiAIiIAiIgP/2Q=="}, {"timing": 3221, "timestamp": 227777320548, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAECAwQFBgcI/8QASxAAAQMCAgQKCQIDBAkEAwAAAQACAwQRBRITITFRBhUWQVJUVZKT0hQXImFxgZSh0TKRB6OkIzVzsiUzNkJTYnKCsSQmdMGis/D/xAAbAQEBAQEBAQEBAAAAAAAAAAAAAQIDBAUGB//EADYRAAIBAQQIAwgCAgMBAAAAAAABAgMEEVKREhMUFSFRU9ExofAFFkFhcYGSsSIzQsEGI+E0/9oADAMBAAIRAxEAPwC2iIgCIiAIiIAiIgCIiAIiIAoPMpRAU6t6at6n5J8kBChTzqEAREQBERAEREAREQBERAEREAREQFaIiAIiIAiIgCIiAIiIAiIgCg/FCo1ICfmoPxTUiAFQiIAiIgCIiAIiIAiIgCIiAIiIAiIgK0REAREQBERAEREAREQBEUGyAa/cmv3KNSakBJuoRQgCIiAIiIAiIgCIiAIiIAiIgCIiAIiICtERAEREAREQBERAEREAUfJPmnzQD5KD8FPzUH4oAoREAREQBERAEREAREQBERAEREAREQBERAVoiIAiIgCIiAIiIAiKNaAjUmpTrTWgI1KFOvWoQBERAEREAREQBERAEREAREQBERAEREAREQFaIouEBKKLqLoCpFTdLoCpFTcpcoCpRq3qFCAq1byo+ahEAREQBEuougJRRdLoCUUXS6AlFF0ugJRRdLoCUUXS6AlFF0ugJRLpdAEREBtzSQ9D7lU+iQ9D7lZKpX8+VttHUlmz+lbBZelHJdiwaSHofcqPRIeh9yr5ULStlo6ks2XYLL0o5LsY/osPQ+5Q0sPQ+5V9HLe2WjqSzZdgsvSj+K7GP6NF0PuVHo0XQ+5V9QtK2WjqPNjYLL0o5LsWfRouh9yqfR4uj9yr6pW1a6+N5suwWXpR/Fdi16PHf9P3Kj0ePo/cq7zotq1V8bzZdgsvSj+K7FkwR9H7lQYGdH7q8VSbLStVfG82Xd9l6UfxXYtaFnR+6GFm77q4oK0rTWxvNl3fZelH8V2LeiZu+6jRN3fdXFBW1aa2N5sbvsnSj+K7FvRt3KNG3cripWlaa2N5su77J0o/iuxQWN3KMjdyrKhbVoq4nmy7vsnSj+K7FGRu5CwblVzqHLStFXE82N32TpR/FdinKNygtCqUFaVerieZV7OsnSj+K7FJAsosqjsULar1MTzLu6ydKP4rsQoUlQtKtUxPMbusnSj+K7EXKZihULSrVMTzLu6ydKP4rsdEqVUqV+SQIKFCoK0ikFCiFbRSlQpULaB0bOCdRLjlJh0NRE8VMAqWzgHKGWJuf2+6wzwfqGUeLVMr2Mbh0ohcLfrcXWsP/PzXTU2MUTOBTKhlTGzFoKZ1CIy4Z3NL2kEDbYNWTjGN4S2TCpoZopIKutbXVUTHBxi9hos4D33NvcvtbNZ3HSTu+Pjz4LJn51W62qei1fxa8PFx4vNcEcJFhNYa2jgqKeam9KkbHG6WMtBuQLi+3aj8KrDXVdNTU81S6mkdG8wxl2wkX1bNi73E8WpxOxrp8PdBLiEUzXiudM4APBzAHUwW1HWFi+msq6KeHCsXpcPqI8Ulnle+YMErC4lrgf8AeAFtXP8Asq7HSXBSv/f0/wBm4+1bQ7pOKS+9y4+Luvfy+pwlLQ1dY97aSlnnczW4RRlxHxsFENDV1AvT0s8oz6O7Iy72t2obfcu3p6z0rCqimoMapqbEG4gZpqh8mgFQy1sw91+b3K5jOPUvoGPyYVWsikmronR6N+R7gGNDnNG2xIOv3rCslNR0nL9fP9fE77yrubhGC8Uvjw4pXvh4O+9fJHBvoaxkMkr6SobFE7I95jIax24m2oql1FVtpBVOpZxSk2ExjOQ/92xei4xjdNUYrwni4xhkoZMPtAwTAsdJlB9nXYuuTs1q7i+N0L6WoqKB1B6FLRaFsUla4OHs/o0IuARvt8112Onx/n4d35HNe1q/8U6fjd5pO768fLwOHwLAm4nQVtZNXQ0dPSuY175Gk/quBsVrF8ArMOrKaBuSs9JjEsDqa7xI07CBa63vAuoiZwdxqm9Kw2GpmfCY217mhjgCSdTr3/bctvU1WHT4xDLxpD6czD3RyilqdFBI8EARtef0i19ltnMtU7PTnSi/i+7/ANFr+0K9G0ziuMV8Lvkmn4c/jf8ALh4nnb6KrZVilfSztqibCExkPJ/6bXWfhfB+rrayrpZmupJqamfUubMwtJDbare+67ufFqB1ZSCLEKOCrdhctNHOJzI2GW4sC869/tFYuA18eH4llxzGaTEGsw2dhYJR7N3sOj0n+8SAfgtxslNS4u9GJ+1rRKm3GNzu+d74+K4XXcPjnz43AuD1Zi9fS04Y+nZUh5jmljIY7KCTY8+zmWqpYXVFRHE0OJe4D2Rc/Ic69BmxOI8LsNxbDq2h9FfTFscFRM2MUtmEGNwH6fdq1rz2nkEU7JHNzhjg7Lci9veNi4VqSgroePH9K4+lZrVVqqU5cOC4cnfJNfa5XmdiOFOpads8bzIwfrI2C98rh7j9iCFg1lPJSzaKYNDwASAb2uLr0fAazD6rDZZnSMcQSZWyABw+I2a7a7ajtXBcIKiKqxiqnpzeJ7rgr51mtE5y1cl4EsNpq1KjpzXga5CiFe9H1UUnYo51J2KFtFIKsNqYnFoa8EuJaBbXcbVfKwoqLR1RqBJeV36zbU4cwtzWXSN3xMSck1ooyyoUlQqjodEqVUqV+WR4y5DBLO4tgifIQLkNaSrcjHMtnaRcXF1l4fPFC5+nLsjrXbo2yB3xBIt8QdS22HV0LpYzHIaaOHIXlzm3c0OeS3WRcWcNlzq2L1UqUZpcbmeWtXnTbejekc2VMjS0lrgQQs6trhUUwhOchmiyA7G5WZXW+Jsfkr8+KMfSyQASCN4fdthYkkFp281lVCF7/kdNbUuX8TUOaQASCARcXG0KlbmDFA2mpo3TVMboYjGHMAdl9su1XI2ggH/pG1U4RiFLSVc0s0cgY94OjYLgsubtIuPdtuNWxbVODaWl4kdaolJ6Hh58foahULcNxFgNORNUMEbMoaGC0RyWzM9raTr5v31ocTj9DlhDnuc9xJL476TWPad7Vr6tx+OtbVOOL1mXXT4fw9ZevpxNNzot9Ni8MktS5z6giaMNdZli4+1q/WbDWNRzDVs1BY9BVR0+GvfLlfI2TLFHca2uA0l9wsAP+4rerjfdpBV5tXuHLz+3rxNQVSfgt1U4jTzlwlfUPu0/2hY3N+oODbX2CxG3n2KzXYiypq6VztL6NEblmoHW8kkc17WHyV0Ir/I3GtN/4esjVKCt/VYvDK8ujlqY3uja18oYC6SxfcG7ybEObtJ/TsVhmJRtMB01S0MjyNaGNIhdltmZr231837rpq4r/L1mZjaKjV7h++3ryNMgBIJDSQBc2GxbybFaWWGQZJWyZJGts0WeXsaMx16jdpJ27VFTjbp4qxhkqAZtI1p1foLmOa069gs4e7MtqnHEFXqu66n6yNEjGOke1jGlz3EAAC5JPMt/huL0tJSU8b2zvdG9ry21wHB97j2rfp1bL++yUmORMkglldUNmbozNI1ocZspPskk7LZf2+C3GnHhfIsrRVV+jT8PXI50qFs6CtigoKmnmMtpdeWMWzatV3X2A8xB91tq2NTjlLJIwtZM9rBmaXtF8zXZo73cdQ1g+4mwVjCLV7ZudepGWioX+voc0pkY5hAc0tJAIuOY7Ct+7G4DFSs0bm6NoBsy5Y/Rlpe0l1rlxzEWFyBr1XVuHEY34w6pNRMwCmLNM4ASFwiy5gL7Sde35rerj8GZjaKnFuF1yb/88DQoVu3YuzSsyPnazO5znhozFxja0Ptf9WYF23n2rJdi1OMMN5JXF8kofFYf2t4o2hz9eq7gXc+sHn1rapx48Q7TUV3/AF+Prkcydil0b2C7mlovbWOf/wDiFvsQx1krXGka6A5HNjDWkGIFzDlDsx9kBp2AbdmtSMdjdUmaV9S+Ug2lcLujJDBqIeCf0kbRqPyW9CPheaVoqtaWr5+vA50qFuKzEoqjD5YLvDnSukaxkeRty69z7Rvq91xvsFp1GkvA9FKcpq+SuIKhSVCqOp0SpW75MY32bUd1U8l8b7NqO6vzqstbA8mfK2yz9SOaNKVBW7PBfG+zKjuqOS+N9mVHdWlZq2B5Mu2WfqRzRpEK3XJbG+zKnuqDwXxvsyp7q2rNWwPJl2yz9SOaNIoW75LY52ZU91RyWxzsyp7q0rNWwPJjbbP1I5o0qoW95LY52ZU91U8lcd7Mqe6tqzVsDyZdts/UjmjR86Ld8lcdv/ddT3U5K472XU91bVnq4Hky7bZ+pHNGjKpK3p4KY72XU91QeCmPdl1PdWlZ6uF5Mu22bqRzRolBW85J492XU91DwUx7sqp7q2qFXC8mXbbN1I5o0Sg2W95J492XU91RyTx7sqp7q2qFXC8httm6kc0aJUrfckse7Kqu6o5JY/2VVd1aVCpheRdts3UjmjQlQt8eCOP9lVXdUckcf7Kqu6tqjUwvIu22bqRzRoedQ5b7kjj/AGVVd1DwRx/smq7q0qNTC8ht1m6kc0aBQVvuSHCDsmq7qHgjwg7Jqu6tKjUwvIqt1m6kc0aA7FC354IcILf3TVd1RyP4Qdk1XdW1SqYXkXbrN1I5o0B51C3/ACQ4QdkVXdUcj+EPZFV3VpUp4XkNus3UjmjQFQt+eB/CHsir7icj+EPZFX3FtUp8mXbrN1I5o+lURF90/lAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAWn4ZVc9BwQxyspJDHU09DPNE8AHK9sbiDY6toC3CIDy6n4YYpT4CammqaCulZT1FVIXVTakAQsjdlBjawNJzOFiDbUdexXcT4f4hholhnZQuqqasfDIAwtbNGNEfYzPFnWl12zn2b5bbO3fwgw0SPjjnfO5hyuFPC+bKdxyNNio4/oehXfQT+RY19Lmszepqcnkc3wY4RVMuN45xjUf+ho6YzZSBZmWqqmF19v6Ym/stNhfDbF4KKcYm0U09RLT1EM2IR5I6eCYkG4aRmawi1yR+ttyF33H9D0a76CfyJx/Q9Gu+gn8ia+nzWY1NTk8jzzDOGuLVMGFwy1dLHJLVQBznM/tKlr6tzHaPmyta0NOon2hrBtfZUfDPGnyYE2enw//SMMVRYER52vcGljC+QXc0XcbB20Cw/Uex4/oehXfQT+RRx/Q9Cu+gn8ia+nzWY1NTk8jiZOHuItwyqmdxW2aCpbHIcwdHGwiQ2B0oD3+xaxcx2u+XYD6PSTCppIZ2hwbKxrwHNLSLi+sHWPgVruP6HoV30E/kU8f0PRrvoJ/IjrU8SzGqqcnkbVFquP6Ho130E/kTj+h6Nd9BP5FNdTxLMuqnhZtUWq4/ouhXfQT+ROP6LoV30E/kTXU8SzJqp8mbVFquP6LoV30E/kTj+i6Fd9BP5E11PEsxqp8mbVFquP6LoV30E/kTj+i6Fd9BP5E11PEsxqp8mbVFquP6LoV30E/kTj+h6Nd9BP5E11PEsxqp4WbVFquP6Ho130E/kTj+h6Nd9BP5E11PEsy6qeFm1Rarj+h6Nd9BP5E4/oejXfQT+RNdTxLMaqeFm1Rarj+h6Nd9BP5E4/oejXfQT+RNdTxLMaqeFm1Rarj+h6Nd9BP5E4/oejXfQT+RNdTxLMaqeFm1Ral3CHDmAuldUxMG18tJMxo+JLQAtnTzRVELJoJGyRPF2vabgjeCrGpGTui7ySpyir5K4rREWzAWg4VzSOFLQRXPpJc6RoOUvY23sg813OaL7iVv1z2Pf7R4N/hy/54VwtP9b+37O1n/sX3/Rn0uDUkVOyOaGOYtFvab7I9zW7Gj4K7xTh/Uabwgq8TnkpqJ8kDA+W7WsaeclwA5xv3hap+K1cT2e1DMBIxkjG072FgLgNZc7Vt3G/3XRUopXJHN1JN3tmy4pw/qNN4QTinD+o03hBWOFUskHBfGJYXujljo5nMew2LSGEgg8xWrwzhFI+h9MrJIpmuMTRBTU7mva6RwaAS51jrPNZXVxfwJrJczd8U4f1Gm8IJxTh/UabwgtUeFMLaprZKWoiphTzTzSvteIxPDXNLQSTr3X2i19dsij4RUtY9kdPDUPmMmidGMpLDYG5IdltZwOondt1Jqo8hrHzM3inD+o03hBOKcP6jTeEFTU4pBTzyQPbIZWGL2QBdwkcWgjXsuDfdYrFh4RUkrIpBFUNjmMege5gAma97Wh7dey7m3vY2OxNXHkXWPmZnFOH9RpvCCcU4f1Gm8IKzBjEVRVMp4YKh7y+VhIAs3RuyuJN9lyLc/uVup4QUVPiZoZC/SAhhd7Ng8tzBtr3uRbXa3vumrjyJrHzMrinD+o03hBOKcP6jTeEFg03COGeSJpo62NshYM72ts0P/QTZxNnHVq2HbZGcIIaiWSKFr4pY5I2ubIGuJa8kA6nath26xzhNVHkNY+ZncU4f1Gm8IJxTh/UabwgowKsfiGCUFZK1rZKiBkrmt2AuaCbfus5NXHkXTlzMLinD+o03hBOKcP6jTeEFmopq48hpy5mFxTh/UabwgnFOH9RpvCCzUTVx5DTlzMLinD+o03hBOKcP6jTeEFmomrjyGnLmYXFOH9RpvCCcU4f1Gm8ILNRNXHkNOXMwuKcP6jTeEE4pw/qNN4QWaiauPIacuZhcU4f1Gm8IJxTh/Uabwgs1E1ceQ05czC4qoR+ilijd0oxkcPmLFaXD2PwnhC2lBAp6vMLdJ4bmD7cxIDwbbS0HnXTrnsV/wBq8H/xD/8ApmXGtBR0ZRVzvXmzvRm5aUXxVz/R0KIi9J5guex/VwhwcnYI5df/AHwroVqeEeHPr6aN8ADqiBxc1jjYSAgtcwnmuCdfMbHmXGvFyg7vl5O87UGlNX/PzRsqmFtRCY3lwBIILTrBBuD+4CxXYcJMomqqmVgcHZHObYkG42DeAtNT8J4aZmjxO8MjdX9taNx+IdYH4tJB9yu8sMJ6zF48fmUVqpYiuy1V/ibTHqOTEMDxGihLWy1NNJCwvNgC5pAv7taxocChjp46d1VVS08bo3Rxvc2zCxwc21gDtaFicsMJ6zF48fmTlhhPWYvHj8yu1UsSJs1XCzMlwCile5zxKczZmvAfYObKQ5wPzAItrFlkUmGsp5GSOnqJ5WlxDpX32gC1gABqA2fFavlhhPWYvHj8ycsMJ6zF48fmTaqWJDZquFmwq8LZVY3R10rW2pWPDNZuXO1XPNYDN3ubnsN4OUbafQh9Ro2ta2EaT/UBrg5oZusWt232AbAsblhhPWYvHj8ycsMJ6zF48fmTaqWJDZquFmzw7CoKBwdE6Vz7yEue65Je4OcT8wkmFxPrJKgSzt0hzSRtfZj3Zctzz7Lar21DUtZywwnrMXjx+ZOWGE9Zi8ePzJtVLEhs1XCzZDCKYNa0Z7AQge1/wjdv32rGouDtJSFuSSd4a2NjA5wsxrCS0CwG8+8rG5YYT1mLx4/MnLDCesxePH5k2qliQ2arhZu8PpI6Chp6SDNoYI2xMzG5sBYXV9c7ywwnrMXjx+ZOWGE9Zi8ePzKbTSxIbNVws6JFzvLDCesxePH5k5YYT1mLx4/Mm00sSLs1XCzokXO8sMJ6zF48fmTlhhPWYvHj8ybTSxIbNVws6JFzvLDCesxePH5k5YYT1mLx4/Mm00sSGzVcLOiRc7ywwnrMXjx+ZOWGE9Zi8ePzJtNLEhs1XCzokXO8sMJ6zF48fmTlhhPWYvHj8ybTSxIbNVws6JFzvLDCesxePH5k5YYT1mLx4/Mm00sSGzVcLOiXPYpr4V4RbmkN/Bl/IVL+F2HuaRTSRSy8zRIHE/JmYn5BTgdHUVOIHFKxj4xlIiY8Wc4usC8t/wB3U1oA2gA31krnOrGq1Gm7+KeRuFKVJOVRXcGjokRF6zyhEVmuq4KCiqKurkEVNTxulleRcNa0XJ1bgEBeRa3DMcw3EopJKSqa5sbgx4kaY3NJFwC1wBFwbjVrWcZogXgysBYLuGYeyPfuQFxFQ2aJ1ssjDfZZw16r/wDhQJ4Te0sepuc+0P07/ggLiLEqMSo6eSnjlqYw+okEUQBuXOsTbV/0n9lloAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAtdwkw92L8HcUw2OQRvrKWWna9wuGl7C25/dbFEBwHCL+Hra7B20dDUtEr82nmqyZnvOiMbSHOuWhoOxtr69e29NVwCnnq8YkMtEY65lix0brufnidmLr5h/q9gOUkg5dVj3+e/6WkjeEzHoO+35V0mS48zrf4a108cDocYZT1EVOxoMUIa0TMkflfZth/q5ZGHUL3B1LLrP4eOe6sjpJ6WKnkgmiikMRM4D6cQtjc6+uNtswHubuufQcx6Dvt+UzHoO+35V0mLkcO3gLoOElNXUgw+KliqYqloFPaVgZCYzG1w1BpJzfEn4rulTmPQd9vymY9B32/Kjd5SpFTmPQd9vymY9B32/KgKkVOY9B32/KZj0Hfb8oCpFTmPQd9vymY9B32/KAqRU5j0Hfb8pmPQd9vygKkVOY9B32/KZj0Hfb8oCpFTmPQd9vymY9B32/KAqRU5j0Hfb8pmPQd9vygKkVOY9B32/KZj0Hfb8oCpFTmPQd9vymY9B32/KAqRU5j0Hfb8pmPQd9vygKkVOY9B32VQIcLjYgCIiAKmQXYRv1KpQ/Z8x/5QEqzDVU80skUM8UkkZs9jXAlvxHMqMTndTUE80eXMxtwXbB7ytNSSU9O+kbTYnTVT2WhDAWlzmuc251G99V1bgdEiLicOiqoMFgjpInQVZdTMkdFh74pGtMjQ+7nXDtRNzbeUSvI2dsi4iduMxVTp4jWS1EdLWxQF0epzhI0x5rC1y0aidtgs7BnYtUVETaqedtOJnO1MNy0MbZri6NurNfWAN19SuiLzqUWjxOoro8cpqOnzGGqDXh7bf2QjN5L+5wLG/ElaqnrMYbSwyvNc6a0L6yN1PqidpW52x2bdwyl+zNqaCDc65cLzsUXP4TJiFVXMfUPqo6dslS4NdFkDgJAIwbi9st7bLqxLV1zseqY6eWqfo6yKNsIivEIjGwvJdbUfaJ27QN+tcLzp0XF01XjElLWueauOwgczSRuLrlz87biIW1BoOVrg26y4qjEpKvDszq5rJIWB7HR2c13tXc46PKea4u0iw1a7K6IvOpRcdh9VwlkqI/SISGyi+UssIzEwtdc22Pkykf8ALchXKJ+K1EsUIqMQEDxCZJpYAx7X2fpGi7dmpmu1gTqKaIvOtRcpQemzcIqOSpNY4sdUiQPhyxRi9o8rrWN2+839y6tRq4J3hERQoREQBERAEREARFpcfpp5iC0OfF7OVrdYabnMSLG9xYD2Tb7oDdKlos91ufWtfgUdRFTuE7XMZqyMcdY1e0fcCdYHN7tg2I/WfgP/ALQEoiIAoffKbC5GuylEABBFxrBSw3BUljTvHwJCjRt3v75QFaKjRt3v75TRt3v75QFaKjRt3v75TRt3v75QECnhFSagRM05ZkMmX2st72vuurio0bd7++U0bd7++UBWqWRsY6RzGNa6Q5nkD9RsBc/IAfJRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtFRo27398po27398oCtUtOZzjzbB700bf8AmPxcVUNQsNiAIiIAqZHsijdJI4NY0Xc4mwA3qpaPhs63BXE7f8ErFSehBz5I3ShpzUObuLD+G/B5ji04iLg21RSEfuGqnlzwd7Q/kSeVeJFUr8pv+0YY+fc/ZL/jdmxSzXY9v5dcHO0f5EnlUcuuDnaP8iTyrxBUlaXt60YY5PuX3bsuKWa7HuPLvg52j/Ik8qcu+DnaP8iTyrw7mVK1vy0YVk+4927Lilmux7ny84N9o/yJPKo5ecG+0f5EnlXhig7Fpe26/Jefcvu1ZcUs12PdOXvBvtH+RJ5U5e8Gu0v5EnlXhJULS9s1+S8+5fdqy4pZrse7cveDXaX8iXypy+4NdpfyJfKvCCoK1vivyXn3HuzZcUs12PeOX/BrtL+RL5VHL/gz2l/Il8q8GUFaXtetyXn3L7s2XFLNdj3rl/wZ7S/kS+VPWBwZ7S/kS+VeCKkrS9rVuS8+492bLilmux756weDHaf9PL5U9YPBjtP+nl8q8CKhaXtStyXn3L7sWTFLNdj371hcGO0/6eXyqPWFwY7T/p5fKvAedU860vadXkvPuX3YsmKWa7H0B6wuC/af9PL5U9YfBftP+nl8q+f3KlaXtKryXn3HuxZMUs12PoL1h8F+0/6eXyqPWJwW7U/p5fKvn0qCtL2hU5L19y+69kxSzXY+g/WJwW7U/p5fKnrF4Ldqf08vlXz2dmpUqq31OS9fce69kxSzXY+hfWLwW7U/p5fKuhwnE6LF6NtVhtQyogcbZm8x3EbQfcV8sFeyfwKd/ofExzekD/KF6bPapVZ6MkfM9r+wqFis+upSd6a8buyPT0RF7j8sFoOG5/8Aa2Jf4JW/XP8ADf8A2XxL/CK42n+mf0f6O9m/uh9V+zwwqFJUFfztH9OIVJVSpK2ijmVKq5lStopCg7FKg7FtFKSoUnYoXRFM/Cg8elSQBpqIotIy7Q4izm3IB5wLn3WupxN0stDSz1bQKmV8ji4tDXPZZgaTbbrz6zt1rAa90bw+NzmuGsOabEJUTS1Er5Z5Hyyu1ue9xJPzK9CmtDROGpbqqfD/AH4XXfT4kU4DqiIFucF4GW9s2vZfmXa4hhgkbNL6O2jjDKstjdSsilbljuG6rtezc/bt1rhkc4naSebautGqqaaavvOdpssq8lKMrrr/AIX+P3Oy5K0WkLXenMjbOIhMXNLZm6J787PZ2XaOc6jv2a7DKCkHCXAGxxufS1gjkdFOQ+13uaQdQBHs7udaipxGeelbTu0TIWuDssUTWZnAEAmw1mxP7lYV9YXd1qd6cYnGnY67jJVKnjw8rv3x+13xOgqsHo2YbNIx1QKmOjjq7lzchzSBpba1+e97rnCqifiqSuc5KTVyuPdZ6UqaanLSvZTzqFPOoRHoBVKlyhbQIKgqSocto0UnYo51UdipK2gQV7F/Az+6sT/+Q3/KF46V7D/Az+6sS/8AkN/yhe2x/wBqPh/8j/8Ahl9V+z1IIgRfXP52Fi4hTRVlLLTzszxSNLXN3grKUEXUaTVzKm070eeTfw9oM5LairAPNmbq/wDxVr1e0XWavvN8q9FMYKp0Q3BePdtl6aPdvS19Rnnfq9ous1feb5VHq8oes1neb5V6LohuCaIbgm7rLgQ3pbOozzr1eUPWazvN8qj1d0PWazvN8q9G0Q3BNENwV3fZsCG9LZ1Gec+rqh6zWd5vlT1dUPWazvN8q9G0Q3BNENwTd9mwIu9bZ1Gecermg6zWd5vlUerig6zW95nlXpGiG4JohuCuwWfAhvW2dRnm/q4oOtVveZ5U9W9B1qt7zPKvSNENwTRDcE2Gz4EN62zqM829W1B1qt7zPKo9W1B1qt7zPKvStENwTRDcFdioYEN62zqM819WuH9are8zyqPVph/Wq3vM8q9L0Q3BNENwV2OhhQ3rbOqzzT1aYf1qu7zPKo9WeH9aru8zyr0zRDcE0Q3BNkoYUN7Wzqs8z9WWH9aru8zyqPVlh3Wq7vM8q9N0Q3BNENwV2SjhQ3tbOqzzL1Y4d1uu7zPKo9WGHdbru8zyr07RDcE0Q3BNlo4UN7W3qs8x9WGHdbru8zyp6r8O63X95nlXp2iG4JohuCuzUsJd7W3qs8w9V+G9br+8zyp6rsN63X95nlXp+iG4JohuCuz0sI3tbeqzzFv8LMMO2sxDvM8q7fgrwfo+D1AaWha+znZ3vebued5/YbFuBGArjW2Wo0oQd8Uca9vtNojoVZtokIiLoeQIiIBcbwlxvCWG4JYbggFxvCXG8JYbglhuCAXG8JcbwlhuCWG4IBcbwlxvCWG4JYbggFxvCXG8JYbglhuCAXG8JcbwlhuCWG4IAtfNjNBC+RkkxBjuDaNxBItcAgWJFxqFytgtTLgVNLO+R0k+RzjI2LMMrHkgucNV7m3OSNZta6A2rHB7GuF7OFxcEH9jsU3G9EIB2hALjeEuN4Sw3BLDcEAuN4S43hLDcEsNwQC43hLjeEsNwSw3BALjeEuN4Sw3BLDcEAuN4S43hLDcEsNwQC43hEsNwQC2xAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBaThXWehUtK91T6NC6pY2WTMG2br5zsW7RZnFyi0jUJKMk2jjzj9bTiJmZkjHucYXysOeqGksGttYA5dd7G4sbbVnUVbX19ToXzRQtdeVjo4zcBkti03OvMANeq1yuiRco0pJ8ZcDrKrBrhG5+vX/AIcy2vrn4NRubVwvq3VYhke2OzR7RBBbf/7CxDwlrH1tHSRGnFTMwAtczVnIf7Q9q+W7Rzc9r3XYojpS4XSKq0ON8DiqrhTiDYIZ4qeBsM5cIjJqBc0MGjJJFnF5eBa/6NhWzx3HpMPxSjpWaO8pjzMe3W4OeG+ycw2XubNPNe110SIqU7n/ACDq021dDn8TmeD2O1dbTSyVrYriihqxoYyLZw+7bXN7ZPusKl4U1E9M17pqOKMyuZ6S9l4yQxjg2zXn2iXEanH9J1XXZomrnclpE1sL29E5OXhFVM9MJNMySF5a6BzHZ4WaQNErzexaWkv5tXPtIqpeEsrmRifQiSZ8bYCGkCdpnLHObr2ZcrhuzbSF1SJq5336Q1lO67R8/XrM4wY9iT6eKSSalga4QyufojZrHSFjgbu9wN/j8Vt6J1W2nxR76xzmMuyJ5Akc1zQcziGjef0gc3vst4isaUlxcrxOrF8IxuOHpsfqhCx8DhVODpPaZJpYyRHf2SA24vrIIuNg5lck4S149I0L6SVkEU02kbE4CRsbYiABm1X0h16+bbz9jNEyaJ8UrQ6N4LXNPOCrdHSQUURjpowxpJcdZJJPOSdZK5qjUXDSNutSfHQ4msxnGHYfWtgOjBla0xBwN3uzgOA+AN1jcGccqMTqnRymCRojL3mJpGhdmsGuuTckaxs2Lo1bp4I6aFsUDAyNuxo5l1cJ6d+lwOanT1bjo8eZcREXU4hERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAEREAREQBERAfKjsXxPtGt8d35Vp2L4p2lW+O78qyQrbmoC87GMV7SrfHf8AlUHGMV7TrvHf+VZLVSWIC/xxivadd47/AMpxxivadd9Q/wDKx8iZEBf44xXtKu+of+U44xXtKu+of+VYyJkQF/jjFe0q76h/5TjjFe0q76h/5VjImRAX+OMV7SrvqH/lOOMV7SrvqH/lY+RMiAyOOMV7TrvqH/lOOMV7TrvqH/lY+RMiAv8AG+Ldp131D/ynG+Ldp131D/yrGRMioL/G+Ldp131D/wApxvi3add9Q/8AKsZEyKAv8b4t2nXfUP8AynG+Ldp131D/AMqxkTJ70Bf44xbtOu+of+U44xbtOu+of+VYye9MnvQF/jjFu0676h/5TjjFe0676h/5VjImRAX+OMV7TrvqH/lOOMV7TrvqH/lWMiZEBkDGMV7TrvqH/lSMYxXtKu8d/wCVjZFORAZQxjFe0q7x3/lXG4xinaVb47/ysINVYagM5uMYp2jW+O78q5xvinaNb47vysBrVXZAVkKkhXFBCAtFqjKFdslkBZypkV2yWQFrImVXbJZAWsqZVdslkBayplV2yWQFrKmVXbJZUFrKmVXbJZAWsqZVdsllAWsqZVdslkBZy+5MvuV6yW1oCzkTIrtksgLWRMiu2SyAtZFORXLJZAUBqkBV2U2QFICmymylAVJZEQCyiylEBFkspRARZLKUQEWSylEBFkspRUEWRSiAhFKa0BCKdahQEIpRAQllKICLJZSiAiyWUogIsllKICLJZSiAIiICUREAREQBERAEREAREVAREQBERAPmnzRFAPmoREAREQBERAEREAREQBERAEREAREVBKIigCIiAIiIAiIgCIioCIiAJrRRqQE60UakUAREQBERAEREAREQBERAEREAREVAREQEoiKAIiIAiIgCIioCIiAIiIB8k+Sj5p81ASoREAREQBERAEREAREQBERAEREAREVAREQEoiKAIiIAiIqAiIgCIiAjmQoigCIiAIiIAiIgCIiAIiIAiIgCIioCIiAIiIAiIgP/2Q=="}, {"timing": 3681, "timestamp": 227777780673, "data": "data:image/jpeg;base64,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"}]}}, "final-screenshot": {"id": "final-screenshot", "title": "Final Screenshot", "description": "The last screenshot captured of the pageload.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "screenshot", "timing": 3682, "timestamp": 227777781579, "data": "data:image/jpeg;base64,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"}}, "total-blocking-time": {"id": "total-blocking-time", "title": "Total Blocking Time", "description": "Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).", "score": 0.99, "scoreDisplayMode": "numeric", "numericValue": 76, "numericUnit": "millisecond", "displayValue": "80 ms", "scoringOptions": {"p10": 200, "median": 600}}, "max-potential-fid": {"id": "max-potential-fid", "title": "Max Potential First Input Delay", "description": "The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).", "score": 0.9, "scoreDisplayMode": "numeric", "numericValue": 127, "numericUnit": "millisecond", "displayValue": "130 ms"}, "cumulative-layout-shift": {"id": "cumulative-layout-shift", "title": "Cumulative Layout Shift", "description": "Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 0, "numericUnit": "unitless", "displayValue": "0", "scoringOptions": {"p10": 0.1, "median": 0.25}, "details": {"type": "debugdata", "items": [{"cumulativeLayoutShiftMainFrame": 0, "newEngineResult": {"cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0}, "newEngineResultDiffered": false}]}}, "errors-in-console": {"id": "errors-in-console", "title": "Browser errors were logged to the console", "description": "Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "sourceLocation", "valueType": "source-location", "label": "Source"}, {"key": "description", "valueType": "code", "label": "Description"}], "items": [{"source": "console.error", "description": "Failed to initialize real-time connection: ReferenceError: supabase is not defined\n    at hm.initializeConnection (http://localhost:4173/assets/index-CGQEF-7R.js:786:51331)\n    at http://localhost:4173/assets/index-CGQEF-7R.js:786:51881", "sourceLocation": {"type": "source-location", "url": "http://localhost:4173/assets/index-CGQEF-7R.js", "urlProvider": "network", "line": 785, "column": 51523}}, {"source": "console.error", "description": "Failed to initialize real-time connection: ReferenceError: supabase is not defined\n    at hm.initializeConnection (http://localhost:4173/assets/index-CGQEF-7R.js:786:51331)\n    at http://localhost:4173/assets/index-CGQEF-7R.js:786:51881", "sourceLocation": {"type": "source-location", "url": "http://localhost:4173/assets/index-CGQEF-7R.js", "urlProvider": "network", "line": 785, "column": 51523}}, {"source": "console.error", "description": "Failed to initialize real-time connection: ReferenceError: supabase is not defined\n    at hm.initializeConnection (http://localhost:4173/assets/index-CGQEF-7R.js:786:51331)\n    at new hm (http://localhost:4173/assets/index-CGQEF-7R.js:786:51169)\n    at hm.getInstance (http://localhost:4173/assets/index-CGQEF-7R.js:786:51246)\n    at http://localhost:4173/assets/index-CGQEF-7R.js:786:55239", "sourceLocation": {"type": "source-location", "url": "http://localhost:4173/assets/index-CGQEF-7R.js", "urlProvider": "network", "line": 785, "column": 51523}}]}}, "server-response-time": {"id": "server-response-time", "title": "Initial server response time was short", "description": "Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0.757, "numericUnit": "millisecond", "displayValue": "Root document took 0 ms", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "responseTime", "valueType": "timespanMs", "label": "Time Spent"}], "items": [{"url": "http://localhost:4173/auth", "responseTime": 0.757}], "overallSavingsMs": 0}, "guidanceLevel": 1}, "interactive": {"id": "interactive", "title": "Time to Interactive", "description": "Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).", "score": 0.56, "scoreDisplayMode": "numeric", "numericValue": 6730.4694, "numericUnit": "millisecond", "displayValue": "6.7 s"}, "user-timings": {"id": "user-timings", "title": "User Timing marks and measures", "description": "Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "critical-request-chains": {"id": "critical-request-chains", "title": "Avoid chaining critical requests", "description": "The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).", "score": 1, "scoreDisplayMode": "informative", "displayValue": "4 chains found", "details": {"type": "criticalrequestchain", "chains": {"F097DAD5E2262BEDD15C4B0EB4A37B36": {"request": {"url": "http://localhost:4173/auth", "startTime": 227774.100757, "endTime": 227774.101955, "responseReceivedTime": 227774.10173499997, "transferSize": 1125}, "children": {"45261.2": {"request": {"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "startTime": 227774.107359, "endTime": 227774.167882, "responseReceivedTime": 227774.111608, "transferSize": 805489}}, "45261.3": {"request": {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "startTime": 227774.107622, "endTime": 227774.112816, "responseReceivedTime": 227774.11120900002, "transferSize": 21531}, "children": {"45261.8": {"request": {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "startTime": 227774.113617, "endTime": 227774.561301, "responseReceivedTime": 227774.56102899997, "transferSize": 1312}, "children": {"45261.16": {"request": {"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "startTime": 227774.623036, "endTime": 227775.283194, "responseReceivedTime": 227775.07669100002, "transferSize": 48981}}}}, "45261.9": {"request": {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "startTime": 227774.114062, "endTime": 227774.561623, "responseReceivedTime": 227774.561462, "transferSize": 737}}}}, "45261.4": {"request": {"url": "https://cdn.gpteng.co/gptengineer.js", "startTime": 227774.108025, "endTime": 227774.560865, "responseReceivedTime": 227774.560701, "transferSize": 232}}}}}, "longestChain": {"duration": 1182.************, "length": 4, "transferSize": 48981}}, "guidanceLevel": 1}, "redirects": {"id": "redirects", "title": "Avoid multiple page redirects", "description": "Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 2}, "image-aspect-ratio": {"id": "image-aspect-ratio", "title": "Displays images with correct aspect ratio", "description": "Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "image-size-responsive": {"id": "image-size-responsive", "title": "Serves images with appropriate resolution", "description": "Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "deprecations": {"id": "deprecations", "title": "Avoids deprecated APIs", "description": "Deprecated APIs will eventually be removed from the browser. [Learn more about deprecated APIs](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "third-party-cookies": {"id": "third-party-cookies", "title": "Avoids third-party cookies", "description": "Third-party cookies may be blocked in some contexts. [Learn more about preparing for third-party cookie restrictions](https://privacysandbox.google.com/cookies/prepare/overview).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "mainthread-work-breakdown": {"id": "mainthread-work-breakdown", "title": "Minimizes main-thread work", "description": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 603.6560000000001, "numericUnit": "millisecond", "displayValue": "0.6 s", "metricSavings": {"TBT": 100}, "details": {"type": "table", "headings": [{"key": "groupLabel", "valueType": "text", "label": "Category"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"group": "other", "groupLabel": "Other", "duration": 298.63600000000037}, {"group": "scriptEvaluation", "groupLabel": "Script Evaluation", "duration": 216.1559999999997}, {"group": "styleLayout", "groupLabel": "Style & Layout", "duration": 60.624}, {"group": "scriptParseCompile", "groupLabel": "Script Parsing & Compilation", "duration": 12.959999999999999}, {"group": "paintCompositeRender", "groupLabel": "Rendering", "duration": 10.119999999999985}, {"group": "parseHTML", "groupLabel": "Parse HTML & CSS", "duration": 5.16}], "sortedBy": ["duration"]}, "guidanceLevel": 1}, "bootup-time": {"id": "bootup-time", "title": "JavaScript execution time", "description": "Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 225.98000000000133, "numericUnit": "millisecond", "displayValue": "0.2 s", "metricSavings": {"TBT": 50}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "total", "granularity": 1, "valueType": "ms", "label": "Total CPU Time"}, {"key": "scripting", "granularity": 1, "valueType": "ms", "label": "Script Evaluation"}, {"key": "scriptParseCompile", "granularity": 1, "valueType": "ms", "label": "<PERSON><PERSON><PERSON> Parse"}], "items": [{"url": "Unattributable", "total": 260.852, "scripting": 5.156, "scriptParseCompile": 0}, {"url": "http://localhost:4173/auth", "total": 167.9360000000009, "scripting": 124.36800000000088, "scriptParseCompile": 0.612}, {"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "total": 162.80400000000049, "scripting": 83.77600000000047, "scriptParseCompile": 12.068}], "summary": {"wastedMs": 225.98000000000133}, "sortedBy": ["total"]}, "guidanceLevel": 1}, "uses-rel-preconnect": {"id": "uses-rel-preconnect", "title": "Preconnect to required origins", "description": "Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 501.1704, "numericUnit": "millisecond", "displayValue": "Est savings of 500 ms", "warnings": [], "metricSavings": {"LCP": 500, "FCP": 500}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Est Savings"}], "items": [{"url": "https://fonts.gstatic.com", "wastedMs": 501.1704}], "overallSavingsMs": 501.1704, "sortedBy": ["wastedMs"]}, "guidanceLevel": 3}, "font-display": {"id": "font-display", "title": "All text remains visible during webfont loads", "description": "Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "diagnostics": {"id": "diagnostics", "title": "Diagnostics", "description": "Collection of useful page vitals.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "debugdata", "items": [{"numRequests": 8, "numScripts": 2, "numStylesheets": 3, "numFonts": 1, "numTasks": 395, "numTasksOver10ms": 4, "numTasksOver25ms": 2, "numTasksOver50ms": 1, "numTasksOver100ms": 0, "numTasksOver500ms": 0, "rtt": 0.0198, "throughput": 27070962.86588716, "maxRtt": 133.944, "maxServerLatency": 70.15899999999995, "totalByteWeight": 894763, "totalTaskTime": 150.91399999999982, "mainDocumentTransferSize": 1125}]}}, "network-requests": {"id": "network-requests", "title": "Network Requests", "description": "Lists the network requests that were made during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "protocol", "valueType": "text", "label": "Protocol"}, {"key": "networkRequestTime", "valueType": "ms", "granularity": 1, "label": "Network Request Time"}, {"key": "networkEndTime", "valueType": "ms", "granularity": 1, "label": "Network End Time"}, {"key": "transferSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Transfer Size"}, {"key": "resourceSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Resource Size"}, {"key": "statusCode", "valueType": "text", "label": "Status Code"}, {"key": "mimeType", "valueType": "text", "label": "MIME Type"}, {"key": "resourceType", "valueType": "text", "label": "Resource Type"}], "items": [{"url": "http://localhost:4173/auth", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 0, "networkRequestTime": 0.5180000066757202, "networkEndTime": 1.7159999907016754, "finished": true, "transferSize": 1125, "resourceSize": 875, "statusCode": 200, "mimeType": "text/html", "resourceType": "Document", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.301000028848648, "networkRequestTime": 7.120000004768372, "networkEndTime": 67.64300000667572, "finished": true, "transferSize": 805489, "resourceSize": 2958309, "statusCode": 200, "mimeType": "text/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 6.542000025510788, "networkRequestTime": 7.383000016212463, "networkEndTime": 12.57700002193451, "finished": true, "transferSize": 21531, "resourceSize": 144473, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "https://cdn.gpteng.co/gptengineer.js", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 6.6030000150203705, "networkRequestTime": 7.78600001335144, "networkEndTime": 460.6260000169277, "finished": true, "transferSize": 232, "resourceSize": 0, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "High", "experimentalFromMainFrame": true, "entity": "gpteng.co"}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 12.948000013828278, "networkRequestTime": 13.377999991178513, "networkEndTime": 461.0620000064373, "finished": true, "transferSize": 1312, "resourceSize": 15042, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "Google Fonts"}, {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 13.017000019550323, "networkRequestTime": 13.823000013828278, "networkEndTime": 461.38400000333786, "finished": true, "transferSize": 737, "resourceSize": 6666, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "Google Fonts"}, {"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 510.80300000309944, "networkRequestTime": 522.7970000207424, "networkEndTime": 1182.9549999833107, "finished": true, "transferSize": 48981, "resourceSize": 48432, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "Google Fonts"}, {"url": "http://localhost:4173/favicon.ico", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 523.7509999871254, "networkRequestTime": 523.983000010252, "networkEndTime": 525.2529999911785, "finished": true, "transferSize": 15356, "resourceSize": 15086, "statusCode": 200, "mimeType": "image/x-icon", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}], "debugData": {"type": "debugdata", "networkStartTimeTs": 227774100239}}}, "network-rtt": {"id": "network-rtt", "title": "Network Round Trip Times", "description": "Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 133.944, "numericUnit": "millisecond", "displayValue": "130 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "rtt", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "https://cdn.gpteng.co", "rtt": 133.944}, {"origin": "https://fonts.googleapis.com", "rtt": 130.532}, {"origin": "https://fonts.gstatic.com", "rtt": 100.605}, {"origin": "http://localhost:4173", "rtt": 0.0198}], "sortedBy": ["rtt"]}}, "network-server-latency": {"id": "network-server-latency", "title": "Server Backend Latencies", "description": "Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 70.15899999999995, "numericUnit": "millisecond", "displayValue": "70 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "serverResponseTime", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "https://fonts.gstatic.com", "serverResponseTime": 70.15899999999995}, {"origin": "https://cdn.gpteng.co", "serverResponseTime": 29.99800000000002}, {"origin": "https://fonts.googleapis.com", "serverResponseTime": 24.008999999999986}, {"origin": "http://localhost:4173", "serverResponseTime": 1.7347}], "sortedBy": ["serverResponseTime"]}}, "main-thread-tasks": {"id": "main-thread-tasks", "title": "Tasks", "description": "Lists the toplevel main thread tasks that executed during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "End Time"}], "items": [{"duration": 50.718, "startTime": 85.32}, {"duration": 31.683, "startTime": 462.802}, {"duration": 10.13, "startTime": 494.906}, {"duration": 17.793, "startTime": 505.163}, {"duration": 5.418, "startTime": 1181.568}]}}, "metrics": {"id": "metrics", "title": "Metrics", "description": "Collects all available metrics.", "score": 1, "scoreDisplayMode": "informative", "numericValue": 6730, "numericUnit": "millisecond", "details": {"type": "debugdata", "items": [{"firstContentfulPaint": 6604, "largestContentfulPaint": 6622, "interactive": 6730, "speedIndex": 6604, "totalBlockingTime": 76, "maxPotentialFID": 127, "cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0, "timeToFirstByte": 452, "observedTimeOrigin": 0, "observedTimeOriginTs": 227774099673, "observedNavigationStart": 0, "observedNavigationStartTs": 227774099673, "observedFirstPaint": 1210, "observedFirstPaintTs": 227775309353, "observedFirstContentfulPaint": 1435, "observedFirstContentfulPaintTs": 227775534368, "observedFirstContentfulPaintAllFrames": 1435, "observedFirstContentfulPaintAllFramesTs": 227775534368, "observedLargestContentfulPaint": 1435, "observedLargestContentfulPaintTs": 227775534368, "observedLargestContentfulPaintAllFrames": 1435, "observedLargestContentfulPaintAllFramesTs": 227775534368, "observedTraceEnd": 3748, "observedTraceEndTs": 227777847671, "observedLoad": 495, "observedLoadTs": 227774594295, "observedDomContentLoaded": 493, "observedDomContentLoadedTs": 227774592847, "observedCumulativeLayoutShift": 0, "observedCumulativeLayoutShiftMainFrame": 0, "observedFirstVisualChange": 1403, "observedFirstVisualChangeTs": 227775502673, "observedLastVisualChange": 3681, "observedLastVisualChangeTs": 227777780673, "observedSpeedIndex": 1459, "observedSpeedIndexTs": 227775558357}, {"lcpInvalidated": false}]}}, "resource-summary": {"id": "resource-summary", "title": "Resources Summary", "description": "Aggregates all network requests and groups them by type", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Resource Type"}, {"key": "requestCount", "valueType": "numeric", "label": "Requests"}, {"key": "transferSize", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"resourceType": "total", "label": "Total", "requestCount": 7, "transferSize": 879407}, {"resourceType": "script", "label": "<PERSON><PERSON><PERSON>", "requestCount": 2, "transferSize": 805721}, {"resourceType": "font", "label": "Font", "requestCount": 1, "transferSize": 48981}, {"resourceType": "stylesheet", "label": "Stylesheet", "requestCount": 3, "transferSize": 23580}, {"resourceType": "document", "label": "Document", "requestCount": 1, "transferSize": 1125}, {"resourceType": "image", "label": "Image", "requestCount": 0, "transferSize": 0}, {"resourceType": "media", "label": "Media", "requestCount": 0, "transferSize": 0}, {"resourceType": "other", "label": "Other", "requestCount": 0, "transferSize": 0}, {"resourceType": "third-party", "label": "Third-party", "requestCount": 4, "transferSize": 51262}]}}, "third-party-summary": {"id": "third-party-summary", "title": "Minimize third-party usage", "description": "Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).", "score": 1, "scoreDisplayMode": "informative", "displayValue": "Third-party code blocked the main thread for 0 ms", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "entity", "valueType": "text", "label": "Third-Party", "subItemsHeading": {"key": "url", "valueType": "url"}}, {"key": "transferSize", "granularity": 1, "valueType": "bytes", "label": "Transfer Size", "subItemsHeading": {"key": "transferSize"}}, {"key": "blockingTime", "granularity": 1, "valueType": "ms", "label": "Main-Thread Blocking Time", "subItemsHeading": {"key": "blockingTime"}}], "items": [{"mainThreadTime": 2.96, "blockingTime": 0, "transferSize": 51030, "tbtImpact": 0, "entity": "Google Fonts", "subItems": {"type": "subitems", "items": [{"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "mainThreadTime": 0, "blockingTime": 0, "transferSize": 48981, "tbtImpact": 0}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "mainThreadTime": 1.8719999999999999, "blockingTime": 0, "transferSize": 1312, "tbtImpact": 0}, {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "mainThreadTime": 1.088, "blockingTime": 0, "transferSize": 737, "tbtImpact": 0}]}}, {"mainThreadTime": 1.788, "blockingTime": 0, "transferSize": 232, "tbtImpact": 0, "entity": "gpteng.co", "subItems": {"type": "subitems", "items": [{"url": "https://cdn.gpteng.co/gptengineer.js", "mainThreadTime": 1.788, "blockingTime": 0, "transferSize": 232, "tbtImpact": 0}]}}], "summary": {"wastedBytes": 51262, "wastedMs": 0}, "isEntityGrouped": true}, "guidanceLevel": 1}, "third-party-facades": {"id": "third-party-facades", "title": "Lazy load third-party resources with facades", "description": "Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 3}, "largest-contentful-paint-element": {"id": "largest-contentful-paint-element", "title": "Largest Contentful Paint element", "description": "This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0, "scoreDisplayMode": "metricSavings", "displayValue": "6,620 ms", "metricSavings": {"LCP": 4100}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Element"}], "items": [{"node": {"type": "node", "lhId": "page-0-P", "path": "1,HTML,1,BODY,5,DIV,0,DIV,0,DIV,0,DIV,1,P", "selector": "div.relative > div.flex > div > p.text-white/90", "boundingRect": {"top": 218, "bottom": 238, "left": 24, "right": 388, "width": 364, "height": 20}, "snippet": "<p class=\"text-white/90 text-sm\">", "nodeLabel": "Sign in to access your account and saved properties"}}]}, {"type": "table", "headings": [{"key": "phase", "valueType": "text", "label": "Phase"}, {"key": "percent", "valueType": "text", "label": "% of LCP"}, {"key": "timing", "valueType": "ms", "label": "Timing"}], "items": [{"phase": "TTFB", "timing": 451.7347, "percent": "7%"}, {"phase": "<PERSON>ad <PERSON>", "timing": 0, "percent": "0%"}, {"phase": "Load Time", "timing": 0, "percent": "0%"}, {"phase": "Render Delay", "timing": 6170.7347, "percent": "93%"}]}]}, "guidanceLevel": 1}, "lcp-lazy-loaded": {"id": "lcp-lazy-loaded", "title": "Largest Contentful Paint image was not lazily loaded", "description": "Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 3}, "layout-shifts": {"id": "layout-shifts", "title": "Avoid large layout shifts", "description": "These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "long-tasks": {"id": "long-tasks", "title": "Avoid long main-thread tasks", "description": "Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "2 long tasks found", "metricSavings": {"TBT": 100}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Duration"}], "items": [{"url": "Unattributable", "duration": 203, "startTime": 602.7347}, {"url": "http://localhost:4173/auth", "duration": 127, "startTime": 6603.4694}], "sortedBy": ["duration"], "skipSumming": ["startTime"], "debugData": {"type": "debugdata", "urls": ["Unattributable", "http://localhost:4173/auth"], "tasks": [{"urlIndex": 0, "startTime": 602.7, "duration": 203, "other": 203}, {"urlIndex": 1, "startTime": 6603.5, "duration": 127, "other": 127, "scriptEvaluation": 0, "styleLayout": 0}]}}, "guidanceLevel": 1}, "non-composited-animations": {"id": "non-composited-animations", "title": "Avoid non-composited animations", "description": "Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "1 animated element found", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "failureReason", "valueType": "text"}, "label": "Element"}, {"key": null, "valueType": "text", "subItemsHeading": {"key": "animation", "valueType": "text"}, "label": "Name"}], "items": [{"node": {"type": "node", "lhId": "page-1-INPUT", "path": "1,HTML,1,BODY,5,DIV,1,DIV,0,FORM,0,DIV,1,DIV,1,INPUT", "selector": "form.space-y-5 > div.space-y-2 > div.relative > input#email", "boundingRect": {"top": 318, "bottom": 366, "left": 24, "right": 388, "width": 364, "height": 48}, "snippet": "<input type=\"email\" class=\"flex w-full border bg-background px-3 py-2 text-base ring-offset-backgroun…\" id=\"email\" placeholder=\"Enter your email\" value=\"\">", "nodeLabel": "form.space-y-5 > div.space-y-2 > div.relative > input#email"}, "subItems": {"type": "subitems", "items": [{"failureReason": "Unsupported CSS Property: border-bottom-color", "animation": "border-bottom-color"}, {"failureReason": "Unsupported CSS Property: border-left-color", "animation": "border-left-color"}, {"failureReason": "Unsupported CSS Property: border-right-color", "animation": "border-right-color"}, {"failureReason": "Unsupported CSS Property: border-top-color", "animation": "border-top-color"}]}}]}, "guidanceLevel": 2}, "unsized-images": {"id": "unsized-images", "title": "Image elements have explicit `width` and `height`", "description": "Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 4}, "valid-source-maps": {"id": "valid-source-maps", "title": "Missing source maps for large first-party JavaScript", "description": "Source maps translate minified code to the original source code. This helps developers debug in production. In addition, Lighthouse is able to provide further insights. Consider deploying source maps to take advantage of these benefits. [Learn more about source maps](https://developer.chrome.com/docs/devtools/javascript/source-maps/).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "scriptUrl", "valueType": "url", "subItemsHeading": {"key": "error"}, "label": "URL"}, {"key": "sourceMapUrl", "valueType": "url", "label": "Map URL"}], "items": [{"scriptUrl": "http://localhost:4173/assets/index-CGQEF-7R.js", "subItems": {"type": "subitems", "items": [{"error": "Large JavaScript file is missing a source map"}]}}]}}, "prioritize-lcp-image": {"id": "prioritize-lcp-image", "title": "Preload Largest Contentful Paint image", "description": "If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 4}, "csp-xss": {"id": "csp-xss", "title": "Ensure CSP is effective against XSS attacks", "description": "A strong Content Security Policy (CSP) significantly reduces the risk of cross-site scripting (XSS) attacks. [Learn how to use a CSP to prevent XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No CSP found in enforcement mode"}]}}, "has-hsts": {"id": "has-hsts", "title": "Use a strong HSTS policy", "description": "Deployment of the HSTS header significantly reduces the risk of downgrading HTTP connections and eavesdropping attacks. A rollout in stages, starting with a low max-age is recommended. [Learn more about using a strong HSTS policy.](https://developer.chrome.com/docs/lighthouse/best-practices/has-hsts)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No HSTS header found"}]}}, "origin-isolation": {"id": "origin-isolation", "title": "Ensure proper origin isolation with COOP", "description": "The Cross-Origin-Opener-Policy (COOP) can be used to isolate the top-level window from other documents such as pop-ups. [Learn more about deploying the COOP header.](https://web.dev/articles/why-coop-coep#coop)", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "directive", "valueType": "code", "subItemsHeading": {"key": "directive"}, "label": "Directive"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"description": "No COOP header found", "severity": "High"}]}}, "clickjacking-mitigation": {"id": "clickjacking-mitigation", "title": "Mitigate clickjacking with XFO or CSP", "description": "The `X-Frame-Options` (XFO) header or the `frame-ancestors` directive in the `Content-Security-Policy` (CSP) header control where a page can be embedded. These can mitigate clickjacking attacks by blocking some or all sites from embedding the page. [Learn more about mitigating clickjacking](https://developer.chrome.com/docs/lighthouse/best-practices/clickjacking-mitigation).", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "description", "valueType": "text", "subItemsHeading": {"key": "description"}, "label": "Description"}, {"key": "severity", "valueType": "text", "subItemsHeading": {"key": "severity"}, "label": "Severity"}], "items": [{"severity": "High", "description": "No frame control policy found"}]}}, "script-treemap-data": {"id": "script-treemap-data", "title": "Script Treemap Data", "description": "Used for treemap app", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "treemap-data", "nodes": [{"name": "http://localhost:4173/assets/index-CGQEF-7R.js", "resourceBytes": 2956446, "encodedBytes": 805133, "unusedBytes": 2320802}, {"name": "https://cdn.gpteng.co/gptengineer.js", "resourceBytes": 0, "encodedBytes": 0}]}}, "accesskeys": {"id": "accesskeys", "title": "`[accesskey]` values are unique", "description": "Access keys let users quickly focus a part of the page. For proper navigation, each access key must be unique. [Learn more about access keys](https://dequeuniversity.com/rules/axe/4.10/accesskeys).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-allowed-attr": {"id": "aria-allowed-attr", "title": "`[aria-*]` attributes match their roles", "description": "Each ARIA `role` supports a specific subset of `aria-*` attributes. Mismatching these invalidates the `aria-*` attributes. [Learn how to match ARIA attributes to their roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-allowed-role": {"id": "aria-allowed-role", "title": "Uses ARIA roles only on compatible elements", "description": "Many HTML elements can only be assigned certain ARIA roles. Using ARIA roles where they are not allowed can interfere with the accessibility of the web page. [Learn more about ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-role).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-command-name": {"id": "aria-command-name", "title": "`button`, `link`, and `menuitem` elements have accessible names", "description": "When an element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to make command elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-command-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-conditional-attr": {"id": "aria-conditional-attr", "title": "ARIA attributes are used as specified for the element's role", "description": "Some ARIA attributes are only allowed on an element under certain conditions. [Learn more about conditional ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-conditional-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-deprecated-role": {"id": "aria-deprecated-role", "title": "Deprecated ARIA roles were not used", "description": "Deprecated ARIA roles may not be processed correctly by assistive technology. [Learn more about deprecated ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-deprecated-role).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-dialog-name": {"id": "aria-dialog-name", "title": "Elements with `role=\"dialog\"` or `role=\"alertdialog\"` have accessible names.", "description": "ARIA dialog elements without accessible names may prevent screen readers users from discerning the purpose of these elements. [Learn how to make ARIA dialog elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-dialog-name).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-hidden-body": {"id": "aria-hidden-body", "title": "`[aria-hidden=\"true\"]` is not present on the document `<body>`", "description": "Assistive technologies, like screen readers, work inconsistently when `aria-hidden=\"true\"` is set on the document `<body>`. [Learn how `aria-hidden` affects the document body](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-body).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-hidden-focus": {"id": "aria-hidden-focus", "title": "`[aria-hidden=\"true\"]` elements do not contain focusable descendents", "description": "Focusable descendents within an `[aria-hidden=\"true\"]` element prevent those interactive elements from being available to users of assistive technologies like screen readers. [Learn how `aria-hidden` affects focusable elements](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-focus).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-input-field-name": {"id": "aria-input-field-name", "title": "ARIA input fields have accessible names", "description": "When an input field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about input field labels](https://dequeuniversity.com/rules/axe/4.10/aria-input-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-meter-name": {"id": "aria-meter-name", "title": "ARIA `meter` elements have accessible names", "description": "When a meter element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `meter` elements](https://dequeuniversity.com/rules/axe/4.10/aria-meter-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-progressbar-name": {"id": "aria-progressbar-name", "title": "ARIA `progressbar` elements have accessible names", "description": "When a `progressbar` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to label `progressbar` elements](https://dequeuniversity.com/rules/axe/4.10/aria-progressbar-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-prohibited-attr": {"id": "aria-prohibited-attr", "title": "Elements use only permitted ARIA attributes", "description": "Using ARIA attributes in roles where they are prohibited can mean that important information is not communicated to users of assistive technologies. [Learn more about prohibited ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-prohibited-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-required-attr": {"id": "aria-required-attr", "title": "`[role]`s have all required `[aria-*]` attributes", "description": "Some ARIA roles have required attributes that describe the state of the element to screen readers. [Learn more about roles and required attributes](https://dequeuniversity.com/rules/axe/4.10/aria-required-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-required-children": {"id": "aria-required-children", "title": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` have all required children.", "description": "Some ARIA parent roles must contain specific child roles to perform their intended accessibility functions. [Learn more about roles and required children elements](https://dequeuniversity.com/rules/axe/4.10/aria-required-children).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-required-parent": {"id": "aria-required-parent", "title": "`[role]`s are contained by their required parent element", "description": "Some ARIA child roles must be contained by specific parent roles to properly perform their intended accessibility functions. [Learn more about ARIA roles and required parent element](https://dequeuniversity.com/rules/axe/4.10/aria-required-parent).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-roles": {"id": "aria-roles", "title": "`[role]` values are valid", "description": "ARIA roles must have valid values in order to perform their intended accessibility functions. [Learn more about valid ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-roles).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-text": {"id": "aria-text", "title": "Elements with the `role=text` attribute do not have focusable descendents.", "description": "Adding `role=text` around a text node split by markup enables VoiceOver to treat it as one phrase, but the element's focusable descendents will not be announced. [Learn more about the `role=text` attribute](https://dequeuniversity.com/rules/axe/4.10/aria-text).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-toggle-field-name": {"id": "aria-toggle-field-name", "title": "ARIA toggle fields have accessible names", "description": "When a toggle field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about toggle fields](https://dequeuniversity.com/rules/axe/4.10/aria-toggle-field-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-tooltip-name": {"id": "aria-tooltip-name", "title": "ARIA `tooltip` elements have accessible names", "description": "When a tooltip element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `tooltip` elements](https://dequeuniversity.com/rules/axe/4.10/aria-tooltip-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-treeitem-name": {"id": "aria-treeitem-name", "title": "ARIA `treeitem` elements have accessible names", "description": "When a `treeitem` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about labeling `treeitem` elements](https://dequeuniversity.com/rules/axe/4.10/aria-treeitem-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "aria-valid-attr-value": {"id": "aria-valid-attr-value", "title": "`[aria-*]` attributes have valid values", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid values. [Learn more about valid values for ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr-value).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "aria-valid-attr": {"id": "aria-valid-attr", "title": "`[aria-*]` attributes are valid and not misspelled", "description": "Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid names. [Learn more about valid ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "button-name": {"id": "button-name", "title": "Buttons do not have an accessible name", "description": "When a button doesn't have an accessible name, screen readers announce it as \"button\", making it unusable for users who rely on screen readers. [Learn how to make buttons more accessible](https://dequeuniversity.com/rules/axe/4.10/button-name).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-0-BUTTON", "path": "1,HTML,1,BODY,5,DIV,1,DIV,0,FORM,1,DIV,1,DIV,2,BUTTON", "selector": "form.space-y-5 > div.space-y-2 > div.relative > button.absolute", "boundingRect": {"top": 420, "bottom": 464, "left": 360, "right": 376, "width": 16, "height": 44}, "snippet": "<button type=\"button\" class=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:te…\">", "nodeLabel": "form.space-y-5 > div.space-y-2 > div.relative > button.absolute", "explanation": "Fix any of the following:\n  Element does not have inner text that is visible to screen readers\n  aria-label attribute does not exist or is empty\n  aria-labelledby attribute does not exist, references elements that do not exist or references elements that are empty\n  Element has no title attribute\n  Element does not have an implicit (wrapped) <label>\n  Element does not have an explicit <label>\n  Element's default semantics were not overridden with role=\"none\" or role=\"presentation\""}}], "debugData": {"type": "debugdata", "impact": "critical", "tags": ["cat.name-role-value", "wcag2a", "wcag412", "section508", "section508.22.a", "TTv5", "TT6.a", "EN-301-549", "EN-*******", "ACT"]}}}, "bypass": {"id": "bypass", "title": "The page contains a heading, skip link, or landmark region", "description": "Adding ways to bypass repetitive content lets keyboard users navigate the page more efficiently. [Learn more about bypass blocks](https://dequeuniversity.com/rules/axe/4.10/bypass).", "score": null, "scoreDisplayMode": "notApplicable"}, "color-contrast": {"id": "color-contrast", "title": "Background and foreground colors do not have a sufficient contrast ratio.", "description": "Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-1-BUTTON", "path": "1,H<PERSON>L,1,BODY,5,DIV,1,DIV,1,DIV,1,BUTTON", "selector": "div#radix-:r0: > div.p-6 > div.mt-6 > button.inline-flex", "boundingRect": {"top": 594, "bottom": 638, "left": 84, "right": 328, "width": 244, "height": 44}, "snippet": "<button class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md…\">", "nodeLabel": "Don't have an account? Sign up", "explanation": "Fix any of the following:\n  Element has insufficient color contrast of 3.55 (foreground color: #ea580c, background color: #ffffff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1"}, "subItems": {"type": "subitems", "items": [{"relatedNode": {"type": "node", "lhId": "1-2-DIV", "path": "1,<PERSON><PERSON><PERSON>,1,BOD<PERSON>,5,DIV", "selector": "body > div#radix-:r0:", "boundingRect": {"top": 162, "bottom": 662, "left": 0, "right": 412, "width": 412, "height": 500}, "snippet": "<div role=\"dialog\" id=\"radix-:r0:\" aria-describedby=\"radix-:r2:\" aria-labelledby=\"radix-:r1:\" data-state=\"open\" class=\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] tr…\" tabindex=\"-1\" style=\"pointer-events: auto;\">", "nodeLabel": "Welcome Back! 👋\n\nSign in to access your account and saved properties\n\nEmail Add…"}}]}}], "debugData": {"type": "debugdata", "impact": "serious", "tags": ["cat.color", "wcag2aa", "wcag143", "TTv5", "TT13.c", "EN-301-549", "EN-*******", "ACT"]}}}, "definition-list": {"id": "definition-list", "title": "`<dl>`'s contain only properly-ordered `<dt>` and `<dd>` groups, `<script>`, `<template>` or `<div>` elements.", "description": "When definition lists are not properly marked up, screen readers may produce confusing or inaccurate output. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/definition-list).", "score": null, "scoreDisplayMode": "notApplicable"}, "dlitem": {"id": "dlitem", "title": "Definition list items are wrapped in `<dl>` elements", "description": "Definition list items (`<dt>` and `<dd>`) must be wrapped in a parent `<dl>` element to ensure that screen readers can properly announce them. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/dlitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "document-title": {"id": "document-title", "title": "Document has a `<title>` element", "description": "The title gives screen reader users an overview of the page, and search engine users rely on it heavily to determine if a page is relevant to their search. [Learn more about document titles](https://dequeuniversity.com/rules/axe/4.10/document-title).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "duplicate-id-aria": {"id": "duplicate-id-aria", "title": "ARIA IDs are unique", "description": "The value of an ARIA ID must be unique to prevent other instances from being overlooked by assistive technologies. [Learn how to fix duplicate ARIA IDs](https://dequeuniversity.com/rules/axe/4.10/duplicate-id-aria).", "score": null, "scoreDisplayMode": "notApplicable"}, "empty-heading": {"id": "empty-heading", "title": "All heading elements contain content.", "description": "A heading with no content or inaccessible text prevent screen reader users from accessing information on the page's structure. [Learn more about headings](https://dequeuniversity.com/rules/axe/4.10/empty-heading).", "score": null, "scoreDisplayMode": "notApplicable"}, "form-field-multiple-labels": {"id": "form-field-multiple-labels", "title": "No form fields have multiple labels", "description": "Form fields with multiple labels can be confusingly announced by assistive technologies like screen readers which use either the first, the last, or all of the labels. [Learn how to use form labels](https://dequeuniversity.com/rules/axe/4.10/form-field-multiple-labels).", "score": null, "scoreDisplayMode": "notApplicable"}, "frame-title": {"id": "frame-title", "title": "`<frame>` or `<iframe>` elements have a title", "description": "Screen reader users rely on frame titles to describe the contents of frames. [Learn more about frame titles](https://dequeuniversity.com/rules/axe/4.10/frame-title).", "score": null, "scoreDisplayMode": "notApplicable"}, "heading-order": {"id": "heading-order", "title": "Heading elements appear in a sequentially-descending order", "description": "Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "html-has-lang": {"id": "html-has-lang", "title": "`<html>` element has a `[lang]` attribute", "description": "If a page doesn't specify a `lang` attribute, a screen reader assumes that the page is in the default language that the user chose when setting up the screen reader. If the page isn't actually in the default language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-has-lang).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "html-lang-valid": {"id": "html-lang-valid", "title": "`<html>` element has a valid value for its `[lang]` attribute", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) helps screen readers announce text properly. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-lang-valid).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "html-xml-lang-mismatch": {"id": "html-xml-lang-mismatch", "title": "`<html>` element has an `[xml:lang]` attribute with the same base language as the `[lang]` attribute.", "description": "If the webpage does not specify a consistent language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-xml-lang-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "identical-links-same-purpose": {"id": "identical-links-same-purpose", "title": "Identical links have the same purpose.", "description": "Links with the same destination should have the same description, to help users understand the link's purpose and decide whether to follow it. [Learn more about identical links](https://dequeuniversity.com/rules/axe/4.10/identical-links-same-purpose).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-alt": {"id": "image-alt", "title": "Image elements have `[alt]` attributes", "description": "Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "image-redundant-alt": {"id": "image-redundant-alt", "title": "Image elements do not have `[alt]` attributes that are redundant text.", "description": "Informative elements should aim for short, descriptive alternative text. Alternative text that is exactly the same as the text adjacent to the link or image is potentially confusing for screen reader users, because the text will be read twice. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-redundant-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-button-name": {"id": "input-button-name", "title": "Input buttons have discernible text.", "description": "Adding discernable and accessible text to input buttons may help screen reader users understand the purpose of the input button. [Learn more about input buttons](https://dequeuniversity.com/rules/axe/4.10/input-button-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "input-image-alt": {"id": "input-image-alt", "title": "`<input type=\"image\">` elements have `[alt]` text", "description": "When an image is being used as an `<input>` button, providing alternative text can help screen reader users understand the purpose of the button. [Learn about input image alt text](https://dequeuniversity.com/rules/axe/4.10/input-image-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "label-content-name-mismatch": {"id": "label-content-name-mismatch", "title": "Elements with visible text labels have matching accessible names.", "description": "Visible text labels that do not match the accessible name can result in a confusing experience for screen reader users. [Learn more about accessible names](https://dequeuniversity.com/rules/axe/4.10/label-content-name-mismatch).", "score": null, "scoreDisplayMode": "notApplicable"}, "label": {"id": "label", "title": "Form elements have associated labels", "description": "Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "landmark-one-main": {"id": "landmark-one-main", "title": "Document has a main landmark.", "description": "One main landmark helps screen reader users navigate a web page. [Learn more about landmarks](https://dequeuniversity.com/rules/axe/4.10/landmark-one-main).", "score": null, "scoreDisplayMode": "notApplicable"}, "link-name": {"id": "link-name", "title": "Links have a discernible name", "description": "Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "link-in-text-block": {"id": "link-in-text-block", "title": "Links are distinguishable without relying on color.", "description": "Low-contrast text is difficult or impossible for many users to read. Link text that is discernible improves the experience for users with low vision. [Learn how to make links distinguishable](https://dequeuniversity.com/rules/axe/4.10/link-in-text-block).", "score": null, "scoreDisplayMode": "notApplicable"}, "list": {"id": "list", "title": "Lists contain only `<li>` elements and script supporting elements (`<script>` and `<template>`).", "description": "Screen readers have a specific way of announcing lists. Ensuring proper list structure aids screen reader output. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/list).", "score": null, "scoreDisplayMode": "notApplicable"}, "listitem": {"id": "listitem", "title": "List items (`<li>`) are contained within `<ul>`, `<ol>` or `<menu>` parent elements", "description": "Screen readers require list items (`<li>`) to be contained within a parent `<ul>`, `<ol>` or `<menu>` to be announced properly. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/listitem).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-refresh": {"id": "meta-refresh", "title": "The document does not use `<meta http-equiv=\"refresh\">`", "description": "Users do not expect a page to refresh automatically, and doing so will move focus back to the top of the page. This may create a frustrating or confusing experience. [Learn more about the refresh meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-refresh).", "score": null, "scoreDisplayMode": "notApplicable"}, "meta-viewport": {"id": "meta-viewport", "title": "`[user-scalable=\"no\"]` is not used in the `<meta name=\"viewport\">` element and the `[maximum-scale]` attribute is not less than 5.", "description": "Disabling zooming is problematic for users with low vision who rely on screen magnification to properly see the contents of a web page. [Learn more about the viewport meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-viewport).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "object-alt": {"id": "object-alt", "title": "`<object>` elements have alternate text", "description": "Screen readers cannot translate non-text content. Adding alternate text to `<object>` elements helps screen readers convey meaning to users. [Learn more about alt text for `object` elements](https://dequeuniversity.com/rules/axe/4.10/object-alt).", "score": null, "scoreDisplayMode": "notApplicable"}, "select-name": {"id": "select-name", "title": "Select elements have associated label elements.", "description": "Form elements without effective labels can create frustrating experiences for screen reader users. [Learn more about the `select` element](https://dequeuniversity.com/rules/axe/4.10/select-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "skip-link": {"id": "skip-link", "title": "Skip links are focusable.", "description": "Including a skip link can help users skip to the main content to save time. [Learn more about skip links](https://dequeuniversity.com/rules/axe/4.10/skip-link).", "score": null, "scoreDisplayMode": "notApplicable"}, "tabindex": {"id": "tabindex", "title": "No element has a `[tabindex]` value greater than 0", "description": "A value greater than 0 implies an explicit navigation ordering. Although technically valid, this often creates frustrating experiences for users who rely on assistive technologies. [Learn more about the `tabindex` attribute](https://dequeuniversity.com/rules/axe/4.10/tabindex).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "table-duplicate-name": {"id": "table-duplicate-name", "title": "Tables have different content in the summary attribute and `<caption>`.", "description": "The summary attribute should describe the table structure, while `<caption>` should have the onscreen title. Accurate table mark-up helps users of screen readers. [Learn more about summary and caption](https://dequeuniversity.com/rules/axe/4.10/table-duplicate-name).", "score": null, "scoreDisplayMode": "notApplicable"}, "table-fake-caption": {"id": "table-fake-caption", "title": "Tables use `<caption>` instead of cells with the `[colspan]` attribute to indicate a caption.", "description": "Screen readers have features to make navigating tables easier. Ensuring that tables use the actual caption element instead of cells with the `[colspan]` attribute may improve the experience for screen reader users. [Learn more about captions](https://dequeuniversity.com/rules/axe/4.10/table-fake-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "target-size": {"id": "target-size", "title": "Touch targets do not have sufficient size or spacing.", "description": "Touch targets with sufficient size and spacing help users who may have difficulty targeting small controls to activate the targets. [Learn more about touch targets](https://dequeuniversity.com/rules/axe/4.10/target-size).", "score": 0, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "relatedNode", "valueType": "node"}, "label": "Failing Elements"}], "items": [{"node": {"type": "node", "lhId": "1-0-BUTTON", "path": "1,HTML,1,BODY,5,DIV,1,DIV,0,FORM,1,DIV,1,DIV,2,BUTTON", "selector": "form.space-y-5 > div.space-y-2 > div.relative > button.absolute", "boundingRect": {"top": 420, "bottom": 464, "left": 360, "right": 376, "width": 16, "height": 44}, "snippet": "<button type=\"button\" class=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:te…\">", "nodeLabel": "form.space-y-5 > div.space-y-2 > div.relative > button.absolute", "explanation": "Fix any of the following:\n  Target has insufficient size (16px by 44px, should be at least 24px by 24px)\n  Target has insufficient space to its closest neighbors. Safe clickable space has a diameter of 16px instead of at least 24px."}, "subItems": {"type": "subitems", "items": [{"relatedNode": {"type": "node", "lhId": "1-3-INPUT", "path": "1,HTML,1,BODY,5,DIV,1,DIV,0,FORM,1,DIV,1,DIV,1,INPUT", "selector": "form.space-y-5 > div.space-y-2 > div.relative > input#password", "boundingRect": {"top": 418, "bottom": 466, "left": 24, "right": 388, "width": 364, "height": 48}, "snippet": "<input type=\"password\" class=\"flex w-full border bg-background px-3 py-2 text-base ring-offset-backgroun…\" id=\"password\" placeholder=\"Enter your password\" value=\"\">", "nodeLabel": "form.space-y-5 > div.space-y-2 > div.relative > input#password"}}]}}], "debugData": {"type": "debugdata", "impact": "serious", "tags": ["cat.sensory-and-visual-cues", "wcag22aa", "wcag258"]}}}, "td-has-header": {"id": "td-has-header", "title": "`<td>` elements in a large `<table>` have one or more table headers.", "description": "Screen readers have features to make navigating tables easier. Ensuring that `<td>` elements in a large table (3 or more cells in width and height) have an associated table header may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/td-has-header).", "score": null, "scoreDisplayMode": "notApplicable"}, "td-headers-attr": {"id": "td-headers-attr", "title": "Cells in a `<table>` element that use the `[headers]` attribute refer to table cells within the same table.", "description": "Screen readers have features to make navigating tables easier. Ensuring `<td>` cells using the `[headers]` attribute only refer to other cells in the same table may improve the experience for screen reader users. [Learn more about the `headers` attribute](https://dequeuniversity.com/rules/axe/4.10/td-headers-attr).", "score": null, "scoreDisplayMode": "notApplicable"}, "th-has-data-cells": {"id": "th-has-data-cells", "title": "`<th>` elements and elements with `[role=\"columnheader\"/\"rowheader\"]` have data cells they describe.", "description": "Screen readers have features to make navigating tables easier. Ensuring table headers always refer to some set of cells may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/th-has-data-cells).", "score": null, "scoreDisplayMode": "notApplicable"}, "valid-lang": {"id": "valid-lang", "title": "`[lang]` attributes have a valid value", "description": "Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) on elements helps ensure that text is pronounced correctly by a screen reader. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/valid-lang).", "score": null, "scoreDisplayMode": "notApplicable"}, "video-caption": {"id": "video-caption", "title": "`<video>` elements contain a `<track>` element with `[kind=\"captions\"]`", "description": "When a video provides a caption it is easier for deaf and hearing impaired users to access its information. [Learn more about video captions](https://dequeuniversity.com/rules/axe/4.10/video-caption).", "score": null, "scoreDisplayMode": "notApplicable"}, "custom-controls-labels": {"id": "custom-controls-labels", "title": "Custom controls have associated labels", "description": "Custom interactive controls have associated labels, provided by aria-label or aria-labelledby. [Learn more about custom controls and labels](https://developer.chrome.com/docs/lighthouse/accessibility/custom-controls-labels/).", "score": null, "scoreDisplayMode": "manual"}, "custom-controls-roles": {"id": "custom-controls-roles", "title": "Custom controls have ARIA roles", "description": "Custom interactive controls have appropriate ARIA roles. [Learn how to add roles to custom controls](https://developer.chrome.com/docs/lighthouse/accessibility/custom-control-roles/).", "score": null, "scoreDisplayMode": "manual"}, "focus-traps": {"id": "focus-traps", "title": "User focus is not accidentally trapped in a region", "description": "A user can tab into and out of any control or region without accidentally trapping their focus. [Learn how to avoid focus traps](https://developer.chrome.com/docs/lighthouse/accessibility/focus-traps/).", "score": null, "scoreDisplayMode": "manual"}, "focusable-controls": {"id": "focusable-controls", "title": "Interactive controls are keyboard focusable", "description": "Custom interactive controls are keyboard focusable and display a focus indicator. [Learn how to make custom controls focusable](https://developer.chrome.com/docs/lighthouse/accessibility/focusable-controls/).", "score": null, "scoreDisplayMode": "manual"}, "interactive-element-affordance": {"id": "interactive-element-affordance", "title": "Interactive elements indicate their purpose and state", "description": "Interactive elements, such as links and buttons, should indicate their state and be distinguishable from non-interactive elements. [Learn how to decorate interactive elements with affordance hints](https://developer.chrome.com/docs/lighthouse/accessibility/interactive-element-affordance/).", "score": null, "scoreDisplayMode": "manual"}, "logical-tab-order": {"id": "logical-tab-order", "title": "The page has a logical tab order", "description": "Tabbing through the page follows the visual layout. Users cannot focus elements that are offscreen. [Learn more about logical tab ordering](https://developer.chrome.com/docs/lighthouse/accessibility/logical-tab-order/).", "score": null, "scoreDisplayMode": "manual"}, "managed-focus": {"id": "managed-focus", "title": "The user's focus is directed to new content added to the page", "description": "If new content, such as a dialog, is added to the page, the user's focus is directed to it. [Learn how to direct focus to new content](https://developer.chrome.com/docs/lighthouse/accessibility/managed-focus/).", "score": null, "scoreDisplayMode": "manual"}, "offscreen-content-hidden": {"id": "offscreen-content-hidden", "title": "Offscreen content is hidden from assistive technology", "description": "Offscreen content is hidden with display: none or aria-hidden=true. [Learn how to properly hide offscreen content](https://developer.chrome.com/docs/lighthouse/accessibility/offscreen-content-hidden/).", "score": null, "scoreDisplayMode": "manual"}, "use-landmarks": {"id": "use-landmarks", "title": "HTML5 landmark elements are used to improve navigation", "description": "Landmark elements (`<main>`, `<nav>`, etc.) are used to improve the keyboard navigation of the page for assistive technology. [Learn more about landmark elements](https://developer.chrome.com/docs/lighthouse/accessibility/use-landmarks/).", "score": null, "scoreDisplayMode": "manual"}, "visual-order-follows-dom": {"id": "visual-order-follows-dom", "title": "Visual order on the page follows DOM order", "description": "DOM order matches the visual order, improving navigation for assistive technology. [Learn more about DOM and visual ordering](https://developer.chrome.com/docs/lighthouse/accessibility/visual-order-follows-dom/).", "score": null, "scoreDisplayMode": "manual"}, "uses-long-cache-ttl": {"id": "uses-long-cache-ttl", "title": "Serve static assets with an efficient cache policy", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 157.76, "numericUnit": "byte", "displayValue": "1 resource found", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "cacheLifetimeMs", "valueType": "ms", "label": "<PERSON><PERSON>", "displayUnit": "duration"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size", "displayUnit": "kb", "granularity": 1}], "items": [{"url": "https://cdn.gpteng.co/gptengineer.js", "debugData": {"type": "debugdata", "public": true, "max-age": 14400}, "cacheLifetimeMs": 14400000, "cacheHitProbability": 0.32, "totalBytes": 232, "wastedBytes": 157.76}], "summary": {"wastedBytes": 157.76}, "sortedBy": ["totalBytes"], "skipSumming": ["cacheLifetimeMs"]}, "guidanceLevel": 3}, "total-byte-weight": {"id": "total-byte-weight", "title": "Avoids enormous network payloads", "description": "Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 894763, "numericUnit": "byte", "displayValue": "Total size was 874 KiB", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "totalBytes": 805489}, {"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "totalBytes": 48981}, {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "totalBytes": 21531}, {"url": "http://localhost:4173/favicon.ico", "totalBytes": 15356}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "totalBytes": 1312}, {"url": "http://localhost:4173/auth", "totalBytes": 1125}, {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "totalBytes": 737}, {"url": "https://cdn.gpteng.co/gptengineer.js", "totalBytes": 232}], "sortedBy": ["totalBytes"]}, "guidanceLevel": 1}, "offscreen-images": {"id": "offscreen-images", "title": "Defer offscreen images", "description": "Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "render-blocking-resources": {"id": "render-blocking-resources", "title": "Eliminate render-blocking resources", "description": "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 1350, "numericUnit": "millisecond", "displayValue": "Est savings of 1,350 ms", "metricSavings": {"FCP": 1350, "LCP": 1350}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Est Savings"}], "items": [{"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "totalBytes": 737, "wastedMs": 281}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "totalBytes": 1312, "wastedMs": 1166}, {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "totalBytes": 21531, "wastedMs": 452}], "overallSavingsMs": 1350}, "guidanceLevel": 2}, "unminified-css": {"id": "unminified-css", "title": "Minify CSS", "description": "Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unminified-javascript": {"id": "unminified-javascript", "title": "Minify JavaScript", "description": "Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unused-css-rules": {"id": "unused-css-rules", "title": "Reduce unused CSS", "description": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 150, "numericUnit": "millisecond", "displayValue": "Est savings of 19 KiB", "metricSavings": {"FCP": 150, "LCP": 150}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "wastedBytes": 19471, "wastedPercent": 91.9175209208641, "totalBytes": 21183}], "overallSavingsMs": 150, "overallSavingsBytes": 19471, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 150, "LCP": 150}}}, "guidanceLevel": 1}, "unused-javascript": {"id": "unused-javascript", "title": "Reduce unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 2760, "numericUnit": "millisecond", "displayValue": "Est savings of 617 KiB", "metricSavings": {"FCP": 2750, "LCP": 2750}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "source", "valueType": "code"}, "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceBytes"}, "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceWastedBytes"}, "label": "Est Savings"}], "items": [{"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "totalBytes": 804626, "wastedBytes": 631629, "wastedPercent": 78.49972568414914}], "overallSavingsMs": 2760, "overallSavingsBytes": 631629, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 2760, "LCP": 2760}}}, "guidanceLevel": 1}, "modern-image-formats": {"id": "modern-image-formats", "title": "Serve images in next-gen formats", "description": "Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-optimized-images": {"id": "uses-optimized-images", "title": "Efficiently encode images", "description": "Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "uses-text-compression": {"id": "uses-text-compression", "title": "Enable text compression", "description": "Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-responsive-images": {"id": "uses-responsive-images", "title": "Properly size images", "description": "Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "efficient-animated-content": {"id": "efficient-animated-content", "title": "Use video formats for animated content", "description": "Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "duplicated-javascript": {"id": "duplicated-javascript", "title": "Remove duplicate modules in JavaScript bundles", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "legacy-javascript": {"id": "legacy-javascript", "title": "Avoid serving legacy JavaScript to modern browsers", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/baseline) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "Est savings of 1 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "wastedBytes": 586, "subItems": {"type": "subitems", "items": [{"signal": "@babel/plugin-transform-classes", "location": {"type": "source-location", "url": "http://localhost:4173/assets/index-CGQEF-7R.js", "urlProvider": "network", "line": 801, "column": 38571}}, {"signal": "@babel/plugin-transform-spread", "location": {"type": "source-location", "url": "http://localhost:4173/assets/index-CGQEF-7R.js", "urlProvider": "network", "line": 812, "column": 65526}}]}, "totalBytes": 0}], "overallSavingsMs": 0, "overallSavingsBytes": 586, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "doctype": {"id": "doctype", "title": "<PERSON> has the HTML doctype", "description": "Specifying a doctype prevents the browser from switching to quirks-mode. [Learn more about the doctype declaration](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/).", "score": 1, "scoreDisplayMode": "binary"}, "charset": {"id": "charset", "title": "<PERSON><PERSON><PERSON> defines charset", "description": "A character encoding declaration is required. It can be done with a `<meta>` tag in the first 1024 bytes of the HTML or in the Content-Type HTTP response header. [Learn more about declaring the character encoding](https://developer.chrome.com/docs/lighthouse/best-practices/charset/).", "score": 1, "scoreDisplayMode": "binary"}, "dom-size": {"id": "dom-size", "title": "Avoids an excessive DOM size", "description": "A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 50, "numericUnit": "element", "displayValue": "50 elements", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total DOM Elements", "value": {"type": "numeric", "granularity": 1, "value": 50}}, {"node": {"type": "node", "lhId": "1-7-path", "path": "1,HTML,1,BODY,5,DIV,1,DIV,0,FORM,1,DIV,1,DIV,2,BUTTON,0,svg,0,path", "selector": "div.relative > button.absolute > svg.lucide > path", "boundingRect": {"top": 437, "bottom": 446, "left": 361, "right": 375, "width": 13, "height": 9}, "snippet": "<path d=\"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696…\">", "nodeLabel": "div.relative > button.absolute > svg.lucide > path"}, "statistic": "Maximum DOM Depth", "value": {"type": "numeric", "granularity": 1, "value": 9}}, {"node": {"type": "node", "lhId": "1-8-BODY", "path": "1,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>", "selector": "body", "boundingRect": {"top": 0, "bottom": 823, "left": 0, "right": 412, "width": 412, "height": 823}, "snippet": "<body data-scroll-locked=\"1\" style=\"pointer-events: none;\">", "nodeLabel": "body"}, "statistic": "Maximum Child Elements", "value": {"type": "numeric", "granularity": 1, "value": 6}}]}, "guidanceLevel": 1}, "geolocation-on-start": {"id": "geolocation-on-start", "title": "Avoids requesting the geolocation permission on page load", "description": "Users are mistrustful of or confused by sites that request their location without context. Consider tying the request to a user action instead. [Learn more about the geolocation permission](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "inspector-issues": {"id": "inspector-issues", "title": "No issues in the `Issues` panel in Chrome Devtools", "description": "Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "no-document-write": {"id": "no-document-write", "title": "Avoids `document.write()`", "description": "For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "js-libraries": {"id": "js-libraries", "title": "Detected JavaScript libraries", "description": "All front-end JavaScript libraries detected on the page. [Learn more about this JavaScript library detection diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/).", "score": null, "scoreDisplayMode": "notApplicable"}, "notification-on-start": {"id": "notification-on-start", "title": "Avoids requesting the notification permission on page load", "description": "Users are mistrustful of or confused by sites that request to send notifications without context. Consider tying the request to user gestures instead. [Learn more about responsibly getting permission for notifications](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "paste-preventing-inputs": {"id": "paste-preventing-inputs", "title": "Allows users to paste into input fields", "description": "Preventing input pasting is a bad practice for the UX, and weakens security by blocking password managers.[Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "uses-http2": {"id": "uses-http2", "title": "Use HTTP/2", "description": "HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 3}, "uses-passive-event-listeners": {"id": "uses-passive-event-listeners", "title": "Uses passive listeners to improve scrolling performance", "description": "Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "meta-description": {"id": "meta-description", "title": "Document has a meta description", "description": "Meta descriptions may be included in search results to concisely summarize page content. [Learn more about the meta description](https://developer.chrome.com/docs/lighthouse/seo/meta-description/).", "score": 1, "scoreDisplayMode": "binary"}, "http-status-code": {"id": "http-status-code", "title": "Page has successful HTTP status code", "description": "Pages with unsuccessful HTTP status codes may not be indexed properly. [Learn more about HTTP status codes](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/).", "score": 1, "scoreDisplayMode": "binary"}, "font-size": {"id": "font-size", "title": "Document uses legible font sizes", "description": "Font sizes less than 12px are too small to be legible and require mobile visitors to “pinch to zoom” in order to read. Strive to have >60% of page text ≥12px. [Learn more about legible font sizes](https://developer.chrome.com/docs/lighthouse/seo/font-size/).", "score": 1, "scoreDisplayMode": "binary", "displayValue": "100% legible text", "details": {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}, {"key": "selector", "valueType": "code", "label": "Selector"}, {"key": "coverage", "valueType": "text", "label": "% of Page Text"}, {"key": "fontSize", "valueType": "text", "label": "Font Size"}], "items": [{"source": {"type": "code", "value": "Legible text"}, "selector": "", "coverage": "100.00%", "fontSize": "≥ 12px"}]}}, "link-text": {"id": "link-text", "title": "Links have descriptive text", "description": "Descriptive link text helps search engines understand your content. [Learn how to make links more accessible](https://developer.chrome.com/docs/lighthouse/seo/link-text/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "crawlable-anchors": {"id": "crawlable-anchors", "title": "Links are crawlable", "description": "Search engines may use `href` attributes on links to crawl websites. Ensure that the `href` attribute of anchor elements links to an appropriate destination, so more pages of the site can be discovered. [Learn how to make links crawlable](https://support.google.com/webmasters/answer/9112205)", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "is-crawlable": {"id": "is-crawlable", "title": "Page isn’t blocked from indexing", "description": "Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).", "score": 1, "scoreDisplayMode": "binary", "warnings": [], "details": {"type": "table", "headings": [], "items": []}}, "robots-txt": {"id": "robots-txt", "title": "robots.txt is not valid", "description": "If your robots.txt file is malformed, crawlers may not be able to understand how you want your website to be crawled or indexed. [Learn more about robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/).", "score": 0, "scoreDisplayMode": "binary", "displayValue": "18 errors found", "details": {"type": "table", "headings": [{"key": "index", "valueType": "text", "label": "Line #"}, {"key": "line", "valueType": "code", "label": "Content"}, {"key": "message", "valueType": "code", "label": "Error"}], "items": [{"index": "2", "line": "<!DOCTYPE html>", "message": "Syntax not understood"}, {"index": "3", "line": "<html lang=\"en\">", "message": "Syntax not understood"}, {"index": "4", "line": "  <head>", "message": "Syntax not understood"}, {"index": "5", "line": "    <meta charset=\"UTF-8\" />", "message": "Syntax not understood"}, {"index": "6", "line": "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />", "message": "Syntax not understood"}, {"index": "7", "line": "    <title>PHCityRent - Port Harcourt's Trusted Rental Platform</title>", "message": "Syntax not understood"}, {"index": "8", "line": "    <meta name=\"description\" content=\"Find verified properties and agents in Port Harcourt. Secure rent payments, avoid scams, and rent safely with PHCityRent.\" />", "message": "Syntax not understood"}, {"index": "9", "line": "    <meta name=\"author\" content=\"PHCityRent Team\" />", "message": "Syntax not understood"}, {"index": "10", "line": "    <meta property=\"og:image\" content=\"/og-image-update.png\" />", "message": "Unknown directive"}, {"index": "11", "line": "    <script type=\"module\" crossorigin src=\"/assets/index-CGQEF-7R.js\"></script>", "message": "Syntax not understood"}, {"index": "12", "line": "    <link rel=\"stylesheet\" crossorigin href=\"/assets/index-AM9p-ZF3.css\">", "message": "Syntax not understood"}, {"index": "13", "line": "  </head>", "message": "Syntax not understood"}, {"index": "15", "line": "  <body>", "message": "Syntax not understood"}, {"index": "16", "line": "    <div id=\"root\"></div>", "message": "Syntax not understood"}, {"index": "17", "line": "    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->", "message": "Unknown directive"}, {"index": "18", "line": "    <script src=\"https://cdn.gpteng.co/gptengineer.js\" type=\"module\"></script>", "message": "Unknown directive"}, {"index": "19", "line": "  </body>", "message": "Syntax not understood"}, {"index": "20", "line": "</html>", "message": "Syntax not understood"}]}}, "hreflang": {"id": "hreflang", "title": "Document has a valid `hreflang`", "description": "hreflang links tell search engines what version of a page they should list in search results for a given language or region. [Learn more about `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/).", "score": 1, "scoreDisplayMode": "binary", "details": {"type": "table", "headings": [], "items": []}}, "canonical": {"id": "canonical", "title": "Document has a valid `rel=canonical`", "description": "Canonical links suggest which URL to show in search results. [Learn more about canonical links](https://developer.chrome.com/docs/lighthouse/seo/canonical/).", "score": null, "scoreDisplayMode": "notApplicable"}, "structured-data": {"id": "structured-data", "title": "Structured data is valid", "description": "Run the [Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool/) and the [Structured Data Linter](http://linter.structured-data.org/) to validate structured data. [Learn more about Structured Data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/).", "score": null, "scoreDisplayMode": "manual"}, "bf-cache": {"id": "bf-cache", "title": "Page didn't prevent back/forward cache restoration", "description": "Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)", "score": 1, "scoreDisplayMode": "binary", "guidanceLevel": 4}, "cache-insight": {"id": "cache-insight", "title": "Use efficient cache lifetimes", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more](https://web.dev/uses-long-cache-ttl/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "displayValue": "Est savings of 0 KiB", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "Request"}, {"key": "cacheLifetimeMs", "valueType": "ms", "label": "<PERSON><PERSON>", "displayUnit": "duration"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Transfer Size", "displayUnit": "kb", "granularity": 1}], "items": [{"url": "https://cdn.gpteng.co/gptengineer.js", "cacheLifetimeMs": 14400000, "wastedBytes": 157.76}], "sortedBy": ["wastedBytes"], "skipSumming": ["cacheLifetimeMs"], "debugData": {"type": "debugdata", "wastedBytes": 157.76}}, "guidanceLevel": 3, "replacesAudits": ["uses-long-cache-ttl"]}, "cls-culprits-insight": {"id": "cls-culprits-insight", "title": "Layout shift culprits", "description": "Layout shifts occur when elements move absent any user interaction. [Investigate the causes of layout shifts](https://web.dev/articles/optimize-cls), such as elements being added, removed, or their fonts changing as the page loads.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"CLS": 0}, "details": {"type": "list", "items": []}, "guidanceLevel": 3, "replacesAudits": ["layout-shifts", "non-composited-animations", "unsized-images"]}, "document-latency-insight": {"id": "document-latency-insight", "title": "Document request latency", "description": "Your first network request is the most important.  Reduce its latency by avoiding redirects, ensuring a fast server response, and enabling text compression.", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "checklist", "items": {"noRedirects": {"label": "Avoids redirects", "value": true}, "serverResponseIsFast": {"label": "Server responds quickly (observed 1 ms) ", "value": true}, "usesCompression": {"label": "Applies text compression", "value": true}}, "debugData": {"type": "debugdata", "redirectDuration": 0, "serverResponseTime": 1, "uncompressedResponseBytes": 0, "wastedBytes": 0}}, "guidanceLevel": 3, "replacesAudits": ["redirects", "server-response-time", "uses-text-compression"]}, "dom-size-insight": {"id": "dom-size-insight", "title": "Optimize DOM size", "description": "A large DOM can increase the duration of style calculations and layout reflows, impacting page responsiveness. A large DOM will also increase memory usage. [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total elements", "value": {"type": "numeric", "granularity": 1, "value": 52}}, {"statistic": "Most children", "node": {"type": "node", "lhId": "page-3-BODY", "path": "1,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>", "selector": "body", "boundingRect": {"top": 0, "bottom": 823, "left": 0, "right": 412, "width": 412, "height": 823}, "snippet": "<body data-scroll-locked=\"1\" style=\"pointer-events: none;\">", "nodeLabel": "body"}, "value": {"type": "numeric", "granularity": 1, "value": 6}}, {"statistic": "DOM depth", "node": {"type": "node", "lhId": "page-4-path", "path": "1,HTML,1,BODY,5,DIV,1,DIV,0,FORM,1,DIV,1,DIV,2,BUTTON,0,svg,0,path", "selector": "div.relative > button.absolute > svg.lucide > path", "boundingRect": {"top": 437, "bottom": 446, "left": 361, "right": 375, "width": 13, "height": 9}, "snippet": "<path d=\"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696…\">", "nodeLabel": "div.relative > button.absolute > svg.lucide > path"}, "value": {"type": "numeric", "granularity": 1, "value": 9}}], "debugData": {"type": "debugdata", "totalElements": 52, "maxChildren": 6, "maxDepth": 9}}, "guidanceLevel": 3, "replacesAudits": ["dom-size"]}, "duplicated-javascript-insight": {"id": "duplicated-javascript-insight", "title": "Duplicated JavaScript", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 2, "replacesAudits": ["duplicated-javascript"]}, "font-display-insight": {"id": "font-display-insight", "title": "Font display", "description": "Consider setting [font-display](https://developer.chrome.com/blog/font-display) to swap or optional to ensure text is consistently visible. swap can be further optimized to mitigate layout shifts with [font metric overrides](https://developer.chrome.com/blog/font-fallbacks).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["font-display"]}, "forced-reflow-insight": {"id": "forced-reflow-insight", "title": "Forced reflow", "description": "Many APIs, typically reading layout geometry, force the rendering engine to pause script execution in order to calculate the style and layout. Learn more about [forced reflow](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) and its mitigations.", "score": 1, "scoreDisplayMode": "numeric", "details": {"type": "list", "items": [{"type": "table", "headings": [], "items": []}]}, "guidanceLevel": 3}, "image-delivery-insight": {"id": "image-delivery-insight", "title": "Improve image delivery", "description": "Reducing the download time of images can improve the perceived load time of the page and LCP. [Learn more about optimizing image size](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["modern-image-formats", "uses-optimized-images", "efficient-animated-content", "uses-responsive-images"]}, "interaction-to-next-paint-insight": {"id": "interaction-to-next-paint-insight", "title": "INP by phase", "description": "Start investigating with the longest phase. [Delays can be minimized](https://web.dev/articles/optimize-inp#optimize_interactions). To reduce processing duration, [optimize the main-thread costs](https://web.dev/articles/optimize-long-tasks), often JS.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["work-during-interaction"]}, "lcp-discovery-insight": {"id": "lcp-discovery-insight", "title": "LCP request discovery", "description": "Optimize LCP by making the LCP image [discoverable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) from the HTML immediately, and [avoiding lazy-loading](https://web.dev/articles/lcp-lazy-loading)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["prioritize-lcp-image", "lcp-lazy-loaded"]}, "lcp-phases-insight": {"id": "lcp-phases-insight", "title": "LCP by phase", "description": "Each [phase has specific improvement strategies](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideally, most of the LCP time should be spent on loading the resources, not within delays.", "score": 1, "scoreDisplayMode": "informative", "metricSavings": {"LCP": 0}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Phase"}, {"key": "duration", "valueType": "ms", "label": "Duration"}], "items": [{"phase": "timeToFirstByte", "label": "Time to first byte", "duration": 2.011}, {"phase": "elementRenderDelay", "label": "Element render delay", "duration": 1432.684}]}, {"type": "node", "lhId": "page-0-P", "path": "1,HTML,1,BODY,5,DIV,0,DIV,0,DIV,0,DIV,1,P", "selector": "div.relative > div.flex > div > p.text-white/90", "boundingRect": {"top": 218, "bottom": 238, "left": 24, "right": 388, "width": 364, "height": 20}, "snippet": "<p class=\"text-white/90 text-sm\">", "nodeLabel": "Sign in to access your account and saved properties"}]}, "guidanceLevel": 3, "replacesAudits": ["largest-contentful-paint-element"]}, "legacy-javascript-insight": {"id": "legacy-javascript-insight", "title": "Legacy JavaScript", "description": "Polyfills and transforms enable older browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/articles/baseline-and-polyfills) features, unless you know you must support older browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 2}, "modern-http-insight": {"id": "modern-http-insight", "title": "Modern HTTP", "description": "HTTP/2 and HTTP/3 offer many benefits over HTTP/1.1, such as multiplexing. [Learn more about using modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3}, "network-dependency-tree-insight": {"id": "network-dependency-tree-insight", "title": "Network dependency tree", "description": "[Avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.", "score": 0, "scoreDisplayMode": "numeric", "metricSavings": {"LCP": 0}, "details": {"type": "network-tree", "chains": {"F097DAD5E2262BEDD15C4B0EB4A37B36": {"url": "http://localhost:4173/auth", "navStartToEndTime": 7, "transferSize": 1125, "isLongest": true, "children": {"45261.2": {"url": "http://localhost:4173/assets/index-CGQEF-7R.js", "navStartToEndTime": 84, "transferSize": 805489, "children": {}}, "45261.3": {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "navStartToEndTime": 14, "transferSize": 21531, "isLongest": true, "children": {"45261.8": {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "navStartToEndTime": 462, "transferSize": 1312, "isLongest": true, "children": {"45261.16": {"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "navStartToEndTime": 1187, "transferSize": 48981, "isLongest": true, "children": {}}}}, "45261.9": {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "navStartToEndTime": 462, "transferSize": 737, "children": {}}}}, "45261.4": {"url": "https://cdn.gpteng.co/gptengineer.js", "navStartToEndTime": 461, "transferSize": 232, "children": {}}}}}, "longestChain": {"duration": 1187}}, "guidanceLevel": 1, "replacesAudits": ["critical-request-chains"]}, "render-blocking-insight": {"id": "render-blocking-insight", "title": "Render blocking requests", "description": "Requests are blocking the page's initial render, which may delay LCP. [Deferring or inlining](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources) can move these network requests out of the critical path.", "score": 0, "scoreDisplayMode": "metricSavings", "metricSavings": {"FCP": 1350, "LCP": 1350}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Est Savings"}], "items": [{"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "totalBytes": 737, "wastedMs": 281}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "totalBytes": 1312, "wastedMs": 1166}, {"url": "http://localhost:4173/assets/index-AM9p-ZF3.css", "totalBytes": 21531, "wastedMs": 452}]}, "guidanceLevel": 3, "replacesAudits": ["render-blocking-resources"]}, "third-parties-insight": {"id": "third-parties-insight", "title": "3rd parties", "description": "3rd party code can significantly impact load performance. [Reduce and defer loading of 3rd party code](https://web.dev/articles/optimizing-content-efficiency-loading-third-party-javascript/) to prioritize your page's content.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "entity", "valueType": "text", "label": "3rd party", "subItemsHeading": {"key": "url", "valueType": "url"}}, {"key": "transferSize", "granularity": 1, "valueType": "bytes", "label": "Transfer size", "subItemsHeading": {"key": "transferSize"}}, {"key": "mainThreadTime", "granularity": 1, "valueType": "ms", "label": "Main thread time", "subItemsHeading": {"key": "mainThreadTime"}}], "items": [{"entity": "gpteng.co", "mainThreadTime": 0, "transferSize": 232, "subItems": {"type": "subitems", "items": [{"url": "https://cdn.gpteng.co/gptengineer.js", "mainThreadTime": 0, "transferSize": 232}]}}, {"entity": "Google Fonts", "mainThreadTime": 0, "transferSize": 51030, "subItems": {"type": "subitems", "items": [{"url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2", "mainThreadTime": 0, "transferSize": 48981}, {"url": "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap", "mainThreadTime": 0, "transferSize": 1312}, {"url": "https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap", "mainThreadTime": 0, "transferSize": 737}]}}], "isEntityGrouped": true}, "guidanceLevel": 3, "replacesAudits": ["third-party-summary"]}, "viewport-insight": {"id": "viewport-insight", "title": "Optimize viewport for mobile", "description": "Tap interactions may be [delayed by up to 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) if the viewport is not optimized for mobile.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}], "items": [{"node": {"type": "node", "lhId": "page-2-META", "path": "1,H<PERSON><PERSON>,0,HEAD,1,<PERSON><PERSON>", "selector": "head > meta", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">", "nodeLabel": "head > meta"}}]}, "guidanceLevel": 3, "replacesAudits": ["viewport"]}}, "configSettings": {"output": ["json"], "maxWaitForFcp": 30000, "maxWaitForLoad": 45000, "pauseAfterFcpMs": 1000, "pauseAfterLoadMs": 1000, "networkQuietThresholdMs": 1000, "cpuQuietThresholdMs": 1000, "formFactor": "mobile", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "requestLatencyMs": 562.5, "downloadThroughputKbps": 1474.5600000000002, "uploadThroughputKbps": 675, "cpuSlowdownMultiplier": 4}, "throttlingMethod": "simulate", "screenEmulation": {"mobile": true, "width": 412, "height": 823, "deviceScaleFactor": 1.75, "disabled": false}, "emulatedUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "auditMode": false, "gatherMode": false, "clearStorageTypes": ["file_systems", "shader_cache", "service_workers", "cache_storage"], "disableStorageReset": false, "debugNavigation": false, "channel": "cli", "usePassiveGathering": false, "disableFullPageScreenshot": false, "skipAboutBlank": false, "blankPage": "about:blank", "ignoreStatusCode": false, "locale": "en-US", "blockedUrlPatterns": null, "additionalTraceCategories": null, "extraHeaders": null, "precomputedLanternData": null, "onlyAudits": null, "onlyCategories": null, "skipAudits": null}, "categories": {"performance": {"title": "Performance", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "first-contentful-paint", "weight": 10, "group": "metrics", "acronym": "FCP"}, {"id": "largest-contentful-paint", "weight": 25, "group": "metrics", "acronym": "LCP"}, {"id": "total-blocking-time", "weight": 30, "group": "metrics", "acronym": "TBT"}, {"id": "cumulative-layout-shift", "weight": 25, "group": "metrics", "acronym": "CLS"}, {"id": "speed-index", "weight": 10, "group": "metrics", "acronym": "SI"}, {"id": "cache-insight", "weight": 0, "group": "hidden"}, {"id": "cls-culprits-insight", "weight": 0, "group": "hidden"}, {"id": "document-latency-insight", "weight": 0, "group": "hidden"}, {"id": "dom-size-insight", "weight": 0, "group": "hidden"}, {"id": "duplicated-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "font-display-insight", "weight": 0, "group": "hidden"}, {"id": "forced-reflow-insight", "weight": 0, "group": "hidden"}, {"id": "image-delivery-insight", "weight": 0, "group": "hidden"}, {"id": "interaction-to-next-paint-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-discovery-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-phases-insight", "weight": 0, "group": "hidden"}, {"id": "legacy-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "modern-http-insight", "weight": 0, "group": "hidden"}, {"id": "network-dependency-tree-insight", "weight": 0, "group": "hidden"}, {"id": "render-blocking-insight", "weight": 0, "group": "hidden"}, {"id": "third-parties-insight", "weight": 0, "group": "hidden"}, {"id": "viewport-insight", "weight": 0, "group": "hidden"}, {"id": "interactive", "weight": 0, "group": "hidden", "acronym": "TTI"}, {"id": "max-potential-fid", "weight": 0, "group": "hidden"}, {"id": "first-meaningful-paint", "weight": 0, "acronym": "FMP", "group": "hidden"}, {"id": "render-blocking-resources", "weight": 0, "group": "diagnostics"}, {"id": "uses-responsive-images", "weight": 0, "group": "diagnostics"}, {"id": "offscreen-images", "weight": 0, "group": "diagnostics"}, {"id": "unminified-css", "weight": 0, "group": "diagnostics"}, {"id": "unminified-javascript", "weight": 0, "group": "diagnostics"}, {"id": "unused-css-rules", "weight": 0, "group": "diagnostics"}, {"id": "unused-javascript", "weight": 0, "group": "diagnostics"}, {"id": "uses-optimized-images", "weight": 0, "group": "diagnostics"}, {"id": "modern-image-formats", "weight": 0, "group": "diagnostics"}, {"id": "uses-text-compression", "weight": 0, "group": "diagnostics"}, {"id": "uses-rel-preconnect", "weight": 0, "group": "diagnostics"}, {"id": "server-response-time", "weight": 0, "group": "diagnostics"}, {"id": "redirects", "weight": 0, "group": "diagnostics"}, {"id": "uses-http2", "weight": 0, "group": "diagnostics"}, {"id": "efficient-animated-content", "weight": 0, "group": "diagnostics"}, {"id": "duplicated-javascript", "weight": 0, "group": "diagnostics"}, {"id": "legacy-javascript", "weight": 0, "group": "diagnostics"}, {"id": "prioritize-lcp-image", "weight": 0, "group": "diagnostics"}, {"id": "total-byte-weight", "weight": 0, "group": "diagnostics"}, {"id": "uses-long-cache-ttl", "weight": 0, "group": "diagnostics"}, {"id": "dom-size", "weight": 0, "group": "diagnostics"}, {"id": "critical-request-chains", "weight": 0, "group": "diagnostics"}, {"id": "user-timings", "weight": 0, "group": "diagnostics"}, {"id": "bootup-time", "weight": 0, "group": "diagnostics"}, {"id": "mainthread-work-breakdown", "weight": 0, "group": "diagnostics"}, {"id": "font-display", "weight": 0, "group": "diagnostics"}, {"id": "third-party-summary", "weight": 0, "group": "diagnostics"}, {"id": "third-party-facades", "weight": 0, "group": "diagnostics"}, {"id": "largest-contentful-paint-element", "weight": 0, "group": "diagnostics"}, {"id": "lcp-lazy-loaded", "weight": 0, "group": "diagnostics"}, {"id": "layout-shifts", "weight": 0, "group": "diagnostics"}, {"id": "uses-passive-event-listeners", "weight": 0, "group": "diagnostics"}, {"id": "no-document-write", "weight": 0, "group": "diagnostics"}, {"id": "long-tasks", "weight": 0, "group": "diagnostics"}, {"id": "non-composited-animations", "weight": 0, "group": "diagnostics"}, {"id": "unsized-images", "weight": 0, "group": "diagnostics"}, {"id": "viewport", "weight": 0, "group": "diagnostics"}, {"id": "bf-cache", "weight": 0, "group": "diagnostics"}, {"id": "network-requests", "weight": 0, "group": "hidden"}, {"id": "network-rtt", "weight": 0, "group": "hidden"}, {"id": "network-server-latency", "weight": 0, "group": "hidden"}, {"id": "main-thread-tasks", "weight": 0, "group": "hidden"}, {"id": "diagnostics", "weight": 0, "group": "hidden"}, {"id": "metrics", "weight": 0, "group": "hidden"}, {"id": "screenshot-thumbnails", "weight": 0, "group": "hidden"}, {"id": "final-screenshot", "weight": 0, "group": "hidden"}, {"id": "script-treemap-data", "weight": 0, "group": "hidden"}, {"id": "resource-summary", "weight": 0, "group": "hidden"}], "id": "performance", "score": 0.61}, "accessibility": {"title": "Accessibility", "description": "These checks highlight opportunities to [improve the accessibility of your web app](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatic detection can only detect a subset of issues and does not guarantee the accessibility of your web app, so [manual testing](https://web.dev/articles/how-to-review) is also encouraged.", "manualDescription": "These items address areas which an automated testing tool cannot cover. Learn more in our guide on [conducting an accessibility review](https://web.dev/articles/how-to-review).", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "accesskeys", "weight": 0, "group": "a11y-navigation"}, {"id": "aria-allowed-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-allowed-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-command-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-conditional-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-deprecated-role", "weight": 1, "group": "a11y-aria"}, {"id": "aria-dialog-name", "weight": 7, "group": "a11y-aria"}, {"id": "aria-hidden-body", "weight": 10, "group": "a11y-aria"}, {"id": "aria-hidden-focus", "weight": 7, "group": "a11y-aria"}, {"id": "aria-input-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-meter-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-progressbar-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-prohibited-attr", "weight": 7, "group": "a11y-aria"}, {"id": "aria-required-attr", "weight": 10, "group": "a11y-aria"}, {"id": "aria-required-children", "weight": 0, "group": "a11y-aria"}, {"id": "aria-required-parent", "weight": 0, "group": "a11y-aria"}, {"id": "aria-roles", "weight": 7, "group": "a11y-aria"}, {"id": "aria-text", "weight": 0, "group": "a11y-aria"}, {"id": "aria-toggle-field-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-tooltip-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-treeitem-name", "weight": 0, "group": "a11y-aria"}, {"id": "aria-valid-attr-value", "weight": 10, "group": "a11y-aria"}, {"id": "aria-valid-attr", "weight": 10, "group": "a11y-aria"}, {"id": "button-name", "weight": 10, "group": "a11y-names-labels"}, {"id": "bypass", "weight": 0, "group": "a11y-navigation"}, {"id": "color-contrast", "weight": 7, "group": "a11y-color-contrast"}, {"id": "definition-list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "dlitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "document-title", "weight": 7, "group": "a11y-names-labels"}, {"id": "duplicate-id-aria", "weight": 0, "group": "a11y-aria"}, {"id": "form-field-multiple-labels", "weight": 0, "group": "a11y-names-labels"}, {"id": "frame-title", "weight": 0, "group": "a11y-names-labels"}, {"id": "heading-order", "weight": 3, "group": "a11y-navigation"}, {"id": "html-has-lang", "weight": 7, "group": "a11y-language"}, {"id": "html-lang-valid", "weight": 7, "group": "a11y-language"}, {"id": "html-xml-lang-mismatch", "weight": 0, "group": "a11y-language"}, {"id": "image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "image-redundant-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-button-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "input-image-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "label", "weight": 7, "group": "a11y-names-labels"}, {"id": "link-in-text-block", "weight": 0, "group": "a11y-color-contrast"}, {"id": "link-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "list", "weight": 0, "group": "a11y-tables-lists"}, {"id": "listitem", "weight": 0, "group": "a11y-tables-lists"}, {"id": "meta-refresh", "weight": 0, "group": "a11y-best-practices"}, {"id": "meta-viewport", "weight": 10, "group": "a11y-best-practices"}, {"id": "object-alt", "weight": 0, "group": "a11y-names-labels"}, {"id": "select-name", "weight": 0, "group": "a11y-names-labels"}, {"id": "skip-link", "weight": 0, "group": "a11y-names-labels"}, {"id": "tabindex", "weight": 7, "group": "a11y-navigation"}, {"id": "table-duplicate-name", "weight": 0, "group": "a11y-tables-lists"}, {"id": "target-size", "weight": 7, "group": "a11y-best-practices"}, {"id": "td-headers-attr", "weight": 0, "group": "a11y-tables-lists"}, {"id": "th-has-data-cells", "weight": 0, "group": "a11y-tables-lists"}, {"id": "valid-lang", "weight": 0, "group": "a11y-language"}, {"id": "video-caption", "weight": 0, "group": "a11y-audio-video"}, {"id": "focusable-controls", "weight": 0}, {"id": "interactive-element-affordance", "weight": 0}, {"id": "logical-tab-order", "weight": 0}, {"id": "visual-order-follows-dom", "weight": 0}, {"id": "focus-traps", "weight": 0}, {"id": "managed-focus", "weight": 0}, {"id": "use-landmarks", "weight": 0}, {"id": "offscreen-content-hidden", "weight": 0}, {"id": "custom-controls-labels", "weight": 0}, {"id": "custom-controls-roles", "weight": 0}, {"id": "empty-heading", "weight": 0, "group": "hidden"}, {"id": "identical-links-same-purpose", "weight": 0, "group": "hidden"}, {"id": "landmark-one-main", "weight": 0, "group": "hidden"}, {"id": "label-content-name-mismatch", "weight": 0, "group": "hidden"}, {"id": "table-fake-caption", "weight": 0, "group": "hidden"}, {"id": "td-has-header", "weight": 0, "group": "hidden"}], "id": "accessibility", "score": 0.85}, "best-practices": {"title": "Best Practices", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "is-on-https", "weight": 5, "group": "best-practices-trust-safety"}, {"id": "redirects-http", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "geolocation-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "notification-on-start", "weight": 1, "group": "best-practices-trust-safety"}, {"id": "csp-xss", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "has-hsts", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "origin-isolation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "clickjacking-mitigation", "weight": 0, "group": "best-practices-trust-safety"}, {"id": "paste-preventing-inputs", "weight": 3, "group": "best-practices-ux"}, {"id": "image-aspect-ratio", "weight": 1, "group": "best-practices-ux"}, {"id": "image-size-responsive", "weight": 1, "group": "best-practices-ux"}, {"id": "viewport", "weight": 1, "group": "best-practices-ux"}, {"id": "font-size", "weight": 1, "group": "best-practices-ux"}, {"id": "doctype", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "charset", "weight": 1, "group": "best-practices-browser-compat"}, {"id": "js-libraries", "weight": 0, "group": "best-practices-general"}, {"id": "deprecations", "weight": 5, "group": "best-practices-general"}, {"id": "third-party-cookies", "weight": 5, "group": "best-practices-general"}, {"id": "errors-in-console", "weight": 1, "group": "best-practices-general"}, {"id": "valid-source-maps", "weight": 0, "group": "best-practices-general"}, {"id": "inspector-issues", "weight": 1, "group": "best-practices-general"}], "id": "best-practices", "score": 0.96}, "seo": {"title": "SEO", "description": "These checks ensure that your page is following basic search engine optimization advice. There are many additional factors Lighthouse does not score here that may affect your search ranking, including performance on [Core Web Vitals](https://web.dev/explore/vitals). [Learn more about Google Search Essentials](https://support.google.com/webmasters/answer/35769).", "manualDescription": "Run these additional validators on your site to check additional SEO best practices.", "supportedModes": ["navigation", "snapshot"], "auditRefs": [{"id": "is-crawlable", "weight": 4.043478260869565, "group": "seo-crawl"}, {"id": "document-title", "weight": 1, "group": "seo-content"}, {"id": "meta-description", "weight": 1, "group": "seo-content"}, {"id": "http-status-code", "weight": 1, "group": "seo-crawl"}, {"id": "link-text", "weight": 1, "group": "seo-content"}, {"id": "crawlable-anchors", "weight": 1, "group": "seo-crawl"}, {"id": "robots-txt", "weight": 1, "group": "seo-crawl"}, {"id": "image-alt", "weight": 0, "group": "seo-content"}, {"id": "hreflang", "weight": 1, "group": "seo-content"}, {"id": "canonical", "weight": 0, "group": "seo-content"}, {"id": "structured-data", "weight": 0}], "id": "seo", "score": 0.91}}, "categoryGroups": {"metrics": {"title": "Metrics"}, "insights": {"title": "Insights", "description": "These insights are also available in the Chrome DevTools Performance Panel - [record a trace](https://developer.chrome.com/docs/devtools/performance/reference) to view more detailed information."}, "diagnostics": {"title": "Diagnostics", "description": "More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."}, "a11y-best-practices": {"title": "Best practices", "description": "These items highlight common accessibility best practices."}, "a11y-color-contrast": {"title": "Contrast", "description": "These are opportunities to improve the legibility of your content."}, "a11y-names-labels": {"title": "Names and labels", "description": "These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-navigation": {"title": "Navigation", "description": "These are opportunities to improve keyboard navigation in your application."}, "a11y-aria": {"title": "ARIA", "description": "These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-language": {"title": "Internationalization and localization", "description": "These are opportunities to improve the interpretation of your content by users in different locales."}, "a11y-audio-video": {"title": "Audio and video", "description": "These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."}, "a11y-tables-lists": {"title": "Tables and lists", "description": "These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."}, "seo-mobile": {"title": "Mobile Friendly", "description": "Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "seo-content": {"title": "Content Best Practices", "description": "Format your HTML in a way that enables crawlers to better understand your app’s content."}, "seo-crawl": {"title": "Crawling and Indexing", "description": "To appear in search results, crawlers need access to your app."}, "best-practices-trust-safety": {"title": "Trust and Safety"}, "best-practices-ux": {"title": "User Experience"}, "best-practices-browser-compat": {"title": "Browser Compatibility"}, "best-practices-general": {"title": "General"}, "hidden": {"title": ""}}, "stackPacks": [], "entities": [{"name": "localhost", "origins": ["http://localhost:4173"], "isFirstParty": true, "isUnrecognized": true}, {"name": "gpteng.co", "origins": ["https://cdn.gpteng.co"], "isUnrecognized": true}, {"name": "Google Fonts", "homepage": "https://fonts.google.com/", "origins": ["https://fonts.googleapis.com", "https://fonts.gstatic.com"], "category": "cdn"}], "fullPageScreenshot": {"screenshot": {"data": "data:image/webp;base64,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", "width": 412, "height": 823}, "nodes": {"page-0-P": {"id": "", "top": 218, "bottom": 238, "left": 24, "right": 388, "width": 364, "height": 20}, "page-1-INPUT": {"id": "email", "top": 318, "bottom": 366, "left": 24, "right": 388, "width": 364, "height": 48}, "page-2-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-3-BODY": {"id": "", "top": 0, "bottom": 823, "left": 0, "right": 412, "width": 412, "height": 823}, "page-4-path": {"id": "", "top": 437, "bottom": 446, "left": 361, "right": 375, "width": 13, "height": 9}, "page-5-DIV": {"id": "", "top": 0, "bottom": 823, "left": 0, "right": 412, "width": 412, "height": 823}, "page-6-DIV": {"id": "radix-:r0:", "top": 162, "bottom": 662, "left": 0, "right": 412, "width": 412, "height": 500}, "1-0-BUTTON": {"id": "", "top": 420, "bottom": 464, "left": 360, "right": 376, "width": 16, "height": 44}, "1-1-BUTTON": {"id": "", "top": 594, "bottom": 638, "left": 84, "right": 328, "width": 244, "height": 44}, "1-2-DIV": {"id": "radix-:r0:", "top": 162, "bottom": 662, "left": 0, "right": 412, "width": 412, "height": 500}, "1-3-INPUT": {"id": "password", "top": 418, "bottom": 466, "left": 24, "right": 388, "width": 364, "height": 48}, "1-4-SPAN": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-5-H2": {"id": "radix-:r1:", "top": 186, "bottom": 218, "left": 24, "right": 388, "width": 364, "height": 32}, "1-6-DIV": {"id": "", "top": 162, "bottom": 262, "left": 0, "right": 412, "width": 412, "height": 100}, "1-7-path": {"id": "", "top": 437, "bottom": 446, "left": 361, "right": 375, "width": 13, "height": 9}, "1-8-BODY": {"id": "", "top": 0, "bottom": 823, "left": 0, "right": 412, "width": 412, "height": 823}, "1-9-FORM": {"id": "", "top": 286, "bottom": 534, "left": 24, "right": 388, "width": 364, "height": 248}, "1-10-LABEL": {"id": "", "top": 290, "bottom": 307, "left": 24, "right": 120, "width": 96, "height": 17}, "1-11-LABEL": {"id": "", "top": 390, "bottom": 407, "left": 24, "right": 90, "width": 66, "height": 17}, "1-12-INPUT": {"id": "email", "top": 318, "bottom": 366, "left": 24, "right": 388, "width": 364, "height": 48}, "1-13-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-14-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-15-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-16-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-17-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-18-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}}}, "timing": {"entries": [{"startTime": 1005.48, "name": "lh:config", "duration": 284.06, "entryType": "measure"}, {"startTime": 1006.44, "name": "lh:config:resolveArtifactsToDefns", "duration": 34.16, "entryType": "measure"}, {"startTime": 1289.69, "name": "lh:runner:gather", "duration": 7191.32, "entryType": "measure"}, {"startTime": 1389.08, "name": "lh:driver:connect", "duration": 6.2, "entryType": "measure"}, {"startTime": 1395.48, "name": "lh:driver:navigate", "duration": 48.34, "entryType": "measure"}, {"startTime": 1443.98, "name": "lh:gather:getBenchmarkIndex", "duration": 1002.39, "entryType": "measure"}, {"startTime": 2446.48, "name": "lh:gather:getVersion", "duration": 0.68, "entryType": "measure"}, {"startTime": 2447.31, "name": "lh:prepare:navigationMode", "duration": 58.66, "entryType": "measure"}, {"startTime": 2460.87, "name": "lh:storage:clearDataForOrigin", "duration": 31.15, "entryType": "measure"}, {"startTime": 2492.15, "name": "lh:storage:clearBrowserCaches", "duration": 12.24, "entryType": "measure"}, {"startTime": 2505.07, "name": "lh:gather:prepareThrottlingAndNetwork", "duration": 0.87, "entryType": "measure"}, {"startTime": 2553.58, "name": "lh:driver:navigate", "duration": 3793.65, "entryType": "measure"}, {"startTime": 6583.41, "name": "lh:computed:NetworkRecords", "duration": 0.63, "entryType": "measure"}, {"startTime": 6584.27, "name": "lh:gather:getArtifact:DevtoolsLog", "duration": 0.05, "entryType": "measure"}, {"startTime": 6584.32, "name": "lh:gather:getArtifact:Trace", "duration": 0.02, "entryType": "measure"}, {"startTime": 6584.35, "name": "lh:gather:getArtifact:Accessibility", "duration": 117.82, "entryType": "measure"}, {"startTime": 6702.28, "name": "lh:gather:getArtifact:AnchorElements", "duration": 3.95, "entryType": "measure"}, {"startTime": 6706.25, "name": "lh:gather:getArtifact:ConsoleMessages", "duration": 0.09, "entryType": "measure"}, {"startTime": 6706.35, "name": "lh:gather:getArtifact:CSSUsage", "duration": 17.7, "entryType": "measure"}, {"startTime": 6724.09, "name": "lh:gather:getArtifact:Doctype", "duration": 0.93, "entryType": "measure"}, {"startTime": 6725.04, "name": "lh:gather:getArtifact:DOMStats", "duration": 3.6, "entryType": "measure"}, {"startTime": 6728.66, "name": "lh:gather:getArtifact:FontSize", "duration": 8.32, "entryType": "measure"}, {"startTime": 6737, "name": "lh:gather:getArtifact:Inputs", "duration": 3.1, "entryType": "measure"}, {"startTime": 6740.12, "name": "lh:gather:getArtifact:ImageElements", "duration": 7.38, "entryType": "measure"}, {"startTime": 6747.6, "name": "lh:gather:getArtifact:InspectorIssues", "duration": 0.18, "entryType": "measure"}, {"startTime": 6747.79, "name": "lh:gather:getArtifact:JsUsage", "duration": 0.15, "entryType": "measure"}, {"startTime": 6747.97, "name": "lh:gather:getArtifact:LinkElements", "duration": 2.22, "entryType": "measure"}, {"startTime": 6750.06, "name": "lh:computed:MainResource", "duration": 0.1, "entryType": "measure"}, {"startTime": 6750.21, "name": "lh:gather:getArtifact:MainDocumentContent", "duration": 0.88, "entryType": "measure"}, {"startTime": 6751.11, "name": "lh:gather:getArtifact:MetaElements", "duration": 1.98, "entryType": "measure"}, {"startTime": 6753.11, "name": "lh:gather:getArtifact:NetworkUserAgent", "duration": 0.11, "entryType": "measure"}, {"startTime": 6753.33, "name": "lh:gather:getArtifact:OptimizedImages", "duration": 0.16, "entryType": "measure"}, {"startTime": 6753.5, "name": "lh:gather:getArtifact:ResponseCompression", "duration": 1.75, "entryType": "measure"}, {"startTime": 6755.27, "name": "lh:gather:getArtifact:RobotsTxt", "duration": 3.29, "entryType": "measure"}, {"startTime": 6758.59, "name": "lh:gather:getArtifact:Scripts", "duration": 0.13, "entryType": "measure"}, {"startTime": 6758.75, "name": "lh:gather:getArtifact:SourceMaps", "duration": 0.04, "entryType": "measure"}, {"startTime": 6758.79, "name": "lh:gather:getArtifact:Stacks", "duration": 5.99, "entryType": "measure"}, {"startTime": 6758.86, "name": "lh:gather:collectStacks", "duration": 5.91, "entryType": "measure"}, {"startTime": 6764.79, "name": "lh:gather:getArtifact:Stylesheets", "duration": 8.43, "entryType": "measure"}, {"startTime": 6773.26, "name": "lh:gather:getArtifact:TraceElements", "duration": 177.05, "entryType": "measure"}, {"startTime": 6773.43, "name": "lh:computed:TraceEngineResult", "duration": 154.01, "entryType": "measure"}, {"startTime": 6773.47, "name": "lh:computed:ProcessedTrace", "duration": 12.2, "entryType": "measure"}, {"startTime": 6786.3, "name": "lh:computed:TraceEngineResult:total", "duration": 137.86, "entryType": "measure"}, {"startTime": 6786.36, "name": "lh:computed:TraceEngineResult:parse", "duration": 114.51, "entryType": "measure"}, {"startTime": 6786.8, "name": "lh:computed:TraceEngineResult:parse:handleEvent", "duration": 57.19, "entryType": "measure"}, {"startTime": 6844.02, "name": "lh:computed:TraceEngineResult:parse:Meta:finalize", "duration": 1.96, "entryType": "measure"}, {"startTime": 6846.26, "name": "lh:computed:TraceEngineResult:parse:AnimationFrames:finalize", "duration": 1.64, "entryType": "measure"}, {"startTime": 6847.92, "name": "lh:computed:TraceEngineResult:parse:Animations:finalize", "duration": 0.46, "entryType": "measure"}, {"startTime": 6848.4, "name": "lh:computed:TraceEngineResult:parse:Samples:finalize", "duration": 1.33, "entryType": "measure"}, {"startTime": 6849.76, "name": "lh:computed:TraceEngineResult:parse:AuctionWorklets:finalize", "duration": 1.24, "entryType": "measure"}, {"startTime": 6851.02, "name": "lh:computed:TraceEngineResult:parse:NetworkRequests:finalize", "duration": 2.59, "entryType": "measure"}, {"startTime": 6853.64, "name": "lh:computed:TraceEngineResult:parse:<PERSON><PERSON>er:finalize", "duration": 5.93, "entryType": "measure"}, {"startTime": 6859.61, "name": "lh:computed:TraceEngineResult:parse:Flows:finalize", "duration": 2.53, "entryType": "measure"}, {"startTime": 6862.16, "name": "lh:computed:TraceEngineResult:parse:AsyncJSCalls:finalize", "duration": 1.76, "entryType": "measure"}, {"startTime": 6863.94, "name": "lh:computed:TraceEngineResult:parse:DOMStats:finalize", "duration": 0.08, "entryType": "measure"}, {"startTime": 6864.05, "name": "lh:computed:TraceEngineResult:parse:UserTimings:finalize", "duration": 1.27, "entryType": "measure"}, {"startTime": 6865.35, "name": "lh:computed:TraceEngineResult:parse:ExtensionTraceData:finalize", "duration": 1.48, "entryType": "measure"}, {"startTime": 6866.85, "name": "lh:computed:TraceEngineResult:parse:LayerTree:finalize", "duration": 1.69, "entryType": "measure"}, {"startTime": 6868.58, "name": "lh:computed:TraceEngineResult:parse:Frames:finalize", "duration": 8.49, "entryType": "measure"}, {"startTime": 6877.11, "name": "lh:computed:TraceEngineResult:parse:GPU:finalize", "duration": 1.43, "entryType": "measure"}, {"startTime": 6878.57, "name": "lh:computed:TraceEngineResult:parse:ImagePainting:finalize", "duration": 1.31, "entryType": "measure"}, {"startTime": 6879.9, "name": "lh:computed:TraceEngineResult:parse:Initiators:finalize", "duration": 1.36, "entryType": "measure"}, {"startTime": 6881.28, "name": "lh:computed:TraceEngineResult:parse:Invalidations:finalize", "duration": 1.25, "entryType": "measure"}, {"startTime": 6882.54, "name": "lh:computed:TraceEngineResult:parse:PageLoadMetrics:finalize", "duration": 1.69, "entryType": "measure"}, {"startTime": 6884.26, "name": "lh:computed:TraceEngineResult:parse:LargestImagePaint:finalize", "duration": 1.34, "entryType": "measure"}, {"startTime": 6885.6, "name": "lh:computed:TraceEngineResult:parse:LargestTextPaint:finalize", "duration": 1.19, "entryType": "measure"}, {"startTime": 6886.81, "name": "lh:computed:TraceEngineResult:parse:Screenshots:finalize", "duration": 2.69, "entryType": "measure"}, {"startTime": 6889.51, "name": "lh:computed:TraceEngineResult:parse:LayoutShifts:finalize", "duration": 1.51, "entryType": "measure"}, {"startTime": 6891.04, "name": "lh:computed:TraceEngineResult:parse:Memory:finalize", "duration": 1.23, "entryType": "measure"}, {"startTime": 6892.29, "name": "lh:computed:TraceEngineResult:parse:PageFrames:finalize", "duration": 1.18, "entryType": "measure"}, {"startTime": 6893.48, "name": "lh:computed:TraceEngineResult:parse:Scripts:finalize", "duration": 1.35, "entryType": "measure"}, {"startTime": 6894.84, "name": "lh:computed:TraceEngineResult:parse:SelectorStats:finalize", "duration": 1.23, "entryType": "measure"}, {"startTime": 6896.09, "name": "lh:computed:TraceEngineResult:parse:UserInteractions:finalize", "duration": 1.23, "entryType": "measure"}, {"startTime": 6897.33, "name": "lh:computed:TraceEngineResult:parse:Workers:finalize", "duration": 1.2, "entryType": "measure"}, {"startTime": 6898.54, "name": "lh:computed:TraceEngineResult:parse:Warnings:finalize", "duration": 1.28, "entryType": "measure"}, {"startTime": 6899.84, "name": "lh:computed:TraceEngineResult:parse:clone", "duration": 1.02, "entryType": "measure"}, {"startTime": 6900.87, "name": "lh:computed:TraceEngineResult:insights", "duration": 23.28, "entryType": "measure"}, {"startTime": 6901, "name": "lh:computed:TraceEngineResult:insights:createLanternContext", "duration": 12.11, "entryType": "measure"}, {"startTime": 6913.21, "name": "lh:computed:TraceEngineResult:insights:CLSCulprits", "duration": 0.45, "entryType": "measure"}, {"startTime": 6913.66, "name": "lh:computed:TraceEngineResult:insights:<PERSON>ache", "duration": 1.17, "entryType": "measure"}, {"startTime": 6914.85, "name": "lh:computed:TraceEngineResult:insights:DOMSize", "duration": 0.16, "entryType": "measure"}, {"startTime": 6915.01, "name": "lh:computed:TraceEngineResult:insights:DocumentLatency", "duration": 0.26, "entryType": "measure"}, {"startTime": 6915.28, "name": "lh:computed:TraceEngineResult:insights:DuplicatedJavaScript", "duration": 0.79, "entryType": "measure"}, {"startTime": 6916.08, "name": "lh:computed:TraceEngineResult:insights:FontDisplay", "duration": 0.11, "entryType": "measure"}, {"startTime": 6916.19, "name": "lh:computed:TraceEngineResult:insights:ForcedReflow", "duration": 0.1, "entryType": "measure"}, {"startTime": 6916.3, "name": "lh:computed:TraceEngineResult:insights:ImageDelivery", "duration": 0.14, "entryType": "measure"}, {"startTime": 6916.44, "name": "lh:computed:TraceEngineResult:insights:InteractionToNextPaint", "duration": 0.06, "entryType": "measure"}, {"startTime": 6916.5, "name": "lh:computed:TraceEngineResult:insights:LCPDiscovery", "duration": 0.25, "entryType": "measure"}, {"startTime": 6916.78, "name": "lh:computed:TraceEngineResult:insights:LCPPhases", "duration": 0.24, "entryType": "measure"}, {"startTime": 6917.02, "name": "lh:computed:TraceEngineResult:insights:LegacyJavaScript", "duration": 0.16, "entryType": "measure"}, {"startTime": 6917.19, "name": "lh:computed:TraceEngineResult:insights:ModernHTTP", "duration": 1.25, "entryType": "measure"}, {"startTime": 6918.45, "name": "lh:computed:TraceEngineResult:insights:NetworkDependencyTree", "duration": 1.06, "entryType": "measure"}, {"startTime": 6919.52, "name": "lh:computed:TraceEngineResult:insights:RenderBlocking", "duration": 0.57, "entryType": "measure"}, {"startTime": 6920.09, "name": "lh:computed:TraceEngineResult:insights:SlowCSSSelector", "duration": 0.1, "entryType": "measure"}, {"startTime": 6920.2, "name": "lh:computed:TraceEngineResult:insights:ThirdParties", "duration": 3.28, "entryType": "measure"}, {"startTime": 6923.5, "name": "lh:computed:TraceEngineResult:insights:Viewport", "duration": 0.1, "entryType": "measure"}, {"startTime": 6928.83, "name": "lh:computed:ProcessedNavigation", "duration": 0.37, "entryType": "measure"}, {"startTime": 6929.24, "name": "lh:computed:CumulativeLayoutShift", "duration": 9.08, "entryType": "measure"}, {"startTime": 6938.83, "name": "lh:computed:Responsiveness", "duration": 0.12, "entryType": "measure"}, {"startTime": 6950.33, "name": "lh:gather:getArtifact:ViewportDimensions", "duration": 0.75, "entryType": "measure"}, {"startTime": 6951.09, "name": "lh:gather:getArtifact:FullPageScreenshot", "duration": 1053.06, "entryType": "measure"}, {"startTime": 8004.18, "name": "lh:gather:getArtifact:BFCacheFailures", "duration": 466.03, "entryType": "measure"}, {"startTime": 8481.43, "name": "lh:runner:audit", "duration": 637.37, "entryType": "measure"}, {"startTime": 8481.5, "name": "lh:runner:auditing", "duration": 636.93, "entryType": "measure"}, {"startTime": 8483.28, "name": "lh:audit:is-on-https", "duration": 0.97, "entryType": "measure"}, {"startTime": 8484.39, "name": "lh:audit:redirects-http", "duration": 0.42, "entryType": "measure"}, {"startTime": 8484.97, "name": "lh:audit:viewport", "duration": 0.94, "entryType": "measure"}, {"startTime": 8485.15, "name": "lh:computed:ViewportMeta", "duration": 0.39, "entryType": "measure"}, {"startTime": 8486.03, "name": "lh:audit:first-contentful-paint", "duration": 5.78, "entryType": "measure"}, {"startTime": 8486.39, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 2.82, "entryType": "measure"}, {"startTime": 8486.69, "name": "lh:computed:Lantern<PERSON><PERSON>t<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 2.52, "entryType": "measure"}, {"startTime": 8486.78, "name": "lh:computed:PageDependencyGraph", "duration": 1.42, "entryType": "measure"}, {"startTime": 8488.22, "name": "lh:computed:LoadSimulator", "duration": 0.45, "entryType": "measure"}, {"startTime": 8488.25, "name": "lh:computed:NetworkAnalysis", "duration": 0.38, "entryType": "measure"}, {"startTime": 8491.96, "name": "lh:audit:largest-contentful-paint", "duration": 1.63, "entryType": "measure"}, {"startTime": 8492.23, "name": "lh:computed:Largest<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.99, "entryType": "measure"}, {"startTime": 8492.27, "name": "lh:computed:LanternLargestContentfulPaint", "duration": 0.94, "entryType": "measure"}, {"startTime": 8493.69, "name": "lh:audit:first-meaningful-paint", "duration": 0.33, "entryType": "measure"}, {"startTime": 8494.11, "name": "lh:audit:speed-index", "duration": 135.34, "entryType": "measure"}, {"startTime": 8494.25, "name": "lh:computed:SpeedIndex", "duration": 134.7, "entryType": "measure"}, {"startTime": 8494.28, "name": "lh:computed:LanternSpeedIndex", "duration": 134.66, "entryType": "measure"}, {"startTime": 8494.3, "name": "lh:computed:Speedline", "duration": 133.59, "entryType": "measure"}, {"startTime": 8629.46, "name": "lh:audit:screenshot-thumbnails", "duration": 0.25, "entryType": "measure"}, {"startTime": 8629.72, "name": "lh:audit:final-screenshot", "duration": 0.62, "entryType": "measure"}, {"startTime": 8629.78, "name": "lh:computed:Screenshots", "duration": 0.55, "entryType": "measure"}, {"startTime": 8630.45, "name": "lh:audit:total-blocking-time", "duration": 2.33, "entryType": "measure"}, {"startTime": 8630.59, "name": "lh:computed:TotalBlockingTime", "duration": 1.65, "entryType": "measure"}, {"startTime": 8630.63, "name": "lh:computed:LanternTotalBlockingTime", "duration": 1.61, "entryType": "measure"}, {"startTime": 8630.66, "name": "lh:computed:LanternInteractive", "duration": 0.52, "entryType": "measure"}, {"startTime": 8632.89, "name": "lh:audit:max-potential-fid", "duration": 1.61, "entryType": "measure"}, {"startTime": 8633.13, "name": "lh:computed:MaxPotentialFID", "duration": 0.7, "entryType": "measure"}, {"startTime": 8633.17, "name": "lh:computed:LanternMaxPotentialFID", "duration": 0.66, "entryType": "measure"}, {"startTime": 8634.6, "name": "lh:audit:cumulative-layout-shift", "duration": 0.39, "entryType": "measure"}, {"startTime": 8635.1, "name": "lh:audit:errors-in-console", "duration": 1.49, "entryType": "measure"}, {"startTime": 8635.54, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON>", "duration": 0.08, "entryType": "measure"}, {"startTime": 8636.71, "name": "lh:audit:server-response-time", "duration": 0.61, "entryType": "measure"}, {"startTime": 8637.44, "name": "lh:audit:interactive", "duration": 0.5, "entryType": "measure"}, {"startTime": 8637.61, "name": "lh:computed:Interactive", "duration": 0.04, "entryType": "measure"}, {"startTime": 8638.04, "name": "lh:audit:user-timings", "duration": 0.73, "entryType": "measure"}, {"startTime": 8638.16, "name": "lh:computed:UserTimings", "duration": 0.35, "entryType": "measure"}, {"startTime": 8638.86, "name": "lh:audit:critical-request-chains", "duration": 2.42, "entryType": "measure"}, {"startTime": 8639.02, "name": "lh:computed:CriticalRequest<PERSON><PERSON>ns", "duration": 0.3, "entryType": "measure"}, {"startTime": 8641.48, "name": "lh:audit:redirects", "duration": 1.19, "entryType": "measure"}, {"startTime": 8642.82, "name": "lh:audit:image-aspect-ratio", "duration": 0.55, "entryType": "measure"}, {"startTime": 8643.51, "name": "lh:audit:image-size-responsive", "duration": 0.83, "entryType": "measure"}, {"startTime": 8643.79, "name": "lh:computed:ImageRecords", "duration": 0.15, "entryType": "measure"}, {"startTime": 8644.47, "name": "lh:audit:deprecations", "duration": 0.51, "entryType": "measure"}, {"startTime": 8645.14, "name": "lh:audit:third-party-cookies", "duration": 0.52, "entryType": "measure"}, {"startTime": 8645.82, "name": "lh:audit:mainthread-work-breakdown", "duration": 4.48, "entryType": "measure"}, {"startTime": 8646.08, "name": "lh:computed:MainThreadTasks", "duration": 3.41, "entryType": "measure"}, {"startTime": 8650.43, "name": "lh:audit:bootup-time", "duration": 7.49, "entryType": "measure"}, {"startTime": 8652.5, "name": "lh:computed:TBTImpactTasks", "duration": 3.82, "entryType": "measure"}, {"startTime": 8658.03, "name": "lh:audit:uses-rel-preconnect", "duration": 1.04, "entryType": "measure"}, {"startTime": 8659.2, "name": "lh:audit:font-display", "duration": 1.29, "entryType": "measure"}, {"startTime": 8660.5, "name": "lh:audit:diagnostics", "duration": 0.27, "entryType": "measure"}, {"startTime": 8660.78, "name": "lh:audit:network-requests", "duration": 1.2, "entryType": "measure"}, {"startTime": 8660.87, "name": "lh:computed:EntityClassification", "duration": 0.87, "entryType": "measure"}, {"startTime": 8662.15, "name": "lh:audit:network-rtt", "duration": 0.61, "entryType": "measure"}, {"startTime": 8662.84, "name": "lh:audit:network-server-latency", "duration": 0.47, "entryType": "measure"}, {"startTime": 8663.32, "name": "lh:audit:main-thread-tasks", "duration": 0.11, "entryType": "measure"}, {"startTime": 8663.44, "name": "lh:audit:metrics", "duration": 3.08, "entryType": "measure"}, {"startTime": 8663.57, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duration": 2.8, "entryType": "measure"}, {"startTime": 8664, "name": "lh:computed:FirstContentfulPaintAllFrames", "duration": 0.22, "entryType": "measure"}, {"startTime": 8664.25, "name": "lh:computed:LargestContentfulPaintAllFrames", "duration": 0.09, "entryType": "measure"}, {"startTime": 8664.55, "name": "lh:computed:LCPBreakdown", "duration": 1.46, "entryType": "measure"}, {"startTime": 8664.98, "name": "lh:computed:TimeToFirstByte", "duration": 0.18, "entryType": "measure"}, {"startTime": 8665.17, "name": "lh:computed:LCPImageRecord", "duration": 0.81, "entryType": "measure"}, {"startTime": 8666.53, "name": "lh:audit:resource-summary", "duration": 0.69, "entryType": "measure"}, {"startTime": 8666.7, "name": "lh:computed:ResourceSummary", "duration": 0.2, "entryType": "measure"}, {"startTime": 8667.36, "name": "lh:audit:third-party-summary", "duration": 2.22, "entryType": "measure"}, {"startTime": 8669.71, "name": "lh:audit:third-party-facades", "duration": 1.71, "entryType": "measure"}, {"startTime": 8671.51, "name": "lh:audit:largest-contentful-paint-element", "duration": 1.05, "entryType": "measure"}, {"startTime": 8672.7, "name": "lh:audit:lcp-lazy-loaded", "duration": 0.81, "entryType": "measure"}, {"startTime": 8673.62, "name": "lh:audit:layout-shifts", "duration": 1.11, "entryType": "measure"}, {"startTime": 8674.91, "name": "lh:audit:long-tasks", "duration": 1.97, "entryType": "measure"}, {"startTime": 8677, "name": "lh:audit:non-composited-animations", "duration": 1.08, "entryType": "measure"}, {"startTime": 8678.21, "name": "lh:audit:unsized-images", "duration": 0.5, "entryType": "measure"}, {"startTime": 8678.83, "name": "lh:audit:valid-source-maps", "duration": 0.71, "entryType": "measure"}, {"startTime": 8679.64, "name": "lh:audit:prioritize-lcp-image", "duration": 0.4, "entryType": "measure"}, {"startTime": 8680.17, "name": "lh:audit:csp-xss", "duration": 0.53, "entryType": "measure"}, {"startTime": 8680.8, "name": "lh:audit:has-hsts", "duration": 0.73, "entryType": "measure"}, {"startTime": 8681.66, "name": "lh:audit:origin-isolation", "duration": 1.2, "entryType": "measure"}, {"startTime": 8683.01, "name": "lh:audit:clickjacking-mitigation", "duration": 0.54, "entryType": "measure"}, {"startTime": 8683.56, "name": "lh:audit:script-treemap-data", "duration": 42.07, "entryType": "measure"}, {"startTime": 8683.74, "name": "lh:computed:ModuleDuplication", "duration": 0.09, "entryType": "measure"}, {"startTime": 8683.85, "name": "lh:computed:UnusedJavascriptSummary", "duration": 41.67, "entryType": "measure"}, {"startTime": 8726.04, "name": "lh:audit:accesskeys", "duration": 0.62, "entryType": "measure"}, {"startTime": 8726.78, "name": "lh:audit:aria-allowed-attr", "duration": 1.84, "entryType": "measure"}, {"startTime": 8728.74, "name": "lh:audit:aria-allowed-role", "duration": 1.74, "entryType": "measure"}, {"startTime": 8730.61, "name": "lh:audit:aria-command-name", "duration": 0.49, "entryType": "measure"}, {"startTime": 8731.22, "name": "lh:audit:aria-conditional-attr", "duration": 2.44, "entryType": "measure"}, {"startTime": 8733.8, "name": "lh:audit:aria-deprecated-role", "duration": 3.21, "entryType": "measure"}, {"startTime": 8737.16, "name": "lh:audit:aria-dialog-name", "duration": 2.16, "entryType": "measure"}, {"startTime": 8739.44, "name": "lh:audit:aria-hidden-body", "duration": 3.08, "entryType": "measure"}, {"startTime": 8742.65, "name": "lh:audit:aria-hidden-focus", "duration": 1.66, "entryType": "measure"}, {"startTime": 8744.41, "name": "lh:audit:aria-input-field-name", "duration": 0.42, "entryType": "measure"}, {"startTime": 8744.93, "name": "lh:audit:aria-meter-name", "duration": 0.43, "entryType": "measure"}, {"startTime": 8745.46, "name": "lh:audit:aria-progressbar-name", "duration": 0.49, "entryType": "measure"}, {"startTime": 8746.05, "name": "lh:audit:aria-prohibited-attr", "duration": 2.02, "entryType": "measure"}, {"startTime": 8748.19, "name": "lh:audit:aria-required-attr", "duration": 2.59, "entryType": "measure"}, {"startTime": 8750.94, "name": "lh:audit:aria-required-children", "duration": 0.71, "entryType": "measure"}, {"startTime": 8751.8, "name": "lh:audit:aria-required-parent", "duration": 0.58, "entryType": "measure"}, {"startTime": 8752.49, "name": "lh:audit:aria-roles", "duration": 1.58, "entryType": "measure"}, {"startTime": 8754.18, "name": "lh:audit:aria-text", "duration": 4.36, "entryType": "measure"}, {"startTime": 8758.66, "name": "lh:audit:aria-toggle-field-name", "duration": 0.64, "entryType": "measure"}, {"startTime": 8759.43, "name": "lh:audit:aria-tooltip-name", "duration": 0.65, "entryType": "measure"}, {"startTime": 8760.18, "name": "lh:audit:aria-treeitem-name", "duration": 0.65, "entryType": "measure"}, {"startTime": 8760.92, "name": "lh:audit:aria-valid-attr-value", "duration": 1.51, "entryType": "measure"}, {"startTime": 8762.53, "name": "lh:audit:aria-valid-attr", "duration": 1.52, "entryType": "measure"}, {"startTime": 8764.14, "name": "lh:audit:button-name", "duration": 2.36, "entryType": "measure"}, {"startTime": 8766.63, "name": "lh:audit:bypass", "duration": 0.69, "entryType": "measure"}, {"startTime": 8767.43, "name": "lh:audit:color-contrast", "duration": 1.85, "entryType": "measure"}, {"startTime": 8769.38, "name": "lh:audit:definition-list", "duration": 4.29, "entryType": "measure"}, {"startTime": 8774.03, "name": "lh:audit:dlitem", "duration": 1.19, "entryType": "measure"}, {"startTime": 8775.36, "name": "lh:audit:document-title", "duration": 1.66, "entryType": "measure"}, {"startTime": 8777.11, "name": "lh:audit:duplicate-id-aria", "duration": 1.6, "entryType": "measure"}, {"startTime": 8778.81, "name": "lh:audit:empty-heading", "duration": 1.67, "entryType": "measure"}, {"startTime": 8780.59, "name": "lh:audit:form-field-multiple-labels", "duration": 1.99, "entryType": "measure"}, {"startTime": 8782.7, "name": "lh:audit:frame-title", "duration": 0.88, "entryType": "measure"}, {"startTime": 8783.68, "name": "lh:audit:heading-order", "duration": 1.67, "entryType": "measure"}, {"startTime": 8785.46, "name": "lh:audit:html-has-lang", "duration": 1.68, "entryType": "measure"}, {"startTime": 8787.24, "name": "lh:audit:html-lang-valid", "duration": 4.99, "entryType": "measure"}, {"startTime": 8792.37, "name": "lh:audit:html-xml-lang-mismatch", "duration": 0.86, "entryType": "measure"}, {"startTime": 8793.33, "name": "lh:audit:identical-links-same-purpose", "duration": 0.77, "entryType": "measure"}, {"startTime": 8794.19, "name": "lh:audit:image-alt", "duration": 0.77, "entryType": "measure"}, {"startTime": 8795.06, "name": "lh:audit:image-redundant-alt", "duration": 0.87, "entryType": "measure"}, {"startTime": 8796.01, "name": "lh:audit:input-button-name", "duration": 0.81, "entryType": "measure"}, {"startTime": 8796.92, "name": "lh:audit:input-image-alt", "duration": 0.86, "entryType": "measure"}, {"startTime": 8797.88, "name": "lh:audit:label-content-name-mismatch", "duration": 1.26, "entryType": "measure"}, {"startTime": 8799.24, "name": "lh:audit:label", "duration": 1.73, "entryType": "measure"}, {"startTime": 8801.2, "name": "lh:audit:landmark-one-main", "duration": 5.28, "entryType": "measure"}, {"startTime": 8806.74, "name": "lh:audit:link-name", "duration": 1.62, "entryType": "measure"}, {"startTime": 8808.49, "name": "lh:audit:link-in-text-block", "duration": 0.99, "entryType": "measure"}, {"startTime": 8809.59, "name": "lh:audit:list", "duration": 1.07, "entryType": "measure"}, {"startTime": 8810.76, "name": "lh:audit:listitem", "duration": 1.03, "entryType": "measure"}, {"startTime": 8811.88, "name": "lh:audit:meta-refresh", "duration": 1.13, "entryType": "measure"}, {"startTime": 8813.16, "name": "lh:audit:meta-viewport", "duration": 1.99, "entryType": "measure"}, {"startTime": 8815.26, "name": "lh:audit:object-alt", "duration": 1.54, "entryType": "measure"}, {"startTime": 8816.91, "name": "lh:audit:select-name", "duration": 1.16, "entryType": "measure"}, {"startTime": 8818.17, "name": "lh:audit:skip-link", "duration": 1.04, "entryType": "measure"}, {"startTime": 8819.3, "name": "lh:audit:tabindex", "duration": 8.49, "entryType": "measure"}, {"startTime": 8828.03, "name": "lh:audit:table-duplicate-name", "duration": 3.25, "entryType": "measure"}, {"startTime": 8831.64, "name": "lh:audit:table-fake-caption", "duration": 4.34, "entryType": "measure"}, {"startTime": 8836.21, "name": "lh:audit:target-size", "duration": 3.34, "entryType": "measure"}, {"startTime": 8839.81, "name": "lh:audit:td-has-header", "duration": 6.3, "entryType": "measure"}, {"startTime": 8846.42, "name": "lh:audit:td-headers-attr", "duration": 2.49, "entryType": "measure"}, {"startTime": 8849.26, "name": "lh:audit:th-has-data-cells", "duration": 1.79, "entryType": "measure"}, {"startTime": 8851.18, "name": "lh:audit:valid-lang", "duration": 1.33, "entryType": "measure"}, {"startTime": 8852.61, "name": "lh:audit:video-caption", "duration": 1.3, "entryType": "measure"}, {"startTime": 8853.92, "name": "lh:audit:custom-controls-labels", "duration": 0.07, "entryType": "measure"}, {"startTime": 8854, "name": "lh:audit:custom-controls-roles", "duration": 0.02, "entryType": "measure"}, {"startTime": 8854.02, "name": "lh:audit:focus-traps", "duration": 0.01, "entryType": "measure"}, {"startTime": 8854.04, "name": "lh:audit:focusable-controls", "duration": 0.02, "entryType": "measure"}, {"startTime": 8854.06, "name": "lh:audit:interactive-element-affordance", "duration": 0.01, "entryType": "measure"}, {"startTime": 8854.07, "name": "lh:audit:logical-tab-order", "duration": 0.01, "entryType": "measure"}, {"startTime": 8854.09, "name": "lh:audit:managed-focus", "duration": 0.01, "entryType": "measure"}, {"startTime": 8854.11, "name": "lh:audit:offscreen-content-hidden", "duration": 0.01, "entryType": "measure"}, {"startTime": 8854.13, "name": "lh:audit:use-landmarks", "duration": 0.01, "entryType": "measure"}, {"startTime": 8854.14, "name": "lh:audit:visual-order-follows-dom", "duration": 0.01, "entryType": "measure"}, {"startTime": 8854.25, "name": "lh:audit:uses-long-cache-ttl", "duration": 1.22, "entryType": "measure"}, {"startTime": 8855.57, "name": "lh:audit:total-byte-weight", "duration": 0.79, "entryType": "measure"}, {"startTime": 8856.47, "name": "lh:audit:offscreen-images", "duration": 2.22, "entryType": "measure"}, {"startTime": 8858.81, "name": "lh:audit:render-blocking-resources", "duration": 7.26, "entryType": "measure"}, {"startTime": 8859.14, "name": "lh:computed:UnusedCSS", "duration": 5.09, "entryType": "measure"}, {"startTime": 8864.27, "name": "lh:computed:NavigationInsights", "duration": 0.12, "entryType": "measure"}, {"startTime": 8864.51, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.07, "entryType": "measure"}, {"startTime": 8866.17, "name": "lh:audit:unminified-css", "duration": 14.62, "entryType": "measure"}, {"startTime": 8880.92, "name": "lh:audit:unminified-javascript", "duration": 80.04, "entryType": "measure"}, {"startTime": 8961.06, "name": "lh:audit:unused-css-rules", "duration": 1.49, "entryType": "measure"}, {"startTime": 8962.64, "name": "lh:audit:unused-javascript", "duration": 1.27, "entryType": "measure"}, {"startTime": 8963.99, "name": "lh:audit:modern-image-formats", "duration": 3.12, "entryType": "measure"}, {"startTime": 8967.2, "name": "lh:audit:uses-optimized-images", "duration": 1.4, "entryType": "measure"}, {"startTime": 8968.78, "name": "lh:audit:uses-text-compression", "duration": 1.42, "entryType": "measure"}, {"startTime": 8970.28, "name": "lh:audit:uses-responsive-images", "duration": 1.32, "entryType": "measure"}, {"startTime": 8971.7, "name": "lh:audit:efficient-animated-content", "duration": 1.33, "entryType": "measure"}, {"startTime": 8973.11, "name": "lh:audit:duplicated-javascript", "duration": 2.25, "entryType": "measure"}, {"startTime": 8975.49, "name": "lh:audit:legacy-javascript", "duration": 110.3, "entryType": "measure"}, {"startTime": 9085.92, "name": "lh:audit:doctype", "duration": 0.56, "entryType": "measure"}, {"startTime": 9086.59, "name": "lh:audit:charset", "duration": 0.52, "entryType": "measure"}, {"startTime": 9087.22, "name": "lh:audit:dom-size", "duration": 1.45, "entryType": "measure"}, {"startTime": 9088.8, "name": "lh:audit:geolocation-on-start", "duration": 0.54, "entryType": "measure"}, {"startTime": 9089.46, "name": "lh:audit:inspector-issues", "duration": 1, "entryType": "measure"}, {"startTime": 9090.66, "name": "lh:audit:no-document-write", "duration": 0.66, "entryType": "measure"}, {"startTime": 9091.4, "name": "lh:audit:js-libraries", "duration": 0.36, "entryType": "measure"}, {"startTime": 9091.88, "name": "lh:audit:notification-on-start", "duration": 0.42, "entryType": "measure"}, {"startTime": 9092.4, "name": "lh:audit:paste-preventing-inputs", "duration": 0.38, "entryType": "measure"}, {"startTime": 9092.85, "name": "lh:audit:uses-http2", "duration": 1.21, "entryType": "measure"}, {"startTime": 9094.19, "name": "lh:audit:uses-passive-event-listeners", "duration": 0.41, "entryType": "measure"}, {"startTime": 9095.97, "name": "lh:audit:meta-description", "duration": 0.44, "entryType": "measure"}, {"startTime": 9096.64, "name": "lh:audit:http-status-code", "duration": 0.5, "entryType": "measure"}, {"startTime": 9097.26, "name": "lh:audit:font-size", "duration": 1.04, "entryType": "measure"}, {"startTime": 9098.44, "name": "lh:audit:link-text", "duration": 0.54, "entryType": "measure"}, {"startTime": 9099.1, "name": "lh:audit:crawlable-anchors", "duration": 0.47, "entryType": "measure"}, {"startTime": 9099.68, "name": "lh:audit:is-crawlable", "duration": 0.85, "entryType": "measure"}, {"startTime": 9100.63, "name": "lh:audit:robots-txt", "duration": 0.81, "entryType": "measure"}, {"startTime": 9101.55, "name": "lh:audit:hreflang", "duration": 0.39, "entryType": "measure"}, {"startTime": 9102.03, "name": "lh:audit:canonical", "duration": 0.4, "entryType": "measure"}, {"startTime": 9102.5, "name": "lh:audit:structured-data", "duration": 0.23, "entryType": "measure"}, {"startTime": 9102.84, "name": "lh:audit:bf-cache", "duration": 0.39, "entryType": "measure"}, {"startTime": 9103.32, "name": "lh:audit:cache-insight", "duration": 0.61, "entryType": "measure"}, {"startTime": 9104.03, "name": "lh:audit:cls-culprits-insight", "duration": 0.44, "entryType": "measure"}, {"startTime": 9104.58, "name": "lh:audit:document-latency-insight", "duration": 0.35, "entryType": "measure"}, {"startTime": 9105.03, "name": "lh:audit:dom-size-insight", "duration": 0.56, "entryType": "measure"}, {"startTime": 9105.68, "name": "lh:audit:duplicated-javascript-insight", "duration": 0.51, "entryType": "measure"}, {"startTime": 9106.34, "name": "lh:audit:font-display-insight", "duration": 0.53, "entryType": "measure"}, {"startTime": 9106.98, "name": "lh:audit:forced-reflow-insight", "duration": 0.52, "entryType": "measure"}, {"startTime": 9107.61, "name": "lh:audit:image-delivery-insight", "duration": 0.73, "entryType": "measure"}, {"startTime": 9108.47, "name": "lh:audit:interaction-to-next-paint-insight", "duration": 0.49, "entryType": "measure"}, {"startTime": 9109.06, "name": "lh:audit:lcp-discovery-insight", "duration": 0.36, "entryType": "measure"}, {"startTime": 9109.51, "name": "lh:audit:lcp-phases-insight", "duration": 0.47, "entryType": "measure"}, {"startTime": 9110.1, "name": "lh:audit:legacy-javascript-insight", "duration": 0.5, "entryType": "measure"}, {"startTime": 9110.7, "name": "lh:audit:modern-http-insight", "duration": 0.36, "entryType": "measure"}, {"startTime": 9111.16, "name": "lh:audit:network-dependency-tree-insight", "duration": 0.41, "entryType": "measure"}, {"startTime": 9111.67, "name": "lh:audit:render-blocking-insight", "duration": 0.43, "entryType": "measure"}, {"startTime": 9112.2, "name": "lh:audit:third-parties-insight", "duration": 5.62, "entryType": "measure"}, {"startTime": 9117.95, "name": "lh:audit:viewport-insight", "duration": 0.47, "entryType": "measure"}, {"startTime": 9118.43, "name": "lh:runner:generate", "duration": 0.37, "entryType": "measure"}], "total": 7828.69}, "i18n": {"rendererFormattedStrings": {"calculatorLink": "See calculator.", "collapseView": "Collapse view", "crcInitialNavigation": "Initial Navigation", "crcLongestDurationLabel": "Maximum critical path latency:", "dropdownCopyJSON": "Copy JSON", "dropdownDarkTheme": "Toggle Dark Theme", "dropdownPrintExpanded": "Print Expanded", "dropdownPrintSummary": "Print Summary", "dropdownSaveGist": "Save as Gist", "dropdownSaveHTML": "Save as HTML", "dropdownSaveJSON": "Save as JSON", "dropdownViewUnthrottledTrace": "View Unthrottled Trace", "dropdownViewer": "Open in Viewer", "errorLabel": "Error!", "errorMissingAuditInfo": "Report error: no audit information", "expandView": "Expand view", "firstPartyChipLabel": "1st party", "footerIssue": "File an issue", "goBackToAudits": "Go back to audits", "hide": "<PERSON>de", "insightsNotice": "Later this year, insights will replace performance audits. [Learn more and provide feedback here](https://github.com/GoogleChrome/lighthouse/discussions/16462).", "labDataTitle": "Lab Data", "lsPerformanceCategoryDescription": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.", "manualAuditsGroupTitle": "Additional items to manually check", "notApplicableAuditsGroupTitle": "Not applicable", "openInANewTabTooltip": "Open in a new tab", "opportunityResourceColumnLabel": "Opportunity", "opportunitySavingsColumnLabel": "Estimated Savings", "passedAuditsGroupTitle": "Passed audits", "runtimeAnalysisWindow": "Initial page load", "runtimeAnalysisWindowSnapshot": "Point-in-time snapshot", "runtimeAnalysisWindowTimespan": "User interactions timespan", "runtimeCustom": "Custom throttling", "runtimeDesktopEmulation": "Emulated Desktop", "runtimeMobileEmulation": "Emulated Moto G Power", "runtimeNoEmulation": "No emulation", "runtimeSettingsAxeVersion": "Axe version", "runtimeSettingsBenchmark": "Unthrottled CPU/Memory Power", "runtimeSettingsCPUThrottling": "CPU throttling", "runtimeSettingsDevice": "<PERSON><PERSON>", "runtimeSettingsNetworkThrottling": "Network throttling", "runtimeSettingsScreenEmulation": "Screen emulation", "runtimeSettingsUANetwork": "User agent (network)", "runtimeSingleLoad": "Single page session", "runtimeSingleLoadTooltip": "This data is taken from a single page session, as opposed to field data summarizing many sessions.", "runtimeSlow4g": "Slow 4G throttling", "runtimeUnknown": "Unknown", "show": "Show", "showRelevantAudits": "Show audits relevant to:", "snippetCollapseButtonLabel": "Collapse snippet", "snippetExpandButtonLabel": "Expand snippet", "thirdPartyResourcesLabel": "Show 3rd-party resources", "throttlingProvided": "Provided by environment", "toplevelWarningsMessage": "There were issues affecting this run of Lighthouse:", "tryInsights": "Try insights", "unattributable": "Unattributable", "varianceDisclaimer": "Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.", "viewTraceLabel": "View Trace", "viewTreemapLabel": "View Treemap", "warningAuditsGroupTitle": "Passed audits but with warnings", "warningHeader": "Warnings: "}, "icuMessagePaths": {"core/audits/is-on-https.js | title": ["audits[is-on-https].title"], "core/audits/is-on-https.js | description": ["audits[is-on-https].description"], "core/audits/redirects-http.js | title": ["audits[redirects-http].title"], "core/audits/redirects-http.js | description": ["audits[redirects-http].description"], "core/audits/viewport.js | title": ["audits.viewport.title"], "core/audits/viewport.js | description": ["audits.viewport.description"], "core/lib/i18n/i18n.js | firstContentfulPaintMetric": ["audits[first-contentful-paint].title"], "core/audits/metrics/first-contentful-paint.js | description": ["audits[first-contentful-paint].description"], "core/lib/i18n/i18n.js | seconds": [{"values": {"timeInMs": 6604.4694}, "path": "audits[first-contentful-paint].displayValue"}, {"values": {"timeInMs": 6622.4694}, "path": "audits[largest-contentful-paint].displayValue"}, {"values": {"timeInMs": 6604.4694}, "path": "audits[speed-index].displayValue"}, {"values": {"timeInMs": 6730.4694}, "path": "audits.interactive.displayValue"}, {"values": {"timeInMs": 603.6560000000001}, "path": "audits[mainthread-work-breakdown].displayValue"}, {"values": {"timeInMs": 225.98000000000133}, "path": "audits[bootup-time].displayValue"}], "core/lib/i18n/i18n.js | largestContentfulPaintMetric": ["audits[largest-contentful-paint].title"], "core/audits/metrics/largest-contentful-paint.js | description": ["audits[largest-contentful-paint].description"], "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": ["audits[first-meaningful-paint].title"], "core/audits/metrics/first-meaningful-paint.js | description": ["audits[first-meaningful-paint].description"], "core/lib/i18n/i18n.js | speedIndexMetric": ["audits[speed-index].title"], "core/audits/metrics/speed-index.js | description": ["audits[speed-index].description"], "core/lib/i18n/i18n.js | totalBlockingTimeMetric": ["audits[total-blocking-time].title"], "core/audits/metrics/total-blocking-time.js | description": ["audits[total-blocking-time].description"], "core/lib/i18n/i18n.js | ms": [{"values": {"timeInMs": 76}, "path": "audits[total-blocking-time].displayValue"}, {"values": {"timeInMs": 127}, "path": "audits[max-potential-fid].displayValue"}, {"values": {"timeInMs": 133.944}, "path": "audits[network-rtt].displayValue"}, {"values": {"timeInMs": 70.15899999999995}, "path": "audits[network-server-latency].displayValue"}, {"values": {"timeInMs": 6622.4694}, "path": "audits[largest-contentful-paint-element].displayValue"}], "core/lib/i18n/i18n.js | maxPotentialFIDMetric": ["audits[max-potential-fid].title"], "core/audits/metrics/max-potential-fid.js | description": ["audits[max-potential-fid].description"], "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": ["audits[cumulative-layout-shift].title"], "core/audits/metrics/cumulative-layout-shift.js | description": ["audits[cumulative-layout-shift].description"], "core/audits/errors-in-console.js | failureTitle": ["audits[errors-in-console].title"], "core/audits/errors-in-console.js | description": ["audits[errors-in-console].description"], "core/lib/i18n/i18n.js | columnSource": ["audits[errors-in-console].details.headings[0].label", "audits[font-size].details.headings[0].label"], "core/lib/i18n/i18n.js | columnDescription": ["audits[errors-in-console].details.headings[1].label", "audits[csp-xss].details.headings[0].label", "audits[has-hsts].details.headings[0].label", "audits[origin-isolation].details.headings[0].label", "audits[clickjacking-mitigation].details.headings[0].label"], "core/audits/server-response-time.js | title": ["audits[server-response-time].title"], "core/audits/server-response-time.js | description": ["audits[server-response-time].description"], "core/audits/server-response-time.js | displayValue": [{"values": {"timeInMs": 0.757}, "path": "audits[server-response-time].displayValue"}], "core/lib/i18n/i18n.js | columnURL": ["audits[server-response-time].details.headings[0].label", "audits[bootup-time].details.headings[0].label", "audits[uses-rel-preconnect].details.headings[0].label", "audits[network-rtt].details.headings[0].label", "audits[network-server-latency].details.headings[0].label", "audits[long-tasks].details.headings[0].label", "audits[valid-source-maps].details.headings[0].label", "audits[uses-long-cache-ttl].details.headings[0].label", "audits[total-byte-weight].details.headings[0].label", "audits[render-blocking-resources].details.headings[0].label", "audits[unused-css-rules].details.headings[0].label", "audits[unused-javascript].details.headings[0].label", "audits[legacy-javascript].details.headings[0].label", "audits[render-blocking-insight].details.headings[0].label"], "core/lib/i18n/i18n.js | columnTimeSpent": ["audits[server-response-time].details.headings[1].label", "audits[mainthread-work-breakdown].details.headings[1].label", "audits[network-rtt].details.headings[1].label", "audits[network-server-latency].details.headings[1].label"], "core/lib/i18n/i18n.js | interactiveMetric": ["audits.interactive.title"], "core/audits/metrics/interactive.js | description": ["audits.interactive.description"], "core/audits/user-timings.js | title": ["audits[user-timings].title"], "core/audits/user-timings.js | description": ["audits[user-timings].description"], "core/audits/critical-request-chains.js | title": ["audits[critical-request-chains].title"], "core/audits/critical-request-chains.js | description": ["audits[critical-request-chains].description"], "core/audits/critical-request-chains.js | displayValue": [{"values": {"itemCount": 4}, "path": "audits[critical-request-chains].displayValue"}], "core/audits/redirects.js | title": ["audits.redirects.title"], "core/audits/redirects.js | description": ["audits.redirects.description"], "core/audits/image-aspect-ratio.js | title": ["audits[image-aspect-ratio].title"], "core/audits/image-aspect-ratio.js | description": ["audits[image-aspect-ratio].description"], "core/audits/image-size-responsive.js | title": ["audits[image-size-responsive].title"], "core/audits/image-size-responsive.js | description": ["audits[image-size-responsive].description"], "core/audits/deprecations.js | title": ["audits.deprecations.title"], "core/audits/deprecations.js | description": ["audits.deprecations.description"], "core/audits/third-party-cookies.js | title": ["audits[third-party-cookies].title"], "core/audits/third-party-cookies.js | description": ["audits[third-party-cookies].description"], "core/audits/mainthread-work-breakdown.js | title": ["audits[mainthread-work-breakdown].title"], "core/audits/mainthread-work-breakdown.js | description": ["audits[mainthread-work-breakdown].description"], "core/audits/mainthread-work-breakdown.js | columnCategory": ["audits[mainthread-work-breakdown].details.headings[0].label"], "core/audits/bootup-time.js | title": ["audits[bootup-time].title"], "core/audits/bootup-time.js | description": ["audits[bootup-time].description"], "core/audits/bootup-time.js | columnTotal": ["audits[bootup-time].details.headings[1].label"], "core/audits/bootup-time.js | columnScriptEval": ["audits[bootup-time].details.headings[2].label"], "core/audits/bootup-time.js | columnScriptParse": ["audits[bootup-time].details.headings[3].label"], "core/audits/uses-rel-preconnect.js | title": ["audits[uses-rel-preconnect].title"], "core/audits/uses-rel-preconnect.js | description": ["audits[uses-rel-preconnect].description"], "core/lib/i18n/i18n.js | displayValueMsSavings": [{"values": {"wastedMs": 501.1704}, "path": "audits[uses-rel-preconnect].displayValue"}, {"values": {"wastedMs": 1350}, "path": "audits[render-blocking-resources].displayValue"}], "core/lib/i18n/i18n.js | columnWastedBytes": ["audits[uses-rel-preconnect].details.headings[1].label", "audits[render-blocking-resources].details.headings[2].label", "audits[unused-css-rules].details.headings[2].label", "audits[unused-javascript].details.headings[2].label", "audits[legacy-javascript].details.headings[2].label", "audits[render-blocking-insight].details.headings[2].label"], "core/audits/font-display.js | title": ["audits[font-display].title"], "core/audits/font-display.js | description": ["audits[font-display].description"], "core/audits/network-rtt.js | title": ["audits[network-rtt].title"], "core/audits/network-rtt.js | description": ["audits[network-rtt].description"], "core/audits/network-server-latency.js | title": ["audits[network-server-latency].title"], "core/audits/network-server-latency.js | description": ["audits[network-server-latency].description"], "core/lib/i18n/i18n.js | columnResourceType": ["audits[resource-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnRequests": ["audits[resource-summary].details.headings[1].label"], "core/lib/i18n/i18n.js | columnTransferSize": ["audits[resource-summary].details.headings[2].label", "audits[third-party-summary].details.headings[1].label", "audits[uses-long-cache-ttl].details.headings[2].label", "audits[total-byte-weight].details.headings[1].label", "audits[render-blocking-resources].details.headings[1].label", "audits[unused-css-rules].details.headings[1].label", "audits[unused-javascript].details.headings[1].label", "audits[cache-insight].details.headings[2].label", "audits[render-blocking-insight].details.headings[1].label"], "core/lib/i18n/i18n.js | total": ["audits[resource-summary].details.items[0].label"], "core/lib/i18n/i18n.js | scriptResourceType": ["audits[resource-summary].details.items[1].label"], "core/lib/i18n/i18n.js | fontResourceType": ["audits[resource-summary].details.items[2].label"], "core/lib/i18n/i18n.js | stylesheetResourceType": ["audits[resource-summary].details.items[3].label"], "core/lib/i18n/i18n.js | documentResourceType": ["audits[resource-summary].details.items[4].label"], "core/lib/i18n/i18n.js | imageResourceType": ["audits[resource-summary].details.items[5].label"], "core/lib/i18n/i18n.js | mediaResourceType": ["audits[resource-summary].details.items[6].label"], "core/lib/i18n/i18n.js | otherResourceType": ["audits[resource-summary].details.items[7].label"], "core/lib/i18n/i18n.js | thirdPartyResourceType": ["audits[resource-summary].details.items[8].label"], "core/audits/third-party-summary.js | title": ["audits[third-party-summary].title"], "core/audits/third-party-summary.js | description": ["audits[third-party-summary].description"], "core/audits/third-party-summary.js | displayValue": [{"values": {"timeInMs": 0}, "path": "audits[third-party-summary].displayValue"}], "core/audits/third-party-summary.js | columnThirdParty": ["audits[third-party-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnBlockingTime": ["audits[third-party-summary].details.headings[2].label"], "core/audits/third-party-facades.js | title": ["audits[third-party-facades].title"], "core/audits/third-party-facades.js | description": ["audits[third-party-facades].description"], "core/audits/largest-contentful-paint-element.js | title": ["audits[largest-contentful-paint-element].title"], "core/audits/largest-contentful-paint-element.js | description": ["audits[largest-contentful-paint-element].description"], "core/lib/i18n/i18n.js | columnElement": ["audits[largest-contentful-paint-element].details.items[0].headings[0].label", "audits[non-composited-animations].details.headings[0].label", "audits[dom-size].details.headings[1].label", "audits[dom-size-insight].details.headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnPhase": ["audits[largest-contentful-paint-element].details.items[1].headings[0].label"], "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": ["audits[largest-contentful-paint-element].details.items[1].headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnTiming": ["audits[largest-contentful-paint-element].details.items[1].headings[2].label"], "core/audits/largest-contentful-paint-element.js | itemTTFB": ["audits[largest-contentful-paint-element].details.items[1].items[0].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadDelay": ["audits[largest-contentful-paint-element].details.items[1].items[1].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadTime": ["audits[largest-contentful-paint-element].details.items[1].items[2].phase"], "core/audits/largest-contentful-paint-element.js | itemRenderDelay": ["audits[largest-contentful-paint-element].details.items[1].items[3].phase"], "core/audits/lcp-lazy-loaded.js | title": ["audits[lcp-lazy-loaded].title"], "core/audits/lcp-lazy-loaded.js | description": ["audits[lcp-lazy-loaded].description"], "core/audits/layout-shifts.js | title": ["audits[layout-shifts].title"], "core/audits/layout-shifts.js | description": ["audits[layout-shifts].description"], "core/audits/long-tasks.js | title": ["audits[long-tasks].title"], "core/audits/long-tasks.js | description": ["audits[long-tasks].description"], "core/audits/long-tasks.js | displayValue": [{"values": {"itemCount": 2}, "path": "audits[long-tasks].displayValue"}], "core/lib/i18n/i18n.js | columnStartTime": ["audits[long-tasks].details.headings[1].label"], "core/lib/i18n/i18n.js | columnDuration": ["audits[long-tasks].details.headings[2].label", "audits[lcp-phases-insight].details.items[0].headings[1].label"], "core/audits/non-composited-animations.js | title": ["audits[non-composited-animations].title"], "core/audits/non-composited-animations.js | description": ["audits[non-composited-animations].description"], "core/audits/non-composited-animations.js | displayValue": [{"values": {"itemCount": 1}, "path": "audits[non-composited-animations].displayValue"}], "core/lib/i18n/i18n.js | columnName": ["audits[non-composited-animations].details.headings[1].label"], "core/audits/non-composited-animations.js | unsupportedCSSProperty": [{"values": {"propertyCount": 1, "properties": "border-bottom-color"}, "path": "audits[non-composited-animations].details.items[0].subItems.items[0].failureReason"}, {"values": {"propertyCount": 1, "properties": "border-left-color"}, "path": "audits[non-composited-animations].details.items[0].subItems.items[1].failureReason"}, {"values": {"propertyCount": 1, "properties": "border-right-color"}, "path": "audits[non-composited-animations].details.items[0].subItems.items[2].failureReason"}, {"values": {"propertyCount": 1, "properties": "border-top-color"}, "path": "audits[non-composited-animations].details.items[0].subItems.items[3].failureReason"}], "core/audits/unsized-images.js | title": ["audits[unsized-images].title"], "core/audits/unsized-images.js | description": ["audits[unsized-images].description"], "core/audits/valid-source-maps.js | failureTitle": ["audits[valid-source-maps].title"], "core/audits/valid-source-maps.js | description": ["audits[valid-source-maps].description"], "core/audits/valid-source-maps.js | columnMapURL": ["audits[valid-source-maps].details.headings[1].label"], "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": ["audits[valid-source-maps].details.items[0].subItems.items[0].error"], "core/audits/prioritize-lcp-image.js | title": ["audits[prioritize-lcp-image].title"], "core/audits/prioritize-lcp-image.js | description": ["audits[prioritize-lcp-image].description"], "core/audits/csp-xss.js | title": ["audits[csp-xss].title"], "core/audits/csp-xss.js | description": ["audits[csp-xss].description"], "core/audits/csp-xss.js | columnDirective": ["audits[csp-xss].details.headings[1].label"], "core/audits/csp-xss.js | columnSeverity": ["audits[csp-xss].details.headings[2].label"], "core/lib/i18n/i18n.js | itemSeverityHigh": ["audits[csp-xss].details.items[0].severity", "audits[has-hsts].details.items[0].severity", "audits[origin-isolation].details.items[0].severity", "audits[clickjacking-mitigation].details.items[0].severity"], "core/audits/csp-xss.js | noCsp": ["audits[csp-xss].details.items[0].description"], "core/audits/has-hsts.js | title": ["audits[has-hsts].title"], "core/audits/has-hsts.js | description": ["audits[has-hsts].description"], "core/audits/has-hsts.js | columnDirective": ["audits[has-hsts].details.headings[1].label"], "core/audits/has-hsts.js | columnSeverity": ["audits[has-hsts].details.headings[2].label"], "core/audits/has-hsts.js | noHsts": ["audits[has-hsts].details.items[0].description"], "core/audits/origin-isolation.js | title": ["audits[origin-isolation].title"], "core/audits/origin-isolation.js | description": ["audits[origin-isolation].description"], "core/audits/origin-isolation.js | columnDirective": ["audits[origin-isolation].details.headings[1].label"], "core/audits/origin-isolation.js | columnSeverity": ["audits[origin-isolation].details.headings[2].label"], "core/audits/origin-isolation.js | noCoop": ["audits[origin-isolation].details.items[0].description"], "core/audits/clickjacking-mitigation.js | title": ["audits[clickjacking-mitigation].title"], "core/audits/clickjacking-mitigation.js | description": ["audits[clickjacking-mitigation].description"], "core/audits/clickjacking-mitigation.js | columnSeverity": ["audits[clickjacking-mitigation].details.headings[1].label"], "core/audits/clickjacking-mitigation.js | noClickjackingMitigation": ["audits[clickjacking-mitigation].details.items[0].description"], "core/audits/accessibility/accesskeys.js | title": ["audits.accesskeys.title"], "core/audits/accessibility/accesskeys.js | description": ["audits.accesskeys.description"], "core/audits/accessibility/aria-allowed-attr.js | title": ["audits[aria-allowed-attr].title"], "core/audits/accessibility/aria-allowed-attr.js | description": ["audits[aria-allowed-attr].description"], "core/audits/accessibility/aria-allowed-role.js | title": ["audits[aria-allowed-role].title"], "core/audits/accessibility/aria-allowed-role.js | description": ["audits[aria-allowed-role].description"], "core/audits/accessibility/aria-command-name.js | title": ["audits[aria-command-name].title"], "core/audits/accessibility/aria-command-name.js | description": ["audits[aria-command-name].description"], "core/audits/accessibility/aria-conditional-attr.js | title": ["audits[aria-conditional-attr].title"], "core/audits/accessibility/aria-conditional-attr.js | description": ["audits[aria-conditional-attr].description"], "core/audits/accessibility/aria-deprecated-role.js | title": ["audits[aria-deprecated-role].title"], "core/audits/accessibility/aria-deprecated-role.js | description": ["audits[aria-deprecated-role].description"], "core/audits/accessibility/aria-dialog-name.js | title": ["audits[aria-dialog-name].title"], "core/audits/accessibility/aria-dialog-name.js | description": ["audits[aria-dialog-name].description"], "core/audits/accessibility/aria-hidden-body.js | title": ["audits[aria-hidden-body].title"], "core/audits/accessibility/aria-hidden-body.js | description": ["audits[aria-hidden-body].description"], "core/audits/accessibility/aria-hidden-focus.js | title": ["audits[aria-hidden-focus].title"], "core/audits/accessibility/aria-hidden-focus.js | description": ["audits[aria-hidden-focus].description"], "core/audits/accessibility/aria-input-field-name.js | title": ["audits[aria-input-field-name].title"], "core/audits/accessibility/aria-input-field-name.js | description": ["audits[aria-input-field-name].description"], "core/audits/accessibility/aria-meter-name.js | title": ["audits[aria-meter-name].title"], "core/audits/accessibility/aria-meter-name.js | description": ["audits[aria-meter-name].description"], "core/audits/accessibility/aria-progressbar-name.js | title": ["audits[aria-progressbar-name].title"], "core/audits/accessibility/aria-progressbar-name.js | description": ["audits[aria-progressbar-name].description"], "core/audits/accessibility/aria-prohibited-attr.js | title": ["audits[aria-prohibited-attr].title"], "core/audits/accessibility/aria-prohibited-attr.js | description": ["audits[aria-prohibited-attr].description"], "core/audits/accessibility/aria-required-attr.js | title": ["audits[aria-required-attr].title"], "core/audits/accessibility/aria-required-attr.js | description": ["audits[aria-required-attr].description"], "core/audits/accessibility/aria-required-children.js | title": ["audits[aria-required-children].title"], "core/audits/accessibility/aria-required-children.js | description": ["audits[aria-required-children].description"], "core/audits/accessibility/aria-required-parent.js | title": ["audits[aria-required-parent].title"], "core/audits/accessibility/aria-required-parent.js | description": ["audits[aria-required-parent].description"], "core/audits/accessibility/aria-roles.js | title": ["audits[aria-roles].title"], "core/audits/accessibility/aria-roles.js | description": ["audits[aria-roles].description"], "core/audits/accessibility/aria-text.js | title": ["audits[aria-text].title"], "core/audits/accessibility/aria-text.js | description": ["audits[aria-text].description"], "core/audits/accessibility/aria-toggle-field-name.js | title": ["audits[aria-toggle-field-name].title"], "core/audits/accessibility/aria-toggle-field-name.js | description": ["audits[aria-toggle-field-name].description"], "core/audits/accessibility/aria-tooltip-name.js | title": ["audits[aria-tooltip-name].title"], "core/audits/accessibility/aria-tooltip-name.js | description": ["audits[aria-tooltip-name].description"], "core/audits/accessibility/aria-treeitem-name.js | title": ["audits[aria-treeitem-name].title"], "core/audits/accessibility/aria-treeitem-name.js | description": ["audits[aria-treeitem-name].description"], "core/audits/accessibility/aria-valid-attr-value.js | title": ["audits[aria-valid-attr-value].title"], "core/audits/accessibility/aria-valid-attr-value.js | description": ["audits[aria-valid-attr-value].description"], "core/audits/accessibility/aria-valid-attr.js | title": ["audits[aria-valid-attr].title"], "core/audits/accessibility/aria-valid-attr.js | description": ["audits[aria-valid-attr].description"], "core/audits/accessibility/button-name.js | failureTitle": ["audits[button-name].title"], "core/audits/accessibility/button-name.js | description": ["audits[button-name].description"], "core/lib/i18n/i18n.js | columnFailingElem": ["audits[button-name].details.headings[0].label", "audits[color-contrast].details.headings[0].label", "audits[target-size].details.headings[0].label"], "core/audits/accessibility/bypass.js | title": ["audits.bypass.title"], "core/audits/accessibility/bypass.js | description": ["audits.bypass.description"], "core/audits/accessibility/color-contrast.js | failureTitle": ["audits[color-contrast].title"], "core/audits/accessibility/color-contrast.js | description": ["audits[color-contrast].description"], "core/audits/accessibility/definition-list.js | title": ["audits[definition-list].title"], "core/audits/accessibility/definition-list.js | description": ["audits[definition-list].description"], "core/audits/accessibility/dlitem.js | title": ["audits.dlitem.title"], "core/audits/accessibility/dlitem.js | description": ["audits.dlitem.description"], "core/audits/accessibility/document-title.js | title": ["audits[document-title].title"], "core/audits/accessibility/document-title.js | description": ["audits[document-title].description"], "core/audits/accessibility/duplicate-id-aria.js | title": ["audits[duplicate-id-aria].title"], "core/audits/accessibility/duplicate-id-aria.js | description": ["audits[duplicate-id-aria].description"], "core/audits/accessibility/empty-heading.js | title": ["audits[empty-heading].title"], "core/audits/accessibility/empty-heading.js | description": ["audits[empty-heading].description"], "core/audits/accessibility/form-field-multiple-labels.js | title": ["audits[form-field-multiple-labels].title"], "core/audits/accessibility/form-field-multiple-labels.js | description": ["audits[form-field-multiple-labels].description"], "core/audits/accessibility/frame-title.js | title": ["audits[frame-title].title"], "core/audits/accessibility/frame-title.js | description": ["audits[frame-title].description"], "core/audits/accessibility/heading-order.js | title": ["audits[heading-order].title"], "core/audits/accessibility/heading-order.js | description": ["audits[heading-order].description"], "core/audits/accessibility/html-has-lang.js | title": ["audits[html-has-lang].title"], "core/audits/accessibility/html-has-lang.js | description": ["audits[html-has-lang].description"], "core/audits/accessibility/html-lang-valid.js | title": ["audits[html-lang-valid].title"], "core/audits/accessibility/html-lang-valid.js | description": ["audits[html-lang-valid].description"], "core/audits/accessibility/html-xml-lang-mismatch.js | title": ["audits[html-xml-lang-mismatch].title"], "core/audits/accessibility/html-xml-lang-mismatch.js | description": ["audits[html-xml-lang-mismatch].description"], "core/audits/accessibility/identical-links-same-purpose.js | title": ["audits[identical-links-same-purpose].title"], "core/audits/accessibility/identical-links-same-purpose.js | description": ["audits[identical-links-same-purpose].description"], "core/audits/accessibility/image-alt.js | title": ["audits[image-alt].title"], "core/audits/accessibility/image-alt.js | description": ["audits[image-alt].description"], "core/audits/accessibility/image-redundant-alt.js | title": ["audits[image-redundant-alt].title"], "core/audits/accessibility/image-redundant-alt.js | description": ["audits[image-redundant-alt].description"], "core/audits/accessibility/input-button-name.js | title": ["audits[input-button-name].title"], "core/audits/accessibility/input-button-name.js | description": ["audits[input-button-name].description"], "core/audits/accessibility/input-image-alt.js | title": ["audits[input-image-alt].title"], "core/audits/accessibility/input-image-alt.js | description": ["audits[input-image-alt].description"], "core/audits/accessibility/label-content-name-mismatch.js | title": ["audits[label-content-name-mismatch].title"], "core/audits/accessibility/label-content-name-mismatch.js | description": ["audits[label-content-name-mismatch].description"], "core/audits/accessibility/label.js | title": ["audits.label.title"], "core/audits/accessibility/label.js | description": ["audits.label.description"], "core/audits/accessibility/landmark-one-main.js | title": ["audits[landmark-one-main].title"], "core/audits/accessibility/landmark-one-main.js | description": ["audits[landmark-one-main].description"], "core/audits/accessibility/link-name.js | title": ["audits[link-name].title"], "core/audits/accessibility/link-name.js | description": ["audits[link-name].description"], "core/audits/accessibility/link-in-text-block.js | title": ["audits[link-in-text-block].title"], "core/audits/accessibility/link-in-text-block.js | description": ["audits[link-in-text-block].description"], "core/audits/accessibility/list.js | title": ["audits.list.title"], "core/audits/accessibility/list.js | description": ["audits.list.description"], "core/audits/accessibility/listitem.js | title": ["audits.listitem.title"], "core/audits/accessibility/listitem.js | description": ["audits.listitem.description"], "core/audits/accessibility/meta-refresh.js | title": ["audits[meta-refresh].title"], "core/audits/accessibility/meta-refresh.js | description": ["audits[meta-refresh].description"], "core/audits/accessibility/meta-viewport.js | title": ["audits[meta-viewport].title"], "core/audits/accessibility/meta-viewport.js | description": ["audits[meta-viewport].description"], "core/audits/accessibility/object-alt.js | title": ["audits[object-alt].title"], "core/audits/accessibility/object-alt.js | description": ["audits[object-alt].description"], "core/audits/accessibility/select-name.js | title": ["audits[select-name].title"], "core/audits/accessibility/select-name.js | description": ["audits[select-name].description"], "core/audits/accessibility/skip-link.js | title": ["audits[skip-link].title"], "core/audits/accessibility/skip-link.js | description": ["audits[skip-link].description"], "core/audits/accessibility/tabindex.js | title": ["audits.tabindex.title"], "core/audits/accessibility/tabindex.js | description": ["audits.tabindex.description"], "core/audits/accessibility/table-duplicate-name.js | title": ["audits[table-duplicate-name].title"], "core/audits/accessibility/table-duplicate-name.js | description": ["audits[table-duplicate-name].description"], "core/audits/accessibility/table-fake-caption.js | title": ["audits[table-fake-caption].title"], "core/audits/accessibility/table-fake-caption.js | description": ["audits[table-fake-caption].description"], "core/audits/accessibility/target-size.js | failureTitle": ["audits[target-size].title"], "core/audits/accessibility/target-size.js | description": ["audits[target-size].description"], "core/audits/accessibility/td-has-header.js | title": ["audits[td-has-header].title"], "core/audits/accessibility/td-has-header.js | description": ["audits[td-has-header].description"], "core/audits/accessibility/td-headers-attr.js | title": ["audits[td-headers-attr].title"], "core/audits/accessibility/td-headers-attr.js | description": ["audits[td-headers-attr].description"], "core/audits/accessibility/th-has-data-cells.js | title": ["audits[th-has-data-cells].title"], "core/audits/accessibility/th-has-data-cells.js | description": ["audits[th-has-data-cells].description"], "core/audits/accessibility/valid-lang.js | title": ["audits[valid-lang].title"], "core/audits/accessibility/valid-lang.js | description": ["audits[valid-lang].description"], "core/audits/accessibility/video-caption.js | title": ["audits[video-caption].title"], "core/audits/accessibility/video-caption.js | description": ["audits[video-caption].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": ["audits[uses-long-cache-ttl].title"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": ["audits[uses-long-cache-ttl].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": [{"values": {"itemCount": 1}, "path": "audits[uses-long-cache-ttl].displayValue"}], "core/lib/i18n/i18n.js | columnCacheTTL": ["audits[uses-long-cache-ttl].details.headings[1].label", "audits[cache-insight].details.headings[1].label"], "core/audits/byte-efficiency/total-byte-weight.js | title": ["audits[total-byte-weight].title"], "core/audits/byte-efficiency/total-byte-weight.js | description": ["audits[total-byte-weight].description"], "core/audits/byte-efficiency/total-byte-weight.js | displayValue": [{"values": {"totalBytes": 894763}, "path": "audits[total-byte-weight].displayValue"}], "core/audits/byte-efficiency/offscreen-images.js | title": ["audits[offscreen-images].title"], "core/audits/byte-efficiency/offscreen-images.js | description": ["audits[offscreen-images].description"], "core/audits/byte-efficiency/render-blocking-resources.js | title": ["audits[render-blocking-resources].title"], "core/audits/byte-efficiency/render-blocking-resources.js | description": ["audits[render-blocking-resources].description"], "core/audits/byte-efficiency/unminified-css.js | title": ["audits[unminified-css].title"], "core/audits/byte-efficiency/unminified-css.js | description": ["audits[unminified-css].description"], "core/audits/byte-efficiency/unminified-javascript.js | title": ["audits[unminified-javascript].title"], "core/audits/byte-efficiency/unminified-javascript.js | description": ["audits[unminified-javascript].description"], "core/audits/byte-efficiency/unused-css-rules.js | title": ["audits[unused-css-rules].title"], "core/audits/byte-efficiency/unused-css-rules.js | description": ["audits[unused-css-rules].description"], "core/lib/i18n/i18n.js | displayValueByteSavings": [{"values": {"wastedBytes": 19471}, "path": "audits[unused-css-rules].displayValue"}, {"values": {"wastedBytes": 631629}, "path": "audits[unused-javascript].displayValue"}, {"values": {"wastedBytes": 586}, "path": "audits[legacy-javascript].displayValue"}, {"values": {"wastedBytes": 157.76}, "path": "audits[cache-insight].displayValue"}], "core/audits/byte-efficiency/unused-javascript.js | title": ["audits[unused-javascript].title"], "core/audits/byte-efficiency/unused-javascript.js | description": ["audits[unused-javascript].description"], "core/audits/byte-efficiency/modern-image-formats.js | title": ["audits[modern-image-formats].title"], "core/audits/byte-efficiency/modern-image-formats.js | description": ["audits[modern-image-formats].description"], "core/audits/byte-efficiency/uses-optimized-images.js | title": ["audits[uses-optimized-images].title"], "core/audits/byte-efficiency/uses-optimized-images.js | description": ["audits[uses-optimized-images].description"], "core/audits/byte-efficiency/uses-text-compression.js | title": ["audits[uses-text-compression].title"], "core/audits/byte-efficiency/uses-text-compression.js | description": ["audits[uses-text-compression].description"], "core/audits/byte-efficiency/uses-responsive-images.js | title": ["audits[uses-responsive-images].title"], "core/audits/byte-efficiency/uses-responsive-images.js | description": ["audits[uses-responsive-images].description"], "core/audits/byte-efficiency/efficient-animated-content.js | title": ["audits[efficient-animated-content].title"], "core/audits/byte-efficiency/efficient-animated-content.js | description": ["audits[efficient-animated-content].description"], "core/audits/byte-efficiency/duplicated-javascript.js | title": ["audits[duplicated-javascript].title"], "core/audits/byte-efficiency/duplicated-javascript.js | description": ["audits[duplicated-javascript].description"], "core/audits/byte-efficiency/legacy-javascript.js | title": ["audits[legacy-javascript].title"], "core/audits/byte-efficiency/legacy-javascript.js | description": ["audits[legacy-javascript].description"], "core/audits/dobetterweb/doctype.js | title": ["audits.doctype.title"], "core/audits/dobetterweb/doctype.js | description": ["audits.doctype.description"], "core/audits/dobetterweb/charset.js | title": ["audits.charset.title"], "core/audits/dobetterweb/charset.js | description": ["audits.charset.description"], "core/audits/dobetterweb/dom-size.js | title": ["audits[dom-size].title"], "core/audits/dobetterweb/dom-size.js | description": ["audits[dom-size].description"], "core/audits/dobetterweb/dom-size.js | displayValue": [{"values": {"itemCount": 50}, "path": "audits[dom-size].displayValue"}], "core/audits/dobetterweb/dom-size.js | columnStatistic": ["audits[dom-size].details.headings[0].label"], "core/audits/dobetterweb/dom-size.js | columnValue": ["audits[dom-size].details.headings[2].label"], "core/audits/dobetterweb/dom-size.js | statisticDOMElements": ["audits[dom-size].details.items[0].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": ["audits[dom-size].details.items[1].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": ["audits[dom-size].details.items[2].statistic"], "core/audits/dobetterweb/geolocation-on-start.js | title": ["audits[geolocation-on-start].title"], "core/audits/dobetterweb/geolocation-on-start.js | description": ["audits[geolocation-on-start].description"], "core/audits/dobetterweb/inspector-issues.js | title": ["audits[inspector-issues].title"], "core/audits/dobetterweb/inspector-issues.js | description": ["audits[inspector-issues].description"], "core/audits/dobetterweb/no-document-write.js | title": ["audits[no-document-write].title"], "core/audits/dobetterweb/no-document-write.js | description": ["audits[no-document-write].description"], "core/audits/dobetterweb/js-libraries.js | title": ["audits[js-libraries].title"], "core/audits/dobetterweb/js-libraries.js | description": ["audits[js-libraries].description"], "core/audits/dobetterweb/notification-on-start.js | title": ["audits[notification-on-start].title"], "core/audits/dobetterweb/notification-on-start.js | description": ["audits[notification-on-start].description"], "core/audits/dobetterweb/paste-preventing-inputs.js | title": ["audits[paste-preventing-inputs].title"], "core/audits/dobetterweb/paste-preventing-inputs.js | description": ["audits[paste-preventing-inputs].description"], "core/audits/dobetterweb/uses-http2.js | title": ["audits[uses-http2].title"], "core/audits/dobetterweb/uses-http2.js | description": ["audits[uses-http2].description"], "core/audits/dobetterweb/uses-passive-event-listeners.js | title": ["audits[uses-passive-event-listeners].title"], "core/audits/dobetterweb/uses-passive-event-listeners.js | description": ["audits[uses-passive-event-listeners].description"], "core/audits/seo/meta-description.js | title": ["audits[meta-description].title"], "core/audits/seo/meta-description.js | description": ["audits[meta-description].description"], "core/audits/seo/http-status-code.js | title": ["audits[http-status-code].title"], "core/audits/seo/http-status-code.js | description": ["audits[http-status-code].description"], "core/audits/seo/font-size.js | title": ["audits[font-size].title"], "core/audits/seo/font-size.js | description": ["audits[font-size].description"], "core/audits/seo/font-size.js | displayValue": [{"values": {"decimalProportion": 1}, "path": "audits[font-size].displayValue"}], "core/audits/seo/font-size.js | columnSelector": ["audits[font-size].details.headings[1].label"], "core/audits/seo/font-size.js | columnPercentPageText": ["audits[font-size].details.headings[2].label"], "core/audits/seo/font-size.js | columnFontSize": ["audits[font-size].details.headings[3].label"], "core/audits/seo/font-size.js | legibleText": ["audits[font-size].details.items[0].source.value"], "core/audits/seo/link-text.js | title": ["audits[link-text].title"], "core/audits/seo/link-text.js | description": ["audits[link-text].description"], "core/audits/seo/crawlable-anchors.js | title": ["audits[crawlable-anchors].title"], "core/audits/seo/crawlable-anchors.js | description": ["audits[crawlable-anchors].description"], "core/audits/seo/is-crawlable.js | title": ["audits[is-crawlable].title"], "core/audits/seo/is-crawlable.js | description": ["audits[is-crawlable].description"], "core/audits/seo/robots-txt.js | failureTitle": ["audits[robots-txt].title"], "core/audits/seo/robots-txt.js | description": ["audits[robots-txt].description"], "core/audits/seo/robots-txt.js | displayValueValidationError": [{"values": {"itemCount": 18}, "path": "audits[robots-txt].displayValue"}], "core/audits/seo/hreflang.js | title": ["audits.hreflang.title"], "core/audits/seo/hreflang.js | description": ["audits.hreflang.description"], "core/audits/seo/canonical.js | title": ["audits.canonical.title"], "core/audits/seo/canonical.js | description": ["audits.canonical.description"], "core/audits/seo/manual/structured-data.js | title": ["audits[structured-data].title"], "core/audits/seo/manual/structured-data.js | description": ["audits[structured-data].description"], "core/audits/bf-cache.js | title": ["audits[bf-cache].title"], "core/audits/bf-cache.js | description": ["audits[bf-cache].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title": ["audits[cache-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description": ["audits[cache-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | requestColumn": ["audits[cache-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title": ["audits[cls-culprits-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description": ["audits[cls-culprits-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title": ["audits[document-latency-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description": ["audits[document-latency-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingRedirects": ["audits[document-latency-insight].details.items.noRedirects.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingServerResponseTime": [{"values": {"PH1": "1 ms"}, "path": "audits[document-latency-insight].details.items.serverResponseIsFast.label"}], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingTextCompression": ["audits[document-latency-insight].details.items.usesCompression.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title": ["audits[dom-size-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description": ["audits[dom-size-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | statistic": ["audits[dom-size-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | value": ["audits[dom-size-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | totalElements": ["audits[dom-size-insight].details.items[0].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxChildren": ["audits[dom-size-insight].details.items[1].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxDOMDepth": ["audits[dom-size-insight].details.items[2].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title": ["audits[duplicated-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description": ["audits[duplicated-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title": ["audits[font-display-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description": ["audits[font-display-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title": ["audits[forced-reflow-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description": ["audits[forced-reflow-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title": ["audits[image-delivery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description": ["audits[image-delivery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | title": ["audits[interaction-to-next-paint-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | description": ["audits[interaction-to-next-paint-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title": ["audits[lcp-discovery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description": ["audits[lcp-discovery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | title": ["audits[lcp-phases-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | description": ["audits[lcp-phases-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | phase": ["audits[lcp-phases-insight].details.items[0].headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | timeToFirstByte": ["audits[lcp-phases-insight].details.items[0].items[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | elementRenderDelay": ["audits[lcp-phases-insight].details.items[0].items[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title": ["audits[legacy-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description": ["audits[legacy-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title": ["audits[modern-http-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description": ["audits[modern-http-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title": ["audits[network-dependency-tree-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description": ["audits[network-dependency-tree-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title": ["audits[render-blocking-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description": ["audits[render-blocking-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title": ["audits[third-parties-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description": ["audits[third-parties-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnThirdParty": ["audits[third-parties-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnTransferSize": ["audits[third-parties-insight].details.headings[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | columnMainThreadTime": ["audits[third-parties-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title": ["audits[viewport-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description": ["audits[viewport-insight].description"], "core/config/default-config.js | performanceCategoryTitle": ["categories.performance.title"], "core/config/default-config.js | a11yCategoryTitle": ["categories.accessibility.title"], "core/config/default-config.js | a11yCategoryDescription": ["categories.accessibility.description"], "core/config/default-config.js | a11yCategoryManualDescription": ["categories.accessibility.manualDescription"], "core/config/default-config.js | bestPracticesCategoryTitle": ["categories[best-practices].title"], "core/config/default-config.js | seoCategoryTitle": ["categories.seo.title"], "core/config/default-config.js | seoCategoryDescription": ["categories.seo.description"], "core/config/default-config.js | seoCategoryManualDescription": ["categories.seo.manualDescription"], "core/config/default-config.js | metricGroupTitle": ["categoryGroups.metrics.title"], "core/config/default-config.js | insightsGroupTitle": ["categoryGroups.insights.title"], "core/config/default-config.js | insightsGroupDescription": ["categoryGroups.insights.description"], "core/config/default-config.js | diagnosticsGroupTitle": ["categoryGroups.diagnostics.title"], "core/config/default-config.js | diagnosticsGroupDescription": ["categoryGroups.diagnostics.description"], "core/config/default-config.js | a11yBestPracticesGroupTitle": ["categoryGroups[a11y-best-practices].title"], "core/config/default-config.js | a11yBestPracticesGroupDescription": ["categoryGroups[a11y-best-practices].description"], "core/config/default-config.js | a11yColorContrastGroupTitle": ["categoryGroups[a11y-color-contrast].title"], "core/config/default-config.js | a11yColorContrastGroupDescription": ["categoryGroups[a11y-color-contrast].description"], "core/config/default-config.js | a11yNamesLabelsGroupTitle": ["categoryGroups[a11y-names-labels].title"], "core/config/default-config.js | a11yNamesLabelsGroupDescription": ["categoryGroups[a11y-names-labels].description"], "core/config/default-config.js | a11yNavigationGroupTitle": ["categoryGroups[a11y-navigation].title"], "core/config/default-config.js | a11yNavigationGroupDescription": ["categoryGroups[a11y-navigation].description"], "core/config/default-config.js | a11yAriaGroupTitle": ["categoryGroups[a11y-aria].title"], "core/config/default-config.js | a11yAriaGroupDescription": ["categoryGroups[a11y-aria].description"], "core/config/default-config.js | a11yLanguageGroupTitle": ["categoryGroups[a11y-language].title"], "core/config/default-config.js | a11yLanguageGroupDescription": ["categoryGroups[a11y-language].description"], "core/config/default-config.js | a11yAudioVideoGroupTitle": ["categoryGroups[a11y-audio-video].title"], "core/config/default-config.js | a11yAudioVideoGroupDescription": ["categoryGroups[a11y-audio-video].description"], "core/config/default-config.js | a11yTablesListsVideoGroupTitle": ["categoryGroups[a11y-tables-lists].title"], "core/config/default-config.js | a11yTablesListsVideoGroupDescription": ["categoryGroups[a11y-tables-lists].description"], "core/config/default-config.js | seoMobileGroupTitle": ["categoryGroups[seo-mobile].title"], "core/config/default-config.js | seoMobileGroupDescription": ["categoryGroups[seo-mobile].description"], "core/config/default-config.js | seoContentGroupTitle": ["categoryGroups[seo-content].title"], "core/config/default-config.js | seoContentGroupDescription": ["categoryGroups[seo-content].description"], "core/config/default-config.js | seoCrawlingGroupTitle": ["categoryGroups[seo-crawl].title"], "core/config/default-config.js | seoCrawlingGroupDescription": ["categoryGroups[seo-crawl].description"], "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": ["categoryGroups[best-practices-trust-safety].title"], "core/config/default-config.js | bestPracticesUXGroupTitle": ["categoryGroups[best-practices-ux].title"], "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": ["categoryGroups[best-practices-browser-compat].title"], "core/config/default-config.js | bestPracticesGeneralGroupTitle": ["categoryGroups[best-practices-general].title"]}}}