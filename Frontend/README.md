# PHCityRent Frontend

React + TypeScript frontend application for the PHCityRent platform.

## 🚀 Quick Start

```bash
npm install
npm run dev
```

## 📁 Project Structure

```
Frontend/
├── src/
│   ├── components/     # Reusable UI components
│   ├── pages/         # Page components
│   ├── hooks/         # Custom React hooks
│   ├── services/      # API services
│   ├── utils/         # Utility functions
│   ├── types/         # TypeScript type definitions
│   └── contexts/      # React contexts
├── public/            # Static assets
├── docs/              # Documentation
├── tests/             # Test files
└── config/            # Configuration files
```

## 🛠️ Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run test` - Run tests
- `npm run lint` - Lint code
- `npm run preview` - Preview production build

## 📚 Documentation

See the `docs/` directory for detailed documentation.
