# 🧪 PHCityRent Testing Guide

## Overview

PHCityRent uses a comprehensive testing strategy that includes unit tests, integration tests, end-to-end tests, and performance testing to ensure high-quality, reliable software.

## Testing Stack

- **Unit/Integration Tests**: Jest + React Testing Library
- **E2E Tests**: Playwright
- **Performance Tests**: Playwright + Custom metrics
- **Mocking**: <PERSON><PERSON> (Mock Service Worker)
- **Coverage**: Jest Coverage + Codecov
- **CI/CD**: GitHub Actions

## Test Structure

```
src/tests/
├── unit/                    # Unit tests
│   ├── components/         # Component tests
│   ├── hooks/             # Hook tests
│   ├── services/          # Service layer tests
│   └── utils/             # Utility function tests
├── integration/            # Integration tests
│   ├── api/               # API integration tests
│   ├── database/          # Database tests
│   └── workflows/         # End-to-end workflows
├── e2e/                   # End-to-end tests
│   ├── user-journey.spec.ts
│   ├── auth.spec.ts
│   └── mobile.spec.ts
├── performance/           # Performance tests
│   ├── load-testing.spec.ts
│   └── memory-testing.spec.ts
├── setup/                 # Test configuration
│   ├── jest.setup.ts
│   ├── test-utils.tsx
│   └── global-setup.ts
├── mocks/                 # Mock implementations
│   ├── server.ts          # MSW server
│   └── handlers.ts        # API handlers
└── fixtures/              # Test data
    ├── properties.json
    └── users.json
```

## Running Tests

### All Tests
```bash
npm run test:all          # Run all test suites
npm run test:ci           # Run tests in CI mode
```

### Unit Tests
```bash
npm run test              # Run unit tests
npm run test:watch        # Run in watch mode
npm run test:coverage     # Run with coverage
npm run test:unit         # Run only unit tests
```

### Integration Tests
```bash
npm run test:integration  # Run integration tests
```

### E2E Tests
```bash
npm run test:e2e          # Run E2E tests
npm run test:e2e:ui       # Run with UI mode
npm run test:e2e:headed   # Run in headed mode
```

### Performance Tests
```bash
npm run test:performance  # Run performance tests
```

### Mobile Tests
```bash
npm run test:mobile       # Run mobile-specific tests
```

## Writing Tests

### Unit Tests

#### Component Testing
```typescript
import { render, screen, fireEvent } from '@tests/setup/test-utils';
import PropertyCard from '@/components/properties/PropertyCard';
import { mockProperty } from '@tests/setup/test-utils';

describe('PropertyCard', () => {
  it('should render property information', () => {
    render(<PropertyCard property={mockProperty} />);
    
    expect(screen.getByText(mockProperty.title)).toBeInTheDocument();
    expect(screen.getByText(`₦${mockProperty.price.toLocaleString()}`)).toBeInTheDocument();
  });

  it('should handle favorite button click', () => {
    const onFavorite = jest.fn();
    render(<PropertyCard property={mockProperty} onFavorite={onFavorite} />);
    
    fireEvent.click(screen.getByTestId('favorite-button'));
    expect(onFavorite).toHaveBeenCalledWith(mockProperty.id);
  });
});
```

#### Hook Testing
```typescript
import { renderHook, act } from '@testing-library/react';
import { useAuth } from '@/hooks/useAuth';
import { createWrapper } from '@tests/setup/test-utils';

describe('useAuth', () => {
  it('should login successfully', async () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper()
    });

    await act(async () => {
      await result.current.login('<EMAIL>', 'password');
    });

    expect(result.current.isAuthenticated).toBe(true);
  });
});
```

#### Service Testing
```typescript
import { validationService } from '@/services/validationService';

describe('ValidationService', () => {
  it('should validate property data', async () => {
    const validProperty = {
      title: 'Test Property',
      price: 500000,
      location: 'Port Harcourt'
    };

    const result = await validationService.validateProperty(validProperty);
    
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });
});
```

### Integration Tests

```typescript
import { supabase } from '@/lib/supabase';
import { mockProperty } from '@tests/setup/test-utils';

describe('Properties API Integration', () => {
  it('should fetch properties with filters', async () => {
    const { data, error } = await supabase
      .from('properties')
      .select('*')
      .eq('property_type', 'apartment')
      .gte('price', 300000);

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(Array.isArray(data)).toBe(true);
  });
});
```

### E2E Tests

```typescript
import { test, expect } from '@playwright/test';

test('user can search and view properties', async ({ page }) => {
  await page.goto('/');
  
  // Search for properties
  await page.fill('[data-testid="search-input"]', 'apartment');
  await page.click('[data-testid="search-button"]');
  
  // Verify results
  await expect(page.locator('[data-testid="property-card"]').first()).toBeVisible();
  
  // View property details
  await page.click('[data-testid="property-card"]').first();
  await expect(page.locator('[data-testid="property-title"]')).toBeVisible();
});
```

### Performance Tests

```typescript
import { test, expect } from '@playwright/test';

test('homepage loads within performance budget', async ({ page }) => {
  const startTime = Date.now();
  await page.goto('/');
  const loadTime = Date.now() - startTime;
  
  expect(loadTime).toBeLessThan(3000); // 3 second budget
  
  // Check Web Vitals
  const vitals = await page.evaluate(() => {
    return new Promise((resolve) => {
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        resolve(entries);
      }).observe({ entryTypes: ['paint'] });
    });
  });
  
  // Assert performance metrics
});
```

## Test Data Management

### Mock Data
Use the provided mock data utilities:

```typescript
import { 
  mockUser, 
  mockProperty, 
  generateMockProperties 
} from '@tests/setup/test-utils';

// Use single mock
const property = mockProperty;

// Generate multiple mocks
const properties = generateMockProperties(10);
```

### Test Fixtures
Store complex test data in fixture files:

```typescript
// tests/fixtures/properties.json
{
  "apartment": {
    "id": "test-apartment-1",
    "title": "Test Apartment",
    "property_type": "apartment"
  }
}
```

## Mocking

### API Mocking with MSW
```typescript
import { rest } from 'msw';
import { server } from '@tests/mocks/server';

// Override default handler for specific test
server.use(
  rest.get('/api/properties', (req, res, ctx) => {
    return res(ctx.json({ properties: [] }));
  })
);
```

### Component Mocking
```typescript
// Mock external dependencies
jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    user: mockUser,
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn()
  })
}));
```

## Coverage Requirements

- **Statements**: 80% minimum
- **Branches**: 80% minimum  
- **Functions**: 80% minimum
- **Lines**: 80% minimum
- **Services**: 90% minimum (critical business logic)

## Performance Budgets

- **Page Load Time**: < 3 seconds
- **First Contentful Paint**: < 1.5 seconds
- **Largest Contentful Paint**: < 2.5 seconds
- **First Input Delay**: < 100ms
- **Cumulative Layout Shift**: < 0.1
- **Bundle Size**: < 500KB

## Best Practices

### General
- Write descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Test behavior, not implementation
- Keep tests independent and isolated
- Use data-testid for reliable element selection

### Unit Tests
- Mock external dependencies
- Test edge cases and error conditions
- Focus on single units of functionality
- Use meaningful assertions

### Integration Tests
- Test real API interactions
- Verify data flow between components
- Test authentication and authorization
- Validate error handling

### E2E Tests
- Test critical user journeys
- Use page object pattern for complex flows
- Test across different browsers and devices
- Verify accessibility requirements

### Performance Tests
- Set realistic performance budgets
- Test under various network conditions
- Monitor memory usage and leaks
- Test with realistic data volumes

## Debugging Tests

### Jest Debugging
```bash
npm run test:debug        # Run with debugging
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Playwright Debugging
```bash
npm run test:e2e:headed   # Run with browser UI
npx playwright test --debug
```

### Common Issues
- **Async/Await**: Always await async operations
- **Cleanup**: Clean up after tests to prevent interference
- **Timeouts**: Increase timeouts for slow operations
- **Flaky Tests**: Use proper waiting strategies

## CI/CD Integration

Tests run automatically on:
- Pull requests
- Pushes to main/develop branches
- Daily scheduled runs
- Manual triggers

### Quality Gates
- All tests must pass
- Coverage thresholds must be met
- Performance budgets must be maintained
- Security scans must pass

## Reporting

Test results are available in:
- GitHub Actions artifacts
- Coverage reports (Codecov)
- Performance reports
- HTML test reports
- JUnit XML reports

## Contributing

When adding new features:
1. Write tests first (TDD approach)
2. Ensure all tests pass
3. Maintain coverage requirements
4. Update test documentation
5. Add performance tests for critical paths

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/)
- [MSW Documentation](https://mswjs.io/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
