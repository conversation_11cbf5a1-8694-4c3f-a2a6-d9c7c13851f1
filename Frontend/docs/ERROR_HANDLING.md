# Error Handling System Documentation

## Overview

This application implements a comprehensive error handling system with multiple layers of protection, recovery mechanisms, and user feedback. The system is designed to provide graceful degradation and excellent user experience even when things go wrong.

## Architecture

### 1. Error Boundaries
React error boundaries catch JavaScript errors anywhere in the component tree and display fallback UI.

#### Components:
- **ErrorBoundary**: Main error boundary with retry functionality
- **PageErrorBoundary**: Page-level error boundary with navigation options
- **ComponentErrorBoundary**: Component-level error boundary with inline fallbacks
- **AsyncErrorBoundary**: Handles async operations and promise rejections

#### Usage:
```tsx
import { PageErrorBoundary } from '@/components/error/PageErrorBoundary';

<PageErrorBoundary pageName="Dashboard">
  <DashboardComponent />
</PageErrorBoundary>
```

### 2. Global Error Handler
Centralized error handling service that processes all application errors.

#### Features:
- **Error Classification**: Categorizes errors (network, authentication, validation, etc.)
- **Severity Levels**: Low, medium, high, critical
- **Context Enrichment**: Adds user, session, and environment data
- **Automatic Reporting**: Sends errors to backend for analysis
- **Offline Queuing**: Stores errors when offline and sends when reconnected

#### Usage:
```typescript
import { errorHandler } from '@/services/errorHandler';

// Handle API errors
errorHandler.handleApiError(error, { component: 'UserProfile', action: 'Load' });

// Report critical errors
errorHandler.reportCriticalError(new Error('System failure'), { userId: '123' });
```

### 3. Enhanced Toast Notifications
Rich notification system with retry capabilities and user actions.

#### Features:
- **Multiple Types**: Success, error, warning, info, loading
- **Retry Functionality**: Automatic retry with exponential backoff
- **Batch Operations**: Handle multiple operations with progress tracking
- **Specialized Notifications**: Pre-configured for common scenarios

#### Usage:
```typescript
import { notifications } from '@/services/notificationService';

// Error with retry
notifications.error('Operation failed', {
  retry: {
    maxRetries: 3,
    onRetry: async () => await retryOperation(),
  },
});

// Batch operation
notifications.batchOperation(
  items,
  async (item) => await processItem(item),
  {
    loading: 'Processing items...',
    success: 'All items processed',
    error: 'Some items failed',
  }
);
```

### 4. Structured Logging
Comprehensive logging system with multiple output targets.

#### Features:
- **Log Levels**: Debug, info, warn, error, critical
- **Context Tracking**: Component, action, user, session data
- **Multiple Outputs**: Console, localStorage, remote endpoint
- **Batching**: Efficient remote logging with batching and retry
- **Performance Tracking**: Built-in timing and grouping

#### Usage:
```typescript
import { logger } from '@/services/logger';

logger.info('User logged in', { userId: '123' }, 'AuthService', 'Login');
logger.error('API call failed', error, { endpoint: '/api/users' }, 'UserService', 'FetchUsers');
```

### 5. Error Recovery Mechanisms
Hooks and utilities for handling failed operations with automatic recovery.

#### Features:
- **Retry Logic**: Exponential backoff with jitter
- **Circuit Breaker**: Prevents cascading failures
- **Fallback States**: Graceful degradation with fallback data
- **Cancellation**: Proper cleanup of ongoing operations

#### Usage:
```typescript
import { useErrorRecovery, useCircuitBreaker } from '@/hooks/useErrorRecovery';

// Basic error recovery
const [state, actions] = useErrorRecovery(
  async () => await fetchUserData(),
  { maxAttempts: 3, baseDelay: 1000 }
);

// Circuit breaker pattern
const [circuitState, circuitActions] = useCircuitBreaker(
  async () => await callExternalService(),
  { failureThreshold: 5, resetTimeout: 60000 }
);
```

### 6. API Client with Error Handling
Enhanced API client with built-in error handling and retry logic.

#### Features:
- **Automatic Retries**: Server errors and network issues
- **Token Refresh**: Automatic token refresh on 401 errors
- **Request Timeout**: Configurable timeout with abort controller
- **Error Classification**: Automatic error categorization
- **Offline Detection**: Handles offline scenarios

#### Usage:
```typescript
import { apiClient } from '@/services/apiClient';

try {
  const response = await apiClient.get('/api/users');
  // Handle success
} catch (error) {
  // Error is automatically handled by global error handler
}
```

## Error Types and Handling

### Network Errors
- **Detection**: Connection failures, timeouts, offline state
- **Handling**: Automatic retry with exponential backoff
- **User Feedback**: Network error notifications with retry options
- **Recovery**: Queue operations for when connection is restored

### Authentication Errors (401)
- **Detection**: Invalid or expired tokens
- **Handling**: Automatic token refresh, redirect to login if needed
- **User Feedback**: Authentication required notification
- **Recovery**: Clear tokens and redirect to auth page

### Authorization Errors (403)
- **Detection**: Insufficient permissions
- **Handling**: Log error and show access denied message
- **User Feedback**: Permission error notification
- **Recovery**: Redirect to appropriate page or show fallback

### Validation Errors (400, 422)
- **Detection**: Invalid input data
- **Handling**: Extract validation messages from response
- **User Feedback**: Field-specific error messages
- **Recovery**: Highlight problematic fields and provide guidance

### Server Errors (500+)
- **Detection**: Internal server errors
- **Handling**: Automatic retry with circuit breaker
- **User Feedback**: Server error notification with retry
- **Recovery**: Fallback to cached data if available

### Client Errors (4xx)
- **Detection**: Bad requests, not found, etc.
- **Handling**: Log error and show appropriate message
- **User Feedback**: Specific error notifications
- **Recovery**: Navigate to valid page or show fallback

## Fallback Components

### Available Fallbacks:
- **ErrorFallback**: Generic error with retry button
- **NetworkErrorFallback**: Network-specific error UI
- **ServerErrorFallback**: Server error with retry
- **TimeoutErrorFallback**: Timeout-specific error
- **OfflineFallback**: Offline state indicator
- **MaintenanceFallback**: Maintenance mode display
- **EmptyStateFallback**: No data available state

### Usage:
```tsx
import { NetworkErrorFallback } from '@/components/error/FallbackComponents';

<ComponentErrorBoundary
  fallbackComponent={<NetworkErrorFallback retry={handleRetry} />}
>
  <NetworkDependentComponent />
</ComponentErrorBoundary>
```

## Configuration

### Error Handler Configuration:
```typescript
import { errorHandler } from '@/services/errorHandler';

errorHandler.configure({
  maxRetries: 3,
  baseDelay: 1000,
  enableReporting: true,
  reportingEndpoint: '/api/v1/errors/report',
});
```

### Logger Configuration:
```typescript
import { logger } from '@/services/logger';

logger.configure({
  level: LogLevel.INFO,
  enableConsole: true,
  enableStorage: true,
  enableRemote: true,
  maxStorageEntries: 100,
});
```

## Best Practices

### 1. Error Boundary Placement
- Place page-level boundaries around route components
- Use component-level boundaries for complex components
- Wrap async operations with AsyncErrorBoundary

### 2. Error Context
- Always provide meaningful context when logging errors
- Include component name, action, and relevant data
- Use consistent naming conventions

### 3. User Feedback
- Show appropriate error messages based on error type
- Provide actionable recovery options when possible
- Use progressive disclosure for technical details

### 4. Performance
- Use circuit breakers for external service calls
- Implement proper timeout handling
- Clean up resources in error scenarios

### 5. Testing
- Test error scenarios in development
- Use the ErrorTestPage component for manual testing
- Implement automated error handling tests

## Monitoring and Debugging

### Local Development:
- Check browser console for detailed error logs
- Use localStorage to view stored error history
- Enable debug logging for detailed information

### Production:
- Monitor error rates and patterns in backend logs
- Set up alerts for critical errors
- Use error IDs to track specific issues

### Error Test Page:
Visit `/error-test` to manually test all error handling scenarios:
- Error boundaries with different error types
- Global error handler with various error categories
- Toast notifications with retry functionality
- Logging system with different levels
- Error recovery mechanisms and circuit breakers
- Fallback component previews

## Integration with Backend

The error handling system integrates with the backend through:
- **Error Reporting Endpoint**: `/api/v1/errors/report`
- **Log Submission Endpoint**: `/api/v1/logs`
- **Health Check Endpoint**: `/api/v1/health`

Ensure these endpoints are implemented in your backend to receive error reports and logs from the frontend.
