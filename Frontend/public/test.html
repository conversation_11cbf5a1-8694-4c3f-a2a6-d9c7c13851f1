<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHCityRent Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        p {
            color: #666;
            line-height: 1.6;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PHCityRent - Static Test Page</h1>
        <p>This is a static HTML test page to verify the server is working correctly.</p>
        <p>If you can see this, the Vite dev server is serving static files properly.</p>
        <button onclick="alert('Static HTML is working!')">Click me!</button>
        
        <div style="margin-top: 20px;">
            <h2>Next Steps:</h2>
            <ul>
                <li>✅ Vite dev server is running</li>
                <li>✅ Static files are being served</li>
                <li>❓ React application needs debugging</li>
            </ul>
        </div>
    </div>
    
    <script>
        console.log('Static HTML test page loaded successfully');
        console.log('Current URL:', window.location.href);
        console.log('User Agent:', navigator.userAgent);
    </script>
</body>
</html>
