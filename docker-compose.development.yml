version: '3.8'

services:
  # Development-specific overrides
  backend:
    build:
      target: development
    environment:
      - NODE_ENV=development
      - DB_LOGGING=true
      - ENABLE_SWAGGER=true
      - ENABLE_DEBUG_MODE=true
    volumes:
      - ./Backend:/app
      - /app/node_modules
      - ./Backend/uploads:/app/uploads
    command: npm run start:dev

  frontend:
    build:
      target: development
    environment:
      - NODE_ENV=development
      - VITE_ENABLE_DEBUG_MODE=true
      - VITE_ENABLE_MOCK_DATA=true
      - VITE_ENABLE_REDUX_DEVTOOLS=true
      - VITE_ENABLE_REACT_QUERY_DEVTOOLS=true
    volumes:
      - ./Frontend:/app
      - /app/node_modules
    command: npm run dev -- --host 0.0.0.0

  # Development database with sample data
  postgres:
    environment:
      POSTGRES_DB: phcityrent_dev
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./Backend/database/seeds:/docker-entrypoint-initdb.d/seeds

  # Development tools
  mailhog:
    image: mailhog/mailhog:latest
    container_name: phcityrent-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - phcityrent-network

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: phcityrent-redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - phcityrent-network
    depends_on:
      - redis

  # pgAdmin for PostgreSQL management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: phcityrent-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - phcityrent-network
    depends_on:
      - postgres

volumes:
  postgres_dev_data:
    driver: local
  pgadmin_data:
    driver: local
