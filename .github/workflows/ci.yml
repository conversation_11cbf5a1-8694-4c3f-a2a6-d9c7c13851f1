name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18.x'
  POSTGRES_VERSION: '15'
  REDIS_VERSION: '7'

jobs:
  # Backend Tests
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: phcityrent_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: Backend/package-lock.json
      
      - name: Install dependencies
        working-directory: ./Backend
        run: npm ci
      
      - name: Create test environment file
        working-directory: ./Backend
        run: |
          cat > .env.test << EOF
          NODE_ENV=test
          DB_HOST=localhost
          DB_PORT=5432
          DB_USERNAME=postgres
          DB_PASSWORD=postgres
          DB_NAME=phcityrent_test
          REDIS_HOST=localhost
          REDIS_PORT=6379
          JWT_SECRET=test-jwt-secret
          JWT_REFRESH_SECRET=test-refresh-secret
          EOF
      
      - name: Run database migrations
        working-directory: ./Backend
        run: npm run migration:run
        env:
          NODE_ENV: test
      
      - name: Run linting
        working-directory: ./Backend
        run: npm run lint
      
      - name: Run unit tests
        working-directory: ./Backend
        run: npm run test
        env:
          NODE_ENV: test
      
      - name: Run integration tests
        working-directory: ./Backend
        run: npm run test:e2e
        env:
          NODE_ENV: test
      
      - name: Generate test coverage
        working-directory: ./Backend
        run: npm run test:cov
        env:
          NODE_ENV: test
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./Backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

  # Frontend Tests
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: Frontend/package-lock.json
      
      - name: Install dependencies
        working-directory: ./Frontend
        run: npm ci
      
      - name: Create test environment file
        working-directory: ./Frontend
        run: |
          cat > .env.test << EOF
          VITE_NODE_ENV=test
          VITE_API_BASE_URL=http://localhost:3001/api/v1
          VITE_ENABLE_MOCK_DATA=true
          EOF
      
      - name: Run linting
        working-directory: ./Frontend
        run: npm run lint
      
      - name: Run type checking
        working-directory: ./Frontend
        run: npm run type-check
      
      - name: Run unit tests
        working-directory: ./Frontend
        run: npm run test
      
      - name: Build application
        working-directory: ./Frontend
        run: npm run build
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./Frontend/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

  # E2E Tests
  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: phcityrent_e2e
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install root dependencies
        run: npm ci
      
      - name: Install backend dependencies
        working-directory: ./Backend
        run: npm ci
      
      - name: Install frontend dependencies
        working-directory: ./Frontend
        run: npm ci
      
      - name: Setup environment files
        run: |
          cp Backend/.env.development Backend/.env
          cp Frontend/.env.development Frontend/.env
      
      - name: Start backend
        working-directory: ./Backend
        run: |
          npm run build
          npm run start:prod &
          sleep 30
        env:
          NODE_ENV: test
      
      - name: Start frontend
        working-directory: ./Frontend
        run: |
          npm run build
          npm run preview &
          sleep 10
      
      - name: Install Playwright
        working-directory: ./Frontend
        run: npx playwright install --with-deps
      
      - name: Run E2E tests
        working-directory: ./Frontend
        run: npm run test:e2e
      
      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: Frontend/playwright-report/
          retention-days: 30

  # Security Audit
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      
      - name: Audit backend dependencies
        working-directory: ./Backend
        run: npm audit --audit-level=moderate
      
      - name: Audit frontend dependencies
        working-directory: ./Frontend
        run: npm audit --audit-level=moderate
      
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # Code Quality
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      
      - name: Install dependencies
        run: |
          cd Backend && npm ci
          cd ../Frontend && npm ci
      
      - name: Run SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # Build and Push Docker Images
  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            phcityrent/backend
            phcityrent/frontend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
      
      - name: Build and push backend image
        uses: docker/build-push-action@v5
        with:
          context: ./Backend
          push: true
          tags: phcityrent/backend:${{ github.sha }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      
      - name: Build and push frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./Frontend
          push: true
          tags: phcityrent/frontend:${{ github.sha }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Notify on completion
  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, e2e-tests, security-audit, code-quality]
    if: always()
    
    steps:
      - name: Notify Slack on success
        if: ${{ needs.backend-tests.result == 'success' && needs.frontend-tests.result == 'success' }}
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#ci-cd'
          text: '✅ CI pipeline passed for ${{ github.ref }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      
      - name: Notify Slack on failure
        if: ${{ needs.backend-tests.result == 'failure' || needs.frontend-tests.result == 'failure' }}
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#ci-cd'
          text: '❌ CI pipeline failed for ${{ github.ref }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
