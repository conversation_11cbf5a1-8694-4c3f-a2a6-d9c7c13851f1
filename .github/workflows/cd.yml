name: Continuous Deployment

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18.x'

jobs:
  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment:
      name: staging
      url: https://staging.phcityrent.com
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Build and push backend image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: phcityrent-backend
          IMAGE_TAG: staging-${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG ./Backend
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
      
      - name: Build and push frontend image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: phcityrent-frontend
          IMAGE_TAG: staging-${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG ./Frontend
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
      
      - name: Deploy to ECS
        run: |
          # Update ECS service with new image
          aws ecs update-service \
            --cluster phcityrent-staging \
            --service phcityrent-backend-staging \
            --force-new-deployment
          
          aws ecs update-service \
            --cluster phcityrent-staging \
            --service phcityrent-frontend-staging \
            --force-new-deployment
      
      - name: Wait for deployment
        run: |
          aws ecs wait services-stable \
            --cluster phcityrent-staging \
            --services phcityrent-backend-staging phcityrent-frontend-staging
      
      - name: Run smoke tests
        run: |
          # Wait for services to be ready
          sleep 60
          
          # Test backend health
          curl -f https://staging-api.phcityrent.com/api/v1/health
          
          # Test frontend
          curl -f https://staging.phcityrent.com
      
      - name: Notify deployment success
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#deployments'
          text: '🚀 Staging deployment successful for commit ${{ github.sha }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment:
      name: production
      url: https://phcityrent.com
    needs: [deploy-staging]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Create database backup
        run: |
          # Create backup before deployment
          aws rds create-db-snapshot \
            --db-instance-identifier phcityrent-prod \
            --db-snapshot-identifier phcityrent-prod-$(date +%Y%m%d-%H%M%S)
      
      - name: Build and push backend image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: phcityrent-backend
          IMAGE_TAG: production-${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG ./Backend
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
      
      - name: Build and push frontend image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: phcityrent-frontend
          IMAGE_TAG: production-${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG ./Frontend
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
      
      - name: Deploy to ECS with blue-green strategy
        run: |
          # Update task definition with new image
          TASK_DEFINITION=$(aws ecs describe-task-definition \
            --task-definition phcityrent-backend-prod \
            --query taskDefinition)
          
          # Update image in task definition
          NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "$ECR_REGISTRY/$ECR_REPOSITORY:production-${{ github.sha }}" \
            '.containerDefinitions[0].image = $IMAGE | del(.taskDefinitionArn) | del(.revision) | del(.status) | del(.requiresAttributes) | del(.placementConstraints) | del(.compatibilities) | del(.registeredAt) | del(.registeredBy)')
          
          # Register new task definition
          aws ecs register-task-definition \
            --cli-input-json "$NEW_TASK_DEFINITION"
          
          # Update service
          aws ecs update-service \
            --cluster phcityrent-production \
            --service phcityrent-backend-production \
            --task-definition phcityrent-backend-prod
      
      - name: Wait for deployment
        run: |
          aws ecs wait services-stable \
            --cluster phcityrent-production \
            --services phcityrent-backend-production phcityrent-frontend-production
      
      - name: Run production smoke tests
        run: |
          # Wait for services to be ready
          sleep 120
          
          # Test backend health
          curl -f https://api.phcityrent.com/api/v1/health
          
          # Test frontend
          curl -f https://phcityrent.com
          
          # Test critical endpoints
          curl -f https://api.phcityrent.com/api/v1/properties
      
      - name: Update DNS and CDN
        run: |
          # Invalidate CloudFront cache
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*"
      
      - name: Notify deployment success
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#deployments'
          text: '🎉 Production deployment successful for version ${{ github.ref_name }}'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Rollback on failure
  rollback:
    name: Rollback on Failure
    runs-on: ubuntu-latest
    if: failure()
    needs: [deploy-production]
    
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      
      - name: Rollback ECS service
        run: |
          # Get previous task definition
          PREVIOUS_TASK_DEF=$(aws ecs describe-services \
            --cluster phcityrent-production \
            --services phcityrent-backend-production \
            --query 'services[0].deployments[1].taskDefinition' \
            --output text)
          
          # Rollback to previous version
          aws ecs update-service \
            --cluster phcityrent-production \
            --service phcityrent-backend-production \
            --task-definition $PREVIOUS_TASK_DEF
      
      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#deployments'
          text: '⚠️ Production deployment failed and was rolled back'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Post-deployment monitoring
  post-deployment-monitoring:
    name: Post-deployment Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: success()
    
    steps:
      - name: Setup monitoring alerts
        run: |
          # Enable enhanced monitoring
          echo "Setting up post-deployment monitoring..."
          
          # You can add specific monitoring setup here
          # For example, enabling specific CloudWatch alarms
      
      - name: Performance baseline
        run: |
          # Run performance tests to establish baseline
          echo "Running performance baseline tests..."
          
          # You can add performance testing tools here
          # For example, Lighthouse CI, k6, etc.
      
      - name: Security scan
        run: |
          # Run security scans on deployed application
          echo "Running security scans..."
          
          # You can add security scanning tools here
          # For example, OWASP ZAP, Snyk, etc.
