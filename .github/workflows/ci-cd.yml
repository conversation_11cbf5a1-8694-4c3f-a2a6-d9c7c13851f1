name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # ==========================================
  # QUALITY CHECKS
  # ==========================================
  quality-checks:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            frontend/package-lock.json
            backend/package-lock.json

      - name: Install Frontend Dependencies
        run: |
          cd frontend
          npm ci

      - name: Install Backend Dependencies
        run: |
          cd backend
          npm ci

      - name: Lint Frontend
        run: |
          cd frontend
          npm run lint
          npm run type-check

      - name: Lint Backend
        run: |
          cd backend
          npm run lint
          npm run type-check

      - name: Security Audit
        run: |
          cd frontend && npm audit --audit-level=high
          cd ../backend && npm audit --audit-level=high

      - name: Code Quality Analysis
        uses: sonarcloud/sonarcloud-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # ==========================================
  # TESTING
  # ==========================================
  test-frontend:
    name: Frontend Tests
    runs-on: ubuntu-latest
    needs: quality-checks
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        run: |
          cd frontend
          npm ci

      - name: Run unit tests
        run: |
          cd frontend
          npm run test:coverage

      - name: Run integration tests
        run: |
          cd frontend
          npm run test:integration

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          directory: frontend/coverage
          flags: frontend

  test-backend:
    name: Backend Tests
    runs-on: ubuntu-latest
    needs: quality-checks
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: phcityrent_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        run: |
          cd backend
          npm ci

      - name: Run database migrations
        run: |
          cd backend
          npm run migration:run
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/phcityrent_test

      - name: Run unit tests
        run: |
          cd backend
          npm run test:coverage
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/phcityrent_test

      - name: Run integration tests
        run: |
          cd backend
          npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/phcityrent_test

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          directory: backend/coverage
          flags: backend

  # ==========================================
  # E2E TESTING
  # ==========================================
  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: |
          cd frontend && npm ci
          cd ../backend && npm ci

      - name: Build frontend
        run: |
          cd frontend
          npm run build

      - name: Start services
        run: |
          cd backend && npm run start:prod &
          cd frontend && npm run preview &
          sleep 30

      - name: Run Playwright tests
        run: |
          cd frontend
          npx playwright test

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: frontend/playwright-report/

  # ==========================================
  # BUILD & DEPLOY
  # ==========================================
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend, e2e-tests]
    if: github.ref == 'refs/heads/main'
    
    strategy:
      matrix:
        environment: [staging, production]
        
    environment:
      name: ${{ matrix.environment }}
      url: ${{ steps.deploy.outputs.url }}

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: |
          cd frontend && npm ci
          cd ../backend && npm ci

      - name: Build frontend
        run: |
          cd frontend
          npm run build
        env:
          VITE_API_URL: ${{ secrets.API_URL }}
          VITE_ENVIRONMENT: ${{ matrix.environment }}

      - name: Build backend
        run: |
          cd backend
          npm run build

      - name: Deploy to ${{ matrix.environment }}
        id: deploy
        run: |
          # Deploy logic here (Docker, Vercel, AWS, etc.)
          echo "Deploying to ${{ matrix.environment }}"
          echo "url=https://${{ matrix.environment }}.phcityrent.com" >> $GITHUB_OUTPUT

      - name: Run smoke tests
        run: |
          curl -f ${{ steps.deploy.outputs.url }}/health || exit 1

      - name: Notify team
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()

  # ==========================================
  # PERFORMANCE MONITORING
  # ==========================================
  performance-audit:
    name: Performance Audit
    runs-on: ubuntu-latest
    needs: build-and-deploy
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          configPath: './lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true

      - name: Bundle size analysis
        run: |
          cd frontend
          npm run analyze:bundle

  # ==========================================
  # SECURITY SCANNING
  # ==========================================
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: quality-checks
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

      - name: OWASP ZAP Scan
        uses: zaproxy/action-full-scan@v0.7.0
        with:
          target: 'https://staging.phcityrent.com'
