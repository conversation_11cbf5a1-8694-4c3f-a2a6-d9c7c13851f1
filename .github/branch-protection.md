# Branch Protection Rules Setup

## 🛡️ **GitHub Branch Protection Configuration**

### **Step 1: Navigate to Repository Settings**
1. Go to your GitHub repository: `https://github.com/Woldreamz-Inc/ptownmoving`
2. Click on **Settings** tab
3. Click on **Branches** in the left sidebar

### **Step 2: Add Branch Protection Rule for `main`**

Click **Add rule** and configure:

```yaml
Branch name pattern: main

Settings to Enable:
✅ Require a pull request before merging
  ✅ Require approvals: 1
  ✅ Dismiss stale PR approvals when new commits are pushed
  ✅ Require review from code owners

✅ Require status checks to pass before merging
  ✅ Require branches to be up to date before merging
  Status checks to require:
    - ci/lint-and-test
    - ci/security-scan
    - ci/build

✅ Require conversation resolution before merging
✅ Require signed commits
✅ Require linear history
✅ Include administrators

Restrictions:
✅ Restrict pushes that create files larger than 100MB
```

### **Step 3: Add Branch Protection Rule for `develop`**

```yaml
Branch name pattern: develop

Settings to Enable:
✅ Require a pull request before merging
  ✅ Require approvals: 1

✅ Require status checks to pass before merging
  Status checks to require:
    - ci/lint-and-test
    - ci/build

✅ Require conversation resolution before merging
✅ Include administrators
```

### **Step 4: Create CODEOWNERS File**

This ensures the right people review critical changes:
